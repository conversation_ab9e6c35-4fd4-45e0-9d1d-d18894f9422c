
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChineseErrorCorrector3-4B 模型架构图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .diagram-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .mermaid {
            text-align: center;
        }
        .download-btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .download-btn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ ChineseErrorCorrector3-4B 模型架构图</h1>

        <h2>📊 模型架构流程图</h2>
        <div class="diagram-container">
            <div class="mermaid" id="diagram1">
graph TD
    A[Input Tokens] --> B[Token Embedding]
    B --> C[Position Encoding]
    C --> D[Dropout]
    D --> E[Transformer Layers]
    
    subgraph "Transformer Block (×36)"
        E --> F[Layer Norm]
        F --> G[Multi-Head Attention]
        G --> H[Residual Connection]
        H --> I[Layer Norm]
        I --> J[Feed Forward Network]
        J --> K[Residual Connection]
    end
    
    K --> L[Final Layer Norm]
    L --> M[Language Model Head]
    M --> N[Output Logits]
    
    subgraph "Multi-Head Attention Details"
        G --> G1[Query Projection]
        G --> G2[Key Projection] 
        G --> G3[Value Projection]
        G1 --> G4[Scaled Dot-Product Attention]
        G2 --> G4
        G3 --> G4
        G4 --> G5[Output Projection]
    end
    
    subgraph "Feed Forward Network Details"
        J --> J1[Linear Layer 1]
        J1 --> J2[Activation Function]
        J2 --> J3[Linear Layer 2]
    end
    
    subgraph "Model Configuration"
        P1["Vocab Size: 151,936"]
        P2["Hidden Size: 2,560"]
        P3["Num Layers: 36"]
        P4["Attention Heads: 32"]
        P5["Intermediate Size: 9,728"]
        P6["Max Position: 40,960"]
        P7["Total Parameters: 4.02B"]
    end
    
    style A fill:#e1f5fe
    style N fill:#e8f5e8
    style E fill:#fff3e0
    style G fill:#fce4ec
    style J fill:#f3e5f5
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="download-btn" onclick="downloadSVG('diagram1', '模型架构流程图')">下载 SVG</button>
                <button class="download-btn" onclick="downloadPNG('diagram1', '模型架构流程图')">下载 PNG</button>
            </div>
        </div>

        <h2>📊 参数分布图</h2>
        <div class="diagram-container">
            <div class="mermaid" id="diagram2">
pie title 参数分布 (总计: 4.02B)
    "MLP Layers": 66.9
    "Attention Layers": 23.5
    "Embedding": 9.7
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="download-btn" onclick="downloadSVG('diagram2', '参数分布图')">下载 SVG</button>
                <button class="download-btn" onclick="downloadPNG('diagram2', '参数分布图')">下载 PNG</button>
            </div>
        </div>

    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
        
        function downloadSVG(diagramId, title) {
            const svg = document.querySelector(`#${diagramId} svg`);
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = `${title}.svg`;
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }
        }
        
        function downloadPNG(diagramId, title) {
            const svg = document.querySelector(`#${diagramId} svg`);
            if (svg) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const url = URL.createObjectURL(svgBlob);
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);
                    
                    canvas.toBlob(function(blob) {
                        const downloadLink = document.createElement('a');
                        downloadLink.href = URL.createObjectURL(blob);
                        downloadLink.download = `${title}.png`;
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    });
                };
                
                img.src = url;
            }
        }
    </script>
</body>
</html>
