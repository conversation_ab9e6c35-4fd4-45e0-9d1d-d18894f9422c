#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Convert DOCX file to Markdown format
"""

import os
import sys
from docx import Document
import re


def clean_text(text):
    """Clean and format text for markdown"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Handle special characters
    text = text.replace('\u2019', "'")  # Right single quotation mark
    text = text.replace('\u2018', "'")  # Left single quotation mark
    text = text.replace('\u201c', '"')  # Left double quotation mark
    text = text.replace('\u201d', '"')  # Right double quotation mark
    text = text.replace('\u2013', '-')  # En dash
    text = text.replace('\u2014', '--') # Em dash
    
    return text


def convert_docx_to_markdown(docx_path, output_path):
    """Convert DOCX file to Markdown format"""
    
    print(f"Converting: {docx_path}")
    print(f"Output: {output_path}")
    
    try:
        # Load the document
        doc = Document(docx_path)
        
        markdown_content = []
        markdown_content.append("# Document Content\n")
        
        # Process each paragraph
        for i, paragraph in enumerate(doc.paragraphs):
            text = clean_text(paragraph.text)
            
            if not text:
                continue
                
            # Detect heading levels based on style or formatting
            style_name = paragraph.style.name.lower() if paragraph.style else ""
            
            if 'heading' in style_name:
                # Extract heading level
                if 'heading 1' in style_name:
                    markdown_content.append(f"\n# {text}\n")
                elif 'heading 2' in style_name:
                    markdown_content.append(f"\n## {text}\n")
                elif 'heading 3' in style_name:
                    markdown_content.append(f"\n### {text}\n")
                elif 'heading 4' in style_name:
                    markdown_content.append(f"\n#### {text}\n")
                elif 'heading 5' in style_name:
                    markdown_content.append(f"\n##### {text}\n")
                elif 'heading 6' in style_name:
                    markdown_content.append(f"\n###### {text}\n")
                else:
                    markdown_content.append(f"\n## {text}\n")
            else:
                # Regular paragraph
                markdown_content.append(f"{text}\n\n")
        
        # Process tables if any
        if doc.tables:
            markdown_content.append("\n## Tables\n")
            for table_idx, table in enumerate(doc.tables):
                markdown_content.append(f"\n### Table {table_idx + 1}\n")
                
                # Get table headers
                if table.rows:
                    headers = []
                    for cell in table.rows[0].cells:
                        headers.append(clean_text(cell.text))
                    
                    if headers:
                        # Add table header
                        markdown_content.append("| " + " | ".join(headers) + " |\n")
                        markdown_content.append("| " + " | ".join(["---"] * len(headers)) + " |\n")
                        
                        # Add table rows
                        for row in table.rows[1:]:
                            row_data = []
                            for cell in row.cells:
                                row_data.append(clean_text(cell.text))
                            markdown_content.append("| " + " | ".join(row_data) + " |\n")
                        
                        markdown_content.append("\n")
        
        # Join all content
        final_content = "".join(markdown_content)
        
        # Write to output file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"✅ Conversion completed successfully!")
        print(f"📄 Output saved to: {output_path}")
        
        # Show preview
        lines = final_content.split('\n')
        preview_lines = min(20, len(lines))
        print(f"\n📖 Preview (first {preview_lines} lines):")
        print("-" * 50)
        for line in lines[:preview_lines]:
            print(line)
        if len(lines) > preview_lines:
            print("...")
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Error converting file: {e}")
        return False


def main():
    """Main function"""
    
    # Input and output paths
    input_path = "ChineseErrorCorrector/data/paper1_test.docx"
    output_path = "ChineseErrorCorrector/data/paper1_test.md"
    
    # Check if input file exists
    if not os.path.exists(input_path):
        print(f"❌ Input file not found: {input_path}")
        return
    
    # Create output directory if needed
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Convert the file
    success = convert_docx_to_markdown(input_path, output_path)
    
    if success:
        print(f"\n🎉 Conversion completed!")
        print(f"📁 Markdown file created: {output_path}")
    else:
        print(f"\n💥 Conversion failed!")


if __name__ == "__main__":
    main()
