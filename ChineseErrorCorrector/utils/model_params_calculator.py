#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型参数量计算工具
用于计算Transformer模型的参数量，支持多种模型架构
"""

import math
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """模型配置信息"""
    # 基本架构参数
    vocab_size: int = 32000          # 词汇表大小
    hidden_size: int = 4096          # 隐藏层维度
    num_layers: int = 32             # Transformer层数
    num_attention_heads: int = 32    # 注意力头数
    intermediate_size: int = 11008   # FFN中间层维度
    
    # 位置编码参数
    max_position_embeddings: int = 2048  # 最大位置编码长度
    
    # 其他参数
    tie_word_embeddings: bool = True     # 是否共享输入输出embedding
    use_bias: bool = False               # 是否使用bias
    rms_norm: bool = True                # 是否使用RMSNorm
    
    # 模型名称和描述
    model_name: str = "ChineseErrorCorrector"
    model_size: str = "4B"


class ModelParamsCalculator:
    """模型参数量计算器"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        
    def calculate_embedding_params(self) -> Dict[str, int]:
        """计算embedding层参数"""
        params = {}
        
        # Token embedding
        params['token_embedding'] = self.config.vocab_size * self.config.hidden_size
        
        # Position embedding (如果使用)
        if hasattr(self.config, 'use_position_embedding') and self.config.use_position_embedding:
            params['position_embedding'] = self.config.max_position_embeddings * self.config.hidden_size
        else:
            params['position_embedding'] = 0
            
        return params
    
    def calculate_attention_params(self) -> Dict[str, int]:
        """计算单个注意力层参数"""
        params = {}
        hidden_size = self.config.hidden_size
        
        # Q, K, V投影矩阵
        params['q_proj'] = hidden_size * hidden_size
        params['k_proj'] = hidden_size * hidden_size  
        params['v_proj'] = hidden_size * hidden_size
        
        # 输出投影矩阵
        params['o_proj'] = hidden_size * hidden_size
        
        # Bias (如果使用)
        if self.config.use_bias:
            params['attention_bias'] = hidden_size * 4  # Q,K,V,O的bias
        else:
            params['attention_bias'] = 0
            
        return params
    
    def calculate_ffn_params(self) -> Dict[str, int]:
        """计算单个FFN层参数"""
        params = {}
        hidden_size = self.config.hidden_size
        intermediate_size = self.config.intermediate_size
        
        # 上投影 (通常是gate_proj和up_proj)
        params['gate_proj'] = hidden_size * intermediate_size
        params['up_proj'] = hidden_size * intermediate_size
        
        # 下投影
        params['down_proj'] = intermediate_size * hidden_size
        
        # Bias (如果使用)
        if self.config.use_bias:
            params['ffn_bias'] = intermediate_size * 2 + hidden_size  # gate, up, down的bias
        else:
            params['ffn_bias'] = 0
            
        return params
    
    def calculate_norm_params(self) -> Dict[str, int]:
        """计算单个层的归一化参数"""
        params = {}
        hidden_size = self.config.hidden_size
        
        # 注意力后的LayerNorm/RMSNorm
        params['attention_norm'] = hidden_size
        
        # FFN后的LayerNorm/RMSNorm  
        params['ffn_norm'] = hidden_size
        
        return params
    
    def calculate_single_layer_params(self) -> Dict[str, int]:
        """计算单个Transformer层参数"""
        params = {}
        
        # 注意力层参数
        attention_params = self.calculate_attention_params()
        params.update({f'attention_{k}': v for k, v in attention_params.items()})
        
        # FFN层参数
        ffn_params = self.calculate_ffn_params()
        params.update({f'ffn_{k}': v for k, v in ffn_params.items()})
        
        # 归一化层参数
        norm_params = self.calculate_norm_params()
        params.update({f'norm_{k}': v for k, v in norm_params.items()})
        
        return params
    
    def calculate_output_layer_params(self) -> Dict[str, int]:
        """计算输出层参数"""
        params = {}
        
        # 最终的LayerNorm/RMSNorm
        params['final_norm'] = self.config.hidden_size
        
        # 输出投影层 (LM head)
        if self.config.tie_word_embeddings:
            # 如果共享embedding，则不需要额外参数
            params['lm_head'] = 0
        else:
            params['lm_head'] = self.config.hidden_size * self.config.vocab_size
            
        return params
    
    def calculate_total_params(self) -> Dict[str, Any]:
        """计算总参数量"""
        total_params = {}
        
        # 1. Embedding层参数
        embedding_params = self.calculate_embedding_params()
        embedding_total = sum(embedding_params.values())
        total_params['embedding'] = {
            'details': embedding_params,
            'total': embedding_total
        }
        
        # 2. 单层参数
        single_layer_params = self.calculate_single_layer_params()
        single_layer_total = sum(single_layer_params.values())
        
        # 3. 所有Transformer层参数
        all_layers_total = single_layer_total * self.config.num_layers
        total_params['transformer_layers'] = {
            'single_layer_details': single_layer_params,
            'single_layer_total': single_layer_total,
            'all_layers_total': all_layers_total,
            'num_layers': self.config.num_layers
        }
        
        # 4. 输出层参数
        output_params = self.calculate_output_layer_params()
        output_total = sum(output_params.values())
        total_params['output'] = {
            'details': output_params,
            'total': output_total
        }
        
        # 5. 总计
        grand_total = embedding_total + all_layers_total + output_total
        total_params['summary'] = {
            'embedding_params': embedding_total,
            'transformer_params': all_layers_total,
            'output_params': output_total,
            'total_params': grand_total,
            'total_params_M': grand_total / 1_000_000,
            'total_params_B': grand_total / 1_000_000_000
        }
        
        return total_params
    
    def print_params_summary(self):
        """打印参数量摘要"""
        params = self.calculate_total_params()
        summary = params['summary']
        
        print("=" * 60)
        print(f"模型参数量计算 - {self.config.model_name} ({self.config.model_size})")
        print("=" * 60)
        
        print(f"📊 模型配置:")
        print(f"  - 词汇表大小: {self.config.vocab_size:,}")
        print(f"  - 隐藏层维度: {self.config.hidden_size:,}")
        print(f"  - Transformer层数: {self.config.num_layers}")
        print(f"  - 注意力头数: {self.config.num_attention_heads}")
        print(f"  - FFN中间维度: {self.config.intermediate_size:,}")
        print(f"  - 共享embedding: {self.config.tie_word_embeddings}")
        print()
        
        print(f"🔢 参数量统计:")
        print(f"  - Embedding层: {summary['embedding_params']:,} ({summary['embedding_params']/1_000_000:.1f}M)")
        print(f"  - Transformer层: {summary['transformer_params']:,} ({summary['transformer_params']/1_000_000:.1f}M)")
        print(f"  - 输出层: {summary['output_params']:,} ({summary['output_params']/1_000_000:.1f}M)")
        print(f"  - 总参数量: {summary['total_params']:,}")
        print(f"  - 总参数量: {summary['total_params_M']:.1f}M")
        print(f"  - 总参数量: {summary['total_params_B']:.2f}B")
        print()
        
        # 计算每层平均参数量
        single_layer = params['transformer_layers']['single_layer_total']
        print(f"📈 详细分析:")
        print(f"  - 单层参数量: {single_layer:,} ({single_layer/1_000_000:.1f}M)")
        print(f"  - 层数占比: {summary['transformer_params']/summary['total_params']*100:.1f}%")
        print(f"  - Embedding占比: {summary['embedding_params']/summary['total_params']*100:.1f}%")
        print("=" * 60)


def get_common_model_configs() -> Dict[str, ModelConfig]:
    """获取常见模型配置"""
    configs = {
        'ChineseErrorCorrector-4B': ModelConfig(
            vocab_size=32000,
            hidden_size=4096,
            num_layers=32,
            num_attention_heads=32,
            intermediate_size=11008,
            model_name="ChineseErrorCorrector",
            model_size="4B"
        ),
        
        'Llama2-7B': ModelConfig(
            vocab_size=32000,
            hidden_size=4096,
            num_layers=32,
            num_attention_heads=32,
            intermediate_size=11008,
            model_name="Llama2",
            model_size="7B"
        ),
        
        'Qwen-7B': ModelConfig(
            vocab_size=151936,
            hidden_size=4096,
            num_layers=32,
            num_attention_heads=32,
            intermediate_size=22016,
            model_name="Qwen",
            model_size="7B"
        ),
        
        'ChatGLM3-6B': ModelConfig(
            vocab_size=65024,
            hidden_size=4096,
            num_layers=28,
            num_attention_heads=32,
            intermediate_size=13696,
            model_name="ChatGLM3",
            model_size="6B"
        )
    }
    
    return configs


def main():
    """主函数 - 演示用法"""
    # 获取预定义配置
    configs = get_common_model_configs()
    
    # 计算ChineseErrorCorrector-4B参数量
    config = configs['ChineseErrorCorrector-4B']
    calculator = ModelParamsCalculator(config)
    calculator.print_params_summary()
    
    print("\n" + "="*60)
    print("其他模型对比:")
    print("="*60)
    
    # 对比其他模型
    for name, cfg in configs.items():
        calc = ModelParamsCalculator(cfg)
        params = calc.calculate_total_params()
        total_b = params['summary']['total_params_B']
        print(f"{name:25}: {total_b:.2f}B 参数")


if __name__ == '__main__':
    main()
