#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG增强纠错器批量处理示例
"""

import asyncio
import os
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

async def batch_processing_example():
    """批量处理示例"""
    print("🔄 批量处理示例")
    
    # 读取演示数据
    with open("demo_input.txt", 'r', encoding='utf-8') as f:
        texts = [line.strip() for line in f if line.strip()]
    
    print(f"📖 读取 {len(texts)} 个文本")
    
    # 创建纠错器
    corrector = LightRAGEnhancedCorrector(
        working_dir="./batch_rag_corrector",
        enable_rag=True,
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    await corrector.initialize()
    
    # 批量纠错
    results = await corrector.batch_correct(texts, batch_size=5)
    
    # 保存结果
    corrector.save_results(results, "batch_output.txt")
    
    print(f"✅ 批量处理完成，结果保存到 batch_output.txt")

if __name__ == "__main__":
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置 OPENAI_API_KEY 环境变量")
        exit(1)
    
    asyncio.run(batch_processing_example())
