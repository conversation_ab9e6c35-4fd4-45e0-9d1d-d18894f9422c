#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文文本校对领域RAG知识库构建器
专门为中文错误纠正构建专业知识库
"""

import os
import sys
import json
import asyncio
from typing import List, Dict, Any
from pathlib import Path

# 添加路径
sys.path.append('.')
sys.path.append('./ChineseErrorCorrector')

try:
    from lightrag_enhanced_corrector import LightRAGEnhancedCorrector
    CORRECTOR_AVAILABLE = True
except ImportError:
    print("⚠️ 增强纠错器不可用")
    CORRECTOR_AVAILABLE = False

class ChineseCorrectionKnowledgeBuilder:
    """中文校对知识库构建器"""
    
    def __init__(self, working_dir: str = "./chinese_correction_kb"):
        self.working_dir = working_dir
        self.knowledge_categories = {
            "grammar_errors": [],      # 语法错误
            "punctuation_errors": [],  # 标点错误
            "word_usage_errors": [],   # 用词错误
            "structure_errors": [],    # 结构错误
            "style_errors": [],        # 文体错误
            "grammar_rules": [],       # 语法规则
            "correction_patterns": [], # 纠错模式
            "domain_knowledge": []     # 领域知识
        }
        
        print(f"🏗️ 中文校对知识库构建器初始化")
        print(f"   工作目录: {working_dir}")
    
    def load_grammar_error_examples(self) -> List[Dict[str, str]]:
        """加载语法错误示例"""
        grammar_errors = [
            # 语气词误用
            {
                "error": "我很喜欢这本书的",
                "correct": "我很喜欢这本书",
                "type": "语气词误用",
                "explanation": "陈述句末尾不需要语气词'的'",
                "rule": "语气词'的'主要用于疑问句或强调句，陈述句末尾通常省略"
            },
            {
                "error": "今天天气很好的",
                "correct": "今天天气很好",
                "type": "语气词误用",
                "explanation": "简单陈述句末尾的'的'是多余的",
                "rule": "陈述客观事实时，句末不使用语气词"
            },
            
            # 得字句错误
            {
                "error": "他跑的很快",
                "correct": "他跑得很快",
                "type": "得字句错误",
                "explanation": "'得'用于补语，表示程度或结果",
                "rule": "动词+得+形容词/副词，表示动作的程度或结果"
            },
            {
                "error": "学生们学习的很认真",
                "correct": "学生们学习得很认真",
                "type": "得字句错误",
                "explanation": "'学习'是动词，后面的程度补语用'得'连接",
                "rule": "动词后的程度补语必须用'得'字连接"
            },
            
            # 的地得混用
            {
                "error": "美丽得花园",
                "correct": "美丽的花园",
                "type": "的地得混用",
                "explanation": "形容词修饰名词用'的'",
                "rule": "形容词+的+名词，表示修饰关系"
            },
            {
                "error": "认真的学习",
                "correct": "认真地学习",
                "type": "的地得混用",
                "explanation": "副词修饰动词用'地'",
                "rule": "副词+地+动词，表示动作的方式"
            },
            
            # 量词错误
            {
                "error": "一头牛奶",
                "correct": "一杯牛奶",
                "type": "量词错误",
                "explanation": "牛奶是液体，应该用'杯'、'瓶'等量词",
                "rule": "量词要与名词的性质相匹配"
            },
            {
                "error": "三个书",
                "correct": "三本书",
                "type": "量词错误",
                "explanation": "书籍的量词是'本'",
                "rule": "不同类别的名词有特定的量词"
            },
            
            # 主谓不一致
            {
                "error": "学生们正在认真的学习",
                "correct": "学生们正在认真地学习",
                "type": "主谓不一致",
                "explanation": "修饰动词'学习'应该用'地'",
                "rule": "副词修饰动词时使用'地'"
            },
            
            # 重复表达
            {
                "error": "大约三点钟左右",
                "correct": "大约三点钟",
                "type": "重复表达",
                "explanation": "'大约'和'左右'意思重复",
                "rule": "避免使用意思相同的词语重复表达"
            },
            {
                "error": "首先第一",
                "correct": "首先",
                "type": "重复表达",
                "explanation": "'首先'和'第一'意思重复",
                "rule": "序数词和序数副词不要重复使用"
            }
        ]
        
        self.knowledge_categories["grammar_errors"] = grammar_errors
        print(f"✅ 加载语法错误示例: {len(grammar_errors)} 个")
        return grammar_errors
    
    def load_punctuation_error_examples(self) -> List[Dict[str, str]]:
        """加载标点错误示例"""
        punctuation_errors = [
            {
                "error": "你好吗？我很好。",
                "correct": "你好吗？我很好。",
                "type": "标点使用正确",
                "explanation": "问句用问号，陈述句用句号",
                "rule": "不同句型使用相应的标点符号"
            },
            {
                "error": "苹果，香蕉，橘子",
                "correct": "苹果、香蕉、橘子",
                "type": "顿号错误",
                "explanation": "并列的词语之间用顿号",
                "rule": "同类词语并列时使用顿号分隔"
            },
            {
                "error": "他说：我要去学校。",
                "correct": "他说：\"我要去学校。\"",
                "type": "引号缺失",
                "explanation": "直接引语应该使用引号",
                "rule": "引用他人话语时使用引号"
            }
        ]
        
        self.knowledge_categories["punctuation_errors"] = punctuation_errors
        print(f"✅ 加载标点错误示例: {len(punctuation_errors)} 个")
        return punctuation_errors
    
    def load_grammar_rules(self) -> List[str]:
        """加载语法规则"""
        grammar_rules = [
            # 基础语法规则
            "主语+谓语+宾语是中文的基本句型结构",
            "形容词修饰名词时使用'的'字连接",
            "副词修饰动词时使用'地'字连接",
            "动词后的补语使用'得'字连接",
            "量词要与名词的性质相匹配",
            
            # 语气词规则
            "陈述句末尾通常不使用语气词'的'",
            "疑问句可以使用'吗'、'呢'等语气词",
            "感叹句使用'啊'、'呀'等语气词",
            
            # 标点符号规则
            "句子结束使用句号、问号或感叹号",
            "并列词语之间使用顿号分隔",
            "直接引语使用引号标示",
            "书名、篇名使用书名号",
            
            # 词语搭配规则
            "动词和宾语要搭配合理",
            "形容词和名词要搭配恰当",
            "避免使用意思重复的词语",
            
            # 语序规则
            "时间状语通常放在主语前或谓语前",
            "地点状语通常放在时间状语后",
            "定语放在被修饰词前面",
            "补语放在被补充说明的词后面"
        ]
        
        self.knowledge_categories["grammar_rules"] = grammar_rules
        print(f"✅ 加载语法规则: {len(grammar_rules)} 条")
        return grammar_rules
    
    def load_correction_patterns(self) -> List[Dict[str, str]]:
        """加载纠错模式"""
        correction_patterns = [
            {
                "pattern": ".*的$",
                "description": "句末语气词'的'检查",
                "action": "检查是否为陈述句，如是则删除'的'",
                "examples": ["我很好的 -> 我很好", "今天很热的 -> 今天很热"]
            },
            {
                "pattern": r"(\w+)的(很|非常|特别)(\w+)",
                "description": "的得混用检查",
                "action": "动词后的程度补语改用'得'",
                "examples": ["跑的很快 -> 跑得很快", "学的很好 -> 学得很好"]
            },
            {
                "pattern": r"(认真|仔细|努力)的(\w+)",
                "description": "副词修饰动词检查",
                "action": "副词修饰动词改用'地'",
                "examples": ["认真的学习 -> 认真地学习", "仔细的观察 -> 仔细地观察"]
            },
            {
                "pattern": r"(大约|约).*?(左右|上下)",
                "description": "重复表达检查",
                "action": "删除重复的表示概数的词",
                "examples": ["大约三点左右 -> 大约三点", "约十个左右 -> 约十个"]
            }
        ]
        
        self.knowledge_categories["correction_patterns"] = correction_patterns
        print(f"✅ 加载纠错模式: {len(correction_patterns)} 个")
        return correction_patterns
    
    def load_domain_knowledge(self) -> List[str]:
        """加载领域知识"""
        domain_knowledge = [
            # 中文写作规范
            "标准中文书面语避免使用过多的语气词",
            "正式文体中应使用规范的词汇和句式",
            "口语化表达在书面语中需要规范化",
            
            # 语言学知识
            "汉语是分析语，语序和虚词是主要的语法手段",
            "汉语的词类划分主要依据词的语法功能",
            "汉语句子的语序相对固定，主要是SVO结构",
            
            # 纠错原则
            "语法纠错需要考虑语境和语义完整性",
            "纠错时要保持原意不变",
            "优先纠正明显的语法错误",
            "注意区分方言和标准语的差异",
            
            # 常见错误类型
            "语气词误用是中文学习者的常见错误",
            "的地得混用是汉语拼音输入法造成的典型错误",
            "量词使用错误反映了对汉语量词系统的不熟悉",
            "重复表达通常是受其他语言影响的结果"
        ]
        
        self.knowledge_categories["domain_knowledge"] = domain_knowledge
        print(f"✅ 加载领域知识: {len(domain_knowledge)} 条")
        return domain_knowledge
    
    def load_existing_datasets(self) -> List[Dict[str, str]]:
        """从现有数据集加载错误示例"""
        dataset_examples = []
        
        # 检查是否有现有的数据集文件
        data_dirs = [
            "../data/FCGEC_data",
            "../data/NLPCC2018_data", 
            "../data/split_data"
        ]
        
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                print(f"📂 发现数据集目录: {data_dir}")
                # 这里可以添加具体的数据集加载逻辑
                # 由于数据集格式可能不同，这里只是示例
        
        # 添加一些通用的错误示例
        common_examples = [
            {
                "error": "这个问题很难解决的",
                "correct": "这个问题很难解决",
                "type": "语气词冗余",
                "source": "通用示例"
            },
            {
                "error": "老师讲课讲的很清楚",
                "correct": "老师讲课讲得很清楚", 
                "type": "得字句错误",
                "source": "通用示例"
            }
        ]
        
        dataset_examples.extend(common_examples)
        print(f"✅ 加载数据集示例: {len(dataset_examples)} 个")
        return dataset_examples
    
    def build_knowledge_base(self) -> Dict[str, Any]:
        """构建完整的知识库"""
        print(f"\n🏗️ 开始构建中文校对知识库")
        print("=" * 50)
        
        # 加载各类知识
        grammar_errors = self.load_grammar_error_examples()
        punctuation_errors = self.load_punctuation_error_examples()
        grammar_rules = self.load_grammar_rules()
        correction_patterns = self.load_correction_patterns()
        domain_knowledge = self.load_domain_knowledge()
        dataset_examples = self.load_existing_datasets()
        
        # 组装知识库
        knowledge_base = {
            "metadata": {
                "name": "中文文本校对知识库",
                "version": "1.0",
                "description": "专门用于中文错误纠正的RAG知识库",
                "created_by": "ChineseCorrectionKnowledgeBuilder",
                "categories": list(self.knowledge_categories.keys())
            },
            "grammar_errors": grammar_errors,
            "punctuation_errors": punctuation_errors,
            "grammar_rules": grammar_rules,
            "correction_patterns": correction_patterns,
            "domain_knowledge": domain_knowledge,
            "dataset_examples": dataset_examples,
            "statistics": {
                "total_error_examples": len(grammar_errors) + len(punctuation_errors) + len(dataset_examples),
                "total_rules": len(grammar_rules),
                "total_patterns": len(correction_patterns),
                "total_domain_knowledge": len(domain_knowledge)
            }
        }
        
        # 保存知识库
        kb_file = os.path.join(self.working_dir, "chinese_correction_knowledge_base.json")
        os.makedirs(self.working_dir, exist_ok=True)
        
        with open(kb_file, 'w', encoding='utf-8') as f:
            json.dump(knowledge_base, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 知识库构建完成:")
        print(f"   错误示例: {knowledge_base['statistics']['total_error_examples']} 个")
        print(f"   语法规则: {knowledge_base['statistics']['total_rules']} 条")
        print(f"   纠错模式: {knowledge_base['statistics']['total_patterns']} 个")
        print(f"   领域知识: {knowledge_base['statistics']['total_domain_knowledge']} 条")
        print(f"   保存位置: {kb_file}")
        
        return knowledge_base

    def format_knowledge_for_rag(self, knowledge_base: Dict[str, Any]) -> List[str]:
        """将知识库格式化为RAG系统可用的文本"""
        rag_texts = []

        # 格式化错误示例
        print("📝 格式化错误示例...")
        for error in knowledge_base["grammar_errors"]:
            text = f"""
错误类型：{error['type']}
错误示例：{error['error']}
正确形式：{error['correct']}
解释说明：{error['explanation']}
语法规则：{error['rule']}
纠错建议：将"{error['error']}"纠正为"{error['correct']}"，因为{error['explanation']}。
"""
            rag_texts.append(text.strip())

        for error in knowledge_base["punctuation_errors"]:
            text = f"""
错误类型：{error['type']}
错误示例：{error['error']}
正确形式：{error['correct']}
解释说明：{error['explanation']}
标点规则：{error['rule']}
"""
            rag_texts.append(text.strip())

        # 格式化语法规则
        print("📖 格式化语法规则...")
        for rule in knowledge_base["grammar_rules"]:
            text = f"中文语法规则：{rule}"
            rag_texts.append(text)

        # 格式化纠错模式
        print("🔧 格式化纠错模式...")
        for pattern in knowledge_base["correction_patterns"]:
            text = f"""
纠错模式：{pattern['description']}
匹配模式：{pattern['pattern']}
纠错动作：{pattern['action']}
示例：{'; '.join(pattern['examples'])}
"""
            rag_texts.append(text.strip())

        # 格式化领域知识
        print("🧠 格式化领域知识...")
        for knowledge in knowledge_base["domain_knowledge"]:
            text = f"中文纠错领域知识：{knowledge}"
            rag_texts.append(text)

        print(f"✅ 格式化完成，共 {len(rag_texts)} 条RAG文本")
        return rag_texts

    async def integrate_with_rag_system(self, knowledge_base: Dict[str, Any],
                                        rag_working_dir: str = "./rag_chinese_corrector") -> bool:
        """将知识库集成到RAG系统"""
        if not CORRECTOR_AVAILABLE:
            print("❌ 增强纠错器不可用，无法集成RAG系统")
            return False

        print(f"\n🔗 集成知识库到RAG系统")
        print("=" * 50)

        try:
            # 创建增强纠错器
            corrector = LightRAGEnhancedCorrector(
                working_dir=rag_working_dir,
                enable_rag=True,
                api_key=os.getenv("OPENAI_API_KEY")
            )

            # 初始化
            await corrector.initialize()

            # 格式化知识为RAG文本
            rag_texts = self.format_knowledge_for_rag(knowledge_base)

            # 构建RAG知识库
            print("🚀 开始构建RAG知识库...")

            # 分批插入知识（避免一次性插入过多）
            batch_size = 50
            total_batches = (len(rag_texts) + batch_size - 1) // batch_size

            for i in range(0, len(rag_texts), batch_size):
                batch = rag_texts[i:i + batch_size]
                batch_num = i // batch_size + 1

                print(f"📚 插入批次 {batch_num}/{total_batches} ({len(batch)} 条知识)")

                if corrector.rag_system:
                    await corrector.rag_system.insert(batch)
                else:
                    print("⚠️ RAG系统未初始化，跳过插入")
                    break

            print("✅ RAG知识库构建完成")
            return True

        except Exception as e:
            print(f"❌ RAG系统集成失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_usage_guide(self) -> str:
        """创建使用指南"""
        guide = """
# 中文校对RAG知识库使用指南

## 📚 知识库内容

### 1. 语法错误示例
- 语气词误用：句末"的"字错误
- 得字句错误："的得"混用
- 量词错误：量词与名词不匹配
- 重复表达：意思重复的词语

### 2. 标点符号错误
- 顿号使用：并列词语分隔
- 引号使用：直接引语标示
- 句号问号：句型对应

### 3. 语法规则库
- 基础句型结构规则
- 修饰关系规则
- 语序规则
- 标点使用规则

### 4. 纠错模式
- 正则表达式模式
- 纠错动作描述
- 实际应用示例

## 🚀 使用方法

### 构建知识库
```python
from build_chinese_correction_knowledge_base import ChineseCorrectionKnowledgeBuilder

# 创建构建器
builder = ChineseCorrectionKnowledgeBuilder()

# 构建知识库
knowledge_base = builder.build_knowledge_base()

# 集成到RAG系统
await builder.integrate_with_rag_system(knowledge_base)
```

### 使用RAG增强纠错
```python
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

# 创建增强纠错器
corrector = LightRAGEnhancedCorrector(enable_rag=True)
await corrector.initialize()

# 纠错文本
result = await corrector.correct_with_rag_enhancement("我很喜欢这本书的")
print(f"纠错结果: {result.corrected_text}")
print(f"纠错理由: {result.correction_reasoning}")
```

## 📈 预期效果

使用专业知识库后，RAG增强纠错系统将能够：

1. **准确识别**常见的中文语法错误
2. **提供详细**的纠错理由和语法解释
3. **基于规则**进行一致性纠错
4. **学习改进**通过知识库持续优化

## 🔧 扩展知识库

### 添加新的错误类型
1. 在相应的加载函数中添加新示例
2. 重新构建知识库
3. 重新集成到RAG系统

### 从数据集导入
1. 实现数据集解析函数
2. 调用load_existing_datasets()
3. 自动格式化为知识库格式

### 用户反馈集成
1. 收集用户纠错反馈
2. 验证反馈质量
3. 添加到知识库中
"""

        guide_file = os.path.join(self.working_dir, "USAGE_GUIDE.md")
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide)

        print(f"📖 使用指南已保存: {guide_file}")
        return guide

async def main():
    """主函数 - 完整的知识库构建流程"""
    print("🚀 中文文本校对RAG知识库构建流程")
    print("=" * 60)

    # 创建知识库构建器
    builder = ChineseCorrectionKnowledgeBuilder(
        working_dir="./chinese_correction_knowledge_base"
    )

    # 步骤1: 构建知识库
    print("\n📋 步骤1: 构建知识库")
    knowledge_base = builder.build_knowledge_base()

    # 步骤2: 创建使用指南
    print("\n📖 步骤2: 创建使用指南")
    builder.create_usage_guide()

    # 步骤3: 集成到RAG系统
    print("\n🔗 步骤3: 集成到RAG系统")
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        success = await builder.integrate_with_rag_system(
            knowledge_base,
            rag_working_dir="./rag_chinese_corrector_with_kb"
        )
        if success:
            print("✅ RAG系统集成成功")
        else:
            print("❌ RAG系统集成失败")
    else:
        print("⚠️ 未设置OPENAI_API_KEY，跳过RAG集成")
        print("💡 设置API密钥后可运行: export OPENAI_API_KEY='your-key'")

    # 步骤4: 测试验证
    print("\n🧪 步骤4: 测试验证")
    if CORRECTOR_AVAILABLE and api_key:
        await test_rag_with_knowledge_base()
    else:
        print("⚠️ 跳过测试（缺少依赖或API密钥）")

    print(f"\n🎉 知识库构建流程完成！")
    print(f"📂 知识库位置: {builder.working_dir}")
    print(f"🔧 RAG系统位置: ./rag_chinese_corrector_with_kb")

async def test_rag_with_knowledge_base():
    """测试带知识库的RAG系统"""
    print("🧪 测试RAG增强纠错（带知识库）")
    print("-" * 40)

    try:
        # 创建增强纠错器
        corrector = LightRAGEnhancedCorrector(
            working_dir="./rag_chinese_corrector_with_kb",
            enable_rag=True
        )

        await corrector.initialize()

        # 测试句子
        test_sentences = [
            "我很喜欢这本书的",
            "他跑的很快",
            "认真的学习",
            "大约三点钟左右"
        ]

        for i, sentence in enumerate(test_sentences, 1):
            print(f"\n📝 测试 {i}: {sentence}")

            result = await corrector.correct_with_rag_enhancement(
                sentence, use_rag=True, rag_mode="hybrid"
            )

            print(f"   纠错结果: {result.corrected_text}")
            print(f"   置信度: {result.confidence:.2f}")
            if result.correction_reasoning:
                print(f"   纠错理由: {result.correction_reasoning}")
            if result.rag_context:
                print(f"   RAG上下文: {result.rag_context[:100]}...")

        print("✅ RAG测试完成")

    except Exception as e:
        print(f"❌ RAG测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
