#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG增强的中文错误纠正器
结合RAG技术改进ChineseErrorCorrector的输出结果
"""

import os
import sys
import asyncio
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

# 添加路径
sys.path.append('.')
sys.path.append('./ChineseErrorCorrector')
sys.path.append('./LightRAG')

try:
    from lightrag_compatibility_wrapper import create_compatible_rag_system, MockQueryParam
    LIGHTRAG_AVAILABLE = True
    print("使用LightRAG兼容性包装器")
except ImportError:
    try:
        from lightrag import LightRAG, QueryParam
        from lightrag.llm.openai import openai_complete_if_cache, openai_embed
        from lightrag.utils import EmbeddingFunc
        LIGHTRAG_AVAILABLE = True
        print("使用原生LightRAG")
    except ImportError:
        print("LightRAG未安装，将使用基础纠错模式")
        LIGHTRAG_AVAILABLE = False

try:
    from ChineseErrorCorrector.llm.infer.hf_infer import HFTextCorrectInfer
    CEC_AVAILABLE = True
except ImportError:
    print("ChineseErrorCorrector未可用")
    CEC_AVAILABLE = False

@dataclass
class CorrectionResult:
    """纠错结果数据类"""
    original_text: str
    corrected_text: str
    confidence: float
    error_types: List[str]
    rag_context: Optional[str] = None
    rag_confidence: Optional[float] = None
    correction_reasoning: Optional[str] = None

class LightRAGEnhancedCorrector:
    """LightRAG增强的中文错误纠正器"""
    
    def __init__(self, 
                 working_dir: str = "./rag_enhanced_corrector",
                 enable_rag: bool = True,
                 api_key: str = None,
                 base_url: str = None):
        """
        初始化增强纠错器
        
        Args:
            working_dir: RAG工作目录
            enable_rag: 是否启用RAG增强
            api_key: OpenAI API密钥
            base_url: API基础URL
        """
        self.working_dir = working_dir
        self.enable_rag = enable_rag and LIGHTRAG_AVAILABLE
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = base_url or "https://api.openai.com/v1"
        
        # 初始化组件
        self.cec_corrector = None
        self.rag_system = None
        self.initialized = False
        
        print(f"   LightRAG增强纠错器初始化")
        print(f"   RAG增强: {'启用' if self.enable_rag else '禁用'}")
        print(f"   工作目录: {working_dir}")
    
    async def initialize(self):
        """异步初始化所有组件"""
        if self.initialized:
            return
        
        print("🚀 正在初始化增强纠错器...")
        
        # 初始化基础纠错器
        if CEC_AVAILABLE:
            print(" 初始化ChineseErrorCorrector...")
            self.cec_corrector = HFTextCorrectInfer()
            print(" ChineseErrorCorrector初始化完成")
        else:
            print(" ChineseErrorCorrector不可用")
        
        # 初始化RAG系统
        if self.enable_rag and self.api_key:
            print(" 初始化LightRAG系统...")
            await self._initialize_rag()
            print(" LightRAG系统初始化完成")
        else:
            print(" RAG系统未启用（缺少API密钥或LightRAG不可用）")
        
        self.initialized = True
        print(" 增强纠错器初始化完成！")
    
    async def _initialize_rag(self):
        """初始化RAG系统"""
        if not os.path.exists(self.working_dir):
            os.makedirs(self.working_dir, exist_ok=True)

        try:
            # 尝试使用兼容性包装器
            self.rag_system = create_compatible_rag_system(
                working_dir=self.working_dir,
                api_key=self.api_key,
                base_url=self.base_url
            )
        except NameError:
            # 如果兼容性包装器不可用，尝试原生LightRAG
            try:
                self.rag_system = LightRAG(
                    working_dir=self.working_dir,
                    llm_model_func=lambda prompt, system_prompt=None, history_messages=[], **kwargs:
                        openai_complete_if_cache(
                            "gpt-4o-mini",
                            prompt,
                            system_prompt=system_prompt,
                            history_messages=history_messages,
                            api_key=self.api_key,
                            base_url=self.base_url,
                            **kwargs
                        ),
                    embedding_func=EmbeddingFunc(
                        embedding_dim=1536,
                        func=lambda texts: openai_embed(
                            texts,
                            model="text-embedding-3-small",
                            api_key=self.api_key,
                            base_url=self.base_url
                        )
                    )
                )
            except NameError:
                raise Exception("无法初始化RAG系统，请检查LightRAG安装")

        # 初始化存储
        await self.rag_system.initialize_storages()
    
    async def build_knowledge_base(self, 
                                   error_examples: List[Dict[str, str]],
                                   grammar_rules: List[str] = None,
                                   domain_texts: List[str] = None):
        """
        构建错误纠正知识库
        
        Args:
            error_examples: 错误示例列表 [{"error": "错误文本", "correct": "正确文本", "type": "错误类型"}]
            grammar_rules: 语法规则列表
            domain_texts: 领域文本列表
        """
        if not self.enable_rag or not self.rag_system:
            print(" RAG系统未启用，跳过知识库构建")
            return
        
        print(" 开始构建错误纠正知识库...")
        
        knowledge_texts = []
        
        # 处理错误示例
        if error_examples:
            print(f" 处理 {len(error_examples)} 个错误示例...")
            for example in error_examples:
                knowledge_text = f"""
错误类型：{example.get('type', '未知')}
错误文本：{example['error']}
正确文本：{example['correct']}
纠错说明：将"{example['error']}"纠正为"{example['correct']}"，这是{example.get('type', '语法')}错误的典型案例。
"""
                knowledge_texts.append(knowledge_text.strip())
        
        # 处理语法规则
        if grammar_rules:
            print(f" 处理 {len(grammar_rules)} 条语法规则...")
            for rule in grammar_rules:
                knowledge_text = f"中文语法规则：{rule}"
                knowledge_texts.append(knowledge_text)
        
        # 处理领域文本
        if domain_texts:
            print(f" 处理 {len(domain_texts)} 个领域文本...")
            knowledge_texts.extend(domain_texts)
        
        # 批量插入知识库
        if knowledge_texts:
            print(f" 向RAG系统插入 {len(knowledge_texts)} 条知识...")
            await self.rag_system.insert(knowledge_texts)
            print(" 知识库构建完成")
        else:
            print(" 没有知识需要插入")
    
    async def correct_with_rag_enhancement(self, 
                                           text: str,
                                           use_rag: bool = True,
                                           rag_mode: str = "hybrid") -> CorrectionResult:
        """
        使用RAG增强进行文本纠错
        
        Args:
            text: 待纠错文本
            use_rag: 是否使用RAG增强
            rag_mode: RAG检索模式
            
        Returns:
            CorrectionResult: 纠错结果
        """
        if not self.initialized:
            await self.initialize()
        
        # 基础纠错
        corrected_text = text
        confidence = 0.5
        error_types = []
        
        if self.cec_corrector:
            try:
                cec_results = self.cec_corrector.infer([text])
                if cec_results:
                    corrected_text = cec_results[0]
                    confidence = 0.8  # 基础置信度
                    error_types = ["语法错误"]  # 简化处理
            except Exception as e:
                print(f" 基础纠错失败: {e}")
        
        # RAG增强
        rag_context = None
        rag_confidence = None
        correction_reasoning = None
        
        if use_rag and self.enable_rag and self.rag_system:
            try:
                # 构建RAG查询
                rag_query = f"如何纠正这个中文句子的错误：{text}"
                
                # 检索相关知识
                try:
                    # 尝试使用QueryParam（原生LightRAG）
                    rag_response = await self.rag_system.query(
                        rag_query,
                        param=QueryParam(mode=rag_mode)
                    )
                except NameError:
                    # 使用MockQueryParam（兼容模式）
                    try:
                        rag_response = await self.rag_system.query(
                            rag_query,
                            param=MockQueryParam(mode=rag_mode)
                        )
                    except NameError:
                        # 直接查询
                        rag_response = await self.rag_system.query(rag_query)
                
                if rag_response:
                    rag_context = rag_response
                    rag_confidence = 0.9  # RAG置信度
                    
                    # 使用RAG结果进一步优化纠错
                    enhanced_result = await self._enhance_correction_with_rag(
                        text, corrected_text, rag_response
                    )
                    
                    if enhanced_result:
                        corrected_text = enhanced_result["corrected"]
                        correction_reasoning = enhanced_result["reasoning"]
                        confidence = min(confidence + 0.2, 1.0)  # 提升置信度
                        
            except Exception as e:
                print(f" RAG增强失败: {e}")
        
        return CorrectionResult(
            original_text=text,
            corrected_text=corrected_text,
            confidence=confidence,
            error_types=error_types,
            rag_context=rag_context,
            rag_confidence=rag_confidence,
            correction_reasoning=correction_reasoning
        )
    
    async def _enhance_correction_with_rag(self, 
                                           original: str, 
                                           corrected: str, 
                                           rag_context: str) -> Optional[Dict[str, str]]:
        """使用RAG上下文增强纠错结果"""
        if not self.rag_system:
            return None
        
        try:
            enhancement_prompt = f"""
基于以下信息，请优化中文句子的纠错结果：

原始句子：{original}
初步纠错：{corrected}
相关知识：{rag_context}

请提供：
1. 最终纠错结果
2. 纠错理由

请以JSON格式回答：
{{"corrected": "最终纠错结果", "reasoning": "纠错理由"}}
"""
            
            response = await self.rag_system.llm_model_func(enhancement_prompt)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                return result
            except:
                # 如果JSON解析失败，返回基础结果
                return {
                    "corrected": corrected,
                    "reasoning": "RAG增强处理中出现格式问题，使用基础纠错结果"
                }
                
        except Exception as e:
            print(f" RAG增强处理失败: {e}")
            return None
    
    async def batch_correct(self, 
                            texts: List[str],
                            batch_size: int = 10,
                            use_rag: bool = True) -> List[CorrectionResult]:
        """批量纠错处理"""
        if not self.initialized:
            await self.initialize()
        
        print(f" 开始批量纠错，共 {len(texts)} 个文本，批次大小 {batch_size}")
        
        results = []
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(texts))
            batch_texts = texts[start_idx:end_idx]
            
            print(f" 处理批次 {batch_idx + 1}/{total_batches} ({len(batch_texts)} 个文本)")
            
            # 并发处理当前批次
            batch_tasks = [
                self.correct_with_rag_enhancement(text, use_rag=use_rag)
                for text in batch_texts
            ]
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    print(f" 文本 {start_idx + i + 1} 处理失败: {result}")
                    # 创建失败结果
                    results.append(CorrectionResult(
                        original_text=batch_texts[i],
                        corrected_text=batch_texts[i],
                        confidence=0.0,
                        error_types=["处理失败"]
                    ))
                else:
                    results.append(result)
        
        print(f" 批量纠错完成，处理了 {len(results)} 个文本")
        return results
    
    def save_results(self, results: List[CorrectionResult], output_file: str):
        """保存纠错结果"""
        print(f" 保存结果到 {output_file}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 保存详细结果
        detailed_results = []
        simple_results = []
        
        for result in results:
            # 详细结果
            detailed_results.append({
                "original_text": result.original_text,
                "corrected_text": result.corrected_text,
                "confidence": result.confidence,
                "error_types": result.error_types,
                "rag_context": result.rag_context,
                "rag_confidence": result.rag_confidence,
                "correction_reasoning": result.correction_reasoning
            })
            
            # 简单结果（只有纠错文本）
            simple_results.append(result.corrected_text)
        
        # 保存详细结果
        detailed_file = output_file.replace('.txt', '_detailed.json')
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)
        
        # 保存简单结果
        with open(output_file, 'w', encoding='utf-8') as f:
            for text in simple_results:
                f.write(text + '\n')
        
        print(f"✅ 结果已保存:")
        print(f"   简单结果: {output_file}")
        print(f"   详细结果: {detailed_file}")

async def demo_usage():
    """演示用法"""
    print(" LightRAG增强中文错误纠正器演示")
    print("=" * 60)

    # 初始化增强纠错器
    corrector = LightRAGEnhancedCorrector(
        working_dir="./rag_corrector_demo",
        enable_rag=True,
        api_key=os.getenv("OPENAI_API_KEY")  # 需要设置环境变量
    )

    await corrector.initialize()

    # 构建示例知识库
    error_examples = [
        {
            "error": "我很喜欢吃苹果的",
            "correct": "我很喜欢吃苹果",
            "type": "语气词误用"
        },
        {
            "error": "他们在学校里学习的很认真",
            "correct": "他们在学校里学习得很认真",
            "type": "得字句错误"
        },
        {
            "error": "这个问题很难解决的",
            "correct": "这个问题很难解决",
            "type": "语气词冗余"
        }
    ]

    grammar_rules = [
        "中文句末通常不需要语气词'的'，除非表示确定或强调",
        "'得'字用于补语，'的'字用于定语",
        "动词后的程度补语应该用'得'连接"
    ]

    print(" 构建错误纠正知识库...")
    await corrector.build_knowledge_base(
        error_examples=error_examples,
        grammar_rules=grammar_rules
    )

    # 测试纠错
    test_texts = [
        "我很喜这本书。",
        "他足包的很快的。",
        "这个菜做的很不错，很难吃。",
        "学生们的学习的很努力。"
    ]

    print(f"\n 测试纠错，共 {len(test_texts)} 个句子")
    print("-" * 40)

    for i, text in enumerate(test_texts, 1):
        print(f"\n 测试 {i}: {text}")

        # 不使用RAG的纠错
        result_basic = await corrector.correct_with_rag_enhancement(
            text, use_rag=False
        )

        # 使用RAG增强的纠错
        result_enhanced = await corrector.correct_with_rag_enhancement(
            text, use_rag=True, rag_mode="hybrid"
        )

        print(f"   基础纠错: {result_basic.corrected_text} (置信度: {result_basic.confidence:.2f})")
        print(f"   RAG增强: {result_enhanced.corrected_text} (置信度: {result_enhanced.confidence:.2f})")

        if result_enhanced.correction_reasoning:
            print(f"   纠错理由: {result_enhanced.correction_reasoning}")

    print(f"\n 演示完成！")

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='LightRAG增强的中文错误纠正器')
    parser.add_argument('--mode', choices=['demo', 'batch', 'single'],
                       default='demo', help='运行模式')
    parser.add_argument('--input', help='输入文件路径（batch模式）')
    parser.add_argument('--output', help='输出文件路径（batch模式）')
    parser.add_argument('--text', help='单个文本（single模式）')
    parser.add_argument('--working_dir', default='./rag_corrector',
                       help='RAG工作目录')
    parser.add_argument('--batch_size', type=int, default=10,
                       help='批处理大小')
    parser.add_argument('--disable_rag', action='store_true',
                       help='禁用RAG增强')

    args = parser.parse_args()

    if args.mode == 'demo':
        await demo_usage()

    elif args.mode == 'single':
        if not args.text:
            print(" single模式需要提供--text参数")
            return

        corrector = LightRAGEnhancedCorrector(
            working_dir=args.working_dir,
            enable_rag=not args.disable_rag
        )

        await corrector.initialize()

        result = await corrector.correct_with_rag_enhancement(
            args.text, use_rag=not args.disable_rag
        )

        print(f"原文: {result.original_text}")
        print(f"纠错: {result.corrected_text}")
        print(f"置信度: {result.confidence:.2f}")
        if result.correction_reasoning:
            print(f"理由: {result.correction_reasoning}")

    elif args.mode == 'batch':
        if not args.input or not args.output:
            print(" batch模式需要提供--input和--output参数")
            return

        if not os.path.exists(args.input):
            print(f" 输入文件不存在: {args.input}")
            return

        # 读取输入文件
        with open(args.input, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]

        print(f" 读取 {len(texts)} 个文本")

        corrector = LightRAGEnhancedCorrector(
            working_dir=args.working_dir,
            enable_rag=not args.disable_rag
        )

        await corrector.initialize()

        # 批量处理
        results = await corrector.batch_correct(
            texts,
            batch_size=args.batch_size,
            use_rag=not args.disable_rag
        )

        # 保存结果
        corrector.save_results(results, args.output)

        # 统计
        total_texts = len(results)
        improved_count = sum(1 for r in results if r.corrected_text != r.original_text)
        avg_confidence = sum(r.confidence for r in results) / total_texts if total_texts > 0 else 0

        print(f"\n 处理统计:")
        print(f"   总文本数: {total_texts}")
        print(f"   改进文本数: {improved_count}")
        print(f"   改进率: {improved_count/total_texts*100:.1f}%")
        print(f"   平均置信度: {avg_confidence:.2f}")

if __name__ == "__main__":
    asyncio.run(main())
