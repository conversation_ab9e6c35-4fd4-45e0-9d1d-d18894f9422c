#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for Chinese Syntax Tree Generator
中文语法树生成器测试脚本

Usage:
    python test_syntax_tree.py
"""

import sys
import os

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from chinese_syntax_tree import ChineseSyntaxTreeGenerator


def test_basic_functionality():
    """测试基本功能"""
    print("🌳 中文语法树生成器测试")
    print("=" * 60)

    # 测试句子
    test_sentences = [
        "我爱中国。",
        "小明在学校里认真学习。",
        "这是一个美丽的春天。",
        "他们正在讨论重要的问题。",
        "北京是中国的首都，也是一个历史悠久的城市。",
        "人工智能技术正在快速发展。"
    ]

    # 测试不同的方法
    methods = ["rule_based", "jieba"]  # 只测试肯定可用的方法

    for method in methods:
        print(f"\n🔧 测试方法: {method}")
        print("-" * 40)

        try:
            generator = ChineseSyntaxTreeGenerator(method=method, enable_implicit=False)

            for i, sentence in enumerate(test_sentences[:3], 1):  # 只测试前3个句子
                print(f"\n📝 示例 {i}: {sentence}")
                print("🌲 语法树结构:")

                result = generator.analyze_sentence(sentence, verbose=False)
                print(result['tree_string'])

                print(f"📊 统计: 节点数={result['statistics']['total_nodes']}, "
                      f"深度={result['statistics']['max_depth']}, "
                      f"叶子={result['statistics']['leaf_nodes']}")

        except Exception as e:
            print(f"❌ 方法 {method} 测试失败: {e}")


def test_implicit_parse_tree():
    """测试隐式解析树功能"""
    print(f"\n{'='*60}")
    print("🔍 测试隐式解析树功能")
    print("=" * 60)

    # 专门测试包含隐式成分的句子
    implicit_test_sentences = [
        "去学校。",  # 省略主语
        "看书了。",  # 省略主语 + 时间标记
        "认真学习。",  # 省略主语 + 方式状语
        "给老师。",  # 省略主语和宾语
        "在图书馆学习。",  # 省略主语 + 地点状语
        "快速跑步。",  # 省略主语 + 方式状语
        "会来的。",  # 省略主语 + 将来时标记
    ]

    generator = ChineseSyntaxTreeGenerator(method="jieba", enable_implicit=True)

    for i, sentence in enumerate(implicit_test_sentences, 1):
        print(f"\n📝 测试句子 {i}: {sentence}")
        print("-" * 30)

        try:
            result = generator.analyze_sentence(sentence, verbose=False, include_implicit=True)

            print("🌲 基础语法树:")
            print(result['tree_string'])

            if result.get('implicit_nodes'):
                print(f"\n🔍 隐式增强树:")
                print(result['implicit_tree_string'])

                print(f"\n📋 隐式成分分析:")
                print(result['implicit_analysis'])

                print(f"📊 隐式统计: 发现 {len(result['implicit_nodes'])} 个隐式成分")
            else:
                print("\n❌ 未发现隐式成分")

        except Exception as e:
            print(f"❌ 隐式分析失败: {e}")


def test_implicit_comparison():
    """对比基础树和隐式树"""
    print(f"\n{'='*60}")
    print("⚖️ 基础树 vs 隐式树对比")
    print("=" * 60)

    test_sentence = "认真学习了。"
    print(f"📝 测试句子: {test_sentence}")

    # 基础树
    print(f"\n🌲 基础语法树:")
    generator_basic = ChineseSyntaxTreeGenerator(method="jieba", enable_implicit=False)
    result_basic = generator_basic.analyze_sentence(test_sentence, verbose=False)
    print(result_basic['tree_string'])
    print(f"节点数: {result_basic['statistics']['total_nodes']}")

    # 隐式树
    print(f"\n🔍 隐式增强树:")
    generator_implicit = ChineseSyntaxTreeGenerator(method="jieba", enable_implicit=True)
    result_implicit = generator_implicit.analyze_sentence(test_sentence, verbose=False, include_implicit=True)

    if result_implicit.get('implicit_tree_string'):
        print(result_implicit['implicit_tree_string'])
        print(f"节点数: {result_implicit['implicit_statistics']['total_nodes']}")
        print(f"隐式节点数: {len(result_implicit['implicit_nodes'])}")

        print(f"\n📋 隐式成分详情:")
        print(result_implicit['implicit_analysis'])
    else:
        print("未生成隐式树")


def test_tree_operations():
    """测试树操作功能"""
    print(f"\n{'='*60}")
    print("🔧 测试树操作功能")
    print("=" * 60)
    
    generator = ChineseSyntaxTreeGenerator(method="rule_based")
    sentence = "我在北京大学学习计算机科学。"
    
    print(f"📝 测试句子: {sentence}")
    
    # 生成语法树
    tree = generator.generate_tree(sentence)
    
    # 测试树转字典
    tree_dict = generator.tree_to_dict(tree)
    print(f"\n📋 树结构 (字典格式):")
    import json
    print(json.dumps(tree_dict, ensure_ascii=False, indent=2))
    
    # 测试保存功能
    output_dir = "ChineseErrorCorrector/utils/test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存为文本格式
    txt_path = os.path.join(output_dir, "syntax_tree.txt")
    generator.save_tree(tree, txt_path, format="txt")
    print(f"\n💾 已保存文本格式到: {txt_path}")
    
    # 保存为JSON格式
    json_path = os.path.join(output_dir, "syntax_tree.json")
    generator.save_tree(tree, json_path, format="json")
    print(f"💾 已保存JSON格式到: {json_path}")
    
    # 验证文件内容
    print(f"\n📖 文本文件内容:")
    with open(txt_path, 'r', encoding='utf-8') as f:
        print(f.read())


def test_error_handling():
    """测试错误处理"""
    print(f"\n{'='*60}")
    print("🛠️ 测试错误处理")
    print("=" * 60)
    
    # 测试空句子
    generator = ChineseSyntaxTreeGenerator()
    
    test_cases = [
        "",
        "   ",
        "。",
        "！？",
        "123",
        "Hello World",
        "中英混合 mixed text 测试"
    ]
    
    for case in test_cases:
        print(f"\n📝 测试用例: '{case}'")
        try:
            result = generator.analyze_sentence(case, verbose=False)
            print(f"✅ 成功处理，节点数: {result['statistics']['total_nodes']}")
        except Exception as e:
            print(f"❌ 处理失败: {e}")


def test_available_methods():
    """测试可用方法检测"""
    print(f"\n{'='*60}")
    print("🔍 检测可用的分析方法")
    print("=" * 60)
    
    generator = ChineseSyntaxTreeGenerator()
    available_methods = generator.available_methods
    
    print(f"📋 可用方法: {', '.join(available_methods)}")
    
    # 测试每个可用方法
    test_sentence = "这是一个测试句子。"
    
    for method in available_methods:
        print(f"\n🔧 测试方法: {method}")
        try:
            method_generator = ChineseSyntaxTreeGenerator(method=method)
            result = method_generator.analyze_sentence(test_sentence, verbose=False)
            print(f"✅ 方法 {method} 工作正常")
            print(f"   节点数: {result['statistics']['total_nodes']}")
        except Exception as e:
            print(f"❌ 方法 {method} 失败: {e}")


def interactive_test():
    """交互式测试"""
    print(f"\n{'='*60}")
    print("🎮 交互式测试模式")
    print("=" * 60)
    print("输入中文句子进行语法分析，输入 'quit' 退出")
    
    generator = ChineseSyntaxTreeGenerator()
    
    while True:
        try:
            sentence = input("\n📝 请输入句子: ").strip()
            
            if sentence.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 再见！")
                break
            
            if not sentence:
                print("⚠️ 请输入有效的句子")
                continue
            
            print(f"\n🌲 分析结果:")
            result = generator.analyze_sentence(sentence, verbose=False)
            print(result['tree_string'])
            
            stats = result['statistics']
            print(f"\n📊 统计信息: 节点数={stats['total_nodes']}, "
                  f"深度={stats['max_depth']}, 叶子节点={stats['leaf_nodes']}")
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 分析出错: {e}")


def main():
    """主测试函数"""
    print("🚀 开始中文语法树生成器测试（包含隐式解析树）")

    # 基本功能测试
    test_basic_functionality()

    # 隐式解析树测试
    test_implicit_parse_tree()

    # 基础树vs隐式树对比
    test_implicit_comparison()

    # 树操作测试
    test_tree_operations()

    # 错误处理测试
    test_error_handling()

    # 可用方法测试
    test_available_methods()

    # 询问是否进行交互式测试
    print(f"\n{'='*60}")
    response = input("🎮 是否进行交互式测试？(y/n): ").strip().lower()
    if response in ['y', 'yes', '是', 'Y']:
        interactive_test()

    print(f"\n✅ 测试完成！")


if __name__ == "__main__":
    main()
