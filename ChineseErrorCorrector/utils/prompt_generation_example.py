#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词生成示例
展示如何根据知识库生成高质量的纠错提示词
"""

from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor

def generate_detailed_prompt_example():
    """生成详细的提示词示例"""
    print("📝 提示词生成详细示例")
    print("=" * 60)
    
    # 初始化处理器
    kb_path = "/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/utils/complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.jsons"
    processor = ChineseKnowledgeBaseProcessor(kb_path)
    
    # 测试文本
    test_text = "我很喜欢这件衣服的。"
    
    print(f"🎯 输入文本: {test_text}")
    print("-" * 40)
    
    # 1. 检索相关知识
    print("🔍 步骤1: 检索相关知识")
    retrieved_knowledge = processor.retrieve_relevant_knowledge(test_text, top_k=3)
    
    for i, result in enumerate(retrieved_knowledge, 1):
        print(f"\n📚 检索结果 {i}:")
        print(f"   匹配方式: {result.match_type}")
        print(f"   相关度: {result.relevance_score:.2f}")
        print(f"   匹配关键词: {', '.join(result.matched_keywords)}")
        
        if 'error' in result.knowledge_item:
            print(f"   错误示例: {result.knowledge_item['error']}")
            print(f"   正确形式: {result.knowledge_item['correct']}")
            print(f"   错误类型: {result.knowledge_item['type']}")
    
    # 2. 生成提示词
    print(f"\n📋 步骤2: 生成纠错提示词")
    prompt = processor.generate_correction_prompt(test_text, retrieved_knowledge)
    
    print(f"\n✅ 生成的完整提示词:")
    print("=" * 60)
    print(prompt)
    print("=" * 60)
    
    # 3. 分析提示词结构
    print(f"\n📊 步骤3: 提示词结构分析")
    prompt_lines = prompt.split('\n')
    
    sections = {
        "角色定义": 0,
        "待纠正文本": 0,
        "知识库信息": 0,
        "纠错要求": 0,
        "输出格式": 0
    }
    
    current_section = None
    for line in prompt_lines:
        if "专业的中文文本校对专家" in line:
            current_section = "角色定义"
        elif "## 待纠正文本" in line:
            current_section = "待纠正文本"
        elif "## 相关知识库信息" in line:
            current_section = "知识库信息"
        elif "## 纠错要求" in line:
            current_section = "纠错要求"
        elif "## 输出格式" in line:
            current_section = "输出格式"
        
        if current_section:
            sections[current_section] += 1
    
    print(f"   总长度: {len(prompt)} 字符")
    print(f"   总行数: {len(prompt_lines)} 行")
    print(f"   知识条目数: {len(retrieved_knowledge)} 个")
    
    for section, line_count in sections.items():
        print(f"   {section}: {line_count} 行")

def show_prompt_variations():
    """展示不同错误类型的提示词变化"""
    print(f"\n🎨 不同错误类型的提示词对比")
    print("=" * 60)
    
    processor = ChineseKnowledgeBaseProcessor(
        "/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/utils/complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    )
    
    test_cases = [
        ("我很喜欢这本书的", "语气词误用"),
        ("他跑的很快", "得字句错误"),
        ("认真的学习", "的地得混用"),
        ("大约三点钟左右", "重复表达")
    ]
    
    for text, expected_type in test_cases:
        print(f"\n📝 测试: {text} (预期: {expected_type})")
        print("-" * 30)
        
        # 检索知识
        retrieved = processor.retrieve_relevant_knowledge(text, top_k=2)
        
        # 分析检索结果
        if retrieved:
            top_result = retrieved[0]
            print(f"   最佳匹配: {top_result.match_type} (相关度: {top_result.relevance_score:.2f})")
            
            if 'type' in top_result.knowledge_item:
                actual_type = top_result.knowledge_item['type']
                match_status = "✅" if expected_type in actual_type else "❌"
                print(f"   检测类型: {actual_type} {match_status}")
        
        # 生成简化提示词
        prompt = processor.generate_correction_prompt(text, retrieved[:1])
        
        # 提取关键信息
        if "错误类型**:" in prompt:
            error_type_line = [line for line in prompt.split('\n') if "错误类型**:" in line][0]
            print(f"   提示词中的错误类型: {error_type_line.split('**:')[1].strip()}")
        
        if "语法规则**:" in prompt:
            rule_line = [line for line in prompt.split('\n') if "语法规则**:" in line][0]
            rule_text = rule_line.split('**:')[1].strip()[:50] + "..."
            print(f"   应用的语法规则: {rule_text}")

def demonstrate_prompt_optimization():
    """演示提示词优化策略"""
    print(f"\n🔧 提示词优化策略演示")
    print("=" * 60)
    
    processor = ChineseKnowledgeBaseProcessor(
        "/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/utils/complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    )
    
    test_text = "学生们学习的很认真"
    
    # 基础提示词
    print("📋 基础提示词生成:")
    basic_knowledge = processor.retrieve_relevant_knowledge(test_text, top_k=1)
    basic_prompt = processor.generate_correction_prompt(test_text, basic_knowledge)
    print(f"   知识条目数: {len(basic_knowledge)}")
    print(f"   提示词长度: {len(basic_prompt)} 字符")
    
    # 增强提示词
    print(f"\n📋 增强提示词生成:")
    enhanced_knowledge = processor.retrieve_relevant_knowledge(test_text, top_k=3)
    enhanced_prompt = processor.generate_correction_prompt(test_text, enhanced_knowledge)
    print(f"   知识条目数: {len(enhanced_knowledge)}")
    print(f"   提示词长度: {len(enhanced_prompt)} 字符")
    
    # 对比分析
    print(f"\n📊 对比分析:")
    print(f"   知识条目增加: {len(enhanced_knowledge) - len(basic_knowledge)} 个")
    print(f"   提示词长度增加: {len(enhanced_prompt) - len(basic_prompt)} 字符")
    print(f"   信息密度提升: {(len(enhanced_prompt) - len(basic_prompt)) / len(enhanced_knowledge):.1f} 字符/条目")
    
    # 检索质量分析
    print(f"\n🎯 检索质量分析:")
    for i, result in enumerate(enhanced_knowledge, 1):
        relevance_level = "高" if result.relevance_score >= 0.8 else "中" if result.relevance_score >= 0.6 else "低"
        print(f"   条目{i}: {result.match_type} | 相关度: {result.relevance_score:.2f} ({relevance_level})")

def show_json_output_example():
    """展示期望的JSON输出示例"""
    print(f"\n📤 期望的LLM输出示例")
    print("=" * 60)
    
    examples = [
        {
            "input": "我很喜欢这本书的",
            "expected_output": {
                "has_error": True,
                "original_text": "我很喜欢这本书的",
                "corrected_text": "我很喜欢这本书",
                "error_type": "语气词误用",
                "correction_reasoning": "陈述句末尾不需要语气词'的'。根据语法规则，语气词'的'主要用于疑问句或强调句，在简单陈述句中应该省略。",
                "confidence": 0.95,
                "applied_rules": [
                    "陈述句末尾通常不使用语气词'的'",
                    "语气词'的'主要用于疑问句或强调句，陈述句末尾通常省略"
                ]
            }
        },
        {
            "input": "他跑的很快",
            "expected_output": {
                "has_error": True,
                "original_text": "他跑的很快",
                "corrected_text": "他跑得很快",
                "error_type": "得字句错误",
                "correction_reasoning": "动词'跑'后面的程度补语'很快'应该用'得'连接，而不是'的'。'得'字专门用于连接动词和表示程度、结果的补语。",
                "confidence": 0.92,
                "applied_rules": [
                    "动词+得+形容词/副词，表示动作的程度或结果",
                    "动词后的程度补语必须用'得'字连接"
                ]
            }
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"📝 示例 {i}:")
        print(f"   输入: {example['input']}")
        print(f"   期望输出:")
        
        import json
        output_json = json.dumps(example['expected_output'], ensure_ascii=False, indent=2)
        for line in output_json.split('\n'):
            print(f"     {line}")
        print()

if __name__ == "__main__":
    # 运行所有演示
    generate_detailed_prompt_example()
    show_prompt_variations()
    demonstrate_prompt_optimization()
    show_json_output_example()
    
    print(f"\n🎉 提示词生成演示完成！")
    print(f"💡 关键要点:")
    print(f"   1. 多层次检索确保知识覆盖全面")
    print(f"   2. 智能重排提高相关知识优先级")
    print(f"   3. 结构化提示词提供清晰指导")
    print(f"   4. 标准化输出格式便于后续处理")
