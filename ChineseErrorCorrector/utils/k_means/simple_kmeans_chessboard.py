#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版K-means聚类二维坐标可视化
包含迭代过程展示功能
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from sklearn.cluster import KMeans
from sklearn.datasets import make_blobs
from scipy.spatial import ConvexHull


class KMeansIterative:
    """自定义K-means类，用于记录迭代过程"""

    def __init__(self, n_clusters=3, max_iter=100, random_state=42):
        self.n_clusters = n_clusters
        self.max_iter = max_iter
        self.random_state = random_state
        self.iteration_history = []

    def fit(self, X):
        """拟合数据并记录迭代过程"""
        np.random.seed(self.random_state)

        # 初始化簇中心
        n_samples, n_features = X.shape
        centers = X[np.random.choice(n_samples, self.n_clusters, replace=False)]

        self.iteration_history = []

        for iteration in range(self.max_iter):
            # 记录当前状态
            old_centers = centers.copy()

            # 分配点到最近的簇中心
            distances = np.sqrt(((X - centers[:, np.newaxis])**2).sum(axis=2))
            labels = np.argmin(distances, axis=0)

            # 更新簇中心
            new_centers = np.array([X[labels == i].mean(axis=0) for i in range(self.n_clusters)])

            # 记录迭代信息
            iteration_info = {
                'iteration': iteration,
                'old_centers': old_centers.copy(),
                'new_centers': new_centers.copy(),
                'labels': labels.copy(),
                'converged': False
            }

            # 检查收敛
            if np.allclose(old_centers, new_centers, rtol=1e-4):
                iteration_info['converged'] = True
                self.iteration_history.append(iteration_info)
                break

            centers = new_centers
            self.iteration_history.append(iteration_info)

        self.cluster_centers_ = centers
        self.labels_ = labels
        self.n_iter_ = len(self.iteration_history)

        return self


def generate_data_points(n_samples=100, n_centers=3, x_range=(-10, 10), y_range=(-10, 10)):
    """生成随机数据点"""
    X, _ = make_blobs(
        n_samples=n_samples,
        centers=n_centers,
        cluster_std=2.0,
        center_box=(x_range[0]*0.8, x_range[1]*0.8),
        random_state=42
    )
    # 确保点在坐标范围内
    X = np.clip(X, [x_range[0], y_range[0]], [x_range[1], y_range[1]])
    return X

def plot_kmeans_2d(X=None, n_clusters=3, x_range=(-10, 10), y_range=(-10, 10)):
    """
    在二维坐标系上可视化K-means聚类

    Args:
        X: 数据点坐标 (如果为None则自动生成)
        n_clusters: 簇数量
        x_range: X轴范围
        y_range: Y轴范围
    """
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 10))

    # 生成数据点（如果没有提供）
    if X is None:
        X = generate_data_points(n_samples=120, n_centers=n_clusters, x_range=x_range, y_range=y_range)

    # 设置坐标轴和网格
    ax.set_xlim(x_range)
    ax.set_ylim(y_range)
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.axhline(y=0, color='black', linewidth=0.8, alpha=0.8)
    ax.axvline(x=0, color='black', linewidth=0.8, alpha=0.8)
    
    # 应用K-means算法
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    labels = kmeans.fit_predict(X)
    centers = kmeans.cluster_centers_
    
    # 定义颜色
    colors = plt.cm.Set3(np.linspace(0, 1, n_clusters))
    
    # 绘制簇的范围（凸包）
    for i in range(n_clusters):
        cluster_points = X[labels == i]
        color = colors[i]
        
        if len(cluster_points) >= 3:
            try:
                # 计算并绘制凸包
                hull = ConvexHull(cluster_points)
                hull_points = cluster_points[hull.vertices]
                polygon = patches.Polygon(hull_points, alpha=0.3, facecolor=color, 
                                        edgecolor=color, linewidth=2)
                ax.add_patch(polygon)
            except:
                # 如果凸包失败，绘制圆形区域
                center = centers[i]
                distances = np.linalg.norm(cluster_points - center, axis=1)
                radius = np.percentile(distances, 80)
                circle = patches.Circle(center, radius, alpha=0.3, facecolor=color,
                                      edgecolor=color, linewidth=2)
                ax.add_patch(circle)
    
    # 绘制数据点
    for i in range(n_clusters):
        cluster_points = X[labels == i]
        ax.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                  c=[colors[i]], s=60, alpha=0.8, edgecolors='black', linewidth=0.5,
                  label=f'簇 {i+1}')
    
    # 绘制簇中心
    ax.scatter(centers[:, 0], centers[:, 1], 
               c='red', s=300, alpha=1.0, marker='X', 
               edgecolors='black', linewidth=3, label='簇中心', zorder=5)
    
    # 标注簇中心
    for i, center in enumerate(centers):
        ax.annotate(f'C{i+1}', 
                   xy=center, 
                   xytext=(8, 8), 
                   textcoords='offset points',
                   fontsize=12, 
                   fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
    
    # 设置图形属性
    ax.set_xlim(x_range)
    ax.set_ylim(y_range)
    ax.set_xlabel('X坐标', fontsize=12)
    ax.set_ylabel('Y坐标', fontsize=12)
    ax.set_title(f'(K={n_clusters})', fontsize=14, fontweight='bold')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.set_aspect('equal')

    # 添加统计信息
    ax.text(0.02, 0.98, f'簇内平方和: {kmeans.inertia_:.2f}',
            transform=ax.transAxes, fontsize=11,
            verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()
    return fig, ax, kmeans, labels, centers


def plot_kmeans_iterations(X=None, n_clusters=3, x_range=(-10, 10), y_range=(-10, 10), max_iter=10):
    """
    可视化K-means算法的迭代过程

    Args:
        X: 数据点坐标 (如果为None则自动生成)
        n_clusters: 簇数量
        x_range: X轴范围
        y_range: Y轴范围
        max_iter: 最大迭代次数
    """
    # 生成数据点（如果没有提供）
    if X is None:
        X = generate_data_points(n_samples=80, n_centers=n_clusters, x_range=x_range, y_range=y_range)

    # 使用自定义K-means进行迭代
    kmeans_iter = KMeansIterative(n_clusters=n_clusters, max_iter=max_iter, random_state=42)
    kmeans_iter.fit(X)

    # 计算需要显示的迭代次数（最多显示前6次迭代）
    iterations_to_show = min(6, len(kmeans_iter.iteration_history))

    # 创建子图
    cols = 3
    rows = (iterations_to_show + cols - 1) // cols
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))

    # 确保axes是二维数组
    if rows == 1:
        axes = axes.reshape(1, -1)
    if cols == 1:
        axes = axes.reshape(-1, 1)

    # 定义颜色
    colors = plt.cm.Set3(np.linspace(0, 1, n_clusters))

    for idx in range(iterations_to_show):
        row = idx // cols
        col = idx % cols
        ax = axes[row, col]

        iteration_info = kmeans_iter.iteration_history[idx]
        iteration_num = iteration_info['iteration']
        old_centers = iteration_info['old_centers']
        new_centers = iteration_info['new_centers']
        labels = iteration_info['labels']
        converged = iteration_info['converged']

        # 设置坐标轴和网格
        ax.set_xlim(x_range)
        ax.set_ylim(y_range)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.axhline(y=0, color='black', linewidth=0.8, alpha=0.8)
        ax.axvline(x=0, color='black', linewidth=0.8, alpha=0.8)

        # 绘制数据点
        for i in range(n_clusters):
            cluster_points = X[labels == i]
            if len(cluster_points) > 0:
                ax.scatter(cluster_points[:, 0], cluster_points[:, 1],
                          c=[colors[i]], s=50, alpha=0.7, edgecolors='black', linewidth=0.5)

        # 绘制初始簇中心（黑色实心圆点）
        ax.scatter(old_centers[:, 0], old_centers[:, 1],
                   c='black', s=200, marker='o', alpha=0.8,
                   label='初始簇中心', zorder=5)

        # 绘制更新后的簇中心（黑色空心圆点）
        ax.scatter(new_centers[:, 0], new_centers[:, 1],
                   c='white', s=200, marker='o', edgecolors='black',
                   linewidth=3, alpha=0.9, label='更新后簇中心', zorder=6)

        # 绘制簇中心移动的箭头
        for i in range(n_clusters):
            if not np.allclose(old_centers[i], new_centers[i]):
                ax.annotate('', xy=new_centers[i], xytext=old_centers[i],
                           arrowprops=dict(arrowstyle='->', color='red', lw=2, alpha=0.7))

        # 标注簇中心编号
        for i, center in enumerate(new_centers):
            ax.annotate(f'{i+1}',
                       xy=center,
                       xytext=(0, 0),
                       textcoords='offset points',
                       fontsize=10,
                       fontweight='bold',
                       ha='center', va='center',
                       color='black')

        # 设置标题
        title = f'迭代 {iteration_num + 1}'
        if converged:
            title += ' (收敛)'
        ax.set_title(title, fontsize=12, fontweight='bold')

        # 只在第一个子图显示图例
        if idx == 0:
            ax.legend(loc='upper right', fontsize=8)

        ax.set_aspect('equal')

    # 隐藏多余的子图
    for idx in range(iterations_to_show, rows * cols):
        row = idx // cols
        col = idx % cols
        axes[row, col].set_visible(False)

    plt.suptitle(f'K-means算法迭代过程 (K={n_clusters})', fontsize=16, fontweight='bold')
    plt.tight_layout()

    return fig, axes, kmeans_iter


def demo_kmeans_iterations():
    """演示K-means迭代过程的专门函数"""
    print("🔄 K-means算法迭代过程演示")
    print("-" * 40)

    # 创建一个容易观察迭代过程的数据集
    np.random.seed(42)

    # 手动创建三个明显分离的簇
    cluster1 = np.random.normal([-4, -4], 0.8, (20, 2))
    cluster2 = np.random.normal([4, 4], 0.8, (20, 2))
    cluster3 = np.random.normal([0, 6], 0.8, (20, 2))

    X = np.vstack([cluster1, cluster2, cluster3])

    print(f"生成了 {len(X)} 个数据点，分布在3个自然簇中")

    # 可视化迭代过程
    fig, axes, kmeans_iter = plot_kmeans_iterations(X=X, n_clusters=3, max_iter=15)

    print(f"算法在 {kmeans_iter.n_iter_} 次迭代后收敛")
    print("图中显示:")
    print("  • 黑色实心圆点: 迭代开始时的簇中心位置")
    print("  • 黑色空心圆点: 迭代结束时的簇中心位置")
    print("  • 红色箭头: 簇中心的移动方向")
    print("  • 彩色点: 分配给各簇的数据点")

    return fig, axes, kmeans_iter


def demo_different_k_values():
    """演示不同K值的效果"""
    # 生成固定数据用于比较
    X = generate_data_points(n_samples=100, n_centers=4, x_range=(0, 10), y_range=(0, 10))

    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    k_values = [2, 3, 4, 5]

    for i, k in enumerate(k_values):
        row, col = i // 2, i % 2
        ax = axes[row, col]

        # 设置坐标轴和网格
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.axhline(y=0, color='black', linewidth=0.8, alpha=0.8)
        ax.axvline(x=0, color='black', linewidth=0.8, alpha=0.8)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(X)
        centers = kmeans.cluster_centers_
        
        # 绘制簇范围和数据点
        colors = plt.cm.Set3(np.linspace(0, 1, k))
        
        for cluster_id in range(k):
            cluster_points = X[labels == cluster_id]
            color = colors[cluster_id]
            
            # 绘制数据点
            ax.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                      c=[color], s=50, alpha=0.7, edgecolors='black', linewidth=0.5)
            
            # 绘制簇范围
            if len(cluster_points) >= 3:
                try:
                    hull = ConvexHull(cluster_points)
                    hull_points = cluster_points[hull.vertices]
                    polygon = patches.Polygon(hull_points, alpha=0.2, facecolor=color, 
                                            edgecolor=color, linewidth=2)
                    ax.add_patch(polygon)
                except:
                    pass
        
        # 绘制簇中心
        ax.scatter(centers[:, 0], centers[:, 1], 
                   c='red', s=200, marker='X', edgecolors='black', linewidth=2)
        
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.set_title(f'K={k}', fontweight='bold')
        ax.set_aspect('equal')


    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    print("🎯 K-means聚类二维可视化")
    print("=" * 40)

    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 示例1: K-means迭代过程可视化
    print("1. K-means算法迭代过程可视化")
    _, _, kmeans_iter = demo_kmeans_iterations()
    plt.show()

    # 示例2: 不同K值的迭代过程比较
    print("\n2. 不同K值的迭代过程比较")

    # 生成固定数据用于比较
    np.random.seed(42)
    test_data = generate_data_points(n_samples=60, n_centers=3, x_range=(-6, 6), y_range=(-6, 6))

    k_values = [2, 3, 4]
    _, axes = plt.subplots(1, 3, figsize=(18, 6))

    for i, k in enumerate(k_values):
        ax = axes[i]

        # 使用自定义K-means
        kmeans_iter = KMeansIterative(n_clusters=k, max_iter=10, random_state=42)
        kmeans_iter.fit(test_data)

        # 获取最终状态
        final_iteration = kmeans_iter.iteration_history[-1]
        labels = final_iteration['labels']
        centers = final_iteration['new_centers']

        # 设置坐标轴和网格
        ax.set_xlim(-6, 6)
        ax.set_ylim(-6, 6)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.axhline(y=0, color='black', linewidth=0.8, alpha=0.8)
        ax.axvline(x=0, color='black', linewidth=0.8, alpha=0.8)

        # 绘制数据点
        colors = plt.cm.Set3(np.linspace(0, 1, k))
        for cluster_id in range(k):
            cluster_points = test_data[labels == cluster_id]
            if len(cluster_points) > 0:
                ax.scatter(cluster_points[:, 0], cluster_points[:, 1],
                          c=[colors[cluster_id]], s=50, alpha=0.7,
                          edgecolors='black', linewidth=0.5)

        # 绘制最终簇中心
        ax.scatter(centers[:, 0], centers[:, 1],
                   c='red', s=200, marker='X', edgecolors='black',
                   linewidth=2, label='最终簇中心')

        ax.set_title(f'K={k}, 迭代次数={kmeans_iter.n_iter_}', fontweight='bold')
        ax.set_aspect('equal')

        if i == 0:
            ax.legend()

    plt.suptitle('不同K值的收敛比较', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()

    # 示例3: 基本可视化
    print("\n3. 基本K-means聚类可视化")
    _, _, kmeans, labels, centers = plot_kmeans_2d(n_clusters=4)
    plt.show()

    # 示例4: 自定义数据点
    print("\n4. 自定义数据点")
    custom_points = np.array([
        [-5, -5], [-4.5, -4.8], [-4.8, -4.5],   # 左下角
        [5, 5], [5.5, 4.8], [4.8, 5.5],         # 右上角
        [-5, 5], [-4.5, 5.2], [-4.8, 4.5],      # 左上角
        [5, -5], [5.2, -4.5], [4.5, -5.2],      # 右下角
        [0, 0], [0.2, 0.1], [-0.2, 0.3]         # 中心
    ])

    _, _, kmeans, labels, centers = plot_kmeans_2d(
        X=custom_points, n_clusters=5, x_range=(0, 10), y_range=(0, 10)
    )
    plt.show()

    # 示例5: 不同K值比较
    print("\n5. 不同K值效果比较")
    demo_different_k_values()

    print("\n🎉 演示完成！")

if __name__ == '__main__':
    main()
