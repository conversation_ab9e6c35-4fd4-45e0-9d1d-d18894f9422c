#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K-means聚类算法的二维坐标可视化
显示数据点，簇中心和簇的范围形状
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from sklearn.cluster import KMeans
from sklearn.datasets import make_blobs
from scipy.spatial import ConvexHull
import seaborn as sns

class KMeansVisualizer:
    """K-means可视化器"""

    def __init__(self, x_range=(-10, 10), y_range=(-10, 10), figsize=(12, 10)):
        """
        初始化可视化器

        Args:
            x_range: X轴范围 (默认-10到10)
            y_range: Y轴范围 (默认-10到10)
            figsize: 图形大小
        """
        self.x_range = x_range
        self.y_range = y_range
        self.figsize = figsize
        self.fig = None
        self.ax = None
        
    def create_coordinate_background(self):
        """创建二维坐标背景"""
        # 设置坐标轴范围
        self.ax.set_xlim(self.x_range)
        self.ax.set_ylim(self.y_range)

        # 添加网格线
        self.ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

        # 添加坐标轴
        self.ax.axhline(y=0, color='black', linewidth=0.8, alpha=0.8)
        self.ax.axvline(x=0, color='black', linewidth=0.8, alpha=0.8)
    
    def generate_sample_data(self, n_samples=200, n_centers=4, random_state=42):
        """
        生成示例数据点

        Args:
            n_samples: 数据点数量
            n_centers: 真实簇中心数量
            random_state: 随机种子

        Returns:
            X: 数据点坐标
        """
        # 生成聚类数据，限制在坐标范围内
        X, _ = make_blobs(
            n_samples=n_samples,
            centers=n_centers,
            cluster_std=2.0,
            center_box=(self.x_range[0]*0.8, self.x_range[1]*0.8),
            random_state=random_state
        )

        # 确保数据点在坐标范围内
        X = np.clip(X, [self.x_range[0], self.y_range[0]], [self.x_range[1], self.y_range[1]])

        return X
    
    def apply_kmeans(self, X, n_clusters=4, random_state=42):
        """
        应用K-means算法
        
        Args:
            X: 数据点
            n_clusters: 簇数量
            random_state: 随机种子
            
        Returns:
            kmeans: 训练好的K-means模型
            labels: 簇标签
            centers: 簇中心
        """
        kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init=10)
        labels = kmeans.fit_predict(X)
        centers = kmeans.cluster_centers_
        
        return kmeans, labels, centers
    
    def draw_cluster_boundaries(self, X, labels, centers, alpha=0.2):
        """
        绘制簇的边界形状
        
        Args:
            X: 数据点
            labels: 簇标签
            centers: 簇中心
            alpha: 透明度
        """
        colors = plt.cm.Set3(np.linspace(0, 1, len(centers)))
        
        for i, (center, color) in enumerate(zip(centers, colors)):
            # 获取属于当前簇的点
            cluster_points = X[labels == i]
            
            if len(cluster_points) >= 3:
                try:
                    # 计算凸包
                    hull = ConvexHull(cluster_points)
                    hull_points = cluster_points[hull.vertices]
                    
                    # 绘制凸包区域
                    polygon = patches.Polygon(hull_points, alpha=alpha, facecolor=color, 
                                            edgecolor=color, linewidth=2)
                    self.ax.add_patch(polygon)
                    
                except Exception:
                    # 如果凸包计算失败，绘制圆形区域
                    distances = np.linalg.norm(cluster_points - center, axis=1)
                    radius = np.percentile(distances, 80)  # 80%的点在圆内
                    circle = patches.Circle(center, radius, alpha=alpha, facecolor=color,
                                          edgecolor=color, linewidth=2)
                    self.ax.add_patch(circle)
            else:
                # 点太少时绘制圆形区域
                if len(cluster_points) > 0:
                    distances = np.linalg.norm(cluster_points - center, axis=1)
                    radius = np.max(distances) if len(distances) > 0 else 0.5
                    circle = patches.Circle(center, radius, alpha=alpha, facecolor=color,
                                          edgecolor=color, linewidth=2)
                    self.ax.add_patch(circle)
    
    def plot_data_points(self, X, labels, centers):
        """
        绘制数据点和簇中心
        
        Args:
            X: 数据点
            labels: 簇标签
            centers: 簇中心
        """
        colors = plt.cm.Set3(np.linspace(0, 1, len(centers)))
        
        # 绘制数据点
        for i, color in enumerate(colors):
            cluster_points = X[labels == i]
            self.ax.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                          c=[color], s=50, alpha=0.7, edgecolors='black', linewidth=0.5,
                          label=f'簇 {i+1}')
        
        # 绘制簇中心
        self.ax.scatter(centers[:, 0], centers[:, 1], 
                       c='red', s=200, alpha=0.9, marker='X', 
                       edgecolors='black', linewidth=2, label='簇中心')
    
    def add_annotations(self, centers):
        """添加簇中心标注"""
        for i, center in enumerate(centers):
            self.ax.annotate(f'C{i+1}', 
                           xy=center, 
                           xytext=(5, 5), 
                           textcoords='offset points',
                           fontsize=12, 
                           fontweight='bold',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    def visualize(self, X=None, n_clusters=4, title="K-means聚类二维可视化"):
        """
        完整的可视化流程

        Args:
            X: 数据点（如果为None则自动生成）
            n_clusters: 簇数量
            title: 图标题
        """
        # 创建图形
        self.fig, self.ax = plt.subplots(figsize=self.figsize)

        # 如果没有提供数据，则生成示例数据
        if X is None:
            X = self.generate_sample_data(n_samples=200, n_centers=n_clusters)

        # 创建坐标背景
        self.create_coordinate_background()

        # 应用K-means算法
        kmeans, labels, centers = self.apply_kmeans(X, n_clusters=n_clusters)

        # 绘制簇边界
        self.draw_cluster_boundaries(X, labels, centers)

        # 绘制数据点和簇中心
        self.plot_data_points(X, labels, centers)

        # 添加标注
        self.add_annotations(centers)

        # 设置图形属性
        self.ax.set_xlim(self.x_range)
        self.ax.set_ylim(self.y_range)
        self.ax.set_xlabel('X坐标', fontsize=12)
        self.ax.set_ylabel('Y坐标', fontsize=12)
        self.ax.set_title(title, fontsize=14, fontweight='bold')
        self.ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        self.ax.set_aspect('equal')

        # 添加统计信息
        inertia = kmeans.inertia_
        self.ax.text(0.02, 0.98, f'簇内平方和: {inertia:.2f}\n数据点数: {len(X)}\n簇数量: {n_clusters}',
                    transform=self.ax.transAxes, fontsize=10,
                    verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        return self.fig, self.ax, kmeans, labels, centers


def demo_interactive_visualization():
    """交互式演示"""
    print("🎯 K-means聚类二维可视化演示")
    print("=" * 50)

    # 创建可视化器
    visualizer = KMeansVisualizer(x_range=(-10, 10), y_range=(-10, 10), figsize=(14, 10))

    # 演示不同的簇数量
    cluster_counts = [2, 3, 4, 5]

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()

    # 生成一组固定的数据用于比较
    X = visualizer.generate_sample_data(n_samples=150, n_centers=4)

    for i, n_clusters in enumerate(cluster_counts):
        # 为每个子图创建新的可视化器
        viz = KMeansVisualizer(x_range=(-10, 10), y_range=(-10, 10), figsize=(6, 6))
        viz.fig, viz.ax = fig, axes[i]

        # 创建坐标背景
        viz.create_coordinate_background()

        # 应用K-means
        kmeans, labels, centers = viz.apply_kmeans(X, n_clusters=n_clusters)

        # 绘制
        viz.draw_cluster_boundaries(X, labels, centers)
        viz.plot_data_points(X, labels, centers)
        viz.add_annotations(centers)

        # 设置子图属性
        viz.ax.set_xlim(-10, 10)
        viz.ax.set_ylim(-10, 10)
        viz.ax.set_title(f'K={n_clusters}, 簇内平方和={kmeans.inertia_:.1f}', fontweight='bold')
        viz.ax.set_aspect('equal')
        viz.ax.grid(True, alpha=0.3)

    plt.suptitle('不同K值的K-means聚类比较', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()


def main():
    """主函数"""
    print("🎯 K-means聚类二维可视化")
    print("=" * 50)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 基本演示
    print("1. 基本K-means可视化")
    visualizer = KMeansVisualizer(x_range=(-10, 10), y_range=(-10, 10))
    fig, ax, kmeans, labels, centers = visualizer.visualize(n_clusters=4)
    plt.show()

    # 交互式演示
    print("\n2. 不同K值比较")
    demo_interactive_visualization()

    # 自定义数据演示
    print("\n3. 自定义数据点演示")
    # 创建一些特殊分布的数据
    np.random.seed(42)
    custom_data = np.array([
        [-5, -5], [-4, -4.5], [-4.5, -5.2],  # 左下角簇
        [5, 5], [5.5, 4.8], [4.8, 5.5],      # 右上角簇
        [-5, 5], [-4.5, 5.2], [-4.8, 4.5],   # 左上角簇
        [5, -5], [5.2, -4.5], [4.5, -5.2],   # 右下角簇
        [0, 0], [0.5, 0.2], [-0.2, 0.5],     # 中心簇
    ])

    visualizer_custom = KMeansVisualizer(x_range=(-8, 8), y_range=(-8, 8))
    fig, ax, kmeans, labels, centers = visualizer_custom.visualize(
        X=custom_data,
        n_clusters=5,
        title="自定义数据的K-means聚类"
    )
    plt.show()

    print("\n🎉 演示完成！")


if __name__ == '__main__':
    main()
