# K-means聚类二维可视化

## 📋 **项目简介**

这个项目提供了两个Python脚本，用于在二维坐标系上可视化K-means聚类算法：

1. **`kmeans_chessboard_visualization.py`** - 完整功能版本
2. **`simple_kmeans_chessboard.py`** - 简化版本

## 🎯 **功能特性**

### 核心功能
- ✅ **二维坐标系**: 标准的X-Y坐标系统，支持自定义范围
- ✅ **数据点可视化**: 支持自定义或随机生成数据点
- ✅ **K-means聚类**: 应用scikit-learn的K-means算法
- ✅ **簇中心显示**: 用红色X标记显示簇中心
- ✅ **簇范围形状**: 使用凸包或圆形显示簇的覆盖范围
- ✅ **多种颜色**: 不同簇使用不同颜色区分
- ✅ **统计信息**: 显示簇内平方和、数据点数等信息

### 高级功能（完整版）
- ✅ **交互式比较**: 同时显示不同K值的聚类效果
- ✅ **自定义参数**: 可调整坐标范围、簇数量等
- ✅ **详细标注**: 簇中心编号和详细统计信息
- ✅ **多种演示**: 包含基本演示、比较演示和自定义数据演示

## 📦 **依赖库**

```bash
pip install numpy matplotlib scikit-learn scipy seaborn
```

### 具体版本要求
- `numpy >= 1.19.0`
- `matplotlib >= 3.3.0`
- `scikit-learn >= 0.24.0`
- `scipy >= 1.6.0`
- `seaborn >= 0.11.0` (仅完整版需要)

## 🚀 **使用方法**

### 1. 简化版本（推荐入门）

```python
# 运行简化版本
python simple_kmeans_chessboard.py
```

**基本用法**:
```python
from simple_kmeans_chessboard import plot_kmeans_2d
import numpy as np

# 基本可视化（自动生成数据）
fig, ax, kmeans, labels, centers = plot_kmeans_2d(n_clusters=4)

# 使用自定义数据点
custom_data = np.array([[-5, -5], [0, 0], [5, 5], [3, -3]])
fig, ax, kmeans, labels, centers = plot_kmeans_2d(
    X=custom_data,
    n_clusters=2,
    x_range=(-10, 10),
    y_range=(-10, 10)
)
```

### 2. 完整版本（高级功能）

```python
# 运行完整版本
python kmeans_chessboard_visualization.py
```

**高级用法**:
```python
from kmeans_chessboard_visualization import KMeansVisualizer

# 创建可视化器
visualizer = KMeansVisualizer(x_range=(-10, 10), y_range=(-10, 10), figsize=(12, 10))

# 完整可视化
fig, ax, kmeans, labels, centers = visualizer.visualize(n_clusters=4)

# 自定义数据
custom_data = np.array([[-5, -5], [0, 0], [5, 5], [3, -3]])
fig, ax, kmeans, labels, centers = visualizer.visualize(
    X=custom_data,
    n_clusters=2,
    title="自定义数据聚类"
)
```

## 📊 **输出说明**

### 可视化元素
1. **二维坐标系**: 标准X-Y坐标轴，支持自定义范围
2. **数据点**: 彩色圆点，不同簇用不同颜色
3. **簇中心**: 红色X标记，标注为C1, C2等
4. **簇范围**: 半透明多边形或圆形，显示簇的覆盖区域
5. **网格线**: 帮助读取坐标
6. **统计信息**: 左上角显示关键指标

### 返回值
```python
fig, ax, kmeans, labels, centers = plot_kmeans_chessboard(...)
```
- `fig`: matplotlib图形对象
- `ax`: matplotlib轴对象
- `kmeans`: 训练好的K-means模型
- `labels`: 每个数据点的簇标签
- `centers`: 簇中心坐标

## 🎨 **自定义选项**

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `X` | array | None | 数据点坐标，None时自动生成 |
| `n_clusters` | int | 3 | 簇数量 |
| `x_range` | tuple | (-10, 10) | X轴坐标范围 |
| `y_range` | tuple | (-10, 10) | Y轴坐标范围 |
| `n_samples` | int | 100 | 自动生成的数据点数量 |
| `random_state` | int | 42 | 随机种子 |

### 颜色和样式
- **数据点**: 使用Set3颜色映射
- **簇中心**: 红色X标记
- **簇范围**: 半透明填充
- **棋盘格**: 灰度显示，透明度0.3

## 📈 **示例场景**

### 1. 教学演示
```python
# 比较不同K值的效果
demo_different_k_values()
```

### 2. 数据分析
```python
# 分析特定数据集
your_data = load_your_data()  # 加载你的数据
fig, ax, kmeans, labels, centers = plot_kmeans_chessboard(
    X=your_data, 
    n_clusters=5
)
```

### 3. 算法研究
```python
# 研究聚类质量
results = []
for k in range(2, 10):
    fig, ax, kmeans, labels, centers = plot_kmeans_chessboard(
        X=data, 
        n_clusters=k
    )
    results.append(kmeans.inertia_)  # 收集簇内平方和
```

## 🔧 **技术细节**

### K-means算法参数
- **n_init**: 10 (运行10次取最佳结果)
- **random_state**: 42 (确保结果可重现)
- **算法**: Lloyd算法 (scikit-learn默认)

### 簇范围计算
1. **凸包方法**: 当簇内点数≥3时使用ConvexHull
2. **圆形方法**: 点数不足时使用80%分位数半径
3. **异常处理**: 自动回退到备用方法

### 性能优化
- 使用numpy向量化操作
- 批量处理数据点绘制
- 优化的颜色映射

## ⚠️ **注意事项**

1. **数据范围**: 确保数据点在[0, board_size]范围内
2. **簇数量**: K值不应超过数据点数量
3. **内存使用**: 大量数据点可能影响渲染速度
4. **中文字体**: 如果中文显示异常，请安装SimHei字体

## 🐛 **常见问题**

### Q1: 中文字体显示为方块
```python
# 解决方案：设置字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
```

### Q2: 凸包计算失败
```python
# 自动回退到圆形显示，无需手动处理
```

### Q3: 数据点超出坐标范围
```python
# 使用np.clip自动裁剪
X = np.clip(X, [x_min, y_min], [x_max, y_max])
```

## 📚 **扩展建议**

1. **添加其他聚类算法**: DBSCAN, 层次聚类等
2. **交互式界面**: 使用Plotly或Bokeh
3. **动画效果**: 显示聚类过程
4. **3D可视化**: 扩展到三维空间
5. **实时数据**: 支持流数据聚类

## 📄 **许可证**

MIT License - 自由使用和修改

## 🤝 **贡献**

欢迎提交Issue和Pull Request来改进这个项目！
