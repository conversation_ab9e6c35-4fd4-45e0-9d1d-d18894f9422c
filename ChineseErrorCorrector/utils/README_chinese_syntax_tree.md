# 中文语法树生成器 (Chinese Syntax Tree Generator)

## 📋 概述

这是一个专门用于生成中文句子语法树的工具，支持多种中文自然语言处理库，能够将中文句子解析为可视化的语法树结构。**新增隐式解析树功能**，能够识别和补充句子中省略的语义成分。

## 🚀 功能特性

- **多种分析方法**：支持jieba、spaCy、LAC和基于规则的分析方法
- **🔍 隐式解析树**：识别省略的主语、宾语、时间状语等隐式成分
- **可视化输出**：生成易于阅读的树形结构，支持隐式节点标注
- **多种输出格式**：支持文本和JSON格式输出
- **统计信息**：提供节点数、深度、叶子节点等统计数据
- **语义角色标注**：为节点添加语义角色信息
- **置信度评估**：为隐式推断提供置信度评分
- **错误处理**：robust的错误处理机制
- **命令行接口**：支持命令行直接使用

## 📦 依赖安装

### 基础依赖（必需）
```bash
# 基础功能无需额外依赖
```

### 可选依赖（推荐）
```bash
# jieba分词
pip install jieba

# spaCy中文模型
pip install spacy
python -m spacy download zh_core_web_sm

# 百度LAC
pip install lac
```

## 🔧 使用方法

### 1. 基本使用

```python
from utils.chinese_syntax_tree import ChineseSyntaxTreeGenerator

# 创建生成器（默认启用隐式分析）
generator = ChineseSyntaxTreeGenerator(method="jieba", enable_implicit=True)

# 分析句子
sentence = "去学校。"  # 省略主语的句子
result = generator.analyze_sentence(sentence)

# 打印基础语法树
print("基础树:", result['tree_string'])

# 打印隐式增强树
if result.get('implicit_tree_string'):
    print("隐式树:", result['implicit_tree_string'])
    print("隐式分析:", result['implicit_analysis'])
```

### 2. 隐式解析树专用功能

```python
# 生成隐式解析树
enhanced_tree, implicit_nodes = generator.generate_implicit_tree("看书了。")

# 打印隐式成分分析
implicit_analysis = generator.print_implicit_analysis(implicit_nodes)
print(implicit_analysis)

# 打印增强的语法树（显示隐式节点）
enhanced_tree_str = generator.print_tree(enhanced_tree, show_implicit=True)
print(enhanced_tree_str)
```

### 3. 命令行使用

```bash
# 基本用法
python chinese_syntax_tree.py "我爱中国。"

# 启用隐式分析（默认启用）
python chinese_syntax_tree.py "去学校。" --implicit

# 禁用隐式分析
python chinese_syntax_tree.py "去学校。" --no-implicit

# 指定分析方法
python chinese_syntax_tree.py "看书了。" --method jieba --implicit

# 保存隐式增强树到文件
python chinese_syntax_tree.py "认真学习。" --output tree.txt --format txt --implicit

# 静默模式
python chinese_syntax_tree.py "快速跑步。" --quiet --implicit
```

### 3. 高级用法

```python
# 生成语法树
tree = generator.generate_tree("这是一个测试句子。")

# 转换为字典格式
tree_dict = generator.tree_to_dict(tree)

# 保存到文件
generator.save_tree(tree, "output.json", format="json")

# 获取统计信息
stats = generator._calculate_tree_stats(tree)
print(f"节点数: {stats['total_nodes']}")
```

## 📊 支持的分析方法

### 1. jieba方法
- **优点**：快速、轻量级、中文支持好
- **适用**：一般的中文文本分析
- **输出**：基于词性的简单短语结构

### 2. spaCy方法
- **优点**：准确度高、依存句法分析
- **适用**：需要精确语法分析的场景
- **输出**：基于依存关系的语法树

### 3. LAC方法
- **优点**：百度开源、中文优化
- **适用**：中文实体识别和词性标注
- **输出**：基于词性的短语结构

### 4. rule_based方法
- **优点**：无依赖、总是可用
- **适用**：简单分析或作为备选方案
- **输出**：基于规则的简单结构

## 🔍 隐式解析树详解

### 支持的隐式成分类型

#### 1. 主语省略
- **检测模式**：动词开头的句子（去、来、看、听等）
- **推断内容**：默认为"(我)"
- **示例**：
  - 输入："去学校。"
  - 推断：(我) 去学校。

#### 2. 宾语省略
- **检测模式**：及物动词结尾（看、听、吃、买等）
- **推断内容**：默认为"(它)"
- **示例**：
  - 输入："我在看。"
  - 推断：我在看 (它)。

#### 3. 时间状语推断
- **检测模式**：时间标记（了、过、正在、会、将等）
- **推断内容**：相应的时间概念
- **示例**：
  - 输入："看书了。"
  - 推断：(过去) 看书了。

#### 4. 地点状语推断
- **检测模式**：地点介词（在、到、从等）
- **推断内容**：地点概念
- **示例**：
  - 输入："在学习。"
  - 推断：在 (某地) 学习。

#### 5. 方式状语推断
- **检测模式**：方式副词（认真地、快速地等）
- **推断内容**：方式概念
- **示例**：
  - 输入："认真学习。"
  - 推断：(以某种方式) 认真学习。

### 置信度评估

隐式推断的置信度基于以下因素：
- **模式匹配度**：0.6-0.8
- **语境一致性**：0.7-0.9
- **语言学合理性**：0.8-1.0

### 应用场景

1. **语言学研究**：分析汉语的省略现象
2. **语义理解**：补全句子的完整语义结构
3. **机器翻译**：为目标语言提供完整的语义信息
4. **对话系统**：理解省略成分的指代关系
5. **语法教学**：帮助学习者理解汉语的省略规律

## 📈 输出示例

### 基础语法树
```
└── S (S)
    ├── VP (VP)
    │   └── 去 (v)
    ├── NP (NP)
    │   └── 学校 (n)
    └── 。 (x)
```

### 隐式增强语法树
```
└── S (S)
    ├── (我) (主语) [隐式, 置信度: 0.70]
    ├── VP (VP)
    │   └── 去 (v)
    ├── NP (NP)
    │   └── 学校 (n)
    └── 。 (x)
```

### 隐式成分分析
```
隐式成分分析:
----------------------------------------
1. 主语: (我)
   位置: 0
   置信度: 0.70
   推断依据: 匹配模式: ^(去|来|走|跑|吃|喝|看|听|说|做)

2. 时间状语: (过去)
   位置: 2
   置信度: 0.80
   推断依据: 时间标记: 了
```

### JSON格式输出
```json
{
  "sentence": "去学校。",
  "method": "jieba",
  "implicit_enabled": true,
  "implicit_nodes": [
    {
      "semantic_type": "主语",
      "inferred_content": "(我)",
      "confidence": 0.7,
      "position": 0,
      "evidence": ["匹配模式: ^(去|来|走|跑|吃|喝|看|听|说|做)"]
    }
  ]
}
```

## 🧪 测试

运行测试脚本：
```bash
python test_syntax_tree.py
```

测试包括：
- 基本功能测试
- 树操作测试
- 错误处理测试
- 可用方法检测
- 交互式测试

## 📝 API参考

### ChineseSyntaxTreeGenerator类

#### 构造函数
```python
ChineseSyntaxTreeGenerator(method: str = "jieba")
```

#### 主要方法

**generate_tree(sentence: str) -> TreeNode**
- 生成语法树
- 参数：输入句子
- 返回：语法树根节点

**analyze_sentence(sentence: str, verbose: bool = True) -> Dict**
- 完整分析句子
- 参数：句子、是否详细输出
- 返回：分析结果字典

**print_tree(node: TreeNode) -> str**
- 打印语法树
- 参数：树节点
- 返回：格式化的树结构字符串

**save_tree(tree: TreeNode, output_path: str, format: str = "txt")**
- 保存语法树到文件
- 参数：树节点、输出路径、格式

### TreeNode类

#### 属性
- `text`: 节点文本
- `pos`: 词性标注
- `children`: 子节点列表
- `parent`: 父节点
- `depth`: 节点深度

## 🔍 使用场景

1. **语言学研究**：分析中文句子的语法结构
2. **教育工具**：帮助学习者理解中文语法
3. **文本分析**：预处理步骤，为后续分析提供结构信息
4. **错误检测**：识别语法结构异常
5. **机器翻译**：提供源语言的语法结构信息

## ⚠️ 注意事项

1. **依赖管理**：不同方法需要不同的依赖包
2. **模型下载**：spaCy需要下载中文模型
3. **性能考虑**：复杂句子可能需要较长处理时间
4. **准确性**：不同方法的准确性有差异
5. **编码问题**：确保输入文本使用UTF-8编码

## 🛠️ 故障排除

### 常见问题

**Q: 提示"jieba not installed"**
A: 运行 `pip install jieba` 安装jieba

**Q: spaCy模型加载失败**
A: 运行 `python -m spacy download zh_core_web_sm` 下载中文模型

**Q: 输出乱码**
A: 确保终端支持UTF-8编码

**Q: 分析结果不准确**
A: 尝试不同的分析方法，或检查输入句子的格式

### 调试模式

```python
# 启用详细输出
result = generator.analyze_sentence(sentence, verbose=True)

# 检查可用方法
print(generator.available_methods)
```

## 📄 许可证

本项目遵循MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

---

**作者**: ChineseErrorCorrector Team  
**更新时间**: 2025-01-01  
**版本**: 1.0.0
