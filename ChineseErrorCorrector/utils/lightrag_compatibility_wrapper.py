#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG兼容性包装器
为Python 3.9环境提供RAG功能的兼容性实现
"""

import os
import sys
import json
import asyncio
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# 检查Python版本
PYTHON_VERSION = sys.version_info
LIGHTRAG_COMPATIBLE = PYTHON_VERSION >= (3, 10)

if LIGHTRAG_COMPATIBLE:
    try:
        from lightrag import LightRAG, QueryParam
        from lightrag.llm.openai import openai_complete_if_cache, openai_embed
        from lightrag.utils import EmbeddingFunc
        LIGHTRAG_AVAILABLE = True
    except ImportError:
        LIGHTRAG_AVAILABLE = False
else:
    LIGHTRAG_AVAILABLE = False

@dataclass
class MockQueryParam:
    """模拟QueryParam类"""
    mode: str = "hybrid"
    only_need_context: bool = False
    only_need_prompt: bool = False
    response_type: str = "Multiple Paragraphs"
    stream: bool = False

class MockRAGSystem:
    """模拟RAG系统，用于Python 3.9兼容性"""
    
    def __init__(self, working_dir: str, **kwargs):
        self.working_dir = working_dir
        self.knowledge_base = []
        self.initialized = False
        
        print(f"🔧 使用模拟RAG系统 (Python {PYTHON_VERSION.major}.{PYTHON_VERSION.minor})")
        
        if not os.path.exists(working_dir):
            os.makedirs(working_dir, exist_ok=True)
    
    async def initialize_storages(self):
        """初始化存储"""
        self.initialized = True
        print("✅ 模拟RAG存储初始化完成")
    
    async def insert(self, texts: List[str]):
        """插入文本到知识库"""
        if isinstance(texts, str):
            texts = [texts]
        
        self.knowledge_base.extend(texts)
        print(f"📚 向模拟知识库插入 {len(texts)} 条知识")
        
        # 保存到文件
        kb_file = os.path.join(self.working_dir, "mock_knowledge_base.json")
        with open(kb_file, 'w', encoding='utf-8') as f:
            json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)
    
    async def query(self, query: str, param: MockQueryParam = None) -> str:
        """查询知识库"""
        if not param:
            param = MockQueryParam()
        
        print(f"🔍 模拟RAG查询: {query[:50]}...")
        
        # 简单的关键词匹配
        relevant_knowledge = []
        query_keywords = query.lower().split()
        
        for knowledge in self.knowledge_base:
            knowledge_lower = knowledge.lower()
            if any(keyword in knowledge_lower for keyword in query_keywords):
                relevant_knowledge.append(knowledge)
        
        if relevant_knowledge:
            # 返回最相关的知识
            response = f"基于知识库检索，找到相关信息：\n{relevant_knowledge[0]}"
            print(f"✅ 找到 {len(relevant_knowledge)} 条相关知识")
        else:
            response = "未在知识库中找到相关信息，建议参考标准中文语法规则进行纠错。"
            print("⚠️ 未找到相关知识")
        
        return response
    
    async def llm_model_func(self, prompt: str, **kwargs) -> str:
        """模拟LLM调用"""
        print(f"🤖 模拟LLM调用: {prompt[:50]}...")
        
        # 简单的规则基础响应
        if "纠正" in prompt or "错误" in prompt:
            return json.dumps({
                "corrected": "模拟纠错结果",
                "reasoning": "基于模拟RAG系统的纠错建议"
            }, ensure_ascii=False)
        
        return "这是模拟的LLM响应，实际使用需要配置真实的API密钥。"

class CompatibilityRAGWrapper:
    """RAG兼容性包装器"""
    
    def __init__(self, working_dir: str = "./rag_storage", **kwargs):
        self.working_dir = working_dir
        self.kwargs = kwargs
        
        if LIGHTRAG_AVAILABLE:
            print("✅ 使用真实LightRAG系统")
            self.rag_system = self._create_real_lightrag()
        else:
            print("⚠️ 使用模拟RAG系统（兼容模式）")
            self.rag_system = MockRAGSystem(working_dir, **kwargs)
    
    def _create_real_lightrag(self):
        """创建真实的LightRAG实例"""
        api_key = self.kwargs.get('api_key') or os.getenv("OPENAI_API_KEY")
        base_url = self.kwargs.get('base_url') or "https://api.openai.com/v1"
        
        if not api_key:
            print("⚠️ 未设置API密钥，将使用模拟系统")
            return MockRAGSystem(self.working_dir, **self.kwargs)
        
        try:
            return LightRAG(
                working_dir=self.working_dir,
                llm_model_func=lambda prompt, system_prompt=None, history_messages=[], **kwargs: 
                    openai_complete_if_cache(
                        "gpt-4o-mini",
                        prompt,
                        system_prompt=system_prompt,
                        history_messages=history_messages,
                        api_key=api_key,
                        base_url=base_url,
                        **kwargs
                    ),
                embedding_func=EmbeddingFunc(
                    embedding_dim=1536,
                    func=lambda texts: openai_embed(
                        texts,
                        model="text-embedding-3-small",
                        api_key=api_key,
                        base_url=base_url
                    )
                )
            )
        except Exception as e:
            print(f"⚠️ LightRAG初始化失败，使用模拟系统: {e}")
            return MockRAGSystem(self.working_dir, **self.kwargs)
    
    async def initialize_storages(self):
        """初始化存储"""
        return await self.rag_system.initialize_storages()
    
    async def insert(self, texts):
        """插入文本"""
        return await self.rag_system.insert(texts)
    
    async def query(self, query: str, param=None):
        """查询"""
        if LIGHTRAG_AVAILABLE and hasattr(self.rag_system, 'query'):
            # 真实LightRAG
            if param is None:
                param = QueryParam(mode="hybrid")
            return await self.rag_system.query(query, param=param)
        else:
            # 模拟RAG
            if param is None:
                param = MockQueryParam(mode="hybrid")
            return await self.rag_system.query(query, param=param)
    
    @property
    def llm_model_func(self):
        """获取LLM函数"""
        if hasattr(self.rag_system, 'llm_model_func'):
            return self.rag_system.llm_model_func
        else:
            return self.rag_system.llm_model_func

def create_compatible_rag_system(working_dir: str = "./rag_storage", **kwargs):
    """创建兼容的RAG系统"""
    return CompatibilityRAGWrapper(working_dir, **kwargs)

async def test_compatibility():
    """测试兼容性"""
    print("🧪 测试RAG兼容性")
    print("-" * 40)
    
    # 创建兼容RAG系统
    rag = create_compatible_rag_system(
        working_dir="./test_compat_rag",
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    # 初始化
    await rag.initialize_storages()
    
    # 插入测试知识
    test_knowledge = [
        "中文句末通常不需要语气词'的'",
        "'得'字用于补语，'的'字用于定语",
        "语法纠错需要考虑语境和语义"
    ]
    
    await rag.insert(test_knowledge)
    
    # 测试查询
    query = "如何纠正句子末尾的'的'字错误"
    response = await rag.query(query)
    
    print(f"✅ 兼容性测试完成")
    print(f"   查询: {query}")
    print(f"   响应: {response[:100]}...")
    
    return True

if __name__ == "__main__":
    print(f"🐍 Python版本: {PYTHON_VERSION.major}.{PYTHON_VERSION.minor}")
    print(f"🔧 LightRAG兼容: {'是' if LIGHTRAG_COMPATIBLE else '否'}")
    print(f"📦 LightRAG可用: {'是' if LIGHTRAG_AVAILABLE else '否'}")
    
    asyncio.run(test_compatibility())
