# 模型参数量计算工具

这个工具用于计算Transformer模型的参数量，支持多种模型架构和配置。可以帮助您快速估算模型大小、内存需求，以及不同配置对参数量的影响。

## 🚀 快速开始

### 基本用法

```python
from ChineseErrorCorrector.utils import ModelParamsCalculator, ModelConfig, get_common_model_configs

# 使用预定义配置
configs = get_common_model_configs()
config = configs['ChineseErrorCorrector-4B']

# 创建计算器并打印摘要
calculator = ModelParamsCalculator(config)
calculator.print_params_summary()
```

### 自定义配置

```python
# 创建自定义配置
custom_config = ModelConfig(
    vocab_size=50000,
    hidden_size=2048,
    num_layers=24,
    num_attention_heads=16,
    intermediate_size=8192,
    model_name="CustomModel",
    model_size="2B"
)

calculator = ModelParamsCalculator(custom_config)
calculator.print_params_summary()
```

## 📊 功能特性

### 1. 详细的参数量计算

- **Embedding层**: 词嵌入、位置编码等
- **Transformer层**: 注意力机制、前馈网络、归一化层等
- **输出层**: 最终归一化、语言模型头等

### 2. 预定义模型配置

支持多种常见模型配置：

- ChineseErrorCorrector-4B
- Llama2-7B
- Qwen-7B
- ChatGLM3-6B

### 3. 内存需求估算

可以估算不同精度下的内存需求：

- FP32 (4字节/参数)
- FP16/BF16 (2字节/参数)
- INT8 (1字节/参数)
- INT4 (0.5字节/参数)

### 4. 扩展性分析

分析不同配置对参数量的影响：

- 层数扩展
- 隐藏层维度扩展
- 词汇表大小扩展

## 🔧 API参考

### `ModelConfig`类

模型配置数据类，包含以下主要参数：

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| vocab_size | int | 32000 | 词汇表大小 |
| hidden_size | int | 4096 | 隐藏层维度 |
| num_layers | int | 32 | Transformer层数 |
| num_attention_heads | int | 32 | 注意力头数 |
| intermediate_size | int | 11008 | FFN中间层维度 |
| max_position_embeddings | int | 2048 | 最大位置编码长度 |
| tie_word_embeddings | bool | True | 是否共享输入输出embedding |
| use_bias | bool | False | 是否使用bias |
| rms_norm | bool | True | 是否使用RMSNorm |
| model_name | str | "ChineseErrorCorrector" | 模型名称 |
| model_size | str | "4B" | 模型大小标识 |

### `ModelParamsCalculator`类

模型参数量计算器，主要方法：

| 方法 | 返回类型 | 描述 |
|------|----------|------|
| calculate_embedding_params() | Dict[str, int] | 计算embedding层参数 |
| calculate_attention_params() | Dict[str, int] | 计算单个注意力层参数 |
| calculate_ffn_params() | Dict[str, int] | 计算单个FFN层参数 |
| calculate_norm_params() | Dict[str, int] | 计算归一化层参数 |
| calculate_single_layer_params() | Dict[str, int] | 计算单个Transformer层参数 |
| calculate_output_layer_params() | Dict[str, int] | 计算输出层参数 |
| calculate_total_params() | Dict[str, Any] | 计算总参数量 |
| print_params_summary() | None | 打印参数量摘要 |

### 辅助函数

| 函数 | 返回类型 | 描述 |
|------|----------|------|
| get_common_model_configs() | Dict[str, ModelConfig] | 获取常见模型配置 |

## 📈 示例输出

```
============================================================
模型参数量计算 - ChineseErrorCorrector (4B)
============================================================
📊 模型配置:
  - 词汇表大小: 32,000
  - 隐藏层维度: 4,096
  - Transformer层数: 32
  - 注意力头数: 32
  - FFN中间维度: 11,008
  - 共享embedding: True

🔢 参数量统计:
  - Embedding层: 131,072,000 (131.1M)
  - Transformer层: 6,476,267,520 (6476.3M)
  - 输出层: 4,096 (0.0M)
  - 总参数量: 6,607,343,616
  - 总参数量: 6607.3M
  - 总参数量: 6.61B

📈 详细分析:
  - 单层参数量: 202,383,360 (202.4M)
  - 层数占比: 98.0%
  - Embedding占比: 2.0%
============================================================
```

## 🔍 详细用法示例

请参考项目根目录下的`example_model_params.py`文件，其中包含以下示例：

1. 基本使用示例
2. 自定义配置示例
3. 详细分析示例
4. 模型对比示例
5. 扩展性分析示例
6. 内存估算示例

## 📝 注意事项

1. 参数量计算基于理论值，实际模型可能因实现细节有所差异
2. 内存估算是近似值，实际内存使用还受到运行时环境、框架优化等因素影响
3. 对于特殊架构（如MoE、稀疏注意力等），可能需要自定义计算逻辑

## 🔧 扩展建议

如需扩展支持其他模型架构，可以：

1. 在`ModelConfig`中添加新的配置参数
2. 在`ModelParamsCalculator`中添加新的计算方法
3. 在`get_common_model_configs()`中添加新的预定义配置

## 📚 参考资料

- [Transformer模型架构](https://arxiv.org/abs/1706.03762)
- [LLaMA模型系列](https://arxiv.org/abs/2302.13971)
- [Qwen技术报告](https://arxiv.org/abs/2309.16609)
- [ChatGLM技术报告](https://arxiv.org/abs/2210.02414)
