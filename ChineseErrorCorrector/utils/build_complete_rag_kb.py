#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的中文校对RAG知识库构建脚本
整合专家知识和数据集知识，构建高质量的RAG知识库
"""

import os
import sys
import json
import asyncio
from typing import List, Dict, Any
from pathlib import Path

# 添加路径
sys.path.append('.')

try:
    from build_chinese_correction_knowledge_base import ChineseCorrectionKnowledgeBuilder
    from dataset_knowledge_extractor import DatasetKnowledgeExtractor
    from lightrag_enhanced_corrector import LightRAGEnhancedCorrector
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 组件导入失败: {e}")
    COMPONENTS_AVAILABLE = False

class CompleteRAGKnowledgeBaseBuilder:
    """完整的RAG知识库构建器"""
    
    def __init__(self, output_dir: str = "./complete_rag_knowledge_base"):
        self.output_dir = output_dir
        self.expert_knowledge = {}
        self.dataset_knowledge = []
        self.merged_knowledge = {}
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"🏗️ 完整RAG知识库构建器初始化")
        print(f"   输出目录: {output_dir}")
    
    async def build_expert_knowledge(self) -> Dict[str, Any]:
        """构建专家知识库"""
        print(f"\n📚 步骤1: 构建专家知识库")
        print("-" * 40)
        
        if not COMPONENTS_AVAILABLE:
            print("❌ 组件不可用，跳过专家知识构建")
            return {}
        
        try:
            # 创建专家知识构建器
            expert_builder = ChineseCorrectionKnowledgeBuilder(
                working_dir=os.path.join(self.output_dir, "expert_knowledge")
            )
            
            # 构建专家知识库
            self.expert_knowledge = expert_builder.build_knowledge_base()
            
            print(f"✅ 专家知识库构建完成")
            print(f"   错误示例: {len(self.expert_knowledge.get('grammar_errors', []))} 个")
            print(f"   语法规则: {len(self.expert_knowledge.get('grammar_rules', []))} 条")
            
            return self.expert_knowledge
            
        except Exception as e:
            print(f"❌ 专家知识库构建失败: {e}")
            return {}
    
    def extract_dataset_knowledge(self) -> List[Dict[str, Any]]:
        """提取数据集知识"""
        print(f"\n🔍 步骤2: 提取数据集知识")
        print("-" * 40)
        
        if not COMPONENTS_AVAILABLE:
            print("❌ 组件不可用，跳过数据集知识提取")
            return []
        
        try:
            # 创建数据集知识提取器
            dataset_extractor = DatasetKnowledgeExtractor()
            
            # 从数据集提取知识
            self.dataset_knowledge = dataset_extractor.extract_from_all_datasets(
                base_data_dir="../data"
            )
            
            # 保存数据集知识
            dataset_kb_file = os.path.join(self.output_dir, "dataset_knowledge.json")
            dataset_extractor.save_extracted_knowledge(
                self.dataset_knowledge, 
                dataset_kb_file
            )
            
            print(f"✅ 数据集知识提取完成")
            print(f"   提取条数: {len(self.dataset_knowledge)} 条")
            
            return self.dataset_knowledge
            
        except Exception as e:
            print(f"❌ 数据集知识提取失败: {e}")
            return []
    
    def merge_knowledge_sources(self) -> Dict[str, Any]:
        """合并知识源"""
        print(f"\n🔗 步骤3: 合并知识源")
        print("-" * 40)
        
        # 初始化合并后的知识库
        self.merged_knowledge = {
            "metadata": {
                "name": "完整中文校对RAG知识库",
                "version": "1.0",
                "description": "整合专家知识和数据集知识的完整RAG知识库",
                "sources": ["expert_knowledge", "dataset_extraction"],
                "created_by": "CompleteRAGKnowledgeBaseBuilder"
            },
            "expert_grammar_errors": self.expert_knowledge.get('grammar_errors', []),
            "expert_punctuation_errors": self.expert_knowledge.get('punctuation_errors', []),
            "expert_grammar_rules": self.expert_knowledge.get('grammar_rules', []),
            "expert_correction_patterns": self.expert_knowledge.get('correction_patterns', []),
            "expert_domain_knowledge": self.expert_knowledge.get('domain_knowledge', []),
            "dataset_extracted_errors": self.dataset_knowledge,
            "statistics": {}
        }
        
        # 计算统计信息
        stats = {
            "expert_error_examples": len(self.merged_knowledge['expert_grammar_errors']) + 
                                   len(self.merged_knowledge['expert_punctuation_errors']),
            "expert_rules": len(self.merged_knowledge['expert_grammar_rules']),
            "expert_patterns": len(self.merged_knowledge['expert_correction_patterns']),
            "expert_domain_knowledge": len(self.merged_knowledge['expert_domain_knowledge']),
            "dataset_extracted_errors": len(self.merged_knowledge['dataset_extracted_errors']),
            "total_knowledge_items": 0
        }
        
        stats["total_knowledge_items"] = (
            stats["expert_error_examples"] + 
            stats["expert_rules"] + 
            stats["expert_patterns"] + 
            stats["expert_domain_knowledge"] + 
            stats["dataset_extracted_errors"]
        )
        
        self.merged_knowledge["statistics"] = stats
        
        # 保存合并后的知识库
        merged_kb_file = os.path.join(self.output_dir, "merged_knowledge_base.json")
        with open(merged_kb_file, 'w', encoding='utf-8') as f:
            json.dump(self.merged_knowledge, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 知识源合并完成")
        print(f"   专家错误示例: {stats['expert_error_examples']} 个")
        print(f"   专家语法规则: {stats['expert_rules']} 条")
        print(f"   专家纠错模式: {stats['expert_patterns']} 个")
        print(f"   专家领域知识: {stats['expert_domain_knowledge']} 条")
        print(f"   数据集提取错误: {stats['dataset_extracted_errors']} 个")
        print(f"   总知识条目: {stats['total_knowledge_items']} 条")
        print(f"   保存位置: {merged_kb_file}")
        
        return self.merged_knowledge
    
    def format_knowledge_for_rag(self) -> List[str]:
        """格式化知识为RAG可用文本"""
        print(f"\n📝 步骤4: 格式化RAG文本")
        print("-" * 40)
        
        rag_texts = []
        
        # 格式化专家语法错误
        for error in self.merged_knowledge.get('expert_grammar_errors', []):
            text = f"""
错误类型：{error['type']}
错误示例：{error['error']}
正确形式：{error['correct']}
解释说明：{error['explanation']}
语法规则：{error['rule']}
知识来源：专家知识
纠错建议：将"{error['error']}"纠正为"{error['correct']}"，因为{error['explanation']}。
"""
            rag_texts.append(text.strip())
        
        # 格式化专家标点错误
        for error in self.merged_knowledge.get('expert_punctuation_errors', []):
            text = f"""
错误类型：{error['type']}
错误示例：{error['error']}
正确形式：{error['correct']}
解释说明：{error['explanation']}
标点规则：{error['rule']}
知识来源：专家知识
"""
            rag_texts.append(text.strip())
        
        # 格式化专家语法规则
        for rule in self.merged_knowledge.get('expert_grammar_rules', []):
            text = f"中文语法规则：{rule}（来源：专家知识）"
            rag_texts.append(text)
        
        # 格式化专家纠错模式
        for pattern in self.merged_knowledge.get('expert_correction_patterns', []):
            text = f"""
纠错模式：{pattern['description']}
匹配模式：{pattern['pattern']}
纠错动作：{pattern['action']}
示例：{'; '.join(pattern['examples'])}
知识来源：专家知识
"""
            rag_texts.append(text.strip())
        
        # 格式化专家领域知识
        for knowledge in self.merged_knowledge.get('expert_domain_knowledge', []):
            text = f"中文纠错领域知识：{knowledge}（来源：专家知识）"
            rag_texts.append(text)
        
        # 格式化数据集提取的错误
        for error in self.merged_knowledge.get('dataset_extracted_errors', []):
            text = f"""
错误类型：{error['type']}
错误示例：{error['error']}
正确形式：{error['correct']}
解释说明：{error['explanation']}
语法规则：{error['rule']}
知识来源：数据集提取（{error.get('dataset', '未知')}）
纠错建议：将"{error['error']}"纠正为"{error['correct']}"，因为{error['explanation']}。
"""
            rag_texts.append(text.strip())
        
        # 保存RAG文本
        rag_texts_file = os.path.join(self.output_dir, "rag_formatted_texts.txt")
        with open(rag_texts_file, 'w', encoding='utf-8') as f:
            for i, text in enumerate(rag_texts, 1):
                f.write(f"=== 知识条目 {i} ===\n")
                f.write(text)
                f.write("\n\n")
        
        print(f"✅ RAG文本格式化完成")
        print(f"   格式化文本: {len(rag_texts)} 条")
        print(f"   保存位置: {rag_texts_file}")
        
        return rag_texts
    
    async def integrate_with_rag_system(self, rag_texts: List[str]) -> bool:
        """集成到RAG系统"""
        print(f"\n🚀 步骤5: 集成RAG系统")
        print("-" * 40)
        
        if not COMPONENTS_AVAILABLE:
            print("❌ 组件不可用，跳过RAG集成")
            return False
        
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("⚠️ 未设置OPENAI_API_KEY，跳过RAG集成")
            print("💡 设置API密钥: export OPENAI_API_KEY='your-key'")
            return False
        
        try:
            # 创建RAG增强纠错器
            rag_working_dir = os.path.join(self.output_dir, "rag_system")
            corrector = LightRAGEnhancedCorrector(
                working_dir=rag_working_dir,
                enable_rag=True,
                api_key=api_key
            )
            
            # 初始化
            await corrector.initialize()
            
            # 分批插入知识
            print("📚 开始插入知识到RAG系统...")
            batch_size = 20  # 减小批次大小以提高稳定性
            total_batches = (len(rag_texts) + batch_size - 1) // batch_size
            
            for i in range(0, len(rag_texts), batch_size):
                batch = rag_texts[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                print(f"📖 插入批次 {batch_num}/{total_batches} ({len(batch)} 条)")
                
                if corrector.rag_system:
                    await corrector.rag_system.insert(batch)
                    print(f"   ✅ 批次 {batch_num} 插入成功")
                else:
                    print("❌ RAG系统未初始化")
                    return False
            
            print("✅ RAG系统集成完成")
            print(f"   RAG工作目录: {rag_working_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ RAG系统集成失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_rag_system(self) -> bool:
        """测试RAG系统"""
        print(f"\n🧪 步骤6: 测试RAG系统")
        print("-" * 40)
        
        if not COMPONENTS_AVAILABLE:
            print("❌ 组件不可用，跳过RAG测试")
            return False
        
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("⚠️ 未设置API密钥，跳过RAG测试")
            return False
        
        try:
            # 创建测试用的纠错器
            rag_working_dir = os.path.join(self.output_dir, "rag_system")
            corrector = LightRAGEnhancedCorrector(
                working_dir=rag_working_dir,
                enable_rag=True,
                api_key=api_key
            )
            
            await corrector.initialize()
            
            # 测试句子
            test_cases = [
                "我很喜欢这本书的",
                "他跑的很快",
                "认真的学习",
                "大约三点钟左右",
                "这个菜做的很好吃的"
            ]
            
            print("🔍 开始RAG增强纠错测试...")
            
            for i, test_text in enumerate(test_cases, 1):
                print(f"\n📝 测试 {i}: {test_text}")
                
                try:
                    result = await corrector.correct_with_rag_enhancement(
                        test_text, use_rag=True, rag_mode="hybrid"
                    )
                    
                    print(f"   纠错结果: {result.corrected_text}")
                    print(f"   置信度: {result.confidence:.2f}")
                    
                    if result.correction_reasoning:
                        print(f"   纠错理由: {result.correction_reasoning[:100]}...")
                    
                    if result.rag_context:
                        print(f"   RAG上下文: 已检索到相关知识")
                    
                except Exception as e:
                    print(f"   ❌ 测试失败: {e}")
            
            print("\n✅ RAG系统测试完成")
            return True
            
        except Exception as e:
            print(f"❌ RAG系统测试失败: {e}")
            return False
    
    def create_deployment_guide(self):
        """创建部署指南"""
        guide = f"""
# 中文校对RAG知识库部署指南

## 📁 知识库结构

```
{self.output_dir}/
├── expert_knowledge/              # 专家知识库
├── dataset_knowledge.json         # 数据集提取知识
├── merged_knowledge_base.json     # 合并后的完整知识库
├── rag_formatted_texts.txt        # RAG格式化文本
├── rag_system/                    # RAG系统存储
└── DEPLOYMENT_GUIDE.md           # 本指南
```

## 🚀 快速部署

### 1. 环境准备
```bash
# 设置API密钥
export OPENAI_API_KEY="your-openai-api-key"

# 安装依赖
pip install openai lightrag-hku
```

### 2. 使用知识库
```python
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

# 创建纠错器（使用已构建的知识库）
corrector = LightRAGEnhancedCorrector(
    working_dir="{os.path.join(self.output_dir, 'rag_system')}",
    enable_rag=True
)

await corrector.initialize()

# 进行RAG增强纠错
result = await corrector.correct_with_rag_enhancement("测试文本")
print(f"纠错结果: {{result.corrected_text}}")
print(f"纠错理由: {{result.correction_reasoning}}")
```

## 📊 知识库统计

{json.dumps(self.merged_knowledge.get('statistics', {}), ensure_ascii=False, indent=2)}

## 🔧 自定义扩展

### 添加新知识
1. 编辑 `merged_knowledge_base.json`
2. 重新运行 RAG 集成流程
3. 测试新知识的效果

### 优化检索效果
1. 调整 RAG 检索参数
2. 优化知识文本格式
3. 增加领域特定知识

## 📈 性能优化

- 批量处理时使用异步接口
- 根据需要调整检索模式（naive/local/global/hybrid）
- 定期更新知识库内容
- 监控纠错质量和用户反馈

## 🛠️ 故障排除

### 常见问题
1. **API密钥错误**: 检查 OPENAI_API_KEY 设置
2. **知识库为空**: 确认知识库构建成功
3. **检索无结果**: 检查查询文本和知识库匹配度
4. **性能问题**: 调整批次大小和并发数

### 日志查看
- RAG系统日志: `{os.path.join(self.output_dir, 'rag_system')}/logs/`
- 纠错器日志: 控制台输出

## 📞 技术支持

如有问题，请检查：
1. 环境配置是否正确
2. 知识库文件是否完整
3. API服务是否正常
4. 网络连接是否稳定
"""
        
        guide_file = os.path.join(self.output_dir, "DEPLOYMENT_GUIDE.md")
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print(f"📖 部署指南已创建: {guide_file}")

async def main():
    """主构建流程"""
    print("🚀 完整中文校对RAG知识库构建流程")
    print("=" * 60)
    
    # 创建完整知识库构建器
    builder = CompleteRAGKnowledgeBaseBuilder()
    
    try:
        # 步骤1: 构建专家知识
        await builder.build_expert_knowledge()
        
        # 步骤2: 提取数据集知识
        builder.extract_dataset_knowledge()
        
        # 步骤3: 合并知识源
        builder.merge_knowledge_sources()
        
        # 步骤4: 格式化RAG文本
        rag_texts = builder.format_knowledge_for_rag()
        
        # 步骤5: 集成RAG系统
        rag_success = await builder.integrate_with_rag_system(rag_texts)
        
        # 步骤6: 测试RAG系统
        if rag_success:
            await builder.test_rag_system()
        
        # 创建部署指南
        builder.create_deployment_guide()
        
        print(f"\n🎉 完整RAG知识库构建完成！")
        print(f"📂 输出目录: {builder.output_dir}")
        print(f"📊 知识库统计: {builder.merged_knowledge.get('statistics', {})}")
        
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
