#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG与ChineseErrorCorrector集成测试脚本
验证RAG增强纠错的效果
"""

import os
import sys
import asyncio
import time
from typing import List, Dict

# 添加路径
sys.path.append('.')
sys.path.append('./ChineseErrorCorrector')
sys.path.append('./LightRAG')

try:
    from lightrag_enhanced_corrector import LightRAGEnhancedCorrector, CorrectionResult
    ENHANCED_CORRECTOR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 增强纠错器导入失败: {e}")
    ENHANCED_CORRECTOR_AVAILABLE = False

async def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试基础功能")
    print("-" * 40)
    
    if not ENHANCED_CORRECTOR_AVAILABLE:
        print("❌ 增强纠错器不可用，跳过测试")
        return False
    
    try:
        # 创建纠错器实例（不使用RAG，避免API依赖）
        corrector = LightRAGEnhancedCorrector(
            working_dir="./test_rag_corrector",
            enable_rag=False  # 禁用RAG进行基础测试
        )
        
        await corrector.initialize()
        
        # 测试单个文本纠错
        test_text = "我很喜欢这本书的"
        result = await corrector.correct_with_rag_enhancement(
            test_text, use_rag=False
        )
        
        print(f"✅ 基础纠错测试成功")
        print(f"   原文: {result.original_text}")
        print(f"   纠错: {result.corrected_text}")
        print(f"   置信度: {result.confidence}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_batch_processing():
    """测试批量处理"""
    print("\n🧪 测试批量处理")
    print("-" * 40)
    
    if not ENHANCED_CORRECTOR_AVAILABLE:
        print("❌ 增强纠错器不可用，跳过测试")
        return False
    
    try:
        corrector = LightRAGEnhancedCorrector(
            working_dir="./test_rag_corrector",
            enable_rag=False
        )
        
        await corrector.initialize()
        
        # 测试文本
        test_texts = [
            "我很喜欢这本书的",
            "他跑的很快的",
            "这个菜做的很好吃",
            "学生们学习的很努力"
        ]
        
        print(f"📝 测试 {len(test_texts)} 个文本的批量处理")
        
        start_time = time.time()
        results = await corrector.batch_correct(
            test_texts, 
            batch_size=2, 
            use_rag=False
        )
        process_time = time.time() - start_time
        
        print(f"✅ 批量处理测试成功")
        print(f"   处理时间: {process_time:.2f} 秒")
        print(f"   处理速度: {len(test_texts)/process_time:.1f} 文本/秒")
        print(f"   结果数量: {len(results)}")
        
        # 显示部分结果
        for i, result in enumerate(results[:2]):
            print(f"   示例 {i+1}: {result.original_text} -> {result.corrected_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_knowledge_base_building():
    """测试知识库构建（模拟）"""
    print("\n🧪 测试知识库构建")
    print("-" * 40)
    
    if not ENHANCED_CORRECTOR_AVAILABLE:
        print("❌ 增强纠错器不可用，跳过测试")
        return False
    
    try:
        corrector = LightRAGEnhancedCorrector(
            working_dir="./test_rag_corrector",
            enable_rag=False  # 不需要真实的RAG系统
        )
        
        await corrector.initialize()
        
        # 准备测试数据
        error_examples = [
            {
                "error": "我很喜欢吃苹果的",
                "correct": "我很喜欢吃苹果",
                "type": "语气词误用"
            },
            {
                "error": "他们在学校里学习的很认真",
                "correct": "他们在学校里学习得很认真",
                "type": "得字句错误"
            }
        ]
        
        grammar_rules = [
            "中文句末通常不需要语气词'的'",
            "'得'字用于补语，'的'字用于定语"
        ]
        
        # 模拟知识库构建（不实际调用RAG）
        print(f"📚 模拟构建知识库:")
        print(f"   错误示例: {len(error_examples)} 个")
        print(f"   语法规则: {len(grammar_rules)} 条")
        
        # 这里只是验证数据格式，不实际构建
        await corrector.build_knowledge_base(
            error_examples=error_examples,
            grammar_rules=grammar_rules
        )
        
        print(f"✅ 知识库构建测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 知识库构建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n🧪 测试文件操作")
    print("-" * 40)
    
    try:
        # 创建测试输入文件
        test_input_file = "./test_input.txt"
        test_output_file = "./test_output.txt"
        
        test_texts = [
            "我很喜欢这本书的",
            "他跑的很快的",
            "这个菜做的很好吃"
        ]
        
        # 写入测试文件
        with open(test_input_file, 'w', encoding='utf-8') as f:
            for text in test_texts:
                f.write(text + '\n')
        
        print(f"✅ 创建测试输入文件: {test_input_file}")
        
        # 模拟结果保存
        if ENHANCED_CORRECTOR_AVAILABLE:
            corrector = LightRAGEnhancedCorrector()
            
            # 创建模拟结果
            mock_results = [
                CorrectionResult(
                    original_text=text,
                    corrected_text=text.replace('的', ''),
                    confidence=0.8,
                    error_types=["语气词误用"]
                )
                for text in test_texts
            ]
            
            corrector.save_results(mock_results, test_output_file)
            print(f"✅ 保存测试结果文件: {test_output_file}")
        
        # 清理测试文件
        for file_path in [test_input_file, test_output_file, test_output_file.replace('.txt', '_detailed.json')]:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🧹 清理测试文件: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_check():
    """检查环境配置"""
    print("\n🧪 检查环境配置")
    print("-" * 40)
    
    # 检查LightRAG
    try:
        from lightrag import LightRAG, QueryParam
        print("✅ LightRAG 可用")
        lightrag_available = True
    except ImportError:
        print("❌ LightRAG 不可用")
        lightrag_available = False
    
    # 检查ChineseErrorCorrector
    try:
        from ChineseErrorCorrector.llm.infer.hf_infer import HFTextCorrectInfer
        print("✅ ChineseErrorCorrector 可用")
        cec_available = True
    except ImportError:
        print("❌ ChineseErrorCorrector 不可用")
        cec_available = False
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print(f"✅ OpenAI API密钥已设置 (长度: {len(api_key)})")
        api_available = True
    else:
        print("⚠️ OpenAI API密钥未设置")
        api_available = False
    
    # 检查增强纠错器
    if ENHANCED_CORRECTOR_AVAILABLE:
        print("✅ LightRAG增强纠错器可用")
    else:
        print("❌ LightRAG增强纠错器不可用")
    
    print(f"\n📊 环境检查总结:")
    print(f"   LightRAG: {'✅' if lightrag_available else '❌'}")
    print(f"   ChineseErrorCorrector: {'✅' if cec_available else '❌'}")
    print(f"   OpenAI API: {'✅' if api_available else '⚠️'}")
    print(f"   增强纠错器: {'✅' if ENHANCED_CORRECTOR_AVAILABLE else '❌'}")
    
    # 给出建议
    if not lightrag_available:
        print("\n💡 安装LightRAG: cd LightRAG && pip install -e .")
    
    if not api_available:
        print("\n💡 设置API密钥: export OPENAI_API_KEY='your-key'")
    
    return lightrag_available and cec_available

async def run_all_tests():
    """运行所有测试"""
    print("🚀 LightRAG与ChineseErrorCorrector集成测试")
    print("=" * 60)
    
    test_results = []
    
    # 环境检查
    env_ok = test_environment_check()
    test_results.append(("环境检查", env_ok))
    
    # 基础功能测试
    basic_ok = await test_basic_functionality()
    test_results.append(("基础功能", basic_ok))
    
    # 批量处理测试
    batch_ok = await test_batch_processing()
    test_results.append(("批量处理", batch_ok))
    
    # 知识库构建测试
    kb_ok = await test_knowledge_base_building()
    test_results.append(("知识库构建", kb_ok))
    
    # 文件操作测试
    file_ok = test_file_operations()
    test_results.append(("文件操作", file_ok))
    
    # 测试总结
    print(f"\n🎯 测试总结")
    print("=" * 60)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📊 测试统计:")
    print(f"   通过: {passed_tests}/{total_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！LightRAG与ChineseErrorCorrector集成成功！")
    else:
        print(f"\n⚠️ 部分测试失败，请检查环境配置和依赖安装")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    asyncio.run(run_all_tests())
