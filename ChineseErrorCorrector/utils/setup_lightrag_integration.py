#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG与ChineseErrorCorrector集成安装配置脚本
自动化安装和配置RAG增强纠错系统
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(command, description, check_return=True):
    """运行命令并处理结果"""
    print(f"🔄 {description}")
    print(f"   命令: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()[:200]}...")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()[:200]}...")
            if check_return:
                return False
            return True
            
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✅ Python版本满足要求 (>=3.8)")
        return True
    else:
        print("❌ Python版本过低，需要3.8或更高版本")
        return False

def install_lightrag():
    """安装LightRAG"""
    print("\n📦 安装LightRAG")
    print("-" * 40)
    
    # 检查LightRAG目录
    lightrag_path = Path("../../LightRAG")
    if lightrag_path.exists():
        print(f"✅ 找到LightRAG目录: {lightrag_path.absolute()}")
        
        # 从源码安装
        install_cmd = f"cd {lightrag_path} && pip install -e ."
        return run_command(install_cmd, "从源码安装LightRAG")
    else:
        print("⚠️ 未找到本地LightRAG目录，尝试从PyPI安装")
        
        # 从PyPI安装
        install_cmd = "pip install lightrag-hku"
        return run_command(install_cmd, "从PyPI安装LightRAG")

def install_dependencies():
    """安装其他依赖"""
    print("\n📦 安装其他依赖")
    print("-" * 40)
    
    dependencies = [
        "openai",
        "numpy",
        "asyncio",
        "dataclasses",
        "typing-extensions"
    ]
    
    success_count = 0
    for dep in dependencies:
        cmd = f"pip install {dep}"
        if run_command(cmd, f"安装 {dep}", check_return=False):
            success_count += 1
    
    print(f"📊 依赖安装结果: {success_count}/{len(dependencies)} 成功")
    return success_count == len(dependencies)

def setup_environment():
    """设置环境配置"""
    print("\n🔧 设置环境配置")
    print("-" * 40)
    
    # 创建环境配置文件
    env_config = {
        "OPENAI_API_KEY": "your-openai-api-key-here",
        "OPENAI_BASE_URL": "https://api.openai.com/v1",
        "RAG_WORKING_DIR": "./rag_enhanced_corrector",
        "BATCH_SIZE": "10",
        "ENABLE_RAG": "true"
    }
    
    env_file = ".env.example"
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            for key, value in env_config.items():
                f.write(f"{key}={value}\n")
        
        print(f"✅ 创建环境配置示例文件: {env_file}")
        print("💡 请复制为 .env 并填入真实的API密钥")
        
        # 创建配置说明
        config_guide = """
# LightRAG增强纠错器环境配置指南

## 必需配置
1. 复制 .env.example 为 .env
2. 设置 OPENAI_API_KEY 为你的真实API密钥

## 可选配置
- OPENAI_BASE_URL: 自定义API端点
- RAG_WORKING_DIR: RAG系统工作目录
- BATCH_SIZE: 批处理大小
- ENABLE_RAG: 是否启用RAG增强

## 使用方法
```bash
# 设置环境变量
export OPENAI_API_KEY="your-real-api-key"

# 或者使用 .env 文件
cp .env.example .env
# 编辑 .env 文件填入真实配置
```
"""
        
        with open("CONFIG_GUIDE.md", 'w', encoding='utf-8') as f:
            f.write(config_guide)
        
        print("✅ 创建配置指南: CONFIG_GUIDE.md")
        return True
        
    except Exception as e:
        print(f"❌ 环境配置设置失败: {e}")
        return False

def create_demo_data():
    """创建演示数据"""
    print("\n📝 创建演示数据")
    print("-" * 40)
    
    try:
        # 创建测试输入文件
        demo_texts = [
            "我很喜欢这本书的",
            "他跑的很快的",
            "这个菜做的很好吃的",
            "学生们学习的很努力",
            "今天天气很好的",
            "我们一起去公园玩的",
            "这个问题很难解决的",
            "老师讲课讲的很清楚"
        ]
        
        demo_input_file = "demo_input.txt"
        with open(demo_input_file, 'w', encoding='utf-8') as f:
            for text in demo_texts:
                f.write(text + '\n')
        
        print(f"✅ 创建演示输入文件: {demo_input_file}")
        print(f"   包含 {len(demo_texts)} 个测试句子")
        
        # 创建知识库示例
        knowledge_base = {
            "error_examples": [
                {
                    "error": "我很喜欢吃苹果的",
                    "correct": "我很喜欢吃苹果",
                    "type": "语气词误用",
                    "explanation": "句末的'的'字是多余的语气词"
                },
                {
                    "error": "他们在学校里学习的很认真",
                    "correct": "他们在学校里学习得很认真",
                    "type": "得字句错误",
                    "explanation": "'得'字用于补语，'的'字用于定语"
                },
                {
                    "error": "这个问题很难解决的",
                    "correct": "这个问题很难解决",
                    "type": "语气词冗余",
                    "explanation": "陈述句末尾不需要语气词'的'"
                }
            ],
            "grammar_rules": [
                "中文句末通常不需要语气词'的'，除非表示确定或强调",
                "'得'字用于补语，表示程度或结果",
                "'的'字用于定语，表示修饰关系",
                "动词后的程度补语应该用'得'连接",
                "陈述句末尾一般不使用语气词"
            ],
            "domain_knowledge": [
                "标准中文书面语避免使用过多的语气词",
                "口语化表达在书面语中需要规范化",
                "语法纠错需要考虑语境和语义完整性"
            ]
        }
        
        kb_file = "demo_knowledge_base.json"
        with open(kb_file, 'w', encoding='utf-8') as f:
            json.dump(knowledge_base, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建知识库示例: {kb_file}")
        print(f"   错误示例: {len(knowledge_base['error_examples'])} 个")
        print(f"   语法规则: {len(knowledge_base['grammar_rules'])} 条")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示数据创建失败: {e}")
        return False

def create_usage_examples():
    """创建使用示例脚本"""
    print("\n📋 创建使用示例")
    print("-" * 40)
    
    try:
        # 基础使用示例
        basic_example = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG增强纠错器基础使用示例
"""

import asyncio
import os
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

async def basic_usage_example():
    """基础使用示例"""
    print("🎯 LightRAG增强纠错器基础使用示例")
    
    # 创建纠错器（需要设置API密钥）
    corrector = LightRAGEnhancedCorrector(
        working_dir="./example_rag_corrector",
        enable_rag=True,  # 启用RAG增强
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    # 初始化
    await corrector.initialize()
    
    # 单文本纠错
    text = "我很喜欢这本书的"
    result = await corrector.correct_with_rag_enhancement(text)
    
    print(f"原文: {result.original_text}")
    print(f"纠错: {result.corrected_text}")
    print(f"置信度: {result.confidence}")
    if result.correction_reasoning:
        print(f"理由: {result.correction_reasoning}")

if __name__ == "__main__":
    # 确保设置了API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置 OPENAI_API_KEY 环境变量")
        exit(1)
    
    asyncio.run(basic_usage_example())
'''
        
        with open("example_basic_usage.py", 'w', encoding='utf-8') as f:
            f.write(basic_example)
        
        print("✅ 创建基础使用示例: example_basic_usage.py")
        
        # 批量处理示例
        batch_example = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG增强纠错器批量处理示例
"""

import asyncio
import os
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

async def batch_processing_example():
    """批量处理示例"""
    print("🔄 批量处理示例")
    
    # 读取演示数据
    with open("demo_input.txt", 'r', encoding='utf-8') as f:
        texts = [line.strip() for line in f if line.strip()]
    
    print(f"📖 读取 {len(texts)} 个文本")
    
    # 创建纠错器
    corrector = LightRAGEnhancedCorrector(
        working_dir="./batch_rag_corrector",
        enable_rag=True,
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    await corrector.initialize()
    
    # 批量纠错
    results = await corrector.batch_correct(texts, batch_size=5)
    
    # 保存结果
    corrector.save_results(results, "batch_output.txt")
    
    print(f"✅ 批量处理完成，结果保存到 batch_output.txt")

if __name__ == "__main__":
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置 OPENAI_API_KEY 环境变量")
        exit(1)
    
    asyncio.run(batch_processing_example())
'''
        
        with open("example_batch_processing.py", 'w', encoding='utf-8') as f:
            f.write(batch_example)
        
        print("✅ 创建批量处理示例: example_batch_processing.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 使用示例创建失败: {e}")
        return False

def run_verification():
    """运行验证测试"""
    print("\n🧪 运行验证测试")
    print("-" * 40)
    
    return run_command(
        "python test_lightrag_integration.py",
        "运行集成测试",
        check_return=False
    )

def main():
    """主安装流程"""
    print("🚀 LightRAG与ChineseErrorCorrector集成安装配置")
    print("=" * 60)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装LightRAG", install_lightrag),
        ("安装其他依赖", install_dependencies),
        ("设置环境配置", setup_environment),
        ("创建演示数据", create_demo_data),
        ("创建使用示例", create_usage_examples),
        ("运行验证测试", run_verification)
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            results.append((step_name, result))
            if result:
                print(f"✅ {step_name} 完成")
            else:
                print(f"⚠️ {step_name} 部分完成或失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
            results.append((step_name, False))
    
    # 安装总结
    print(f"\n🎯 安装配置总结")
    print("=" * 60)
    
    success_count = sum(1 for _, result in results if result)
    total_steps = len(results)
    
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {step_name}: {status}")
    
    print(f"\n📊 安装统计:")
    print(f"   成功步骤: {success_count}/{total_steps}")
    print(f"   成功率: {success_count/total_steps*100:.1f}%")
    
    if success_count >= total_steps - 1:  # 允许一个步骤失败
        print(f"\n🎉 安装配置基本完成！")
        print(f"\n📝 下一步操作:")
        print(f"   1. 设置API密钥: export OPENAI_API_KEY='your-key'")
        print(f"   2. 运行演示: python lightrag_enhanced_corrector.py --mode demo")
        print(f"   3. 查看文档: README_lightrag_enhanced_corrector.md")
    else:
        print(f"\n⚠️ 安装配置未完全成功，请检查错误信息并重试")

if __name__ == "__main__":
    main()
