#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文纠错数据集知识提取器
从现有的中文纠错数据集中提取知识用于构建RAG知识库
"""

import os
import json
import re
from typing import List, Dict, Any, Tuple
from pathlib import Path
import difflib

class DatasetKnowledgeExtractor:
    """数据集知识提取器"""
    
    def __init__(self):
        self.error_patterns = {}
        self.correction_rules = []
        self.extracted_knowledge = []
        
        print("🔍 数据集知识提取器初始化")
    
    def analyze_error_correction_pair(self, error_text: str, correct_text: str) -> Dict[str, Any]:
        """分析错误-纠正对，提取知识"""
        if not error_text or not correct_text:
            return None
        
        # 计算差异
        diff = list(difflib.unified_diff(
            error_text.split(), 
            correct_text.split(), 
            lineterm=''
        ))
        
        # 提取错误类型
        error_type = self.classify_error_type(error_text, correct_text)
        
        # 生成解释
        explanation = self.generate_explanation(error_text, correct_text, error_type)
        
        # 提取语法规则
        rule = self.extract_grammar_rule(error_text, correct_text, error_type)
        
        return {
            "error": error_text,
            "correct": correct_text,
            "type": error_type,
            "explanation": explanation,
            "rule": rule,
            "diff": diff,
            "source": "dataset_extraction"
        }
    
    def classify_error_type(self, error_text: str, correct_text: str) -> str:
        """分类错误类型"""
        # 语气词错误
        if error_text.endswith('的') and not correct_text.endswith('的'):
            return "语气词误用"
        
        # 的地得混用
        if '的' in error_text and '得' in correct_text:
            if re.search(r'\w+的(很|非常|特别)', error_text):
                return "得字句错误"
        
        if '的' in error_text and '地' in correct_text:
            if re.search(r'(认真|仔细|努力)的\w+', error_text):
                return "的地得混用"
        
        # 量词错误
        measure_words_error = re.search(r'(一|二|三|四|五|六|七|八|九|十|\d+)(个|头|只|本|张|条)', error_text)
        measure_words_correct = re.search(r'(一|二|三|四|五|六|七|八|九|十|\d+)(个|头|只|本|张|条)', correct_text)
        
        if measure_words_error and measure_words_correct:
            if measure_words_error.group(2) != measure_words_correct.group(2):
                return "量词错误"
        
        # 重复表达
        if len(error_text) > len(correct_text):
            redundant_patterns = [
                r'大约.*?左右',
                r'首先.*?第一',
                r'最后.*?最终'
            ]
            for pattern in redundant_patterns:
                if re.search(pattern, error_text):
                    return "重复表达"
        
        # 标点错误
        error_punct = re.findall(r'[，。？！；：""''（）【】]', error_text)
        correct_punct = re.findall(r'[，。？！；：""''（）【】]', correct_text)
        
        if error_punct != correct_punct:
            return "标点错误"
        
        # 词序错误
        error_words = error_text.split()
        correct_words = correct_text.split()
        
        if set(error_words) == set(correct_words) and error_words != correct_words:
            return "词序错误"
        
        # 用词错误
        if len(set(error_words) - set(correct_words)) > 0:
            return "用词错误"
        
        return "其他错误"
    
    def generate_explanation(self, error_text: str, correct_text: str, error_type: str) -> str:
        """生成错误解释"""
        explanations = {
            "语气词误用": f"陈述句末尾不需要语气词'的'",
            "得字句错误": f"动词后的程度补语应该用'得'连接",
            "的地得混用": f"副词修饰动词时应该用'地'",
            "量词错误": f"量词使用不当，应该根据名词性质选择合适的量词",
            "重复表达": f"存在意思重复的词语，应该删除冗余表达",
            "标点错误": f"标点符号使用不当",
            "词序错误": f"词语顺序不当，影响语义表达",
            "用词错误": f"词语选择不当，应该使用更合适的词汇",
            "其他错误": f"语法或表达不规范"
        }
        
        base_explanation = explanations.get(error_type, "语法错误")
        
        # 添加具体的差异说明
        if error_text != correct_text:
            return f"{base_explanation}。将'{error_text}'纠正为'{correct_text}'"
        
        return base_explanation
    
    def extract_grammar_rule(self, error_text: str, correct_text: str, error_type: str) -> str:
        """提取语法规则"""
        rules = {
            "语气词误用": "陈述句末尾通常不使用语气词'的'",
            "得字句错误": "动词+得+形容词/副词，表示动作的程度或结果",
            "的地得混用": "副词+地+动词，表示动作的方式",
            "量词错误": "量词要与名词的性质相匹配",
            "重复表达": "避免使用意思相同的词语重复表达",
            "标点错误": "不同句型使用相应的标点符号",
            "词序错误": "中文语序相对固定，主要是SVO结构",
            "用词错误": "选择语义准确、搭配恰当的词汇",
            "其他错误": "遵循标准中文语法规范"
        }
        
        return rules.get(error_type, "遵循标准中文语法规范")
    
    def load_fcgec_dataset(self, data_path: str) -> List[Dict[str, Any]]:
        """加载FCGEC数据集"""
        knowledge_items = []
        
        if not os.path.exists(data_path):
            print(f"⚠️ FCGEC数据集路径不存在: {data_path}")
            return knowledge_items
        
        print(f"📂 加载FCGEC数据集: {data_path}")
        
        try:
            # 假设FCGEC数据格式为每行一个JSON对象
            with open(data_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())
                        
                        # 提取错误和正确文本
                        error_text = data.get('source', '').strip()
                        correct_text = data.get('target', '').strip()
                        
                        if error_text and correct_text and error_text != correct_text:
                            knowledge_item = self.analyze_error_correction_pair(
                                error_text, correct_text
                            )
                            
                            if knowledge_item:
                                knowledge_item['dataset'] = 'FCGEC'
                                knowledge_item['line_number'] = line_num
                                knowledge_items.append(knowledge_item)
                    
                    except json.JSONDecodeError:
                        continue
                    except Exception as e:
                        print(f"⚠️ 处理第{line_num}行时出错: {e}")
                        continue
        
        except Exception as e:
            print(f"❌ 加载FCGEC数据集失败: {e}")
        
        print(f"✅ 从FCGEC提取知识: {len(knowledge_items)} 条")
        return knowledge_items
    
    def load_nlpcc_dataset(self, data_path: str) -> List[Dict[str, Any]]:
        """加载NLPCC数据集"""
        knowledge_items = []
        
        if not os.path.exists(data_path):
            print(f"⚠️ NLPCC数据集路径不存在: {data_path}")
            return knowledge_items
        
        print(f"📂 加载NLPCC数据集: {data_path}")
        
        try:
            with open(data_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        # 假设格式为: 错误句子\t正确句子
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            error_text = parts[0].strip()
                            correct_text = parts[1].strip()
                            
                            if error_text and correct_text and error_text != correct_text:
                                knowledge_item = self.analyze_error_correction_pair(
                                    error_text, correct_text
                                )
                                
                                if knowledge_item:
                                    knowledge_item['dataset'] = 'NLPCC'
                                    knowledge_item['line_number'] = line_num
                                    knowledge_items.append(knowledge_item)
                    
                    except Exception as e:
                        print(f"⚠️ 处理第{line_num}行时出错: {e}")
                        continue
        
        except Exception as e:
            print(f"❌ 加载NLPCC数据集失败: {e}")
        
        print(f"✅ 从NLPCC提取知识: {len(knowledge_items)} 条")
        return knowledge_items
    
    def extract_from_all_datasets(self, base_data_dir: str = "../data") -> List[Dict[str, Any]]:
        """从所有可用数据集提取知识"""
        all_knowledge = []
        
        print(f"🔍 搜索数据集目录: {base_data_dir}")
        
        # 定义数据集路径和加载函数
        dataset_configs = [
            {
                "name": "FCGEC",
                "paths": [
                    os.path.join(base_data_dir, "FCGEC_data", "train.json"),
                    os.path.join(base_data_dir, "FCGEC_data", "dev.json"),
                    os.path.join(base_data_dir, "FCGEC_data", "test.json")
                ],
                "loader": self.load_fcgec_dataset
            },
            {
                "name": "NLPCC",
                "paths": [
                    os.path.join(base_data_dir, "NLPCC2018_data", "train.txt"),
                    os.path.join(base_data_dir, "NLPCC2018_data", "dev.txt"),
                    os.path.join(base_data_dir, "NLPCC2018_data", "test.txt")
                ],
                "loader": self.load_nlpcc_dataset
            }
        ]
        
        # 加载各个数据集
        for config in dataset_configs:
            dataset_knowledge = []
            
            for path in config["paths"]:
                if os.path.exists(path):
                    knowledge_items = config["loader"](path)
                    dataset_knowledge.extend(knowledge_items)
            
            if dataset_knowledge:
                print(f"📊 {config['name']}数据集提取: {len(dataset_knowledge)} 条知识")
                all_knowledge.extend(dataset_knowledge)
            else:
                print(f"⚠️ {config['name']}数据集未找到或为空")
        
        # 去重和质量过滤
        filtered_knowledge = self.filter_and_deduplicate(all_knowledge)
        
        print(f"🎯 总计提取知识: {len(filtered_knowledge)} 条（去重后）")
        return filtered_knowledge
    
    def filter_and_deduplicate(self, knowledge_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和去重知识项"""
        seen_pairs = set()
        filtered_items = []
        
        for item in knowledge_items:
            # 创建唯一标识
            pair_key = (item['error'], item['correct'])
            
            # 去重
            if pair_key in seen_pairs:
                continue
            
            # 质量过滤
            if self.is_high_quality_knowledge(item):
                seen_pairs.add(pair_key)
                filtered_items.append(item)
        
        print(f"🔧 质量过滤: {len(knowledge_items)} -> {len(filtered_items)}")
        return filtered_items
    
    def is_high_quality_knowledge(self, item: Dict[str, Any]) -> bool:
        """判断知识项质量"""
        error_text = item['error']
        correct_text = item['correct']
        
        # 基本质量检查
        if not error_text or not correct_text:
            return False
        
        # 长度检查
        if len(error_text) > 200 or len(correct_text) > 200:
            return False
        
        # 差异检查
        if error_text == correct_text:
            return False
        
        # 字符检查
        if not all(ord(c) < 65536 for c in error_text + correct_text):
            return False
        
        return True
    
    def save_extracted_knowledge(self, knowledge_items: List[Dict[str, Any]], 
                                output_path: str = "./extracted_dataset_knowledge.json"):
        """保存提取的知识"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(knowledge_items, f, ensure_ascii=False, indent=2)
            
            print(f"💾 提取的知识已保存: {output_path}")
            print(f"📊 统计信息:")
            
            # 统计错误类型
            type_counts = {}
            for item in knowledge_items:
                error_type = item['type']
                type_counts[error_type] = type_counts.get(error_type, 0) + 1
            
            for error_type, count in sorted(type_counts.items()):
                print(f"   {error_type}: {count} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存知识失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 数据集知识提取流程")
    print("=" * 50)
    
    # 创建提取器
    extractor = DatasetKnowledgeExtractor()
    
    # 从所有数据集提取知识
    knowledge_items = extractor.extract_from_all_datasets()
    
    if knowledge_items:
        # 保存提取的知识
        extractor.save_extracted_knowledge(knowledge_items)
        
        print(f"\n🎉 知识提取完成！")
        print(f"📈 提取统计:")
        print(f"   总知识条数: {len(knowledge_items)}")
        print(f"   平均错误长度: {sum(len(item['error']) for item in knowledge_items) / len(knowledge_items):.1f} 字符")
        print(f"   平均纠正长度: {sum(len(item['correct']) for item in knowledge_items) / len(knowledge_items):.1f} 字符")
    else:
        print("⚠️ 未提取到有效知识，请检查数据集路径和格式")

if __name__ == "__main__":
    main()
