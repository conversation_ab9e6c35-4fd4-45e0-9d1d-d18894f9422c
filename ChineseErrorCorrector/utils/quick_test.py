#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
验证所有组件是否正常工作
"""

import os
import sys

def test_file_existence():
    """测试文件是否存在"""
    print("🔍 检查文件存在性")
    print("-" * 30)
    
    files_to_check = [
        "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json",
        "./rag_knowledge_usage_guide.py",
        "./prompt_generation_example.py",
        "./enhanced_prompt_demo.py"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            all_exist = False
    
    return all_exist

def test_import():
    """测试模块导入"""
    print(f"\n🔧 测试模块导入")
    print("-" * 30)
    
    try:
        from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
        print("✅ ChineseKnowledgeBaseProcessor 导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_knowledge_base_loading():
    """测试知识库加载"""
    print(f"\n📚 测试知识库加载")
    print("-" * 30)
    
    try:
        from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
        
        kb_path = "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
        processor = ChineseKnowledgeBaseProcessor(kb_path)
        
        total_knowledge = processor.get_total_knowledge_count()
        print(f"✅ 知识库加载成功")
        print(f"   总知识条目: {total_knowledge}")
        
        return True
    except Exception as e:
        print(f"❌ 知识库加载失败: {e}")
        return False

def test_retrieval():
    """测试知识检索"""
    print(f"\n🔍 测试知识检索")
    print("-" * 30)
    
    try:
        from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
        
        kb_path = "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
        processor = ChineseKnowledgeBaseProcessor(kb_path)
        
        test_text = "我很喜欢这件衣服的。"
        results = processor.retrieve_relevant_knowledge(test_text, top_k=2)
        
        print(f"✅ 知识检索成功")
        print(f"   测试文本: {test_text}")
        print(f"   检索结果: {len(results)} 条")
        
        if results:
            print(f"   最佳匹配: {results[0].match_type} (相关度: {results[0].relevance_score:.2f})")
        
        return True
    except Exception as e:
        print(f"❌ 知识检索失败: {e}")
        return False

def test_prompt_generation():
    """测试提示词生成"""
    print(f"\n📝 测试提示词生成")
    print("-" * 30)
    
    try:
        from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
        
        kb_path = "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
        processor = ChineseKnowledgeBaseProcessor(kb_path)
        
        test_text = "我很喜欢这件衣服的。"
        results = processor.retrieve_relevant_knowledge(test_text, top_k=2)
        prompt = processor.generate_correction_prompt(test_text, results)
        
        print(f"✅ 提示词生成成功")
        print(f"   提示词长度: {len(prompt)} 字符")
        print(f"   包含知识条目: {len(results)} 个")
        
        # 显示提示词片段
        preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
        print(f"   提示词预览: {preview}")
        
        return True
    except Exception as e:
        print(f"❌ 提示词生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 RAG知识库系统快速测试")
    print("=" * 50)
    
    tests = [
        ("文件存在性检查", test_file_existence),
        ("模块导入测试", test_import),
        ("知识库加载测试", test_knowledge_base_loading),
        ("知识检索测试", test_retrieval),
        ("提示词生成测试", test_prompt_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n🎯 测试总结")
    print("=" * 50)
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        print("\n📋 可以运行的脚本:")
        print("   python prompt_generation_example.py")
        print("   python enhanced_prompt_demo.py")
        print("   python test_rag_knowledge_base.py")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
