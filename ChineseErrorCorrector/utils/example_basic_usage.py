#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRAG增强纠错器基础使用示例
"""

import asyncio
import os
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

async def basic_usage_example():
    """基础使用示例"""
    print("🎯 LightRAG增强纠错器基础使用示例")
    
    # 创建纠错器（需要设置API密钥）
    corrector = LightRAGEnhancedCorrector(
        working_dir="./example_rag_corrector",
        enable_rag=True,  # 启用RAG增强
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    # 初始化
    await corrector.initialize()
    
    # 单文本纠错
    text = "我很喜欢这本书的"
    result = await corrector.correct_with_rag_enhancement(text)
    
    print(f"原文: {result.original_text}")
    print(f"纠错: {result.corrected_text}")
    print(f"置信度: {result.confidence}")
    if result.correction_reasoning:
        print(f"理由: {result.correction_reasoning}")

if __name__ == "__main__":
    # 确保设置了API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置 OPENAI_API_KEY 环境变量")
        exit(1)
    
    asyncio.run(basic_usage_example())
