#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单批次CEC纠错处理脚本
每处理20条记录就重新初始化模型，避免内存积累
"""

import os
import sys
import argparse
import time
import gc

# 添加路径
sys.path.append('.')
sys.path.append('./ChineseErrorCorrector')

def process_batch(lines, start_idx, batch_num, total_batches):
    """处理一个批次的数据"""
    print(f"\n{'='*50}")
    print(f"📋 批次 {batch_num}/{total_batches}")
    print(f"📍 处理行范围: {start_idx + 1} - {start_idx + len(lines)}")
    print(f"📊 本批次行数: {len(lines)}")
    print(f"{'='*50}")
    
    try:
        # 清理之前的内存
        gc.collect()
        
        # 重新导入和初始化模型
        print("🔧 重新初始化CEC模型...")
        
        # 清理可能存在的模块缓存
        modules_to_remove = []
        for module_name in sys.modules:
            if 'ChineseErrorCorrector' in module_name:
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            if module_name in sys.modules:
                del sys.modules[module_name]
        
        # 重新导入
        from ChineseErrorCorrector.llm.infer.hf_infer import HFTextCorrectInfer
        
        # 初始化模型
        corrector = HFTextCorrectInfer()
        print("✅ CEC模型重新加载成功")
        
        # 处理文本
        print("🔄 开始纠错处理...")
        start_time = time.time()
        
        results = corrector.infer(lines)
        
        process_time = time.time() - start_time
        print(f"✅ 纠错完成，用时 {process_time:.1f} 秒")
        print(f"📊 得到 {len(results)} 个结果")
        
        # 清理模型内存
        del corrector
        
        # 强制清理GPU内存
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
        except:
            pass
        
        # 多次垃圾回收
        for _ in range(3):
            gc.collect()
        
        print("🧹 内存清理完成")
        
        return results, True
        
    except Exception as e:
        print(f"❌ 批次处理失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理内存
        try:
            del corrector
        except:
            pass
        
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except:
            pass
        
        gc.collect()
        
        return lines, False  # 返回原文

def process_file_simple_batch(input_file, output_file, batch_size=20, max_lines=None):
    """简单批次处理文件"""
    
    print(f"🎯 简单批次CEC纠错处理")
    print(f"{'='*60}")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"批次大小: {batch_size} 行")
    if max_lines:
        print(f"最大处理行数: {max_lines}")
    print(f"{'='*60}")
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return False
    
    # 读取输入文件
    print(f"📖 读取输入文件...")
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    
    if max_lines and max_lines < len(lines):
        lines = lines[:max_lines]
        print(f"⚠️  限制处理前 {max_lines} 行")
    
    print(f"✅ 读取 {len(lines)} 行文本")
    
    # 准备输出文件
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 清空输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        pass
    
    # 计算批次
    total_batches = (len(lines) + batch_size - 1) // batch_size
    completed_batches = 0
    failed_batches = 0
    start_time = time.time()
    
    print(f"\n🚀 开始批次处理，总共 {total_batches} 个批次...")
    print(f"每个批次处理 {batch_size} 行，处理完后重新初始化模型")
    
    all_results = []
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, len(lines))
        batch_lines = lines[start_idx:end_idx]
        
        # 处理当前批次
        batch_results, success = process_batch(
            batch_lines, 
            start_idx, 
            batch_num + 1, 
            total_batches
        )
        
        # 记录结果
        all_results.extend(batch_results)
        
        if success:
            completed_batches += 1
            print(f"✅ 批次 {batch_num + 1} 成功完成")
        else:
            failed_batches += 1
            print(f"❌ 批次 {batch_num + 1} 失败，使用原文")
        
        # 立即写入结果
        with open(output_file, 'a', encoding='utf-8') as f:
            for result in batch_results:
                f.write(str(result) + '\n')
        
        # 显示总体进度
        elapsed = time.time() - start_time
        avg_time = elapsed / (batch_num + 1)
        remaining_batches = total_batches - batch_num - 1
        eta = remaining_batches * avg_time
        
        print(f"\n📊 总体进度:")
        print(f"   完成批次: {completed_batches}/{total_batches}")
        print(f"   失败批次: {failed_batches}")
        print(f"   已处理行数: {len(all_results)}")
        print(f"   已用时间: {elapsed/60:.1f} 分钟")
        print(f"   预计剩余: {eta/60:.1f} 分钟")
        print(f"   平均速度: {len(all_results)/elapsed:.1f} 行/秒")
        
        # 休息一下
        if batch_num < total_batches - 1:  # 不是最后一个批次
            print(f"😴 休息 3 秒，让系统清理资源...")
            time.sleep(3)
    
    # 最终统计
    total_time = time.time() - start_time
    success_rate = completed_batches / total_batches * 100 if total_batches > 0 else 0
    
    print(f"\n🎉 批次处理完成！")
    print(f"{'='*60}")
    print(f"📊 最终统计:")
    print(f"   总批次数: {total_batches}")
    print(f"   成功批次: {completed_batches}")
    print(f"   失败批次: {failed_batches}")
    print(f"   成功率: {success_rate:.1f}%")
    print(f"   总处理行数: {len(all_results)}")
    print(f"   总用时: {total_time/60:.1f} 分钟")
    print(f"   平均速度: {len(all_results)/total_time:.1f} 行/秒")
    print(f"   输出文件: {output_file}")
    
    try:
        file_size = os.path.getsize(output_file) / 1024
        print(f"   文件大小: {file_size:.1f} KB")
    except:
        pass
    
    print(f"{'='*60}")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简单批次CEC纠错处理')
    parser.add_argument('--input', '-i',
                       default='/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/data/FCGEC_data/dev.json',
                       help='输入文件路径')
    parser.add_argument('--output', '-o',
                       default='/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/data/FCGEC_data/simple_batch_results.txt',
                       help='输出文件路径')
    parser.add_argument('--batch_size', '-b',
                       type=int, default=20,
                       help='批次大小')
    parser.add_argument('--max_lines', '-n',
                       type=int, default=None,
                       help='最大处理行数')
    
    args = parser.parse_args()
    
    print("🔄 简单批次CEC纠错处理器")
    print("每个批次处理完后重新初始化模型，避免内存积累")
    
    try:
        success = process_file_simple_batch(
            args.input,
            args.output,
            args.batch_size,
            args.max_lines
        )
        
        if success:
            print("\n🎉 简单批次处理成功完成！")
        else:
            print("\n❌ 简单批次处理失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 处理异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
