#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG知识库使用指南
详细说明如何使用中文校对知识库进行重排和提示词生成
"""

import json
import re
import math
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class RetrievalResult:
    """检索结果"""
    knowledge_item: Dict[str, Any]
    relevance_score: float
    match_type: str
    matched_keywords: List[str]

class ChineseKnowledgeBaseProcessor:
    """中文知识库处理器"""
    
    def __init__(self, knowledge_base_path: str):
        self.knowledge_base = {}
        self.indexed_knowledge = {}
        self.load_knowledge_base(knowledge_base_path)
        self.build_search_index()
        
        print(f"📚 中文知识库处理器初始化完成")
        print(f"   知识条目总数: {self.get_total_knowledge_count()}")
    
    def load_knowledge_base(self, path: str):
        """加载知识库"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                self.knowledge_base = json.load(f)
            print(f"✅ 知识库加载成功: {path}")
        except Exception as e:
            print(f"❌ 知识库加载失败: {e}")
            raise
    
    def build_search_index(self):
        """构建搜索索引"""
        print("🔍 构建知识库搜索索引...")
        
        self.indexed_knowledge = {
            'error_patterns': {},      # 错误模式索引
            'keywords': defaultdict(list),  # 关键词索引
            'error_types': defaultdict(list),  # 错误类型索引
            'rules': [],               # 语法规则索引
            'patterns': []             # 正则模式索引
        }
        
        # 索引语法错误
        for i, error in enumerate(self.knowledge_base.get('grammar_errors', [])):
            error_text = error['error']
            error_type = error['type']
            
            # 错误模式索引
            self.indexed_knowledge['error_patterns'][error_text] = {
                'index': i,
                'category': 'grammar_errors',
                'data': error
            }
            
            # 关键词索引
            keywords = self.extract_keywords(error_text + ' ' + error['explanation'])
            for keyword in keywords:
                self.indexed_knowledge['keywords'][keyword].append({
                    'index': i,
                    'category': 'grammar_errors',
                    'data': error,
                    'relevance': 1.0
                })
            
            # 错误类型索引
            self.indexed_knowledge['error_types'][error_type].append({
                'index': i,
                'category': 'grammar_errors', 
                'data': error
            })
        
        # 索引标点错误
        for i, error in enumerate(self.knowledge_base.get('punctuation_errors', [])):
            error_text = error['error']
            error_type = error['type']
            
            keywords = self.extract_keywords(error_text + ' ' + error['explanation'])
            for keyword in keywords:
                self.indexed_knowledge['keywords'][keyword].append({
                    'index': i,
                    'category': 'punctuation_errors',
                    'data': error,
                    'relevance': 0.8
                })
        
        # 索引语法规则
        for i, rule in enumerate(self.knowledge_base.get('grammar_rules', [])):
            self.indexed_knowledge['rules'].append({
                'index': i,
                'text': rule,
                'keywords': self.extract_keywords(rule)
            })
            
            keywords = self.extract_keywords(rule)
            for keyword in keywords:
                self.indexed_knowledge['keywords'][keyword].append({
                    'index': i,
                    'category': 'grammar_rules',
                    'data': {'rule': rule},
                    'relevance': 0.6
                })
        
        # 索引纠错模式
        for i, pattern in enumerate(self.knowledge_base.get('correction_patterns', [])):
            self.indexed_knowledge['patterns'].append({
                'index': i,
                'data': pattern,
                'keywords': self.extract_keywords(pattern['description'])
            })
        
        print(f"✅ 搜索索引构建完成")
        print(f"   错误模式: {len(self.indexed_knowledge['error_patterns'])} 个")
        print(f"   关键词: {len(self.indexed_knowledge['keywords'])} 个")
        print(f"   错误类型: {len(self.indexed_knowledge['error_types'])} 种")
    
    def extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 移除标点符号
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # 分词（简单按字符分割）
        words = []
        for char in text:
            if char.strip():
                words.append(char)
        
        # 提取2-3字的词组
        keywords = set(words)
        for i in range(len(words) - 1):
            if i < len(words) - 1:
                keywords.add(words[i] + words[i+1])
            if i < len(words) - 2:
                keywords.add(words[i] + words[i+1] + words[i+2])
        
        return list(keywords)
    
    def get_total_knowledge_count(self) -> int:
        """获取知识总数"""
        count = 0
        count += len(self.knowledge_base.get('grammar_errors', []))
        count += len(self.knowledge_base.get('punctuation_errors', []))
        count += len(self.knowledge_base.get('grammar_rules', []))
        count += len(self.knowledge_base.get('correction_patterns', []))
        count += len(self.knowledge_base.get('domain_knowledge', []))
        return count
    
    def retrieve_relevant_knowledge(self, query_text: str, top_k: int = 5) -> List[RetrievalResult]:
        """检索相关知识"""
        print(f"🔍 检索相关知识: {query_text}")
        
        results = []
        query_keywords = self.extract_keywords(query_text)
        
        # 1. 精确匹配检索
        exact_matches = self.exact_pattern_matching(query_text)
        results.extend(exact_matches)
        
        # 2. 关键词匹配检索
        keyword_matches = self.keyword_matching(query_keywords)
        results.extend(keyword_matches)
        
        # 3. 语义相似度检索
        semantic_matches = self.semantic_similarity_matching(query_text)
        results.extend(semantic_matches)
        
        # 4. 错误类型推断检索
        type_matches = self.error_type_inference_matching(query_text)
        results.extend(type_matches)
        
        # 去重和重排
        unique_results = self.deduplicate_and_rerank(results, query_text)
        
        print(f"   检索结果: {len(unique_results)} 条")
        return unique_results[:top_k]
    
    def exact_pattern_matching(self, query_text: str) -> List[RetrievalResult]:
        """精确模式匹配"""
        results = []
        
        # 检查是否有完全匹配的错误模式
        if query_text in self.indexed_knowledge['error_patterns']:
            item = self.indexed_knowledge['error_patterns'][query_text]
            results.append(RetrievalResult(
                knowledge_item=item['data'],
                relevance_score=1.0,
                match_type="exact_pattern",
                matched_keywords=[query_text]
            ))
        
        # 检查正则模式匹配
        for pattern_info in self.indexed_knowledge['patterns']:
            pattern = pattern_info['data']
            try:
                if re.search(pattern['pattern'], query_text):
                    results.append(RetrievalResult(
                        knowledge_item=pattern,
                        relevance_score=0.9,
                        match_type="regex_pattern",
                        matched_keywords=[pattern['description']]
                    ))
            except re.error:
                continue
        
        return results
    
    def keyword_matching(self, query_keywords: List[str]) -> List[RetrievalResult]:
        """关键词匹配"""
        results = []
        keyword_scores = defaultdict(float)
        keyword_items = defaultdict(list)
        
        for keyword in query_keywords:
            if keyword in self.indexed_knowledge['keywords']:
                for item in self.indexed_knowledge['keywords'][keyword]:
                    key = f"{item['category']}_{item['index']}"
                    keyword_scores[key] += item['relevance']
                    keyword_items[key].append((keyword, item))
        
        # 转换为RetrievalResult
        for key, score in keyword_scores.items():
            if score > 0.3:  # 阈值过滤
                items = keyword_items[key]
                representative_item = items[0][1]  # 取第一个作为代表
                matched_keywords = [item[0] for item in items]
                
                results.append(RetrievalResult(
                    knowledge_item=representative_item['data'],
                    relevance_score=min(score, 1.0),
                    match_type="keyword_match",
                    matched_keywords=matched_keywords
                ))
        
        return results
    
    def semantic_similarity_matching(self, query_text: str) -> List[RetrievalResult]:
        """语义相似度匹配（简化版）"""
        results = []
        
        # 简单的语义匹配规则
        semantic_rules = {
            '的': ['语气词', '修饰', '定语'],
            '得': ['补语', '程度', '结果'],
            '地': ['副词', '方式', '状语'],
            '标点': ['符号', '句号', '问号', '引号'],
            '量词': ['个', '本', '头', '只'],
            '重复': ['冗余', '多余', '重复']
        }
        
        for char in query_text:
            if char in semantic_rules:
                related_concepts = semantic_rules[char]
                for concept in related_concepts:
                    # 在错误类型中查找
                    for error_type, items in self.indexed_knowledge['error_types'].items():
                        if concept in error_type or char in error_type:
                            for item in items:
                                results.append(RetrievalResult(
                                    knowledge_item=item['data'],
                                    relevance_score=0.7,
                                    match_type="semantic_similarity",
                                    matched_keywords=[concept]
                                ))
        
        return results
    
    def error_type_inference_matching(self, query_text: str) -> List[RetrievalResult]:
        """错误类型推断匹配"""
        results = []
        
        # 错误类型推断规则
        inference_rules = [
            (r'.*的$', '语气词误用'),
            (r'.*的(很|非常|特别)', '得字句错误'),
            (r'(认真|仔细|努力)的\w+', '的地得混用'),
            (r'(大约|约).*?(左右|上下)', '重复表达'),
            (r'[，。？！]', '标点错误'),
            (r'(一|二|三|四|五|六|七|八|九|十|\d+)(个|头|只|本)', '量词错误')
        ]
        
        for pattern, error_type in inference_rules:
            try:
                if re.search(pattern, query_text):
                    if error_type in self.indexed_knowledge['error_types']:
                        for item in self.indexed_knowledge['error_types'][error_type]:
                            results.append(RetrievalResult(
                                knowledge_item=item['data'],
                                relevance_score=0.8,
                                match_type="type_inference",
                                matched_keywords=[error_type]
                            ))
            except re.error:
                continue
        
        return results
    
    def deduplicate_and_rerank(self, results: List[RetrievalResult], query_text: str) -> List[RetrievalResult]:
        """去重和重排"""
        # 去重（基于知识项内容）
        seen_items = set()
        unique_results = []
        
        for result in results:
            # 创建唯一标识
            if 'error' in result.knowledge_item:
                item_id = result.knowledge_item['error']
            elif 'rule' in result.knowledge_item:
                item_id = result.knowledge_item['rule']
            elif 'description' in result.knowledge_item:
                item_id = result.knowledge_item['description']
            else:
                item_id = str(result.knowledge_item)
            
            if item_id not in seen_items:
                seen_items.add(item_id)
                unique_results.append(result)
        
        # 重排序
        def rerank_score(result: RetrievalResult) -> float:
            base_score = result.relevance_score
            
            # 匹配类型权重
            type_weights = {
                "exact_pattern": 1.0,
                "regex_pattern": 0.9,
                "type_inference": 0.8,
                "semantic_similarity": 0.7,
                "keyword_match": 0.6
            }
            
            type_weight = type_weights.get(result.match_type, 0.5)
            
            # 关键词匹配数量加权
            keyword_bonus = min(len(result.matched_keywords) * 0.1, 0.3)
            
            # 查询文本长度相似度加权
            if 'error' in result.knowledge_item:
                length_similarity = 1.0 - abs(len(query_text) - len(result.knowledge_item['error'])) / max(len(query_text), len(result.knowledge_item['error']))
                length_bonus = length_similarity * 0.2
            else:
                length_bonus = 0
            
            final_score = base_score * type_weight + keyword_bonus + length_bonus
            return final_score
        
        # 按重排分数排序
        unique_results.sort(key=rerank_score, reverse=True)
        
        return unique_results
    
    def generate_correction_prompt(self, query_text: str, retrieved_knowledge: List[RetrievalResult]) -> str:
        """生成纠错提示词"""
        print(f"📝 生成纠错提示词")
        
        # 构建提示词模板
        prompt_template = """你是一个专业的中文文本校对专家。请根据以下知识库信息，对给定的中文文本进行错误检测和纠正。

## 待纠正文本
{query_text}

## 相关知识库信息
{knowledge_context}

## 纠错要求
1. 仔细分析文本中可能存在的语法错误、标点错误、用词错误等
2. 参考知识库中的错误示例和语法规则
3. 如果发现错误，请提供纠正后的文本
4. 详细说明纠错理由和依据的语法规则
5. 给出纠错的置信度评分（0-1之间）

## 输出格式
请按以下JSON格式输出结果：
```json
{{
    "has_error": true/false,
    "original_text": "原始文本",
    "corrected_text": "纠正后的文本",
    "error_type": "错误类型",
    "correction_reasoning": "详细的纠错理由",
    "confidence": 0.95,
    "applied_rules": ["应用的语法规则1", "应用的语法规则2"]
}}
```"""

        # 构建知识上下文
        knowledge_context = self.build_knowledge_context(retrieved_knowledge)
        
        # 填充模板
        prompt = prompt_template.format(
            query_text=query_text,
            knowledge_context=knowledge_context
        )
        
        return prompt
    
    def build_knowledge_context(self, retrieved_knowledge: List[RetrievalResult]) -> str:
        """构建知识上下文"""
        context_parts = []
        
        for i, result in enumerate(retrieved_knowledge, 1):
            knowledge_item = result.knowledge_item
            
            if 'error' in knowledge_item:
                # 错误示例
                context_part = f"""
### 知识条目 {i} (相关度: {result.relevance_score:.2f})
**错误类型**: {knowledge_item.get('type', '未知')}
**错误示例**: {knowledge_item['error']}
**正确形式**: {knowledge_item['correct']}
**解释说明**: {knowledge_item.get('explanation', '')}
**语法规则**: {knowledge_item.get('rule', '')}
**匹配方式**: {result.match_type}
**匹配关键词**: {', '.join(result.matched_keywords)}
"""
            elif 'rule' in knowledge_item:
                # 语法规则
                context_part = f"""
### 知识条目 {i} (相关度: {result.relevance_score:.2f})
**语法规则**: {knowledge_item['rule']}
**匹配方式**: {result.match_type}
**匹配关键词**: {', '.join(result.matched_keywords)}
"""
            elif 'description' in knowledge_item:
                # 纠错模式
                context_part = f"""
### 知识条目 {i} (相关度: {result.relevance_score:.2f})
**纠错模式**: {knowledge_item['description']}
**匹配模式**: {knowledge_item.get('pattern', '')}
**纠错动作**: {knowledge_item.get('action', '')}
**示例**: {', '.join(knowledge_item.get('examples', []))}
**匹配方式**: {result.match_type}
"""
            else:
                # 其他知识
                context_part = f"""
### 知识条目 {i} (相关度: {result.relevance_score:.2f})
**内容**: {str(knowledge_item)}
**匹配方式**: {result.match_type}
**匹配关键词**: {', '.join(result.matched_keywords)}
"""
            
            context_parts.append(context_part.strip())
        
        return '\n\n'.join(context_parts)

def demonstrate_usage():
    """演示使用过程"""
    print("🎯 RAG知识库使用演示")
    print("=" * 50)
    
    # 初始化处理器
    kb_path = "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    processor = ChineseKnowledgeBaseProcessor(kb_path)
    
    # 测试用例
    test_cases = [
        "我很喜欢这本书的",
        "他跑的很快",
        "认真的学习中文",
        "大约三点钟左右"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试用例 {i}: {test_case}")
        print("-" * 30)
        
        # 1. 检索相关知识
        retrieved_knowledge = processor.retrieve_relevant_knowledge(test_case, top_k=3)
        
        print(f"🔍 检索到 {len(retrieved_knowledge)} 条相关知识:")
        for j, result in enumerate(retrieved_knowledge, 1):
            print(f"   {j}. {result.match_type} (相关度: {result.relevance_score:.2f})")
            if 'error' in result.knowledge_item:
                print(f"      错误: {result.knowledge_item['error']}")
                print(f"      纠正: {result.knowledge_item['correct']}")
        
        # 2. 生成提示词
        prompt = processor.generate_correction_prompt(test_case, retrieved_knowledge)
        
        print(f"\n📋 生成的提示词长度: {len(prompt)} 字符")
        print(f"   包含知识条目: {len(retrieved_knowledge)} 个")
        
        # 显示提示词片段
        prompt_preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
        print(f"   提示词预览: {prompt_preview}")

if __name__ == "__main__":
    demonstrate_usage()
