#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU优化实际使用示例
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from ChineseErrorCorrector.utils import (
    GPUMemoryMonitor, 
    OptimizationPipeline,
    ModelOptimizer
)
from ChineseErrorCorrector.config import TextCorrectConfig


def example_memory_monitoring():
    """示例1: GPU内存监控"""
    print("=== GPU内存监控示例 ===")
    
    # 获取当前GPU内存信息
    memory_info = GPUMemoryMonitor.get_gpu_memory_info()
    print(f"GPU内存信息:")
    for key, value in memory_info.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.2f}")
        else:
            print(f"  {key}: {value}")
    
    # 清理缓存
    print("\n清理GPU缓存...")
    GPUMemoryMonitor.clear_cache()
    
    memory_info_after = GPUMemoryMonitor.get_gpu_memory_info()
    print(f"清理后GPU内存: {memory_info_after.get('allocated_gb', 0):.2f} GB")


def example_optimization_config():
    """示例2: 根据GPU内存获取优化配置"""
    print("\n=== 优化配置示例 ===")
    
    # 模拟不同内存大小的GPU
    memory_scenarios = [6, 12, 24]
    
    for memory_gb in memory_scenarios:
        print(f"\n{memory_gb}GB GPU的推荐配置:")
        
        # 创建模拟模型
        class DummyModel:
            pass
        
        optimizer = ModelOptimizer(DummyModel(), target_memory_gb=memory_gb)
        config = optimizer.get_optimization_config()
        
        for key, value in config.items():
            print(f"  {key}: {value}")


def example_model_optimization():
    """示例3: 实际模型优化"""
    print("\n=== 模型优化示例 ===")
    
    try:
        # 加载模型（使用CPU避免GPU内存问题）
        model_path = TextCorrectConfig.DEFAULT_CKPT_PATH
        print(f"加载模型: {model_path}")
        
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="cpu"  # 先加载到CPU
        )
        
        print(f"模型加载成功，参数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 创建优化流水线
        pipeline = OptimizationPipeline(model, target_memory_gb=8.0)
        
        # 应用优化
        print("\n应用优化...")
        optimization_results = pipeline.apply_optimizations()
        
        print("优化结果:")
        for key, value in optimization_results.items():
            print(f"  {key}: {value}")
        
        # 测试数据
        test_inputs = [
            "这个句子有一些语法错误需要修正。",
            "我们应该要好好学习中文语法。",
            "这是一个测试句子，看看模型能否正确处理。"
        ]
        
        # 性能基准测试
        print("\n进行性能基准测试...")
        benchmark_results = pipeline.benchmark_performance(test_inputs)
        
        print("基准测试结果:")
        for key, value in benchmark_results.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for k, v in value.items():
                    if isinstance(v, float):
                        print(f"    {k}: {v:.2f}")
                    else:
                        print(f"    {k}: {v}")
            elif isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"模型优化示例失败: {e}")
        print("这可能是因为模型文件不存在或GPU内存不足")


def example_dynamic_batch_processing():
    """示例4: 动态批处理"""
    print("\n=== 动态批处理示例 ===")
    
    from ChineseErrorCorrector.utils import DynamicBatchProcessor
    
    # 创建模拟模型
    class MockModel:
        def process(self, texts):
            return [f"corrected_{text}" for text in texts]
    
    model = MockModel()
    processor = DynamicBatchProcessor(model, max_memory_gb=6.0)
    
    # 测试不同长度的输入
    test_cases = [
        ["短文本"] * 10,
        ["这是一个中等长度的测试文本，用来测试批处理的效果"] * 5,
        ["这是一个非常长的测试文本，包含了很多字符，用来模拟长文档的处理情况，看看动态批处理器如何调整批大小来适应不同的内存限制"] * 2
    ]
    
    for i, test_inputs in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: {len(test_inputs)} 个输入，平均长度 {len(test_inputs[0])}")
        
        # 获取推荐的批大小
        optimal_batch_size = processor.get_optimal_batch_size(len(test_inputs[0]))
        print(f"推荐批大小: {optimal_batch_size}")
        
        # 处理批次
        results = processor.process_batch(test_inputs.copy())
        print(f"处理完成，输出数量: {len(results)}")


def example_memory_optimization_strategies():
    """示例5: 不同内存优化策略对比"""
    print("\n=== 内存优化策略对比 ===")
    
    strategies = {
        "无优化": {
            "quantization": None,
            "gradient_checkpointing": False,
            "batch_size": 4
        },
        "基础优化": {
            "quantization": "fp16",
            "gradient_checkpointing": True,
            "batch_size": 2
        },
        "激进优化": {
            "quantization": "int8",
            "gradient_checkpointing": True,
            "batch_size": 1,
            "cpu_offload": True
        }
    }
    
    print("不同优化策略的预期效果:")
    print(f"{'策略':<12} {'内存节省':<10} {'速度影响':<10} {'质量影响':<10}")
    print("-" * 50)
    
    effects = {
        "无优化": ("0%", "100%", "100%"),
        "基础优化": ("40-50%", "90-95%", "98-99%"),
        "激进优化": ("60-70%", "70-80%", "95-98%")
    }
    
    for strategy, effect in effects.items():
        memory_save, speed, quality = effect
        print(f"{strategy:<12} {memory_save:<10} {speed:<10} {quality:<10}")


def main():
    """主函数"""
    print("🚀 GPU优化工具包使用示例")
    print("=" * 60)
    
    # 运行所有示例
    example_memory_monitoring()
    example_optimization_config()
    example_dynamic_batch_processing()
    example_memory_optimization_strategies()
    
    # 最后运行模型优化示例（可能需要较长时间）
    print("\n" + "="*60)
    print("注意: 以下示例需要加载实际模型，可能需要较长时间...")
    user_input = input("是否继续运行模型优化示例? (y/n): ")
    
    if user_input.lower() == 'y':
        example_model_optimization()
    else:
        print("跳过模型优化示例")
    
    print("\n" + "="*60)
    print("🎉 GPU优化示例演示完成！")
    
    print("\n💡 优化建议总结:")
    print("1. 优先优化MLP层 - 占66.9%参数，效果最显著")
    print("2. 根据GPU内存选择合适的优化策略")
    print("3. 使用动态批处理适应内存限制")
    print("4. 监控内存使用，及时清理缓存")
    print("5. 在效果和效率间找到平衡点")


if __name__ == '__main__':
    main()
