#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chinese Syntax Tree Generator
中文语法树生成器

This module provides functionality to generate and visualize syntax trees for Chinese sentences.
支持多种中文句法分析工具，生成可视化的语法树结构。

Author: ChineseErrorCorrector Team
Date: 2025-01-01
"""

import os
import sys
import json
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')

try:
    import jieba
    import jieba.posseg as pseg
except ImportError:
    print("Warning: jieba not installed. Please install with: pip install jieba")
    jieba = None

try:
    import spacy
    # Try to load Chinese model
    try:
        nlp_spacy = spacy.load("zh_core_web_sm")
    except OSError:
        print("Warning: spaCy Chinese model not found. Please install with: python -m spacy download zh_core_web_sm")
        nlp_spacy = None
except ImportError:
    print("Warning: spaCy not installed. Please install with: pip install spacy")
    spacy = None
    nlp_spacy = None

try:
    from LAC import LAC
    lac = LAC(mode='lac')
except ImportError:
    print("Warning: LAC not installed. Please install with: pip install lac")
    lac = None


@dataclass
class TreeNode:
    """语法树节点"""
    text: str
    pos: str
    children: List['TreeNode']
    parent: Optional['TreeNode'] = None
    depth: int = 0
    semantic_role: Optional[str] = None  # 语义角色
    is_implicit: bool = False  # 是否为隐式节点
    confidence: float = 1.0  # 置信度

    def __post_init__(self):
        for child in self.children:
            child.parent = self
            child.depth = self.depth + 1


@dataclass
class ImplicitNode:
    """隐式节点，表示省略或隐含的语义成分"""
    semantic_type: str  # 语义类型：主语、宾语、状语等
    inferred_content: str  # 推断的内容
    confidence: float  # 推断置信度
    position: int  # 在句子中的位置
    evidence: List[str]  # 推断依据


class ChineseSyntaxTreeGenerator:
    """中文语法树生成器"""

    def __init__(self, method: str = "jieba", enable_implicit: bool = True):
        """
        初始化语法树生成器

        Args:
            method: 使用的分析方法 ("jieba", "spacy", "lac", "rule_based")
            enable_implicit: 是否启用隐式解析树功能
        """
        self.method = method
        self.enable_implicit = enable_implicit
        self.available_methods = self._check_available_methods()

        # 隐式成分推断规则
        self.implicit_rules = self._load_implicit_rules()

        if method not in self.available_methods:
            print(f"Warning: Method '{method}' not available. Using 'rule_based' instead.")
            self.method = "rule_based"
    
    def _check_available_methods(self) -> List[str]:
        """检查可用的分析方法"""
        methods = ["rule_based"]  # 基于规则的方法总是可用

        if jieba is not None:
            methods.append("jieba")
        if nlp_spacy is not None:
            methods.append("spacy")
        if lac is not None:
            methods.append("lac")

        return methods

    def _load_implicit_rules(self) -> Dict[str, Any]:
        """加载隐式成分推断规则"""
        return {
            # 主语省略规则
            'subject_ellipsis': {
                'patterns': [
                    r'^(去|来|走|跑|吃|喝|看|听|说|做)',  # 动词开头，可能省略主语
                    r'^(很|非常|特别|十分)\s*(好|美|高|大)',  # 形容词开头，可能省略主语
                ],
                'default_subject': '(我)',
                'confidence': 0.7
            },

            # 宾语省略规则
            'object_ellipsis': {
                'patterns': [
                    r'(看|听|吃|喝|买|卖|学|教)$',  # 及物动词结尾，可能省略宾语
                    r'(给|送|借|还)\s*$',  # 双宾动词，可能省略宾语
                ],
                'default_object': '(它)',
                'confidence': 0.6
            },

            # 时间状语推断
            'temporal_inference': {
                'patterns': [
                    r'(了|过)$',  # 完成体，暗示过去时间
                    r'(正在|在)\s*',  # 进行体，暗示现在时间
                    r'(会|将|要)\s*',  # 将来时标记
                ],
                'temporal_markers': {
                    '了': '(过去)',
                    '过': '(过去)',
                    '正在': '(现在)',
                    '在': '(现在)',
                    '会': '(将来)',
                    '将': '(将来)',
                    '要': '(将来)'
                },
                'confidence': 0.8
            },

            # 地点状语推断
            'location_inference': {
                'patterns': [
                    r'(在|到|从|向|往)\s*([^在到从向往]*?)\s*(去|来|走)',
                    r'(上|下|里|外|前|后|左|右)\s*(去|来|走)',
                ],
                'confidence': 0.7
            },

            # 方式状语推断
            'manner_inference': {
                'patterns': [
                    r'(快|慢|轻|重|大声|小声)\s*(地|的)\s*',
                    r'(认真|仔细|努力|用心)\s*(地|的)\s*',
                ],
                'confidence': 0.8
            }
        }

    def generate_implicit_tree(self, sentence: str) -> Tuple[TreeNode, List[ImplicitNode]]:
        """
        生成隐式解析树

        Args:
            sentence: 输入句子

        Returns:
            Tuple[TreeNode, List[ImplicitNode]]: 增强的语法树和隐式节点列表
        """
        # 首先生成基础语法树
        base_tree = self.generate_tree(sentence)

        # 分析隐式成分
        implicit_nodes = self._analyze_implicit_components(sentence, base_tree)

        # 将隐式成分整合到语法树中
        enhanced_tree = self._integrate_implicit_nodes(base_tree, implicit_nodes)

        return enhanced_tree, implicit_nodes

    def _analyze_implicit_components(self, sentence: str, tree: TreeNode) -> List[ImplicitNode]:
        """分析句子中的隐式成分"""
        implicit_nodes = []

        # 分析主语省略
        subject_ellipsis = self._detect_subject_ellipsis(sentence)
        if subject_ellipsis:
            implicit_nodes.append(subject_ellipsis)

        # 分析宾语省略
        object_ellipsis = self._detect_object_ellipsis(sentence)
        if object_ellipsis:
            implicit_nodes.append(object_ellipsis)

        # 分析时间状语
        temporal_adverbials = self._infer_temporal_adverbials(sentence)
        implicit_nodes.extend(temporal_adverbials)

        # 分析地点状语
        location_adverbials = self._infer_location_adverbials(sentence)
        implicit_nodes.extend(location_adverbials)

        # 分析方式状语
        manner_adverbials = self._infer_manner_adverbials(sentence)
        implicit_nodes.extend(manner_adverbials)

        return implicit_nodes

    def _detect_subject_ellipsis(self, sentence: str) -> Optional[ImplicitNode]:
        """检测主语省略"""
        rules = self.implicit_rules['subject_ellipsis']

        for pattern in rules['patterns']:
            if re.match(pattern, sentence):
                return ImplicitNode(
                    semantic_type="主语",
                    inferred_content=rules['default_subject'],
                    confidence=rules['confidence'],
                    position=0,
                    evidence=[f"匹配模式: {pattern}"]
                )
        return None

    def _detect_object_ellipsis(self, sentence: str) -> Optional[ImplicitNode]:
        """检测宾语省略"""
        rules = self.implicit_rules['object_ellipsis']

        for pattern in rules['patterns']:
            if re.search(pattern, sentence):
                return ImplicitNode(
                    semantic_type="宾语",
                    inferred_content=rules['default_object'],
                    confidence=rules['confidence'],
                    position=len(sentence),
                    evidence=[f"匹配模式: {pattern}"]
                )
        return None

    def _infer_temporal_adverbials(self, sentence: str) -> List[ImplicitNode]:
        """推断时间状语"""
        temporal_nodes = []
        rules = self.implicit_rules['temporal_inference']

        for pattern in rules['patterns']:
            matches = re.finditer(pattern, sentence)
            for match in matches:
                marker = match.group()
                if marker in rules['temporal_markers']:
                    temporal_content = rules['temporal_markers'][marker]
                    temporal_nodes.append(ImplicitNode(
                        semantic_type="时间状语",
                        inferred_content=temporal_content,
                        confidence=rules['confidence'],
                        position=match.start(),
                        evidence=[f"时间标记: {marker}"]
                    ))

        return temporal_nodes

    def _infer_location_adverbials(self, sentence: str) -> List[ImplicitNode]:
        """推断地点状语"""
        location_nodes = []
        rules = self.implicit_rules['location_inference']

        for pattern in rules['patterns']:
            matches = re.finditer(pattern, sentence)
            for match in matches:
                location_nodes.append(ImplicitNode(
                    semantic_type="地点状语",
                    inferred_content="(某地)",
                    confidence=rules['confidence'],
                    position=match.start(),
                    evidence=[f"地点模式: {match.group()}"]
                ))

        return location_nodes

    def _infer_manner_adverbials(self, sentence: str) -> List[ImplicitNode]:
        """推断方式状语"""
        manner_nodes = []
        rules = self.implicit_rules['manner_inference']

        for pattern in rules['patterns']:
            matches = re.finditer(pattern, sentence)
            for match in matches:
                manner_nodes.append(ImplicitNode(
                    semantic_type="方式状语",
                    inferred_content="(以某种方式)",
                    confidence=rules['confidence'],
                    position=match.start(),
                    evidence=[f"方式模式: {match.group()}"]
                ))

        return manner_nodes

    def _integrate_implicit_nodes(self, tree: TreeNode, implicit_nodes: List[ImplicitNode]) -> TreeNode:
        """将隐式节点整合到语法树中"""
        # 创建增强的树副本
        enhanced_tree = self._copy_tree(tree)

        # 按位置排序隐式节点
        implicit_nodes.sort(key=lambda x: x.position)

        # 将隐式节点添加到适当位置
        for implicit_node in implicit_nodes:
            implicit_tree_node = TreeNode(
                text=implicit_node.inferred_content,
                pos=implicit_node.semantic_type,
                children=[],
                semantic_role=implicit_node.semantic_type,
                is_implicit=True,
                confidence=implicit_node.confidence
            )

            # 根据语义类型决定插入位置
            if implicit_node.semantic_type == "主语":
                enhanced_tree.children.insert(0, implicit_tree_node)
            elif implicit_node.semantic_type == "宾语":
                enhanced_tree.children.append(implicit_tree_node)
            else:  # 状语类
                # 插入到适当的位置（通常在动词前）
                self._insert_adverbial(enhanced_tree, implicit_tree_node)

        return enhanced_tree

    def _copy_tree(self, node: TreeNode) -> TreeNode:
        """深度复制语法树"""
        copied_children = [self._copy_tree(child) for child in node.children]
        return TreeNode(
            text=node.text,
            pos=node.pos,
            children=copied_children,
            semantic_role=node.semantic_role,
            is_implicit=node.is_implicit,
            confidence=node.confidence
        )

    def _insert_adverbial(self, tree: TreeNode, adverbial_node: TreeNode):
        """在适当位置插入状语节点"""
        # 简单策略：插入到第一个动词短语前
        for i, child in enumerate(tree.children):
            if child.pos in ['VP', 'v', 'vd', 'vn']:
                tree.children.insert(i, adverbial_node)
                return

        # 如果没找到动词，插入到开头
        tree.children.insert(0, adverbial_node)

    def generate_tree(self, sentence: str) -> TreeNode:
        """
        生成语法树
        
        Args:
            sentence: 输入的中文句子
            
        Returns:
            TreeNode: 语法树的根节点
        """
        sentence = sentence.strip()
        
        if self.method == "jieba":
            return self._generate_tree_jieba(sentence)
        elif self.method == "spacy":
            return self._generate_tree_spacy(sentence)
        elif self.method == "lac":
            return self._generate_tree_lac(sentence)
        else:
            return self._generate_tree_rule_based(sentence)
    
    def _generate_tree_jieba(self, sentence: str) -> TreeNode:
        """使用jieba生成语法树"""
        words = list(pseg.cut(sentence))
        
        # 创建根节点
        root = TreeNode("S", "S", [])
        
        # 简单的语法规则：主语-谓语-宾语结构
        current_phrase = None
        phrase_type = None
        
        for word, pos in words:
            word_node = TreeNode(word, pos, [])
            
            # 根据词性判断短语类型
            if pos in ['n', 'nr', 'ns', 'nt', 'nz']:  # 名词类
                if phrase_type != "NP":
                    current_phrase = TreeNode("NP", "NP", [])
                    root.children.append(current_phrase)
                    phrase_type = "NP"
                current_phrase.children.append(word_node)
                
            elif pos in ['v', 'vd', 'vn', 'vshi', 'vyou']:  # 动词类
                if phrase_type != "VP":
                    current_phrase = TreeNode("VP", "VP", [])
                    root.children.append(current_phrase)
                    phrase_type = "VP"
                current_phrase.children.append(word_node)
                
            elif pos in ['a', 'ad', 'an', 'ag']:  # 形容词类
                if phrase_type != "AP":
                    current_phrase = TreeNode("AP", "AP", [])
                    root.children.append(current_phrase)
                    phrase_type = "AP"
                current_phrase.children.append(word_node)
                
            else:  # 其他词类
                root.children.append(word_node)
        
        return root
    
    def _generate_tree_spacy(self, sentence: str) -> TreeNode:
        """使用spaCy生成语法树"""
        doc = nlp_spacy(sentence)
        
        # 找到根节点
        root_token = None
        for token in doc:
            if token.head == token:
                root_token = token
                break
        
        if root_token is None:
            root_token = doc[0]
        
        def build_subtree(token) -> TreeNode:
            children = []
            for child in token.children:
                children.append(build_subtree(child))
            
            return TreeNode(token.text, token.pos_, children)
        
        return build_subtree(root_token)
    
    def _generate_tree_lac(self, sentence: str) -> TreeNode:
        """使用LAC生成语法树"""
        result = lac.run(sentence)
        words, pos_tags = result
        
        # 创建根节点
        root = TreeNode("S", "S", [])
        
        # 简单的短语组合
        i = 0
        while i < len(words):
            word = words[i]
            pos = pos_tags[i]
            
            if pos in ['n', 'PER', 'LOC', 'ORG']:  # 名词短语
                np = TreeNode("NP", "NP", [])
                np.children.append(TreeNode(word, pos, []))
                
                # 查看后续是否有修饰词
                j = i + 1
                while j < len(words) and pos_tags[j] in ['n', 'a']:
                    np.children.append(TreeNode(words[j], pos_tags[j], []))
                    j += 1
                
                root.children.append(np)
                i = j
                
            elif pos in ['v']:  # 动词短语
                vp = TreeNode("VP", "VP", [])
                vp.children.append(TreeNode(word, pos, []))
                root.children.append(vp)
                i += 1
                
            else:
                root.children.append(TreeNode(word, pos, []))
                i += 1
        
        return root
    
    def _generate_tree_rule_based(self, sentence: str) -> TreeNode:
        """基于规则的简单语法树生成"""
        # 简单的字符级分析
        root = TreeNode("S", "S", [])
        
        # 按标点符号分割
        clauses = re.split(r'[，。！？；]', sentence)
        
        for clause in clauses:
            clause = clause.strip()
            if not clause:
                continue
                
            clause_node = TreeNode("CLAUSE", "CLAUSE", [])
            
            # 简单的字符分组
            for char in clause:
                if char.strip():
                    char_node = TreeNode(char, "CHAR", [])
                    clause_node.children.append(char_node)
            
            if clause_node.children:
                root.children.append(clause_node)
        
        return root
    
    def print_tree(self, node: TreeNode, prefix: str = "", is_last: bool = True, show_implicit: bool = False) -> str:
        """
        打印语法树

        Args:
            node: 树节点
            prefix: 前缀字符串
            is_last: 是否是最后一个子节点
            show_implicit: 是否显示隐式节点信息

        Returns:
            str: 格式化的树结构字符串
        """
        result = []

        # 当前节点
        connector = "└── " if is_last else "├── "

        # 构建节点显示文本
        node_text = f"{node.text} ({node.pos})"

        if show_implicit and hasattr(node, 'is_implicit') and node.is_implicit:
            node_text += f" [隐式, 置信度: {node.confidence:.2f}]"
        elif show_implicit and hasattr(node, 'semantic_role') and node.semantic_role:
            node_text += f" [语义角色: {node.semantic_role}]"

        result.append(f"{prefix}{connector}{node_text}")

        # 子节点
        if node.children:
            extension = "    " if is_last else "│   "
            new_prefix = prefix + extension

            for i, child in enumerate(node.children):
                is_child_last = (i == len(node.children) - 1)
                result.append(self.print_tree(child, new_prefix, is_child_last, show_implicit))

        return "\n".join(result)

    def print_implicit_analysis(self, implicit_nodes: List[ImplicitNode]) -> str:
        """
        打印隐式成分分析结果

        Args:
            implicit_nodes: 隐式节点列表

        Returns:
            str: 格式化的隐式分析结果
        """
        if not implicit_nodes:
            return "未发现隐式成分"

        result = []
        result.append("隐式成分分析:")
        result.append("-" * 40)

        for i, node in enumerate(implicit_nodes, 1):
            result.append(f"{i}. {node.semantic_type}: {node.inferred_content}")
            result.append(f"   位置: {node.position}")
            result.append(f"   置信度: {node.confidence:.2f}")
            result.append(f"   推断依据: {', '.join(node.evidence)}")
            result.append("")

        return "\n".join(result)
    
    def tree_to_dict(self, node: TreeNode) -> Dict[str, Any]:
        """
        将语法树转换为字典格式
        
        Args:
            node: 树节点
            
        Returns:
            Dict: 树的字典表示
        """
        return {
            "text": node.text,
            "pos": node.pos,
            "depth": node.depth,
            "children": [self.tree_to_dict(child) for child in node.children]
        }
    
    def save_tree(self, tree: TreeNode, output_path: str, format: str = "txt"):
        """
        保存语法树到文件

        Args:
            tree: 语法树根节点
            output_path: 输出文件路径
            format: 输出格式 ("txt", "json")
        """
        output_dir = os.path.dirname(output_path)
        if output_dir:  # 只有当目录不为空时才创建
            os.makedirs(output_dir, exist_ok=True)
        
        if format == "txt":
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(self.print_tree(tree))
        elif format == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.tree_to_dict(tree), f, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def analyze_sentence(self, sentence: str, verbose: bool = True, include_implicit: bool = None) -> Dict[str, Any]:
        """
        完整分析句子并返回结果

        Args:
            sentence: 输入句子
            verbose: 是否打印详细信息
            include_implicit: 是否包含隐式分析（None时使用实例设置）

        Returns:
            Dict: 分析结果
        """
        if include_implicit is None:
            include_implicit = self.enable_implicit

        if verbose:
            print(f"分析句子: {sentence}")
            print(f"使用方法: {self.method}")
            print(f"隐式分析: {'启用' if include_implicit else '禁用'}")
            print(f"可用方法: {', '.join(self.available_methods)}")
            print("-" * 50)

        # 生成基础语法树
        tree = self.generate_tree(sentence)
        tree_str = self.print_tree(tree)

        # 隐式分析
        implicit_tree = None
        implicit_nodes = []
        implicit_tree_str = ""
        implicit_analysis_str = ""

        if include_implicit:
            implicit_tree, implicit_nodes = self.generate_implicit_tree(sentence)
            implicit_tree_str = self.print_tree(implicit_tree, show_implicit=True)
            implicit_analysis_str = self.print_implicit_analysis(implicit_nodes)

        if verbose:
            print("基础语法树结构:")
            print(tree_str)

            if include_implicit:
                print(f"\n隐式解析树结构:")
                print(implicit_tree_str)
                print(f"\n{implicit_analysis_str}")

        # 统计信息
        stats = self._calculate_tree_stats(tree)
        implicit_stats = self._calculate_tree_stats(implicit_tree) if implicit_tree else None

        if verbose:
            print(f"\n基础树统计:")
            print(f"  节点总数: {stats['total_nodes']}")
            print(f"  最大深度: {stats['max_depth']}")
            print(f"  叶子节点数: {stats['leaf_nodes']}")

            if implicit_stats:
                print(f"\n隐式树统计:")
                print(f"  节点总数: {implicit_stats['total_nodes']}")
                print(f"  隐式节点数: {len(implicit_nodes)}")
                print(f"  最大深度: {implicit_stats['max_depth']}")

        result = {
            "sentence": sentence,
            "method": self.method,
            "tree": self.tree_to_dict(tree),
            "tree_string": tree_str,
            "statistics": stats,
            "implicit_enabled": include_implicit
        }

        if include_implicit:
            result.update({
                "implicit_tree": self.tree_to_dict(implicit_tree) if implicit_tree else None,
                "implicit_tree_string": implicit_tree_str,
                "implicit_nodes": [self._implicit_node_to_dict(node) for node in implicit_nodes],
                "implicit_analysis": implicit_analysis_str,
                "implicit_statistics": implicit_stats
            })

        return result

    def _implicit_node_to_dict(self, node: ImplicitNode) -> Dict[str, Any]:
        """将隐式节点转换为字典"""
        return {
            "semantic_type": node.semantic_type,
            "inferred_content": node.inferred_content,
            "confidence": node.confidence,
            "position": node.position,
            "evidence": node.evidence
        }
    
    def _calculate_tree_stats(self, node: TreeNode) -> Dict[str, int]:
        """计算树的统计信息"""
        def count_nodes(n):
            count = 1
            for child in n.children:
                count += count_nodes(child)
            return count
        
        def max_depth(n):
            if not n.children:
                return n.depth
            return max(max_depth(child) for child in n.children)
        
        def count_leaves(n):
            if not n.children:
                return 1
            return sum(count_leaves(child) for child in n.children)
        
        return {
            "total_nodes": count_nodes(node),
            "max_depth": max_depth(node),
            "leaf_nodes": count_leaves(node)
        }


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description="中文语法树生成器（支持隐式解析树）")
    parser.add_argument("sentence", help="要分析的中文句子")
    parser.add_argument("--method", choices=["jieba", "spacy", "lac", "rule_based"],
                       default="jieba", help="分析方法")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--format", choices=["txt", "json"], default="txt", help="输出格式")
    parser.add_argument("--quiet", action="store_true", help="静默模式")
    parser.add_argument("--implicit", action="store_true", help="启用隐式解析树分析")
    parser.add_argument("--no-implicit", action="store_true", help="禁用隐式解析树分析")

    args = parser.parse_args()

    # 确定是否启用隐式分析
    enable_implicit = True  # 默认启用
    if args.no_implicit:
        enable_implicit = False
    elif args.implicit:
        enable_implicit = True

    # 创建生成器
    generator = ChineseSyntaxTreeGenerator(method=args.method, enable_implicit=enable_implicit)

    # 分析句子
    result = generator.analyze_sentence(args.sentence, verbose=not args.quiet)

    # 保存结果
    if args.output:
        # 根据是否启用隐式分析选择保存的树
        if enable_implicit and 'implicit_tree' in result and result['implicit_tree']:
            tree = generator.generate_implicit_tree(args.sentence)[0]
        else:
            tree = generator.generate_tree(args.sentence)

        generator.save_tree(tree, args.output, args.format)
        if not args.quiet:
            print(f"\n结果已保存到: {args.output}")


if __name__ == "__main__":
    # 示例用法
    if len(sys.argv) == 1:
        print("中文语法树生成器示例（包含隐式解析树）")
        print("=" * 60)

        # 示例句子 - 特别选择包含隐式成分的句子
        test_sentences = [
            "去学校。",  # 省略主语
            "看书了。",  # 省略主语，包含时间标记
            "认真学习。",  # 省略主语，包含方式状语
            "给老师。",  # 省略主语和宾语
            "在图书馆学习。",  # 省略主语，包含地点状语
        ]

        print("\n🌳 基础语法树示例:")
        print("-" * 30)
        generator_basic = ChineseSyntaxTreeGenerator(enable_implicit=False)

        for sentence in test_sentences[:2]:
            print(f"\n📝 句子: {sentence}")
            result = generator_basic.analyze_sentence(sentence, verbose=False)
            print(result['tree_string'])

        print(f"\n{'='*60}")
        print("🔍 隐式解析树示例:")
        print("-" * 30)
        generator_implicit = ChineseSyntaxTreeGenerator(enable_implicit=True)

        for sentence in test_sentences:
            print(f"\n📝 句子: {sentence}")
            result = generator_implicit.analyze_sentence(sentence, verbose=False, include_implicit=True)

            print("基础树:")
            print(result['tree_string'])

            if result.get('implicit_nodes'):
                print("\n隐式增强树:")
                print(result['implicit_tree_string'])
                print(f"\n{result['implicit_analysis']}")
            else:
                print("\n未发现隐式成分")
            print("-" * 40)
    else:
        main()
