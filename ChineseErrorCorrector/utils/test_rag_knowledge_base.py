#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG知识库测试脚本
测试构建的中文校对RAG知识库的功能
"""

import os
import json
import asyncio
import re
from typing import List, Dict, Any

class RAGKnowledgeBaseTester:
    """RAG知识库测试器"""
    
    def __init__(self, kb_dir: str = "./complete_rag_knowledge_base"):
        self.kb_dir = kb_dir
        self.knowledge_base = {}
        self.rag_texts = []
        
        print(f"🧪 RAG知识库测试器初始化")
        print(f"   知识库目录: {kb_dir}")
    
    def load_knowledge_base(self) -> bool:
        """加载知识库"""
        kb_file = os.path.join(self.kb_dir, "merged_knowledge_base.json")
        
        if not os.path.exists(kb_file):
            print(f"❌ 知识库文件不存在: {kb_file}")
            return False
        
        try:
            with open(kb_file, 'r', encoding='utf-8') as f:
                self.knowledge_base = json.load(f)
            
            print(f"✅ 知识库加载成功")
            print(f"   专家错误示例: {len(self.knowledge_base.get('expert_grammar_errors', []))} 个")
            print(f"   专家语法规则: {len(self.knowledge_base.get('expert_grammar_rules', []))} 条")
            print(f"   专家纠错模式: {len(self.knowledge_base.get('expert_correction_patterns', []))} 个")
            
            return True
            
        except Exception as e:
            print(f"❌ 知识库加载失败: {e}")
            return False
    
    def load_rag_texts(self) -> bool:
        """加载RAG格式化文本"""
        rag_file = os.path.join(self.kb_dir, "rag_formatted_texts.txt")
        
        if not os.path.exists(rag_file):
            print(f"❌ RAG文本文件不存在: {rag_file}")
            return False
        
        try:
            with open(rag_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分割知识条目
            self.rag_texts = []
            sections = content.split("=== 知识条目")
            
            for section in sections[1:]:  # 跳过第一个空段
                lines = section.strip().split('\n')
                if len(lines) > 1:
                    text = '\n'.join(lines[1:]).strip()  # 跳过编号行
                    if text:
                        self.rag_texts.append(text)
            
            print(f"✅ RAG文本加载成功: {len(self.rag_texts)} 条")
            return True
            
        except Exception as e:
            print(f"❌ RAG文本加载失败: {e}")
            return False
    
    def simulate_rag_retrieval(self, query: str, top_k: int = 3) -> List[str]:
        """模拟RAG检索"""
        print(f"🔍 模拟RAG检索: {query}")
        
        # 简单的关键词匹配检索
        query_keywords = set(query.lower().split())
        scored_texts = []
        
        for text in self.rag_texts:
            text_lower = text.lower()
            
            # 计算匹配分数
            score = 0
            for keyword in query_keywords:
                if keyword in text_lower:
                    score += 1
            
            # 特殊关键词加权
            if '错误' in query and '错误' in text_lower:
                score += 2
            if '纠正' in query and '纠正' in text_lower:
                score += 2
            if '语法' in query and '语法' in text_lower:
                score += 2
            
            if score > 0:
                scored_texts.append((score, text))
        
        # 按分数排序并返回top_k
        scored_texts.sort(key=lambda x: x[0], reverse=True)
        retrieved_texts = [text for score, text in scored_texts[:top_k]]
        
        print(f"   检索到 {len(retrieved_texts)} 条相关知识")
        return retrieved_texts
    
    def simulate_correction_with_rag(self, text: str) -> Dict[str, Any]:
        """模拟基于RAG的纠错"""
        print(f"\n📝 模拟RAG增强纠错: {text}")
        
        # 构建检索查询
        query = f"纠正错误 {text}"
        
        # 检索相关知识
        retrieved_knowledge = self.simulate_rag_retrieval(query)
        
        # 基于规则的简单纠错
        corrected_text = text
        correction_reason = "未发现明显错误"
        confidence = 0.5
        
        # 检查常见错误模式
        if text.endswith('的') and not any(c in text for c in '？！'):
            corrected_text = text[:-1]
            correction_reason = "删除句末多余的语气词'的'"
            confidence = 0.9
        
        elif '的很' in text:
            corrected_text = text.replace('的很', '得很')
            correction_reason = "动词后的程度补语应该用'得'"
            confidence = 0.85
        
        elif re.search(r'(认真|仔细|努力)的\w+', text):
            corrected_text = re.sub(r'(认真|仔细|努力)的(\w+)', r'\1地\2', text)
            correction_reason = "副词修饰动词应该用'地'"
            confidence = 0.8
        
        elif '大约' in text and '左右' in text:
            corrected_text = text.replace('左右', '')
            correction_reason = "删除重复表达'左右'"
            confidence = 0.75
        
        # 如果有检索到的知识，提高置信度
        if retrieved_knowledge and corrected_text != text:
            confidence = min(confidence + 0.1, 1.0)
        
        return {
            "original_text": text,
            "corrected_text": corrected_text,
            "correction_reason": correction_reason,
            "confidence": confidence,
            "retrieved_knowledge": retrieved_knowledge[:2],  # 只显示前2条
            "has_correction": corrected_text != text
        }
    
    def test_error_detection_patterns(self):
        """测试错误检测模式"""
        print(f"\n🔧 测试错误检测模式")
        print("-" * 40)
        
        # 从知识库中获取纠错模式
        patterns = self.knowledge_base.get('expert_correction_patterns', [])
        
        test_cases = [
            "我很喜欢这本书的",
            "他跑的很快",
            "认真的学习",
            "大约三点钟左右"
        ]
        
        for pattern in patterns:
            print(f"\n📋 模式: {pattern['description']}")
            print(f"   正则: {pattern['pattern']}")
            print(f"   动作: {pattern['action']}")
            
            # 测试模式匹配
            for test_case in test_cases:
                try:
                    if re.search(pattern['pattern'], test_case):
                        print(f"   ✅ 匹配: {test_case}")
                    else:
                        print(f"   ❌ 不匹配: {test_case}")
                except re.error:
                    print(f"   ⚠️ 正则表达式错误: {pattern['pattern']}")
    
    def test_knowledge_coverage(self):
        """测试知识覆盖度"""
        print(f"\n📊 测试知识覆盖度")
        print("-" * 40)
        
        # 统计错误类型覆盖
        error_types = set()
        
        for error in self.knowledge_base.get('expert_grammar_errors', []):
            error_types.add(error['type'])
        
        for error in self.knowledge_base.get('expert_punctuation_errors', []):
            error_types.add(error['type'])
        
        print(f"📈 错误类型覆盖:")
        for error_type in sorted(error_types):
            print(f"   - {error_type}")
        
        # 统计语法规则覆盖
        rules = self.knowledge_base.get('expert_grammar_rules', [])
        print(f"\n📖 语法规则覆盖: {len(rules)} 条")
        
        rule_categories = {
            "基础语法": 0,
            "修饰关系": 0,
            "标点符号": 0,
            "语序规则": 0,
            "其他": 0
        }
        
        for rule in rules:
            if any(keyword in rule for keyword in ['主语', '谓语', '宾语', '句型']):
                rule_categories["基础语法"] += 1
            elif any(keyword in rule for keyword in ['修饰', '定语', '补语', '的', '地', '得']):
                rule_categories["修饰关系"] += 1
            elif any(keyword in rule for keyword in ['标点', '句号', '问号', '引号']):
                rule_categories["标点符号"] += 1
            elif any(keyword in rule for keyword in ['语序', '状语', '位置']):
                rule_categories["语序规则"] += 1
            else:
                rule_categories["其他"] += 1
        
        for category, count in rule_categories.items():
            print(f"   {category}: {count} 条")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print(f"\n🧪 运行综合测试")
        print("-" * 40)
        
        # 测试用例
        test_cases = [
            "我很喜欢这本书的",
            "他跑的很快的",
            "认真的学习中文",
            "大约三点钟左右",
            "这个菜做的很好吃",
            "学生们学习的很努力",
            "老师讲课讲的很清楚",
            "今天天气很好的",
            "苹果，香蕉，橘子",
            "一头牛奶"
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            result = self.simulate_correction_with_rag(test_case)
            results.append(result)
            
            print(f"   原文: {result['original_text']}")
            print(f"   纠错: {result['corrected_text']}")
            print(f"   理由: {result['correction_reason']}")
            print(f"   置信度: {result['confidence']:.2f}")
            
            if result['retrieved_knowledge']:
                print(f"   检索知识: {len(result['retrieved_knowledge'])} 条")
            
            print()
        
        # 统计测试结果
        corrected_count = sum(1 for r in results if r['has_correction'])
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        
        print(f"📊 测试统计:")
        print(f"   测试用例: {len(test_cases)} 个")
        print(f"   检测到错误: {corrected_count} 个")
        print(f"   检测率: {corrected_count/len(test_cases)*100:.1f}%")
        print(f"   平均置信度: {avg_confidence:.2f}")

async def main():
    """主测试流程"""
    print("🚀 RAG知识库测试流程")
    print("=" * 50)
    
    # 创建测试器
    tester = RAGKnowledgeBaseTester()
    
    # 加载知识库
    if not tester.load_knowledge_base():
        print("❌ 知识库加载失败，退出测试")
        return
    
    # 加载RAG文本
    if not tester.load_rag_texts():
        print("❌ RAG文本加载失败，退出测试")
        return
    
    # 运行各项测试
    tester.test_error_detection_patterns()
    tester.test_knowledge_coverage()
    tester.run_comprehensive_test()
    
    print(f"\n🎉 RAG知识库测试完成！")
    print(f"💡 要启用真实的RAG功能，请设置OPENAI_API_KEY并运行:")
    print(f"   export OPENAI_API_KEY='your-key'")
    print(f"   python lightrag_enhanced_corrector.py --mode demo")

if __name__ == "__main__":
    asyncio.run(main())
