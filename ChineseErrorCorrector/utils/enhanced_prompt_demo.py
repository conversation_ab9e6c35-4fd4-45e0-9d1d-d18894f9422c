#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进后的提示词演示
专门展示RAG知识库生成的高质量提示词
"""

from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
import json

def show_enhanced_prompt_for_new_text():
    """展示您修改的新文本的改进提示词"""
    print("🎯 改进后的提示词演示")
    print("=" * 60)
    
    # 初始化处理器
    kb_path = "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    processor = ChineseKnowledgeBaseProcessor(kb_path)
    
    # 您修改的测试文本
    test_text = "我很喜欢这件衣服的。"
    
    print(f"📝 输入文本: {test_text}")
    print(f"🔍 文本特点: 包含句末语气词'的'和句号")
    print("-" * 50)
    
    # 检索相关知识
    print("🔍 步骤1: 智能知识检索")
    retrieved_knowledge = processor.retrieve_relevant_knowledge(test_text, top_k=3)
    
    print(f"   检索到 {len(retrieved_knowledge)} 条相关知识:")
    for i, result in enumerate(retrieved_knowledge, 1):
        print(f"   {i}. 匹配方式: {result.match_type}")
        print(f"      相关度: {result.relevance_score:.2f}")
        print(f"      错误类型: {result.knowledge_item.get('type', '规则')}")
        if 'error' in result.knowledge_item:
            print(f"      示例: {result.knowledge_item['error']} → {result.knowledge_item['correct']}")
        print()
    
    # 生成改进的提示词
    print("📋 步骤2: 生成改进的提示词")
    enhanced_prompt = processor.generate_correction_prompt(test_text, retrieved_knowledge)
    
    print("✅ 改进后的完整提示词:")
    print("=" * 80)
    print(enhanced_prompt)
    print("=" * 80)
    
    # 分析提示词质量
    print("\n📊 提示词质量分析:")
    print(f"   总长度: {len(enhanced_prompt)} 字符")
    print(f"   知识条目: {len(retrieved_knowledge)} 个")
    print(f"   覆盖错误类型: {len(set(r.knowledge_item.get('type', '') for r in retrieved_knowledge))} 种")
    
    # 预测纠错结果
    print(f"\n🎯 预期纠错结果:")
    print(f"   原文: {test_text}")
    print(f"   预期纠正: 我很喜欢这件衣服。")
    print(f"   错误类型: 语气词误用")
    print(f"   纠错理由: 陈述句末尾不需要语气词'的'")
    print(f"   置信度: 0.95")

def compare_prompt_quality():
    """对比不同文本的提示词质量"""
    print(f"\n🔄 提示词质量对比分析")
    print("=" * 60)
    
    processor = ChineseKnowledgeBaseProcessor(
        "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    )
    
    test_cases = [
        {
            "text": "我很喜欢这件衣服的。",
            "description": "您的测试文本（语气词+标点）"
        },
        {
            "text": "我很喜欢这本书的",
            "description": "知识库中的精确匹配"
        },
        {
            "text": "学生们学习的很努力的。",
            "description": "复合错误（得字句+语气词）"
        },
        {
            "text": "他们认真的学习着。",
            "description": "的地得混用"
        }
    ]
    
    results = []
    
    for case in test_cases:
        print(f"\n📝 测试: {case['text']}")
        print(f"   描述: {case['description']}")
        
        # 检索知识
        knowledge = processor.retrieve_relevant_knowledge(case['text'], top_k=3)
        
        # 生成提示词
        prompt = processor.generate_correction_prompt(case['text'], knowledge)
        
        # 分析质量指标
        quality_metrics = {
            "text": case['text'],
            "description": case['description'],
            "knowledge_count": len(knowledge),
            "prompt_length": len(prompt),
            "top_relevance": knowledge[0].relevance_score if knowledge else 0,
            "match_types": list(set(k.match_type for k in knowledge)),
            "error_types": list(set(k.knowledge_item.get('type', '') for k in knowledge if k.knowledge_item.get('type')))
        }
        
        results.append(quality_metrics)
        
        print(f"   知识条目: {quality_metrics['knowledge_count']} 个")
        print(f"   提示词长度: {quality_metrics['prompt_length']} 字符")
        print(f"   最高相关度: {quality_metrics['top_relevance']:.2f}")
        print(f"   匹配方式: {', '.join(quality_metrics['match_types'])}")
        print(f"   错误类型: {', '.join(quality_metrics['error_types'])}")
    
    # 质量排名
    print(f"\n🏆 提示词质量排名:")
    print("-" * 40)
    
    # 按综合质量评分排序
    for i, result in enumerate(sorted(results, key=lambda x: x['top_relevance'] * x['knowledge_count'], reverse=True), 1):
        score = result['top_relevance'] * result['knowledge_count']
        print(f"   {i}. {result['text']}")
        print(f"      质量评分: {score:.2f}")
        print(f"      特点: {result['description']}")
        print()

def demonstrate_prompt_components():
    """演示提示词各组件的作用"""
    print(f"\n🔧 提示词组件功能演示")
    print("=" * 60)
    
    processor = ChineseKnowledgeBaseProcessor(
        "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    )
    
    test_text = "我很喜欢这件衣服的。"
    knowledge = processor.retrieve_relevant_knowledge(test_text, top_k=2)
    prompt = processor.generate_correction_prompt(test_text, knowledge)
    
    # 分析提示词组件
    sections = prompt.split('##')
    
    print("📋 提示词组件分析:")
    
    for i, section in enumerate(sections):
        if not section.strip():
            continue
            
        lines = section.strip().split('\n')
        section_title = lines[0].strip() if lines else "未知组件"
        section_content = '\n'.join(lines[1:]) if len(lines) > 1 else ""
        
        print(f"\n🔹 组件 {i}: {section_title}")
        print(f"   长度: {len(section_content)} 字符")
        print(f"   行数: {len(lines) - 1}")
        
        # 分析组件功能
        if "待纠正文本" in section_title:
            print(f"   功能: 明确指定需要纠错的目标文本")
        elif "相关知识库信息" in section_title:
            print(f"   功能: 提供专业的纠错知识和规则依据")
            print(f"   知识条目数: {len(knowledge)}")
        elif "纠错要求" in section_title:
            print(f"   功能: 详细说明纠错任务的具体要求")
        elif "输出格式" in section_title:
            print(f"   功能: 规范化输出格式，确保结果可解析")
        else:
            print(f"   功能: 角色定义和任务背景说明")
        
        # 显示内容预览
        preview = section_content[:100] + "..." if len(section_content) > 100 else section_content
        print(f"   内容预览: {preview}")

def show_expected_llm_response():
    """展示期望的LLM响应"""
    print(f"\n🎯 期望的LLM响应示例")
    print("=" * 60)
    
    test_text = "我很喜欢这件衣服的。"
    
    expected_response = {
        "has_error": True,
        "original_text": "我很喜欢这件衣服的。",
        "corrected_text": "我很喜欢这件衣服。",
        "error_type": "语气词误用",
        "correction_reasoning": "陈述句末尾不需要语气词'的'。根据中文语法规则，语气词'的'主要用于疑问句或强调句，在表达客观事实的陈述句中应该省略。此外，句子已经有句号作为结束标点，不需要额外的语气词。",
        "confidence": 0.95,
        "applied_rules": [
            "陈述句末尾通常不使用语气词'的'",
            "语气词'的'主要用于疑问句或强调句，陈述句末尾通常省略",
            "陈述客观事实时，句末不使用语气词"
        ]
    }
    
    print(f"📝 输入文本: {test_text}")
    print(f"📤 期望的JSON输出:")
    print("-" * 40)
    
    formatted_json = json.dumps(expected_response, ensure_ascii=False, indent=2)
    print(formatted_json)
    
    print(f"\n📊 响应质量指标:")
    print(f"   错误检测: ✅ 正确识别语气词误用")
    print(f"   纠错准确性: ✅ 正确删除多余的'的'")
    print(f"   理由完整性: ✅ 提供详细的语法解释")
    print(f"   规则引用: ✅ 引用了3条相关语法规则")
    print(f"   置信度合理: ✅ 0.95表示高置信度")

def main():
    """主演示函数"""
    print("🚀 改进后的RAG提示词演示")
    print("=" * 80)
    
    # 运行各个演示
    show_enhanced_prompt_for_new_text()
    compare_prompt_quality()
    demonstrate_prompt_components()
    show_expected_llm_response()
    
    print(f"\n🎉 演示完成！")
    print(f"\n💡 改进后的提示词优势:")
    print(f"   ✅ 智能知识检索: 多层次匹配确保相关性")
    print(f"   ✅ 结构化组织: 清晰的组件划分便于理解")
    print(f"   ✅ 专业知识支撑: 基于专家知识库的权威依据")
    print(f"   ✅ 标准化输出: JSON格式便于程序处理")
    print(f"   ✅ 可解释性强: 详细的纠错理由和规则引用")
    
    print(f"\n🔧 使用建议:")
    print(f"   1. 根据具体错误类型调整top_k参数")
    print(f"   2. 可以添加领域特定的知识条目")
    print(f"   3. 定期更新知识库以提高覆盖率")
    print(f"   4. 结合用户反馈优化检索算法")

if __name__ == "__main__":
    main()
