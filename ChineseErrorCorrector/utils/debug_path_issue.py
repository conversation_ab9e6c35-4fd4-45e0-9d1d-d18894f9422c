#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径问题诊断脚本
帮助找出为什么 prompt_generation_example.py 报告文件不存在
"""

import os
import sys
from pathlib import Path

def diagnose_path_issue():
    """诊断路径问题"""
    print("🔍 路径问题诊断")
    print("=" * 50)
    
    # 1. 当前工作目录
    current_dir = os.getcwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 2. 脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"📄 脚本所在目录: {script_dir}")
    
    # 3. Python路径
    print(f"🐍 Python路径:")
    for i, path in enumerate(sys.path):
        print(f"   {i}: {path}")
    
    # 4. 检查各种可能的路径
    possible_paths = [
        "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json",
        "complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json",
        os.path.join(current_dir, "complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"),
        os.path.join(script_dir, "complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"),
        "/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/utils/complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json"
    ]
    
    print(f"\n📋 检查可能的路径:")
    for i, path in enumerate(possible_paths, 1):
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"   {i}. {status} {path}")
        if exists:
            try:
                size = os.path.getsize(path)
                print(f"      文件大小: {size} 字节")
            except Exception as e:
                print(f"      无法获取文件大小: {e}")
    
    # 5. 列出当前目录内容
    print(f"\n📂 当前目录内容:")
    try:
        items = os.listdir(current_dir)
        for item in sorted(items):
            item_path = os.path.join(current_dir, item)
            if os.path.isdir(item_path):
                print(f"   📁 {item}/")
            else:
                print(f"   📄 {item}")
    except Exception as e:
        print(f"   ❌ 无法列出目录内容: {e}")
    
    # 6. 检查 complete_rag_knowledge_base 目录
    kb_dir = "complete_rag_knowledge_base"
    print(f"\n📂 检查 {kb_dir} 目录:")
    if os.path.exists(kb_dir):
        print(f"   ✅ {kb_dir} 目录存在")
        try:
            expert_dir = os.path.join(kb_dir, "expert_knowledge")
            if os.path.exists(expert_dir):
                print(f"   ✅ {expert_dir} 目录存在")
                kb_file = os.path.join(expert_dir, "chinese_correction_knowledge_base.json")
                if os.path.exists(kb_file):
                    print(f"   ✅ {kb_file} 文件存在")
                    print(f"   📊 文件大小: {os.path.getsize(kb_file)} 字节")
                else:
                    print(f"   ❌ {kb_file} 文件不存在")
                    # 列出 expert_knowledge 目录内容
                    print(f"   📂 {expert_dir} 目录内容:")
                    for item in os.listdir(expert_dir):
                        print(f"      - {item}")
            else:
                print(f"   ❌ {expert_dir} 目录不存在")
                # 列出 complete_rag_knowledge_base 目录内容
                print(f"   📂 {kb_dir} 目录内容:")
                for item in os.listdir(kb_dir):
                    print(f"      - {item}")
        except Exception as e:
            print(f"   ❌ 检查目录时出错: {e}")
    else:
        print(f"   ❌ {kb_dir} 目录不存在")

def test_import():
    """测试导入"""
    print(f"\n🔧 测试模块导入:")
    try:
        from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
        print("   ✅ ChineseKnowledgeBaseProcessor 导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def test_knowledge_base_loading():
    """测试知识库加载"""
    print(f"\n📚 测试知识库加载:")
    
    # 找到正确的路径
    possible_paths = [
        "./complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json",
        "complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json",
        os.path.join(os.getcwd(), "complete_rag_knowledge_base/expert_knowledge/chinese_correction_knowledge_base.json")
    ]
    
    working_path = None
    for path in possible_paths:
        if os.path.exists(path):
            working_path = path
            break
    
    if not working_path:
        print("   ❌ 找不到知识库文件")
        return False
    
    print(f"   📁 使用路径: {working_path}")
    
    try:
        from rag_knowledge_usage_guide import ChineseKnowledgeBaseProcessor
        processor = ChineseKnowledgeBaseProcessor(working_path)
        print("   ✅ 知识库加载成功")
        return True
    except Exception as e:
        print(f"   ❌ 知识库加载失败: {e}")
        return False

def suggest_fix():
    """建议修复方案"""
    print(f"\n💡 修复建议:")
    
    current_dir = os.getcwd()
    expected_dir = "/home/<USER>/wangjiahao/Model/CGEC_P2/ChineseErrorCorrector/utils"
    
    if current_dir != expected_dir:
        print(f"   1. 确保在正确的目录下运行脚本:")
        print(f"      当前目录: {current_dir}")
        print(f"      期望目录: {expected_dir}")
        print(f"      运行: cd {expected_dir}")
    
    print(f"   2. 如果文件确实不存在，重新构建知识库:")
    print(f"      python build_complete_rag_kb.py")
    
    print(f"   3. 使用绝对路径:")
    print(f"      修改 prompt_generation_example.py 中的路径为绝对路径")
    
    print(f"   4. 检查文件权限:")
    print(f"      ls -la complete_rag_knowledge_base/expert_knowledge/")

def main():
    """主函数"""
    print("🚨 路径问题诊断工具")
    print("=" * 60)
    
    diagnose_path_issue()
    
    if test_import():
        test_knowledge_base_loading()
    
    suggest_fix()
    
    print(f"\n🎯 诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
