
# 中文校对RAG知识库部署指南

## 📁 知识库结构

```
./complete_rag_knowledge_base/
├── expert_knowledge/              # 专家知识库
├── dataset_knowledge.json         # 数据集提取知识
├── merged_knowledge_base.json     # 合并后的完整知识库
├── rag_formatted_texts.txt        # RAG格式化文本
├── rag_system/                    # RAG系统存储
└── DEPLOYMENT_GUIDE.md           # 本指南
```

## 🚀 快速部署

### 1. 环境准备
```bash
# 设置API密钥
export OPENAI_API_KEY="your-openai-api-key"

# 安装依赖
pip install openai lightrag-hku
```

### 2. 使用知识库
```python
from lightrag_enhanced_corrector import LightRAGEnhancedCorrector

# 创建纠错器（使用已构建的知识库）
corrector = LightRAGEnhancedCorrector(
    working_dir="./complete_rag_knowledge_base/rag_system",
    enable_rag=True
)

await corrector.initialize()

# 进行RAG增强纠错
result = await corrector.correct_with_rag_enhancement("测试文本")
print(f"纠错结果: {result.corrected_text}")
print(f"纠错理由: {result.correction_reasoning}")
```

## 📊 知识库统计

{
  "expert_error_examples": 14,
  "expert_rules": 19,
  "expert_patterns": 4,
  "expert_domain_knowledge": 14,
  "dataset_extracted_errors": 0,
  "total_knowledge_items": 51
}

## 🔧 自定义扩展

### 添加新知识
1. 编辑 `merged_knowledge_base.json`
2. 重新运行 RAG 集成流程
3. 测试新知识的效果

### 优化检索效果
1. 调整 RAG 检索参数
2. 优化知识文本格式
3. 增加领域特定知识

## 📈 性能优化

- 批量处理时使用异步接口
- 根据需要调整检索模式（naive/local/global/hybrid）
- 定期更新知识库内容
- 监控纠错质量和用户反馈

## 🛠️ 故障排除

### 常见问题
1. **API密钥错误**: 检查 OPENAI_API_KEY 设置
2. **知识库为空**: 确认知识库构建成功
3. **检索无结果**: 检查查询文本和知识库匹配度
4. **性能问题**: 调整批次大小和并发数

### 日志查看
- RAG系统日志: `./complete_rag_knowledge_base/rag_system/logs/`
- 纠错器日志: 控制台输出

## 📞 技术支持

如有问题，请检查：
1. 环境配置是否正确
2. 知识库文件是否完整
3. API服务是否正常
4. 网络连接是否稳定
