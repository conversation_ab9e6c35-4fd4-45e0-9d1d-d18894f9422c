#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
确保所有脚本都能正常运行
"""

import subprocess
import sys
import os

def run_script(script_name, description):
    """运行脚本并检查结果"""
    print(f"\n🧪 测试: {description}")
    print(f"📄 脚本: {script_name}")
    print("-" * 50)
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            timeout=60,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {script_name} 运行成功")
            # 显示输出的前几行
            output_lines = result.stdout.split('\n')[:5]
            for line in output_lines:
                if line.strip():
                    print(f"   {line}")
            total_lines = len(result.stdout.split('\n'))
            if total_lines > 5:
                print(f"   ... (共 {total_lines} 行输出)")
            return True
        else:
            print(f"❌ {script_name} 运行失败")
            print(f"   返回码: {result.returncode}")
            if result.stderr:
                print(f"   错误信息: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {script_name} 运行超时")
        return False
    except Exception as e:
        print(f"💥 {script_name} 运行异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 最终测试 - 确保所有脚本正常运行")
    print("=" * 60)
    
    # 要测试的脚本列表
    test_scripts = [
        ("prompt_generation_example.py", "提示词生成示例"),
        ("enhanced_prompt_demo.py", "增强版提示词演示"),
        ("test_rag_knowledge_base.py", "RAG知识库测试"),
        ("quick_test.py", "快速系统测试"),
        ("debug_path_issue.py", "路径问题诊断")
    ]
    
    passed = 0
    total = len(test_scripts)
    
    for script_name, description in test_scripts:
        if os.path.exists(script_name):
            if run_script(script_name, description):
                passed += 1
        else:
            print(f"\n❌ 脚本不存在: {script_name}")
    
    # 测试总结
    print(f"\n🎯 测试总结")
    print("=" * 60)
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有脚本都能正常运行！")
        print("\n📋 可用的脚本:")
        for script_name, description in test_scripts:
            if os.path.exists(script_name):
                print(f"   ✅ python {script_name} - {description}")
    else:
        print("⚠️ 部分脚本运行失败，请检查错误信息")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 确保在正确的目录下运行: {os.getcwd()}")
    print(f"   2. 如果遇到路径问题，运行: python debug_path_issue.py")
    print(f"   3. 如果需要重建知识库，运行: python build_complete_rag_kb.py")

if __name__ == "__main__":
    main()
