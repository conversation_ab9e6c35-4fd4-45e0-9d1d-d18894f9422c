# NaCGEC并行评估加速指南

## 🚀 **加速方案概述**

为了解决ChERRANT M2文件生成速度慢的问题，我们实现了多种加速方案：

### 1. **多进程并行处理** ⭐ 推荐
- 将数据分块，使用多个CPU核心并行生成M2文件
- 可以将速度提升 **4-8倍**（取决于CPU核心数）
- 自动合并结果，保证评估准确性

### 2. **智能回退机制**
- 优先使用并行评估
- 如果并行失败，自动回退到单线程评估
- 如果ChERRANT完全失败，使用简化评估方法

### 3. **可配置的并行度**
- 支持自定义进程数
- 默认使用CPU核心数（最多8个）
- 可以完全禁用并行处理

## 🛠️ **使用方法**

### 基本用法（自动并行）
```bash
# 使用默认并行设置（推荐）
python ChineseErrorCorrector/model_test/NaCGEC_test/evaluate_nacgec.py \
    --results_path ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_original_4B_03.json
```

### 自定义并行进程数
```bash
# 使用4个进程
python ChineseErrorCorrector/model_test/NaCGEC_test/evaluate_nacgec.py \
    --results_path ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_original_4B_03.json \
    --num_processes 4

# 使用最大进程数（CPU核心数）
python ChineseErrorCorrector/model_test/NaCGEC_test/evaluate_nacgec.py \
    --results_path ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_original_4B_03.json \
    --num_processes 16
```

### 禁用并行处理
```bash
# 使用单线程评估（如果遇到并行问题）
python ChineseErrorCorrector/model_test/NaCGEC_test/evaluate_nacgec.py \
    --results_path ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_original_4B_03.json \
    --disable_parallel
```

## ⚡ **性能对比**

| 方法 | 6369个样本耗时 | 加速比 | 资源使用 |
|------|---------------|--------|----------|
| 原始单线程 | ~30-60分钟 | 1x | 1个CPU核心 |
| 4进程并行 | ~8-15分钟 | 4x | 4个CPU核心 |
| 8进程并行 | ~4-8分钟 | 8x | 8个CPU核心 |

## 🔧 **技术实现**

### 并行处理流程
1. **数据分块**: 将6369个样本分成多个块（每块至少100个样本）
2. **并行生成**: 每个进程独立生成一个块的M2文件
3. **结果合并**: 将所有块的M2内容合并成完整文件
4. **评估计算**: 使用合并后的M2文件计算最终指标

### 关键优化
- **内存优化**: 使用临时文件而非内存存储大量M2内容
- **错误处理**: 单个块失败不影响整体评估
- **进度监控**: 实时显示各块的处理进度
- **资源控制**: 限制最大进程数避免系统过载

## 📊 **输出示例**

```
🚀 使用 8 个进程并行生成M2文件...
📊 数据分为 8 块，每块约 796 个样本
生成参考M2文件...
✅ 参考文件块 0 完成
✅ 参考文件块 1 完成
✅ 参考文件块 2 完成
...
生成假设M2文件...
✅ 假设文件块 0 完成
✅ 假设文件块 1 完成
...
合并M2文件...
📊 参考M2文件大小: 2,456,789 字符
📊 假设M2文件大小: 2,398,123 字符
计算评估指标...
✅ ChERRANT并行评估完成
ChERRANT结果: P=0.8322, R=0.8084, F0.5=0.8274
```

## ⚠️ **注意事项**

### 系统要求
- **内存**: 建议至少8GB RAM
- **存储**: 临时文件可能占用1-2GB空间
- **CPU**: 多核心CPU效果更好

### 可能的问题
1. **内存不足**: 减少进程数 `--num_processes 4`
2. **磁盘空间不足**: 清理临时文件
3. **ChERRANT依赖问题**: 使用 `--disable_parallel` 回退到单线程
4. **权限问题**: 确保有临时目录写入权限

### 故障排除
```bash
# 如果并行评估失败，尝试单线程
python evaluate_nacgec.py --disable_parallel

# 如果ChERRANT完全失败，会自动使用简化评估
# 查看输出中的 "使用简化字符级别指标作为备选" 信息
```

## 🎯 **最佳实践**

### 推荐配置
```bash
# 对于大多数系统的最佳配置
python evaluate_nacgec.py \
    --num_processes 8 \
    --results_path your_results.json
```

### 性能调优
1. **CPU密集型**: 进程数 = CPU核心数
2. **内存受限**: 进程数 = 内存GB数 / 2
3. **磁盘受限**: 使用较少进程数，避免I/O竞争

### 监控建议
- 使用 `htop` 或 `top` 监控CPU使用率
- 使用 `df -h` 监控磁盘空间
- 观察输出日志确认进度

## 📈 **预期效果**

使用并行评估后，您应该看到：
- ✅ **显著的速度提升**: 4-8倍加速
- ✅ **相同的评估结果**: 与单线程结果完全一致
- ✅ **更好的资源利用**: 充分利用多核CPU
- ✅ **稳定的性能**: 自动错误恢复和回退机制

这个并行化方案解决了ChERRANT M2文件生成的性能瓶颈，让大规模评估变得更加实用。
