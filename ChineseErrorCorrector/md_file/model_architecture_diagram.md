# ChineseErrorCorrector3-4B 模型架构图

## 模型架构流程图

```mermaid

graph TD
    A[Input Tokens] --> B[Token Embedding]
    B --> C[Position Encoding]
    C --> D[Dropout]
    D --> E[Transformer Layers]
    
    subgraph "Transformer Block (×36)"
        E --> F[Layer Norm]
        F --> G[Multi-Head Attention]
        G --> H[Residual Connection]
        H --> I[Layer Norm]
        I --> J[Feed Forward Network]
        J --> K[Residual Connection]
    end
    
    K --> L[Final Layer Norm]
    L --> M[Language Model Head]
    M --> N[Output Logits]
    
    subgraph "Multi-Head Attention Details"
        G --> G1[Query Projection]
        G --> G2[Key Projection] 
        G --> G3[Value Projection]
        G1 --> G4[Scaled Dot-Product Attention]
        G2 --> G4
        G3 --> G4
        G4 --> G5[Output Projection]
    end
    
    subgraph "Feed Forward Network Details"
        J --> J1[Linear Layer 1]
        J1 --> J2[Activation Function]
        J2 --> J3[Linear Layer 2]
    end
    
    subgraph "Model Configuration"
        P1["Vocab Size: 151,936"]
        P2["Hidden Size: 2,560"]
        P3["Num Layers: 36"]
        P4["Attention Heads: 32"]
        P5["Intermediate Size: 9,728"]
        P6["Max Position: 40,960"]
        P7["Total Parameters: 4.02B"]
    end
    
    style A fill:#e1f5fe
    style N fill:#e8f5e8
    style E fill:#fff3e0
    style G fill:#fce4ec
    style J fill:#f3e5f5

```

## 参数分布图

```mermaid

pie title 参数分布 (总计: 4.02B)
    "MLP Layers": 66.9
    "Attention Layers": 23.5
    "Embedding": 9.7

```
