# 模型路径迁移说明

## 概述
本次修改将项目中的模型加载路径从原先的 `ChineseErrorCorrector/pre_model` 改为 `Model/models`。

## 修改内容

### 1. 配置文件修改
**文件**: `ChineseErrorCorrector/config.py`

**修改前**:
```python
MODEL_DIR = os.path.join(PROJECT_DIR, 'pre_model')
```

**修改后**:
```python
# 修改模型路径从pre_model改为Model/models
MODEL_DIR = os.path.join(os.path.dirname(PROJECT_DIR), 'Model', 'models')
```

**影响的配置类**:
- `LTPPath.LTP_MODEL_DIR`: 现在指向 `Model/models/ltp_tiny`
- `TextCorrectConfig.DEFAULT_CKPT_PATH`: 现在指向 `Model/models/ChineseErrorCorrector3-4B`

### 2. 文档更新
**文件**: `README.md`

**修改前**:
```
将模型下载，放在ChineseErrorCorrector/pre_model/twnlp/ChineseErrorCorrector3-4B
```

**修改后**:
```
将模型下载，放在Model/models/ChineseErrorCorrector3-4B
```

### 3. 目录结构变更

**新建目录**:
- `Model/models/` - 新的模型存放目录

**模型文件迁移**:
- 已将 `ChineseErrorCorrector/pre_model/ltp_tiny` 复制到 `Model/models/ltp_tiny`

## 路径对比

### 原路径结构
```
CGEC_P2/
├── ChineseErrorCorrector/
│   ├── pre_model/
│   │   ├── ltp_tiny/
│   │   └── ChineseErrorCorrector3-4B/  (需要下载)
│   └── ...
```

### 新路径结构
```
CGEC_P2/
├── Model/
│   └── models/
│       ├── ltp_tiny/
│       └── ChineseErrorCorrector3-4B/  (需要下载到此处)
├── ChineseErrorCorrector/
│   └── ...
```

## 使用说明

### 对于新用户
1. 下载 `ChineseErrorCorrector3-4B` 模型
2. 将模型放置在 `Model/models/ChineseErrorCorrector3-4B/` 目录下
3. 运行项目即可

### 对于现有用户
1. 如果已有 `ChineseErrorCorrector3-4B` 模型在 `ChineseErrorCorrector/pre_model/` 目录下
2. 需要将模型移动到 `Model/models/ChineseErrorCorrector3-4B/` 目录下
3. 或者重新下载模型到新位置

## 验证
可以运行以下Python代码验证路径配置是否正确：

```python
from ChineseErrorCorrector.config import MODEL_DIR, TextCorrectConfig, LTPPath
import os

print("MODEL_DIR:", MODEL_DIR)
print("DEFAULT_CKPT_PATH:", TextCorrectConfig.DEFAULT_CKPT_PATH)
print("LTP_MODEL_DIR:", LTPPath.LTP_MODEL_DIR)
print("MODEL_DIR exists:", os.path.exists(MODEL_DIR))
```

## 注意事项
- 原 `ChineseErrorCorrector/pre_model/` 目录仍然存在，但不再被使用
- 所有模型加载代码会自动使用新的路径配置
- 确保新的模型目录有足够的存储空间
