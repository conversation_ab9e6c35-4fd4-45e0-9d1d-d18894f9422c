# Document Content
Abstract

Chinese spelling errors usually refer to single Chinese character errors, such as visual error and phonetic error. Broadly speaking, errors resulting from the improper use of single Chinese character are categorized as Chinese spelling errors, such as compound character splitting error. Non-native beginners lack a knowledge of Chinese character structure, which can easily make splitting error in Chinese learning. Currently, there is a lack of research on correcting this error. For the SOTA models in Chinese spelling correction and Chinese grammar error correction, correcting splitting error remains a challenging task. In this paper, we propose a Compound Characters Splitting Error Correction Model (CSECM) to correct both common spelling errors and compound character splitting error. We conduct a comprehensive analysis of the causes of splitting error, ensuring that the correction process of CSECM aligns more closely with native learners. We constructed a series of specialized data for splitting error correction, including compound character knowledge base, splitting error training set and test set. The specialized dataset significantly enhances the model's capability to detect and correct splitting error. Furthermore, we propose a splitting error guided detection method that focuses more attention on potential errors while effectively preventing overcorrection during correction. Moreover, we employ a novel loss function that integrates flexible length loss and compound splitting loss. In this manner, our model can correct more generalized spelling errors with higher precision. Experiments are conducted on widely used benchmarks demonstrate that our model achieves advanced results compared to baseline models.

Key Words Chinese spelling correction·Attention mechanism·Deep learning·Natural language processing

1. Introduction

Chinese spelling correction (CSC) aims to detect and correct spelling errors in texts [1]. Spelling errors are common in human and generated text, caused typically by human writing, optical character recognition systems, automatic speech recognition, and generative artificial intelligence. In recent years, CSC has been widely used in several NLP studies, such as search engines, optical character recognition, and automatic speech recognition. CSC methods play an important role in the above researches, such as such as search engine [2], optical character recognition (OCR) [3], automatic speech recognition (ASR) [4], and Large Language Model (LLM) [5]. In the narrow sense, CSC generally focuses exclusively on correcting tokens, without involving the addition or deletion of tokens.

Different from native Chinese learners, non-native beginners may cause a special type of spelling error in the process of learning Chinese: Compound character splitting error [6]. This type of error refers to the wrong split of a compound character into two single-component characters (e.g., "好(good)" being mistakenly split into "女(female)" and "子(child)") during writing. The example of spelling errors in actual writing is illustrated in Fig.1. Misspelling characters are marked in red, and the correction are marked in green. Strictly speaking, compound character splitting error should be categorized as a type of spelling error rather than grammatical error, as they stem from learners' inadequate understanding of Chinese character structures and are not associated with wrong understanding about grammatical knowledge. Currently, nearly all CSC models do not consider the correction of compound splitting error. In fact, the splitting error is a common error in teaching Chinese as a foreign language, so solving this problem has high practical value.

Fig.1 Examples of Chinese spelling errors

In this paper, we propose a Compound character Error Correction Model (CSECM) to correct Chinese spelling errors. To enable CSECM to effectively correct both common spelling errors and compound splitting error, we implemented the following work. First, we construct a compound character knowledge base based on the structural relationship features of Chinese characters. The knowledge base can improve the performance of CSECM in detecting and correcting compound splitting error. The contents in the knowledge base are as follows: (1) All the commonly used single-component characters and compound characters; (2) Relationships between single-component characters and compound character (e.g., The Chinese character "云(cloud)" and "力(power)" combine to form the character "动(move)"); (3) Glyph similarity confusion sets of single-component characters and compound characters. Based on the knowledge base, we construct compound splitting error data for training and testing. Secondly, we propose a splitting error guided detection method. We combine MacBERT and our knowledge base to obtain the guided attention mask of splitting error. Thirdly, we propose CSECM, a model capable of detecting and correcting a broader range of Chinese spelling errors. Compared with the traditional CSC model, we incorporate the character splitting prediction task into CSECM, thereby enhancing the model's ability to identify compound character splitting error. Furthermore, we design a loss function that leverages the knowledge base to enhance the model's performance during the correction process. We conduct comparisons with other baseline models on both the SIGHAN15 test set and the compound splitting test set, achieving advanced results.

In summary, our contributions are as follows:

(1) We construct the compound character knowledge base, which is a multi-level and structured Chinese character knowledge base built for compound characters. The structure information of split characters and the confusion set at the partial level are included in the knowledge base, which can provide more key information for the model to improve the model's understanding of the structure of Chinese characters.

(2) We investigate the cognitive processes of Chinese native speakers during the error correction process and propose a loss function that more closely aligns with human cognition. By incorporating length prediction loss and confusion set loss into the loss function, we effectively enable the detection and correction of compound characters splitting error.

(3) We propose a compound character error correction model that effectively addresses the problem of detecting and correcting about compound character spelling errors. Our model has better performance on public datasets, and the correction results have good interpretability.

2. Related Work

Chinese Spelling Check aims to automatic detection and correction spelling errors in the text. Li et al. [7] focused on spelling errors caused by pronunciation similarity. In addition to the regular spelling errors, an additional auxiliary task of Chinese pronunciation prediction was designed in the training, and pinyin information was used to improve the training effect of the model. Finally, the adaptive balance between the two decoders is achieved according to the pronunciation similarity between the input character and the target character. This method considers the error correction of phonetic close characters, but does not fully consider the error correction of shape close characters. Based on Convolutional Neural Network (CNN), Lv et al. [8] designed a visual information encoder and a pronunciation information encoder to encode glyphs and phonemes respectively, which were used as input vectors together with the sentence encoding of BERT [9]. This method takes full account of the phonological and visual information of Chinese text, but it is only suitable for correcting a single error, and performs poorly for sentences with multiple spelling errors. In order to solve this problem, Li et al. [10] designed a multi-round error correction model, in which only one error was corrected in each round, and the model was iterated on this basis until the probability of correct correction was less than the set threshold. In addition, four models are defined according to the different training data sets, and the prediction results of each model are given different weights. The final output is the integration of the output of all models. But this approach is prone to overcorrection. For this problem, Sun et al. [11] designed an encoder that can focus on the wrong position to reduce the model's attention to the correct position, so as to avoid over-correction during training. In the inference process, the error-guided mask prediction strategy is used to mask and predict only the possible error positions, which greatly reduces the probability of the multi-degree correction problem.

With the development of large language models, the cross research between large language models and other research fields has gradually become a hot topic. Zhang et al. [12] extracted the context information of characters through a pre-trained model, and then calculated the context similarity of characters. Compared with the traditional character similarity based on confusion set, the context similarity has better performance in CSC task. Although large language models have excellent contextual understanding ability, this does not mean that their cross-domain understanding ability is equally strong. Wang et al. [13] evaluated the cross-domain ability of the latest Chinese spelling check model and the large language model ChatGPT [14] by constructing three datasets in the fields of finance, medical care and law. The results show that the model trained with the general corpus has poor cross-domain understanding ability. In order to maintain good context understanding in different domains, it is necessary to use domain data for training. At present, the training process of deep learning models is not interpretable. Wang et al. [15] explored this problem and proposed an interpretable deep learning model AxBERT. Specifically, the uninterpretable BERT model is aligned with the interpretable association knowledge network at the rule and semantic level, and the cohesion and transition between the two are realized through the translation matrix, and then the interpretable CSC method is realized. Some researchers design models by simulating human thinking habits, so as to give the model a certain degree of interpretability. Liu et al. [16] proposed a rewriting language model ReLM. Different from the conventional sequence labeling idea, the idea of ReLM is to rewrite the sentence to express the correct semantics, and realize spelling error correction in the rewriting process. This idea is more similar to human thinking habits, and the method has achieved the best performance in CSC benchmark tests.

3. Methods

We follow to the classification of Chinese spelling errors in the Global Chinese Interlanguage Corpus and categorize compound character splitting error as a special type of Chinese spelling error. Currently, there is a lack of specialized research regarding the correction methods for Compound splitting Error. We have attempted to design a non-conventional CSC approach to address this issue. Compared with the traditional CSC method, the proposed approach ensures that the output sentence length remains dynamically controllable rather than being entirely fixed. This also aligns with the real goal of the CSC task--correcting potential spell errors in a sentence, rather than maintaining sentence length in this process.

In this section, we detail the correction procedure for Compound splitting Error. It mainly includes the construction of Chinese Compound character splitting confusion set, the preparation of training data, the optimization of model structure, and the design of loss function.

3.1 Data Preparation

Currently, there is a limited number of datasets available for split token error correction. In this section, we filter the commonly used Chinese tokens and construct a Chinese Character-split Dictionary according to specific rules. Subsequently, based on this dictionary, we design a data augmentation method aimed at simulating splitting token errors. The artificially generated errors using this method more closely resemble the actual errors made by foreign learners.

3.1.1 Construct the Compound Character Knowledge Base

We have constructed a compound character knowledge base based on the General Standard Chinese Token List. We primarily utilized the first and second-level token lists in this list. Both of these token lists consist of high-frequency Chinese tokens, totaling 6,500 tokens, which constitute over 80% of the entire token list and can satisfy the usage requirements in more than 95% of cases. The majority of these Chinese tokens are not related to compound splitting error. Therefore, we define filtering rules to retain only those Chinese tokens that are highly correlated with the splitting error. If the confusion set is expressed as C={C1, C2, …, Cn}, any Chinese token Ci in it should satisfy the following conditions:

Ⅰ Ci is a Chinese token composed of left-right structure;

Ⅱ Ci is composed of two complete Chinese tokens rather than radical (such as "氵"、"忄");

After the above operations, we extract 2067 fusion tokens (tokens composed of two monosyllabic tokens) and 145 compound tokens (tokens composed of monosyllabic tokens and merged tokens), and constructed the confusion set. The structure of confusion set is shown in Fig.2. Our confusion set uses the confusion set of similar strokes, and does not involve the information of similar pinyin.

Fig.2 An example of knowledge base

3.1.2 Construct Compound Character Splitting Error Dataset

There are few cases about token compound splitting error in the existing datasets, particularly lacking sufficient real error data. To alleviate the situation of data deficiency, we designed a method for generating compound splitting error dataset that can simulate the real errors made by foreign learners. Specifically, we extracted 35,000 labeled raw data from the HSK dynamic composition corpus. These data are all from the essays written by foreign Chinese learners in the Advanced Chinese Proficiency Test (HSK). We converted all the raw data into the correct form based on the labels, and then used the confusion set to identify the split tokens. We use a percentage of 20% compound characters for modifying, where 60% will only split into two single-component characters, 20% split and one of the characters will be randomly replaced according to the confusion set in confusion set, and keep with original tokens for the rest of 20%. To avoid excessive repetition of training samples, we set the maximum occurrence frequency of the same error to 5, even if it may exist in different contexts. By employing the aforementioned method, we constructed a Splitting Error Dataset with 32,385 manually labeled sentences. The data augmentation process of dataset is shown in Fig.3. Misspelling characters are marked in red, and the green parts are the corresponding corrections.

Fig.3 Examples of different operations conducted in splitting error dataset

3.2 Compound Character Splitting Error Pre-detection

Different from traditional spelling errors, there is no effective detection method for spelling errors from compound character splitting. In this section, we propose a pre-detection method for compound character splitting error, which can significantly enhance the CSC model's capability to detect such errors.

Given an input sentence X={x1,... xn}, we iteratively apply Whole Word Masking (WWM) to the sentence until all tokens are masked, as depicted in the left side of Fig.4. Then, we employ MacBERT [17] pre-trained model to predict each masked position condition on the unmasked tokens in the same column.

For original sentence, we extract all token pairs [xi, xi+1] that can be merged into a new character based on the confusion set mentioned in section 3.1.1. If the new character is among the three outputs with the highest probability, we provisionally assume that the token pairs were written with the wrong split. Next, we get the Splitting Error Attention Mask (SEAM) and Splitting Error Correction Prompt (SECP) based on the outputs of multi-error detection. SEAM is used to ignore incorrect tokens during encoding, allowing more correct information to be retained. The value of SEAM in the i-th position of original input is determined by the Equation (1) :

(1)

SECP is a prompt sentence with a masked token, only the masked position will be decoded and the non-masked position is fixed. The length of the SECP, which is the length of the target sequence, is predicted during the decoding of the length tag. Note that, for token compound splitting error, we replace the token pair [xi, xi+1] with a [Mask] token. The process of compound splitting error detection is shown in Fig.4.

Fig.4 An example of compound character splitting error pre-detection method

3.3 Advanced Spelling Error Correction Model

With the rapid advancement of artificial intelligence, how to make the model have intelligent behavior in the research field is a huge challenge. Our objective is to develop a flexible model for Chinese spelling correction, which can handle a wider range of Chinese spelling errors. The structure of our proposed CSECM is shown in Fig.5. We employ the CMLM [18] as the basic architecture, which comprises a standard Transformer [19] encoder and a decoder without masked self-attention, optimized through masked language modeling objectives. We optimize the encoder and decoder separately, thereby enabling the model to perform splitting error correction effectively.

3.3.1 Splitting Error Aware Encoder

We enhanced the encoder by integrating the splitting error aware attention mask. This enables the improved encoder to leverage the knowledge base for learning the merging relationships among characters, thereby effectively detecting splitting error in sentences. The architecture of the encoder is depicted in the left portion of Fig.5.

The wrong sentence is first input into the standard encoder of the Transformer named Enctrans, and then the output of this part is input into the splitting error aware encode Encsplit. In this encoder, spell errors and split errors are given in the form of masks to pay more attention to potential errors. In this way, the model is guided to learn from the error tokens in the original sequence how to identify the split character errors of Chinese characters. The encoding process of the input sentence through the Split-aware Encoder is shown in Equations (2) and (3):

(2)

(3)

Fig.5 The structure of proposed CSECM

3.3.2 Scoped Sentence Length Prediction

For autoregressive (AR) methods, the appearance of EOS tokens signifies the completion of sentence prediction. However, for non-autoregressive (NAR) decoding, where predictions are performed in parallel, it is essential to determine the sentence length in advance. Additionally, in standard CSC tasks, the sentence length typically remains constant. For the special type of error--compound character splitting error, the variation in sentence length is the key information that helps the model learn to fusion tokens correctly like a real human.

We attempt to enable the model learn the fusion relationship between Chinese characters through sentence length prediction. In other words, when two tokens are identified as compound character splitting error, our model will assign a [MASK] to generate the output at only one position. We follow CMLM and add a special [LENGTH] token to the input for sentence length prediction. We obtain the length of the target sentence through the prediction output of the [LENGTH] token. To enhance the accuracy of this prediction, we restrict the prediction range of L as Equation (4):

(4)

The variable i represents the count of character splitting error, which is obtained by the method of pre-detection method described in section 3.2. We use LENsource to denote the length of source sentence and LENpredict to denote the range of the target sentence length in prediction process. This range can serve as a guiding parameter to assist the model in making accurate fusion decisions while ensuring compliance with the length control requirements of the CSC task.

3.3.3 Splitting Error Correction Decoder

We adopted a non-autoregressive approach with length constraints for decoding. When predicting the length of a sentence, the model is guided to learn the correct split-word merging decision. Through the output of splitting error pre-detection module, we get the length range of the generated sentence [LEN-i, LEN]. Where i is the number of splitting errors. Before decoding starts, the length prediction will be performed first, and a sentence length with the highest probability will be output. Further the decoder predicts the model output based on Equations (5) and (6). The symbol X denotes the input sentence, and the rest of the tokens in Y that are unmasked construct the set of Yobs. For splitting error, X in the following equations should be [xi, xi+1] during decoding.

(5)

(6)

3.3.4 Loss Function

We parse the CSC task into three parts and design a new loss function to integrate compound character splitting error correction. We use the Negative Log-Likelihood (NLL) as the loss function for the regular spelling errors for learning the correct correction of a single token in the basic CSC task. The calculation method is as follows:

(7)

When decoding the [LENGTH] token, we give priority to predicting the length of the sentence within the range [LEN-i, LEN], avoiding too liberal generation. Loss function for length prediction, we still use cross-entropy loss function. Llen denotes the loss of sentence length prediction, which is used in the proposed model to help the model correctly identify splitting error in sentences.

(8)

P(L=Lk) is calculated according to the softmax function and shown in Equation (9):

(9)

Lori denotes the original length of input sentence and is also the upper limit of the sentence length. In Equation (10), where i denotes the number of splitting errors detected by our model. If no splitting error is identified in the input sentence, the corresponding loss will not be calculated. Lmerge represents the loss of the character merging prediction task, which is used to guide the model to learn the merging relationship between Chinese characters in training, so as to correct the splitting errors.

(10)

The final loss function is the combination of these three losses, where θ and λ are hyperparameters：

(11)

4. Experiments

In this section, we show the performance of CSECM on the CSC benchmark dataset, while evaluating the results of our model on the compound splitting test set.

4.1 Experimental Setup

4.1.1 Dataset and benchmarks

Training step: We used the classical dataset of the CSC task during training step, including SIGHAN14、SIGHAN15 and 271K. Moreover, we added the Split-token Training Set constructed in Section 3.1 as the training data for splitting error. The overall size of the training data is shown as Tab.1.

Table 1 Statistics of training set

Evaluation step: We directly used the SIGHAN15 test set to evaluate the performance of the model on the CSC task. In addition, we selected part of the compound splitting error data from the interlanguage corpus as an additional test set. The test set statistics are as shown in Tab.2:

Tab.2 Statistics of the test set

4.1.2 Baseline Models

ReLM [16] treats CSC as a sentence rewriting task based on the semantics of original sentence. ReLM is evaluated as zero-shot learning on the SIGHAN dataset and achieved state-of-the-art (SOTA) performance (F1=57.0). We use the CSWE dataset for training and keep its original training strategy.

EGCM [11] adds the guidance strategy in the process of correcting errors, and only makes the prediction at the wrong position, and the correct position is not changed. This method has previously achieved SOTA results in correction quality.

SCOPE [7] adopts an encoder and two decoders in the model structure, and introduces glyph information and pinyin information in the middle layer. SCOPE achieves high-quality spelling error correction through pronunciation prediction and multiple iterations.

ECSpell [8] adopts the error consistent masking strategy for pre-training and constructs domain specific datasets.

PLOME [20] implements a confusion set based masking strategy to jointly learn semantic knowledge and spelling errors. Moreover, the phonological and visual similarity knowledge of characters are also applied to the process of modeling.

REALISE [21] utilizes multimodal information multimodal for CSC, mixing the semantic, phonetic and graphic information of inputs to predict the correct output.

SpellGCN [22] incorporates visual and phonological similarity knowledge through a graph convolutional network.

GrammarGPT [23] is a Chinese grammatical error correction model trained on a mixed dataset generated by ChatGPT and manually labeled. We only compare the performance of GrammarGPT with CSECM on the compound splitting test set, rather than on any other test sets.

BERT [9] is a very representative classical model that suitable for most NLP tasks. We fine-tune the BERT model using the CSWE dataset and show its performance on SIGHAN and split-token test set.

4.1.3 Hyperparameter setting

We follow most of the standard hyperparameters for transformers in the base configuration. We set up 6 layers of encoders with 8 attention heads in each layer, 512 dimensions for word embeddings and 2048 dimensions for hidden layers. We follow the weight initialization scheme from BERT (Devlin et al., 2018). The hyperparameters θ and λ are used to weight the length prediction loss and compound splitting correction loss, respectively, and are both set to 2 during training after tuning. We train batches of 64 sentences using Adam (Kingma and Ba, 2014) with β = (0.9, 0.999) and ε = 1e−6. The learning rate is set to 5e−5, and the model is trained with learning rate warming up and linear decay. We trained our model for 30 epochs in training dataset and saved the 5 best checkpoints to create the final model.

4.2 Experimental results and analysis

4.2.1 Performance on SIGHAN

We compare seven SOTA methods related to the CSC task in the last five years, and the comparison results on detection level and correction level are shown in Tab.3. The results marked with "*" are directly taken from the original literature. Other results marked with "†" are form our implementation via the official released code.

Tab.3 Performances of different models on SIGHAN15 test set

The results demonstrate that our approach achieves superior performance compared to all baseline methods on the SIGHAN15 test set. BERT-FT is obtained by fine-tuning the BERT model and optimizing for input sequence length (the maximum length is 128 tokens). Compared to EGCM, the SOTA model in 2023, the precision, recall and F1 value of CSECM are increased by 1.6%, 5.4% and 3.6% in correction level, respectively. SCOPE is a robust baseline model and we implement it according to the official code. We test the performance of SCOPE under the same experimental conditions, and the experimental results show that our method improves the precision by 3.6 points. ReLM implements error correction in the process of sentence rephrasing without error detection, therefore, only the results of correction level are presented. We also implemented ReLM based on publicly available code. The results indicate that our model outperforms ReLM by 4.9 points in precision, but RELM achieves a higher recall, surpassing CSECM by 4 points. Overall, CSECM achieves the highest F1 score compared to the baseline models.

4.2.2 Performance on compound character splitting error correction

From the perspective of the causes of errors, splitting error are a kind of generalized spelling errors. Current methods of CSC are incapable of addressing compound character splitting error effectively. Some CGEC methods treat compound character splitting error as a combination of two distinct types of errors--confusion and redundant characters--and handle them separately. This type of correction method diverges from the root cause of the error, resulting in poor interpretability of the correction results. We compare the proposed method with BERT-FT and GrammarGPT, both of which are capable of CGEC. The results are shown in Fig.6.

Fig.6 Performances of different models on compound character splitting test set

The above results show the performance of the baseline methods on the Compound splitting Test Set dataset. During generation, we remove the sentence length restriction of BERT-FT and use <eos> as the end token. BERT-FT does not perform well in correcting compound splitting error, although it has been fine-tuned through CSWE dataset. GrammarGPT, which excels at gramamr error correction, struggles to address compound splitting error effectively. The F1 score of our proposed model exceeds that of GrammarGPT by 14.9 points at the sentence level and by 15.8 points at the character level, respectively.

4.3 Ablation study

In this part, we analyze the effect of various components on the overall model, and primarily focus on studying the following three aspects:

(1) "- pre-detection" means removing the splitting error attention mask and prompt embeddings of the splitting error pre-detection part.

(2) "- Llen" denotes removing scoped length prediction loss. This indicates that the model is unable to acquire the information about the merging relationship between Chinese characters from the knowledge base.

(3) "- Lconf" means removing the loss of confusion set.

In order to reflect the results of ablation studies more comprehensively, we compare the performance of different ablation models at the character level. We test the different models above with SIGHAN15 test set and Split test set, and the results at Sentence level and Character level are shown in Table 4 and Table 5.

Tab.4 Ablation studies for CSC

According to the experimental results in Table 4 and Table 5, after removing the guidance information of the splitting error pre-detection module, the performance of the model on both CSC and splitting tasks decreases, which indicates that error pre-detection is helpful for the model. After removing the scoped length prediction task, the output sequence length in this case is equal to the input length, so the splitting cannot be merged correctly. Under this restriction, we only test on the SIGHAN15 test set. At this point the model becomes a traditional CSC model and cannot be used to handle splitting error. Interestingly, there is a slight improvement on the SIGHAN15 test set, which indicates that the model is better at handling relatively simple tasks. At the same time, it is also proved that the model obtains the ability of splitting word correction without obvious negative impact on its performance. Finally, we evaluate the impact of the splitting confusion set within the knowledge base on the model's performance. Our findings indicate that the confusion set exerts a more significant influence on the model's performance compared to other components. This demonstrates that the compound character knowledge base contributes essential information for effective model training.

Tab.5 Ablation studies for compound splitting error correction

The experimental results show that confusion set has the largest performance improvement for Correction level with 4.5 points for CSC and 5.6 points for Splitting error correction. The reason is that for both CSC and splitting error correction, the confusion set enhances the learning ability of the model for the correct output. In particular, for the split character error correction, the confusion set provides a more direct indication of the result of Chinese character combination, thus bringing a greater improvement.

4.4 Case study

In this paper, we demonstrate the value of compound splitting confusion set loss and sentence length prediction loss, and we compare the performance of different models through case studies. We selected some examples of sentences correction results from the baseline model, as shown in Fig.7. In the case of CSC, our model is able to make the correct decision aided by the knowledge of the confusion set. Because "休" and "秋" are visually similar, there is annotation information in the confusion set that can help the model output more accurate prediction.

Fig.7 Examples of sentence correction for partial baseline models

Through the splitting correction case, we can see that in the input sentence, the word "样(shape)" is split into two characters: "木(wood)" and "羊(sheep)". Traditional CSC method cannot effectively correct this type of error, and the same is true for the grammatical error correction method, because the model misinterprets "木(wood)" and "羊(sheep)" as the name of Peking Opera. After identifying the splitting error, our model outputs the correct character "样(shape)" based on the input and confusion set. Our correction results have good interpretability, and the correction of the original sentence is achieved using minimal modifications.

5. Conclusion

This paper points out a kind of derivative error type of spelling error, compound characters splitting error, which cannot be corrected by current CSC model. We propose a split character error correction model for non-native Chinese learners. The sentence length prediction task is redesigned to achieve flexible compound splitting error detection, and a new splitting confusion set is designed to improve the ability of model about learning the combinational relationships between Chinese characters. Finally, the experimental results show that the ability of splitting error correction can be increased while maintaining the advanced CSC task processing ability.

Declarations

Data Availability The datasets we use include public datasets SIGHAN14, SIGHAN15, Wang271k, and self-constructed data. The knowledge base and all data we used will be publicly available on GitHub after the publication of this paper.

Funding All funding originates from the project funds as detailed in the acknowledgments section.

Conflict of Interest The authors declare no financial or non-financial interests.

Ethical Approval This article does not contain any studies with human participants or animals performed by any of the authors.

Impact on the General Public This article does not have any impact on public health or general welfare.

References

Yu J, Li Z. Chinese spelling error detection and correction based on language model, pronunciation, and shape[C]//Proceedings of The Third CIPS-SIGHAN Joint Conference on Chinese Language Processing. 2014: 220-223.

Martins B, Silva M J. Spelling correction for search engine queries[C]//Advances in Natural Language Processing: 4th International Conference, EsTAL 2004, Alicante, Spain, October 20-22, 2004. Proceedings 4. Springer Berlin Heidelberg, 2004: 372-383.

Afli H, Qui Z, Way A, et al. Using SMT for OCR error correction of historical texts[J]. 2016.

Hinton G, Deng L, Yu D, et al. Deep neural networks for acoustic modeling in speech recognition: The shared views of four research groups[J]. IEEE Signal processing magazine, 2012, 29(6): 82-97.

Zhang S, Huang H, Liu J, et al. Spelling error correction with soft-masked BERT[J]. arXiv preprint arXiv:2005.07421, 2020.

Zhang L, Xing H. The interaction of orthography, phonology and semantics in the process of second language learners' Chinese character production[J]. Frontiers in Psychology, 2023, 14: 1076810.

Li J, Wang Q, Mao Z, et al. Improving Chinese spelling check by character pronunciation prediction: The effects of adaptivity and granularity[J]. arXiv preprint arXiv:2210.10996, 2022.

Lv Q, Cao Z, Geng L, et al. General and domain-adaptive Chinese spelling check with error-consistent pretraining[J]. ACM Transactions on Asian and Low-Resource Language Information Processing, 2023, 22(5): 1-18.

Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. 2018. Bert: Pre-training of deep bidirectional transformers for language understanding. Cite arxiv:1810.04805.

Li X, Du H, Zhao Y, et al. Towards Robust Chinese Spelling Check Systems: Multi-round Error Correction with Ensemble Enhancement[C]//CCF International Conference on Natural Language Processing and Chinese Computing. Cham: Springer Nature Switzerland, 2023: 325-336.

Sun R, Wu X, Wu Y. An error-guided correction model for Chinese spelling error correction[J]. arXiv preprint arXiv:2301.06323, 2023.

Zhang D, Li Y, Zhou Q, et al. Contextual similarity is more valuable than character similarity: An empirical study for Chinese spell checking[C]//ICASSP 2023-2023 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP). IEEE, 2023: 1-5.

Wang X, Zhao R, Dai H, et al. An Empirical Investigation of Domain Adaptation Ability for Chinese Spelling Check Models[C]//ICASSP 2024-2024 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP). IEEE, 2024: 9996-10000.

OpenAI R. Gpt-4 technical report. arxiv 2303.08774[J]. View in Article, 2023, 2(5).

Wang F, Shao H, Xie Z. AxBERT: An explainable Chinese spelling correction method driven by associative knowledge network[J]//ICLR. 2023.

Liu L, Wu H, Zhao H. Chinese Spelling Correction as Rephrasing Language Model[C]//Proceedings of the AAAI Conference on Artificial Intelligence. 2024, 38(17): 18662-18670.

Cui Y, Che W, Liu T, et al. Revisiting pre-trained models for Chinese natural language processing[J]. arXiv preprint arXiv:2004.13922, 2020.

Ghazvininejad M, Levy O, Liu Y, et al. Mask-predict: Parallel decoding of conditional masked language models[J]. arXiv preprint arXiv:1904.09324, 2019.

Vaswani A, Shazeer N, Parmar N, et al. Attention is all you need[J]. Advances in neural information processing systems, 2017, 30.

Liu S, Yang T, Yue T, et al. PLOME: Pre-training with misspelled knowledge for Chinese spelling correction[C]//Proceedings of the 59th Annual Meeting of the Association for Computational Linguistics and the 11th International Joint Conference on Natural Language Processing (Volume 1: Long Papers). 2021: 2991-3000.

Xu H D, Li Z, Zhou Q, et al. Read, listen, and see: Leveraging multimodal information helps Chinese spell checking[J]. arXiv preprint arXiv:2105.12306, 2021.

Cheng X, Xu W, Chen K, et al. Spellgcn: Incorporating phonological and visual similarities into language models for Chinese spelling check[J]. arXiv preprint arXiv:2004.14166, 2020.

Fan Y, Jiang F, Li P, et al. Grammargpt: Exploring open-source llms for native Chinese grammatical error correction with supervised fine-tuning[C]//CCF International Conference on Natural Language Processing and Chinese Computing. Cham: Springer Nature Switzerland, 2023: 69-80.


## Tables

### Table 1
| Training Set | #Sent | #Errors |
| --- | --- | --- |
| SIGHAN15 | 2,338 | 3,037 |
| SIGHAN14 | 3,437 | 5,122 |
| Wang271K | 271,329 | 381,962 |
| Compound Splitting Training Set | 32,385 | 40,643 |


### Table 2
| Test Set | #Sent | #Errors |
| --- | --- | --- |
| SIGHAN15 | 1,100 | 703 |
| Compound Splitting Test Set | 1,000 | 612 |


### Table 3
| Model | Detection Level | Detection Level | Detection Level | Correction Level | Correction Level | Correction Level |
| --- | --- | --- | --- | --- | --- | --- |
| Model | P | R | F1 | P | R | F1 |
| SpellGCN* (2020) | 74.8 | 80.7 | 77.7 | 72.1 | 77.7 | 75.9 |
| REALISE* (2021) | 77.3 | 81.3 | 79.3 | 75.9 | 79.9 | 77.8 |
| PLOME* (2021) | 77.4 | 81.5 | 79.4 | 75.3 | 79.3 | 77.2 |
| ECSpell* (2022) | 76.4 | 79.9 | 78.1 | 74.4 | 77.9 | 76.1 |
| EGCM* (2023) | 82.7 | 77.6 | 80.0 | 80.6 | 74.7 | 77.5 |
| BERT-FT† (2018) | 75.3 | 79.3 | 77.2 | 73.1 | 76.9 | 74.9 |
| SCOPE† (2022) | 79.5 | 84.3 | 81.8 | 78.6 | 83.4 | 80.9 |
| ReLM† (2024) | -- | -- | -- | 77.3 | 84.1 | 80.6 |
| CSECM (ours) | 83.5 | 81.4 | 82.4 | 82.2 | 80.1 | 81.1 |


### Table 4
| Model | Detection Level | Detection Level | Detection Level | Correction Level | Correction Level | Correction Level |
| --- | --- | --- | --- | --- | --- | --- |
| Model | P | R | F1 | P | R | F1 |
| ours | 83.5 | 81.4 | 82.4 | 82.2 | 80.1 | 81.1 |
| - pre-detection | 81.3 | 78.6 | 79.9 | 79.2 | 76.6 | 77.9 |
| -Llen | 82.4 | 80.2 | 81.3 | 81.7 | 79.5 | 80.6 |
| -Lconf | 79.4 | 76.8 | 78.1 | 77.9 | 75.3 | 76.6 |


### Table 5
| Model | Detection Level | Detection Level | Detection Level | Correction Level | Correction Level | Correction Level |
| --- | --- | --- | --- | --- | --- | --- |
| Model | P | R | F0.5 | P | R | F0.5 |
| ours | 62.9 | 52.2 | 60.4 | 61.2 | 50.8 | 58.8 |
| - pre-detection | 58.6 | 50.4 | 56.8 | 57.0 | 49.0 | 55.2 |
| - Llen | -- | -- | -- | -- | -- | -- |
| - Lconf | 59.5 | 51.2 | 57.7 | 54.9 | 47.2 | 53.2 |

