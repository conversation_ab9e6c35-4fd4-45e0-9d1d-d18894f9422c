#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
与Split测试系统集成的语义分割解析器
基于现有的split_test_nacgec.py进行扩展
"""

import os
import sys
import json
import time
from typing import List, Dict, Tuple

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.semantic_parser.semantic_parser_api import SemanticParserAPI


class SplitSemanticIntegration:
    """Split测试与语义分析集成器"""
    
    def __init__(self):
        self.semantic_api = SemanticParserAPI()
        
        # 从现有split测试数据中提取的样本
        self.split_test_samples = [
            {
                "id": 1,
                "source": "乌兰巴托不到4百年白勺历史。",
                "target": "乌兰巴托不到4百年的历史。",
                "error_type": "split_character",
                "split_info": {"白勺": "的"}
            },
            {
                "id": 2,
                "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。",
                "target": "我为什么喜欢阿拉木图，我觉得有几个特点。",
                "error_type": "split_character",
                "split_info": {"牛寺": "特"}
            },
            {
                "id": 3,
                "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。",
                "target": "你先择几个个性重点培养，最终形成自己独特的男人魅力。",
                "error_type": "split_character",
                "split_info": {"鬼未力": "魅力"}
            },
            {
                "id": 4,
                "source": "这个问题很复杂，但是我们需要仔细分析，因此要分步骤解决。",
                "target": "这个问题很复杂，但是我们需要仔细分析，因此要分步骤解决。",
                "error_type": "semantic_break",
                "split_info": {}
            },
            {
                "id": 5,
                "source": "如果天气好白勺话，我们就去公园散步，否则就在家里看书。",
                "target": "如果天气好的话，我们就去公园散步，否则就在家里看书。",
                "error_type": "mixed",
                "split_info": {"白勺": "的"}
            }
        ]
    
    def enhanced_split_analysis(self, sample: Dict, max_chunks: int = 3) -> Dict:
        """
        增强的拆分字分析，结合语义分割
        
        Args:
            sample: 测试样本
            max_chunks: 最大语义块数量
            
        Returns:
            增强分析结果
        """
        source_text = sample['source']
        
        print(f"\n🔍 分析样本 {sample['id']}: {source_text}")
        
        # 1. 使用语义解析器分析
        semantic_result = self.semantic_api.parse_with_syntax_trees(source_text, max_chunks)
        
        # 2. 验证拆分字检测准确性
        expected_splits = sample.get('split_info', {})
        detected_splits = {error['error_text']: error['correct_text'] 
                          for error in semantic_result['split_errors']}
        
        # 3. 计算检测准确性
        detection_accuracy = self._calculate_detection_accuracy(expected_splits, detected_splits)
        
        # 4. 分析语义连续性
        semantic_analysis = self._analyze_semantic_continuity(semantic_result)
        
        # 5. 构建增强结果
        enhanced_result = {
            'sample_info': sample,
            'semantic_parsing': semantic_result,
            'detection_accuracy': detection_accuracy,
            'semantic_analysis': semantic_analysis,
            'recommendations': self._generate_recommendations(semantic_result, sample)
        }
        
        return enhanced_result
    
    def _calculate_detection_accuracy(self, expected: Dict, detected: Dict) -> Dict:
        """计算检测准确性"""
        if not expected:
            # 没有预期错误的情况
            return {
                'precision': 1.0 if not detected else 0.0,
                'recall': 1.0,
                'f1_score': 1.0 if not detected else 0.0,
                'true_positives': 0,
                'false_positives': len(detected),
                'false_negatives': 0
            }
        
        # 计算TP, FP, FN
        true_positives = len(set(expected.keys()) & set(detected.keys()))
        false_positives = len(set(detected.keys()) - set(expected.keys()))
        false_negatives = len(set(expected.keys()) - set(detected.keys()))
        
        # 计算指标
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'precision': round(precision, 3),
            'recall': round(recall, 3),
            'f1_score': round(f1_score, 3),
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'expected_errors': expected,
            'detected_errors': detected
        }
    
    def _analyze_semantic_continuity(self, semantic_result: Dict) -> Dict:
        """分析语义连续性"""
        chunks = semantic_result['semantic_chunks']
        
        # 计算语义连续性指标
        avg_confidence = sum(chunk['confidence'] for chunk in chunks) / len(chunks) if chunks else 0
        
        # 分析块类型分布
        chunk_types = {}
        for chunk in chunks:
            chunk_type = chunk['chunk_type']
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
        
        # 计算语义完整性
        semantic_completeness = self._calculate_semantic_completeness(chunks)
        
        return {
            'total_chunks': len(chunks),
            'average_confidence': round(avg_confidence, 3),
            'chunk_type_distribution': chunk_types,
            'semantic_completeness': semantic_completeness,
            'has_error_chunks': any(chunk['chunk_type'] == 'error_containing' for chunk in chunks)
        }
    
    def _calculate_semantic_completeness(self, chunks: List[Dict]) -> float:
        """计算语义完整性分数"""
        if not chunks:
            return 0.0
        
        # 基于多个因素计算完整性
        factors = []
        
        # 1. 平均置信度
        avg_confidence = sum(chunk['confidence'] for chunk in chunks) / len(chunks)
        factors.append(avg_confidence)
        
        # 2. 块数量合理性（太多块可能表示过度分割）
        chunk_count_score = max(0, 1 - (len(chunks) - 3) * 0.1)  # 3个块为理想数量
        factors.append(chunk_count_score)
        
        # 3. 错误块比例（错误块越少越好）
        error_chunk_ratio = sum(1 for chunk in chunks if chunk['chunk_type'] == 'error_containing') / len(chunks)
        error_score = 1 - error_chunk_ratio
        factors.append(error_score)
        
        # 综合评分
        completeness = sum(factors) / len(factors)
        return round(completeness, 3)
    
    def _generate_recommendations(self, semantic_result: Dict, sample: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于检测结果生成建议
        if semantic_result['split_errors']:
            recommendations.append("检测到拆分字错误，建议进行纠错处理")
        
        chunks = semantic_result['semantic_chunks']
        
        # 基于语义块分析生成建议
        if len(chunks) > 4:
            recommendations.append("语义分割过于细碎，建议优化分割策略")
        
        error_chunks = [chunk for chunk in chunks if chunk['chunk_type'] == 'error_containing']
        if len(error_chunks) > len(chunks) // 2:
            recommendations.append("错误密度较高，建议重点关注文本质量")
        
        low_confidence_chunks = [chunk for chunk in chunks if chunk['confidence'] < 0.7]
        if low_confidence_chunks:
            recommendations.append(f"有{len(low_confidence_chunks)}个低置信度语义块，建议人工审核")
        
        if not recommendations:
            recommendations.append("文本语义结构良好，无需特殊处理")
        
        return recommendations
    
    def batch_analysis(self, output_file: str = None) -> Dict:
        """批量分析所有测试样本"""
        print("🚀 开始批量语义分析...")
        
        results = []
        total_start_time = time.time()
        
        for sample in self.split_test_samples:
            start_time = time.time()
            
            try:
                result = self.enhanced_split_analysis(sample, max_chunks=3)
                result['processing_time'] = round(time.time() - start_time, 3)
                results.append(result)
                
                print(f"✅ 样本 {sample['id']} 处理完成 ({result['processing_time']}s)")
                
            except Exception as e:
                error_result = {
                    'sample_info': sample,
                    'error': str(e),
                    'processing_time': round(time.time() - start_time, 3)
                }
                results.append(error_result)
                print(f"❌ 样本 {sample['id']} 处理失败: {e}")
        
        total_time = time.time() - total_start_time
        
        # 计算整体统计
        successful_results = [r for r in results if 'error' not in r]
        overall_stats = self._calculate_overall_statistics(successful_results)
        
        # 构建最终结果
        final_result = {
            'processing_info': {
                'total_samples': len(self.split_test_samples),
                'successful_analyses': len(successful_results),
                'total_processing_time': round(total_time, 3),
                'average_processing_time': round(total_time / len(self.split_test_samples), 3)
            },
            'overall_statistics': overall_stats,
            'detailed_results': results
        }
        
        # 保存结果
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(final_result, f, ensure_ascii=False, indent=2)
            print(f"💾 结果已保存到: {output_file}")
        
        return final_result
    
    def _calculate_overall_statistics(self, results: List[Dict]) -> Dict:
        """计算整体统计信息"""
        if not results:
            return {}
        
        # 检测准确性统计
        detection_stats = [r['detection_accuracy'] for r in results if 'detection_accuracy' in r]
        
        avg_precision = sum(stat['precision'] for stat in detection_stats) / len(detection_stats) if detection_stats else 0
        avg_recall = sum(stat['recall'] for stat in detection_stats) / len(detection_stats) if detection_stats else 0
        avg_f1 = sum(stat['f1_score'] for stat in detection_stats) / len(detection_stats) if detection_stats else 0
        
        # 语义分析统计
        semantic_stats = [r['semantic_analysis'] for r in results if 'semantic_analysis' in r]
        
        avg_chunks = sum(stat['total_chunks'] for stat in semantic_stats) / len(semantic_stats) if semantic_stats else 0
        avg_confidence = sum(stat['average_confidence'] for stat in semantic_stats) / len(semantic_stats) if semantic_stats else 0
        avg_completeness = sum(stat['semantic_completeness'] for stat in semantic_stats) / len(semantic_stats) if semantic_stats else 0
        
        return {
            'detection_performance': {
                'average_precision': round(avg_precision, 3),
                'average_recall': round(avg_recall, 3),
                'average_f1_score': round(avg_f1, 3)
            },
            'semantic_analysis_performance': {
                'average_chunks_per_sentence': round(avg_chunks, 2),
                'average_confidence': round(avg_confidence, 3),
                'average_semantic_completeness': round(avg_completeness, 3)
            },
            'error_distribution': {
                'samples_with_split_errors': len([r for r in results if r['semantic_parsing']['split_errors']]),
                'samples_with_semantic_breaks': len([r for r in results if r['semantic_analysis']['total_chunks'] > 1])
            }
        }
    
    def print_summary_report(self, results: Dict):
        """打印摘要报告"""
        print(f"\n{'='*60}")
        print(f"📊 Split语义分析摘要报告")
        print(f"{'='*60}")
        
        info = results['processing_info']
        stats = results['overall_statistics']
        
        print(f"\n📈 处理信息:")
        print(f"  总样本数: {info['total_samples']}")
        print(f"  成功分析: {info['successful_analyses']}")
        print(f"  总处理时间: {info['total_processing_time']}s")
        print(f"  平均处理时间: {info['average_processing_time']}s")
        
        if 'detection_performance' in stats:
            det_perf = stats['detection_performance']
            print(f"\n🎯 检测性能:")
            print(f"  平均精确率: {det_perf['average_precision']}")
            print(f"  平均召回率: {det_perf['average_recall']}")
            print(f"  平均F1分数: {det_perf['average_f1_score']}")
        
        if 'semantic_analysis_performance' in stats:
            sem_perf = stats['semantic_analysis_performance']
            print(f"\n🔗 语义分析性能:")
            print(f"  平均语义块数: {sem_perf['average_chunks_per_sentence']}")
            print(f"  平均置信度: {sem_perf['average_confidence']}")
            print(f"  平均语义完整性: {sem_perf['average_semantic_completeness']}")
        
        if 'error_distribution' in stats:
            err_dist = stats['error_distribution']
            print(f"\n📋 错误分布:")
            print(f"  包含拆分字错误的样本: {err_dist['samples_with_split_errors']}")
            print(f"  包含语义断点的样本: {err_dist['samples_with_semantic_breaks']}")


def main():
    """主函数"""
    print("🔗 Split测试与语义分析集成系统")
    
    # 创建集成器
    integrator = SplitSemanticIntegration()
    
    # 执行批量分析
    output_file = "ChineseErrorCorrector/semantic_parser/split_semantic_analysis_results.json"
    results = integrator.batch_analysis(output_file)
    
    # 打印摘要报告
    integrator.print_summary_report(results)
    
    print(f"\n✅ 分析完成！详细结果已保存到: {output_file}")


if __name__ == '__main__':
    main()
