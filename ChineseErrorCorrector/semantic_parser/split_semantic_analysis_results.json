{"processing_info": {"total_samples": 5, "successful_analyses": 5, "total_processing_time": 0.004, "average_processing_time": 0.001}, "overall_statistics": {"detection_performance": {"average_precision": 1.0, "average_recall": 1.0, "average_f1_score": 1.0}, "semantic_analysis_performance": {"average_chunks_per_sentence": 2.4, "average_confidence": 0.727, "average_semantic_completeness": 0.807}, "error_distribution": {"samples_with_split_errors": 4, "samples_with_semantic_breaks": 5}}, "detailed_results": [{"sample_info": {"id": 1, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "error_type": "split_character", "split_info": {"白勺": "的"}}, "semantic_parsing": {"input_text": "乌兰巴托不到4百年白勺历史。", "split_errors": [{"position": [9, 11], "error_text": "白勺", "correct_text": "的"}], "semantic_chunks": [{"chunk_id": 1, "text": "乌兰巴托不到4百年", "position": [0, 9], "chunk_type": "error_containing", "confidence": 0.6, "word_segmentation": [["乌兰巴托", "nrt"], ["不到", "v"], ["4", "x"], ["百年", "m"]], "syntax_tree": {"type": "sentence", "text": "乌兰巴托不到4百年", "children": [{"type": "NP", "words": [["乌兰巴托", "nrt"]]}, {"type": "VP", "words": [["不到", "v"]]}, {"type": "WORD", "word": "4", "pos": "x"}, {"type": "WORD", "word": "百年", "pos": "m"}]}, "tree_visualization": "[S] 乌兰巴托不到4百年\n  [NP] 乌兰巴托\n  [VP] 不到\n  [x] 4\n  [m] 百年"}, {"chunk_id": 2, "text": "白勺历史。", "position": [9, 14], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["白勺", "nr"], ["历史", "n"], ["。", "x"]], "syntax_tree": {"type": "sentence", "text": "白勺历史。", "children": [{"type": "NP", "words": [["白勺", "nr"], ["历史", "n"]]}, {"type": "WORD", "word": "。", "pos": "x"}]}, "tree_visualization": "[S] 白勺历史。\n  [NP] 白勺 历史\n  [x] 。"}], "summary": {"total_chunks": 2, "split_errors_found": 1, "has_semantic_breaks": false}}, "detection_accuracy": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "true_positives": 1, "false_positives": 0, "false_negatives": 0, "expected_errors": {"白勺": "的"}, "detected_errors": {"白勺": "的"}}, "semantic_analysis": {"total_chunks": 2, "average_confidence": 0.7, "chunk_type_distribution": {"error_containing": 1, "normal": 1}, "semantic_completeness": 0.767, "has_error_chunks": true}, "recommendations": ["检测到拆分字错误，建议进行纠错处理", "有1个低置信度语义块，建议人工审核"], "processing_time": 0.001}, {"sample_info": {"id": 2, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木图，我觉得有几个特点。", "error_type": "split_character", "split_info": {"牛寺": "特"}}, "semantic_parsing": {"input_text": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "split_errors": [{"position": [17, 19], "error_text": "牛寺", "correct_text": "特"}], "semantic_chunks": [{"chunk_id": 1, "text": "我为什么喜欢阿拉木图，我觉得有几个", "position": [0, 17], "chunk_type": "error_containing", "confidence": 0.6, "word_segmentation": [["我", "r"], ["为什么", "r"], ["喜欢", "v"], ["阿拉木图", "ns"], ["，", "x"], ["我", "r"], ["觉得", "v"], ["有", "v"], ["几个", "m"]], "syntax_tree": {"type": "sentence", "text": "我为什么喜欢阿拉木图，我觉得有几个", "children": [{"type": "WORD", "word": "我", "pos": "r"}, {"type": "WORD", "word": "为什么", "pos": "r"}, {"type": "VP", "words": [["喜欢", "v"]]}, {"type": "NP", "words": [["阿拉木图", "ns"]]}, {"type": "WORD", "word": "，", "pos": "x"}, {"type": "WORD", "word": "我", "pos": "r"}, {"type": "VP", "words": [["觉得", "v"], ["有", "v"]]}, {"type": "WORD", "word": "几个", "pos": "m"}]}, "tree_visualization": "[S] 我为什么喜欢阿拉木图，我觉得有几个\n  [r] 我\n  [r] 为什么\n  [VP] 喜欢\n  [NP] 阿拉木图\n  [x] ，\n  [r] 我\n  [VP] 觉得 有\n  [m] 几个"}, {"chunk_id": 2, "text": "牛寺点。", "position": [17, 21], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["牛寺", "nr"], ["点", "q"], ["。", "x"]], "syntax_tree": {"type": "sentence", "text": "牛寺点。", "children": [{"type": "NP", "words": [["牛寺", "nr"]]}, {"type": "WORD", "word": "点", "pos": "q"}, {"type": "WORD", "word": "。", "pos": "x"}]}, "tree_visualization": "[S] 牛寺点。\n  [NP] 牛寺\n  [q] 点\n  [x] 。"}], "summary": {"total_chunks": 2, "split_errors_found": 1, "has_semantic_breaks": false}}, "detection_accuracy": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "true_positives": 1, "false_positives": 0, "false_negatives": 0, "expected_errors": {"牛寺": "特"}, "detected_errors": {"牛寺": "特"}}, "semantic_analysis": {"total_chunks": 2, "average_confidence": 0.7, "chunk_type_distribution": {"error_containing": 1, "normal": 1}, "semantic_completeness": 0.767, "has_error_chunks": true}, "recommendations": ["检测到拆分字错误，建议进行纠错处理", "有1个低置信度语义块，建议人工审核"], "processing_time": 0.0}, {"sample_info": {"id": 3, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你先择几个个性重点培养，最终形成自己独特的男人魅力。", "error_type": "split_character", "split_info": {"鬼未力": "魅力"}}, "semantic_parsing": {"input_text": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "split_errors": [{"position": [23, 26], "error_text": "鬼未力", "correct_text": "魅力"}], "semantic_chunks": [{"chunk_id": 1, "text": "你先择几个个性重点培养，", "position": [0, 12], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["你", "r"], ["先择", "v"], ["几个", "m"], ["个性", "n"], ["重点", "n"], ["培养", "v"], ["，", "x"]], "syntax_tree": {"type": "sentence", "text": "你先择几个个性重点培养，", "children": [{"type": "WORD", "word": "你", "pos": "r"}, {"type": "VP", "words": [["先择", "v"]]}, {"type": "WORD", "word": "几个", "pos": "m"}, {"type": "NP", "words": [["个性", "n"], ["重点", "n"]]}, {"type": "VP", "words": [["培养", "v"]]}, {"type": "WORD", "word": "，", "pos": "x"}]}, "tree_visualization": "[S] 你先择几个个性重点培养，\n  [r] 你\n  [VP] 先择\n  [m] 几个\n  [NP] 个性 重点\n  [VP] 培养\n  [x] ，"}, {"chunk_id": 2, "text": "最终形成自己独特的男人", "position": [12, 23], "chunk_type": "error_containing", "confidence": 0.6, "word_segmentation": [["最终", "d"], ["形成", "v"], ["自己", "r"], ["独特", "a"], ["的", "uj"], ["男人", "n"]], "syntax_tree": {"type": "sentence", "text": "最终形成自己独特的男人", "children": [{"type": "WORD", "word": "最终", "pos": "d"}, {"type": "VP", "words": [["形成", "v"]]}, {"type": "WORD", "word": "自己", "pos": "r"}, {"type": "AP", "words": [["独特", "a"]]}, {"type": "WORD", "word": "的", "pos": "uj"}, {"type": "NP", "words": [["男人", "n"]]}]}, "tree_visualization": "[S] 最终形成自己独特的男人\n  [d] 最终\n  [VP] 形成\n  [r] 自己\n  [AP] 独特\n  [uj] 的\n  [NP] 男人"}, {"chunk_id": 3, "text": "鬼未力。", "position": [23, 27], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["鬼未力", "i"], ["。", "x"]], "syntax_tree": {"type": "sentence", "text": "鬼未力。", "children": [{"type": "WORD", "word": "鬼未力", "pos": "i"}, {"type": "WORD", "word": "。", "pos": "x"}]}, "tree_visualization": "[S] 鬼未力。\n  [i] 鬼未力\n  [x] 。"}], "summary": {"total_chunks": 3, "split_errors_found": 1, "has_semantic_breaks": true}}, "detection_accuracy": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "true_positives": 1, "false_positives": 0, "false_negatives": 0, "expected_errors": {"鬼未力": "魅力"}, "detected_errors": {"鬼未力": "魅力"}}, "semantic_analysis": {"total_chunks": 3, "average_confidence": 0.733, "chunk_type_distribution": {"normal": 2, "error_containing": 1}, "semantic_completeness": 0.8, "has_error_chunks": true}, "recommendations": ["检测到拆分字错误，建议进行纠错处理", "有1个低置信度语义块，建议人工审核"], "processing_time": 0.001}, {"sample_info": {"id": 4, "source": "这个问题很复杂，但是我们需要仔细分析，因此要分步骤解决。", "target": "这个问题很复杂，但是我们需要仔细分析，因此要分步骤解决。", "error_type": "semantic_break", "split_info": {}}, "semantic_parsing": {"input_text": "这个问题很复杂，但是我们需要仔细分析，因此要分步骤解决。", "split_errors": [], "semantic_chunks": [{"chunk_id": 1, "text": "这个问题很复杂，", "position": [0, 8], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["这个", "r"], ["问题", "n"], ["很", "zg"], ["复杂", "a"], ["，", "x"]], "syntax_tree": {"type": "sentence", "text": "这个问题很复杂，", "children": [{"type": "WORD", "word": "这个", "pos": "r"}, {"type": "NP", "words": [["问题", "n"]]}, {"type": "WORD", "word": "很", "pos": "zg"}, {"type": "AP", "words": [["复杂", "a"]]}, {"type": "WORD", "word": "，", "pos": "x"}]}, "tree_visualization": "[S] 这个问题很复杂，\n  [r] 这个\n  [NP] 问题\n  [zg] 很\n  [AP] 复杂\n  [x] ，"}, {"chunk_id": 2, "text": "但是我们需要仔细分析，", "position": [8, 19], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["但是", "c"], ["我们", "r"], ["需要", "v"], ["仔细分析", "n"], ["，", "x"]], "syntax_tree": {"type": "sentence", "text": "但是我们需要仔细分析，", "children": [{"type": "WORD", "word": "但是", "pos": "c"}, {"type": "WORD", "word": "我们", "pos": "r"}, {"type": "VP", "words": [["需要", "v"]]}, {"type": "NP", "words": [["仔细分析", "n"]]}, {"type": "WORD", "word": "，", "pos": "x"}]}, "tree_visualization": "[S] 但是我们需要仔细分析，\n  [c] 但是\n  [r] 我们\n  [VP] 需要\n  [NP] 仔细分析\n  [x] ，"}, {"chunk_id": 3, "text": "因此要分步骤解决。", "position": [19, 28], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["因此", "c"], ["要", "v"], ["分", "q"], ["步骤", "n"], ["解决", "v"], ["。", "x"]], "syntax_tree": {"type": "sentence", "text": "因此要分步骤解决。", "children": [{"type": "WORD", "word": "因此", "pos": "c"}, {"type": "VP", "words": [["要", "v"]]}, {"type": "WORD", "word": "分", "pos": "q"}, {"type": "NP", "words": [["步骤", "n"]]}, {"type": "VP", "words": [["解决", "v"]]}, {"type": "WORD", "word": "。", "pos": "x"}]}, "tree_visualization": "[S] 因此要分步骤解决。\n  [c] 因此\n  [VP] 要\n  [q] 分\n  [NP] 步骤\n  [VP] 解决\n  [x] 。"}], "summary": {"total_chunks": 3, "split_errors_found": 0, "has_semantic_breaks": true}}, "detection_accuracy": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "true_positives": 0, "false_positives": 0, "false_negatives": 0}, "semantic_analysis": {"total_chunks": 3, "average_confidence": 0.8, "chunk_type_distribution": {"normal": 3}, "semantic_completeness": 0.933, "has_error_chunks": false}, "recommendations": ["文本语义结构良好，无需特殊处理"], "processing_time": 0.001}, {"sample_info": {"id": 5, "source": "如果天气好白勺话，我们就去公园散步，否则就在家里看书。", "target": "如果天气好的话，我们就去公园散步，否则就在家里看书。", "error_type": "mixed", "split_info": {"白勺": "的"}}, "semantic_parsing": {"input_text": "如果天气好白勺话，我们就去公园散步，否则就在家里看书。", "split_errors": [{"position": [5, 7], "error_text": "白勺", "correct_text": "的"}], "semantic_chunks": [{"chunk_id": 1, "text": "如果天气好", "position": [0, 5], "chunk_type": "error_containing", "confidence": 0.6, "word_segmentation": [["如果", "c"], ["天气", "n"], ["好", "a"]], "syntax_tree": {"type": "sentence", "text": "如果天气好", "children": [{"type": "WORD", "word": "如果", "pos": "c"}, {"type": "NP", "words": [["天气", "n"]]}, {"type": "AP", "words": [["好", "a"]]}]}, "tree_visualization": "[S] 如果天气好\n  [c] 如果\n  [NP] 天气\n  [AP] 好"}, {"chunk_id": 2, "text": "白勺话，我们就去公园散步，否则就在家里看书。", "position": [5, 27], "chunk_type": "normal", "confidence": 0.8, "word_segmentation": [["白勺", "nr"], ["话", "n"], ["，", "x"], ["我们", "r"], ["就", "d"], ["去", "v"], ["公园", "n"], ["散步", "n"], ["，", "x"], ["否则", "c"], ["就", "d"], ["在", "p"], ["家里", "s"], ["看书", "v"], ["。", "x"]], "syntax_tree": {"type": "sentence", "text": "白勺话，我们就去公园散步，否则就在家里看书。", "children": [{"type": "NP", "words": [["白勺", "nr"], ["话", "n"]]}, {"type": "WORD", "word": "，", "pos": "x"}, {"type": "WORD", "word": "我们", "pos": "r"}, {"type": "WORD", "word": "就", "pos": "d"}, {"type": "VP", "words": [["去", "v"]]}, {"type": "NP", "words": [["公园", "n"], ["散步", "n"]]}, {"type": "WORD", "word": "，", "pos": "x"}, {"type": "WORD", "word": "否则", "pos": "c"}, {"type": "WORD", "word": "就", "pos": "d"}, {"type": "WORD", "word": "在", "pos": "p"}, {"type": "WORD", "word": "家里", "pos": "s"}, {"type": "VP", "words": [["看书", "v"]]}, {"type": "WORD", "word": "。", "pos": "x"}]}, "tree_visualization": "[S] 白勺话，我们就去公园散步，否则就在家里看书。\n  [NP] 白勺 话\n  [x] ，\n  [r] 我们\n  [d] 就\n  [VP] 去\n  [NP] 公园 散步\n  [x] ，\n  [c] 否则\n  [d] 就\n  [p] 在\n  [s] 家里\n  [VP] 看书\n  [x] 。"}], "summary": {"total_chunks": 2, "split_errors_found": 1, "has_semantic_breaks": true}}, "detection_accuracy": {"precision": 1.0, "recall": 1.0, "f1_score": 1.0, "true_positives": 1, "false_positives": 0, "false_negatives": 0, "expected_errors": {"白勺": "的"}, "detected_errors": {"白勺": "的"}}, "semantic_analysis": {"total_chunks": 2, "average_confidence": 0.7, "chunk_type_distribution": {"error_containing": 1, "normal": 1}, "semantic_completeness": 0.767, "has_error_chunks": true}, "recommendations": ["检测到拆分字错误，建议进行纠错处理", "有1个低置信度语义块，建议人工审核"], "processing_time": 0.001}]}