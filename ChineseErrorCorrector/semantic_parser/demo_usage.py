#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语义分割与句法解析系统使用演示
"""

import os
import sys
import json

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.semantic_parser.semantic_parser_api import SemanticParserAPI


def demo_basic_usage():
    """基本使用演示"""
    print("🚀 基本使用演示")
    print("=" * 50)
    
    # 创建解析器
    parser = SemanticParserAPI()
    
    # 测试句子
    test_sentence = "乌兰巴托不到4百年白勺历史，但是这个城市有很多牛寺点。"
    
    print(f"📝 输入句子: {test_sentence}")
    
    # 解析句子，返回前3个语义块
    result = parser.parse_with_syntax_trees(test_sentence, max_chunks=3)
    
    print(f"\n📊 分析摘要:")
    print(f"  总语义块数: {result['summary']['total_chunks']}")
    print(f"  拆分字错误数: {result['summary']['split_errors_found']}")
    print(f"  包含语义断点: {result['summary']['has_semantic_breaks']}")
    
    # 显示拆分字错误
    if result['split_errors']:
        print(f"\n❌ 检测到的拆分字错误:")
        for error in result['split_errors']:
            print(f"  位置 {error['position']}: '{error['error_text']}' → '{error['correct_text']}'")
    
    # 显示语义块和句法树
    print(f"\n📝 语义块与句法解析树:")
    for chunk in result['semantic_chunks']:
        print(f"\n--- 语义块 {chunk['chunk_id']} ---")
        print(f"文本: \"{chunk['text']}\"")
        print(f"位置: {chunk['position']}")
        print(f"类型: {chunk['chunk_type']} (置信度: {chunk['confidence']})")
        print(f"词性标注: {chunk['word_segmentation']}")
        print(f"句法树:")
        print(chunk['tree_visualization'])


def demo_split_error_detection():
    """拆分字错误检测演示"""
    print("\n🔍 拆分字错误检测演示")
    print("=" * 50)
    
    parser = SemanticParserAPI()
    
    # 包含各种拆分字错误的句子
    test_sentences = [
        "这是一个白勺例子。",  # 白勺 → 的
        "我们需要牛寺别关注这个问题。",  # 牛寺 → 特
        "他有很强白勺鬼未力。",  # 白勺 → 的, 鬼未力 → 魅力
        "这个木几器很好用。",  # 木几 → 机
        "请扌丁开这个门。"  # 扌丁 → 打
    ]
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n测试 {i}: {sentence}")
        
        # 仅检测拆分字错误
        errors = parser.detect_split_errors_only(sentence)
        
        if errors:
            print("  检测结果:")
            for error in errors:
                print(f"    位置 {error['position']}: '{error['error_text']}' → '{error['correct_text']}'")
        else:
            print("  未检测到拆分字错误")


def demo_semantic_segmentation():
    """语义分割演示"""
    print("\n🔗 语义分割演示")
    print("=" * 50)
    
    parser = SemanticParserAPI()
    
    # 包含语义断点的句子
    test_sentences = [
        "天气很好，但是我不想出门，因此决定在家看书。",
        "如果明天下雨，我们就取消野餐，否则按原计划进行。",
        "首先准备材料，然后开始制作，最后进行装饰。",
        "这个方案有优点，同时也有缺点，需要综合考虑。"
    ]
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n测试 {i}: {sentence}")
        
        # 按语义断点分割
        chunks = parser.segment_by_semantic_breaks(sentence)
        
        print(f"  分割为 {len(chunks)} 个语义块:")
        for chunk in chunks:
            print(f"    块{chunk['chunk_id']}: \"{chunk['text']}\" (置信度: {chunk['confidence']})")


def demo_syntax_tree_generation():
    """句法树生成演示"""
    print("\n🌳 句法树生成演示")
    print("=" * 50)
    
    parser = SemanticParserAPI()
    
    # 不同结构的句子
    test_sentences = [
        "小明在公园里踢足球。",  # 简单句
        "美丽的花朵在春天盛开。",  # 形容词修饰
        "老师给学生们讲解了这个复杂的数学问题。",  # 复杂句
        "如果天气好，我们就去爬山。"  # 条件句
    ]
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n测试 {i}: {sentence}")
        
        result = parser.parse_with_syntax_trees(sentence, max_chunks=2)
        
        for chunk in result['semantic_chunks']:
            print(f"\n  语义块: \"{chunk['text']}\"")
            print(f"  句法树:")
            print(f"  {chunk['tree_visualization']}")


def demo_batch_processing():
    """批量处理演示"""
    print("\n📦 批量处理演示")
    print("=" * 50)
    
    # 创建测试数据
    test_data = [
        "乌兰巴托不到4百年白勺历史。",
        "我为什么喜欢阿拉木图，我觉得有几个牛寺点。",
        "这个问题很复杂，但是我们需要仔细分析。",
        "如果天气好白勺话，我们就去公园散步。"
    ]
    
    # 保存测试数据到文件
    test_file = "test_sentences.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        for sentence in test_data:
            f.write(sentence + '\n')
    
    print(f"📝 创建测试文件: {test_file}")
    print(f"包含 {len(test_data)} 个测试句子")
    
    # 批量处理
    from ChineseErrorCorrector.semantic_parser.semantic_parser_api import batch_parse_from_file
    
    output_file = "batch_results.json"
    batch_parse_from_file(test_file, output_file, max_chunks=2)
    
    # 读取并显示结果摘要
    with open(output_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print(f"\n📊 批量处理结果:")
    print(f"  处理句子数: {results['processing_info']['total_sentences']}")
    print(f"  成功解析数: {results['processing_info']['successful_parses']}")
    
    # 统计拆分字错误
    total_errors = sum(len(r.get('split_errors', [])) for r in results['results'])
    print(f"  总拆分字错误: {total_errors}")
    
    # 清理临时文件
    os.remove(test_file)
    os.remove(output_file)
    print(f"🧹 清理临时文件完成")


def demo_integration_with_existing_system():
    """与现有系统集成演示"""
    print("\n🔗 与现有Split测试系统集成演示")
    print("=" * 50)
    
    try:
        from ChineseErrorCorrector.semantic_parser.split_semantic_integration import SplitSemanticIntegration
        
        # 创建集成器
        integrator = SplitSemanticIntegration()
        
        # 分析单个样本
        sample = {
            "id": 999,
            "source": "这是一个测试白勺句子，包含牛寺殊白勺错误。",
            "target": "这是一个测试的句子，包含特殊的错误。",
            "error_type": "split_character",
            "split_info": {"白勺": "的", "牛寺": "特"}
        }
        
        print(f"📝 测试样本: {sample['source']}")
        
        # 执行增强分析
        result = integrator.enhanced_split_analysis(sample, max_chunks=3)
        
        print(f"\n📊 分析结果:")
        print(f"  检测准确性:")
        acc = result['detection_accuracy']
        print(f"    精确率: {acc['precision']}")
        print(f"    召回率: {acc['recall']}")
        print(f"    F1分数: {acc['f1_score']}")
        
        print(f"\n  语义分析:")
        sem = result['semantic_analysis']
        print(f"    语义块数: {sem['total_chunks']}")
        print(f"    平均置信度: {sem['average_confidence']}")
        print(f"    语义完整性: {sem['semantic_completeness']}")
        
        print(f"\n  改进建议:")
        for rec in result['recommendations']:
            print(f"    • {rec}")
            
    except ImportError as e:
        print(f"❌ 集成模块导入失败: {e}")
        print("请确保所有依赖模块已正确安装")


def main():
    """主演示函数"""
    print("🎯 语义分割与句法解析系统演示")
    print("=" * 60)
    
    # 运行各种演示
    demo_basic_usage()
    demo_split_error_detection()
    demo_semantic_segmentation()
    demo_syntax_tree_generation()
    demo_batch_processing()
    demo_integration_with_existing_system()
    
    print(f"\n✅ 所有演示完成!")
    print(f"\n📚 更多信息请参考:")
    print(f"  - README_Semantic_Parser.md: 详细文档")
    print(f"  - semantic_parser_api.py: API接口")
    print(f"  - split_semantic_integration.py: 系统集成")


if __name__ == '__main__':
    main()
