#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语义分割与句法解析器
识别拆分字错误和语义断点，进行句子切块和句法解析
"""

import os
import sys
import re
import json
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Tuple, Optional
import numpy as np
from dataclasses import dataclass

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)


@dataclass
class SemanticBreakpoint:
    """语义断点信息"""
    position: int           # 断点位置
    break_type: str        # 断点类型: 'split_error', 'semantic_gap', 'syntax_break'
    confidence: float      # 置信度
    description: str       # 描述
    error_tokens: List[str] = None  # 错误token（如果是拆分字错误）


@dataclass
class SemanticChunk:
    """语义块信息"""
    text: str              # 文本内容
    start_pos: int         # 起始位置
    end_pos: int          # 结束位置
    chunk_type: str       # 块类型: 'normal', 'error_containing', 'incomplete'
    confidence: float     # 语义完整性置信度
    pos_tags: List[Tuple[str, str]] = None  # 词性标注
    syntax_tree: Dict = None  # 句法树


class SplitCharacterDetector:
    """拆分字错误检测器"""
    
    def __init__(self):
        # 常见拆分字映射
        self.split_mappings = {
            # 简单拆分
            ('白', '勺'): '的',
            ('木', '寸'): '村', 
            ('火', '丁'): '灯',
            ('亻', '尔'): '你',
            ('氵', '可'): '河',
            ('牛', '寺'): '特',
            ('言', '吾'): '语',
            ('讠', '舌'): '话',
            ('扌', '丁'): '打',
            ('女', '子'): '好',
            
            # 复杂拆分
            ('鬼', '未', '力'): '魅力',
            ('言', '兑'): '说',
            ('亻', '言', '兑'): '说话',
            ('木', '几'): '机',
            ('氵', '工'): '江',
            ('艹', '化'): '花',
            ('忄', '生'): '性',
            ('扌', '求'): '救',
            ('口', '十'): '古',
            ('日', '月'): '明',
        }
        
        # 构建反向映射用于检测
        self.reverse_mappings = {}
        for split_chars, correct_char in self.split_mappings.items():
            if len(split_chars) == 2:
                self.reverse_mappings[correct_char] = split_chars
        
        # 常见部首
        self.radicals = {
            '亻', '氵', '扌', '讠', '忄', '艹', '木', '火', '土', '金', 
            '石', '日', '月', '山', '水', '心', '手', '口', '目', '耳'
        }
    
    def detect_split_errors(self, text: str) -> List[Tuple[int, int, str, str]]:
        """
        检测拆分字错误
        返回: [(start_pos, end_pos, error_text, correct_text), ...]
        """
        errors = []
        
        # 检测连续的可能拆分字符
        i = 0
        while i < len(text):
            # 检查2字符拆分
            if i + 1 < len(text):
                char_pair = (text[i], text[i+1])
                if char_pair in self.split_mappings:
                    correct_char = self.split_mappings[char_pair]
                    errors.append((i, i+2, text[i:i+2], correct_char))
                    i += 2
                    continue
            
            # 检查3字符拆分
            if i + 2 < len(text):
                char_triple = (text[i], text[i+1], text[i+2])
                if char_triple in self.split_mappings:
                    correct_text = self.split_mappings[char_triple]
                    errors.append((i, i+3, text[i:i+3], correct_text))
                    i += 3
                    continue
            
            # 检查部首+字符的组合
            if text[i] in self.radicals and i + 1 < len(text):
                # 简单的部首组合检测
                if self._is_likely_split_combination(text[i], text[i+1]):
                    errors.append((i, i+2, text[i:i+2], f"[可能的拆分字: {text[i:i+2]}]"))
                    i += 2
                    continue
            
            i += 1
        
        return errors
    
    def _is_likely_split_combination(self, char1: str, char2: str) -> bool:
        """判断是否可能是拆分字组合"""
        # 简单的启发式规则
        if char1 in self.radicals:
            # 部首后跟常见字符
            common_chars = {'寸', '丁', '可', '寺', '吾', '舌', '几', '工', '化', '生', '求', '十'}
            return char2 in common_chars
        return False


class SemanticContinuityAnalyzer:
    """语义连续性分析器"""
    
    def __init__(self):
        # 语义断裂指示词
        self.break_indicators = {
            'topic_change': ['但是', '然而', '不过', '另外', '此外', '而且', '同时'],
            'temporal_break': ['然后', '接着', '后来', '最后', '最终', '现在', '当时'],
            'logical_break': ['因此', '所以', '因为', '由于', '如果', '假如', '虽然'],
            'contrast': ['相反', '相对', '与此同时', '另一方面', '反之'],
        }
        
        # 语义完整性检查词汇
        self.completeness_indicators = {
            'incomplete_start': ['如果', '当', '虽然', '因为', '由于', '为了'],
            'incomplete_end': ['的', '了', '着', '过', '呢', '吗', '吧'],
            'sentence_enders': ['。', '！', '？', '；'],
        }
    
    def analyze_semantic_continuity(self, text: str) -> List[SemanticBreakpoint]:
        """分析语义连续性，识别语义断点"""
        breakpoints = []
        
        # 使用jieba进行分词和词性标注
        words = list(pseg.cut(text))
        
        # 检查语义断裂指示词
        for i, (word, pos) in enumerate(words):
            for break_type, indicators in self.break_indicators.items():
                if word in indicators:
                    # 计算断点位置
                    position = sum(len(w) for w, p in words[:i])
                    confidence = self._calculate_break_confidence(words, i, break_type)
                    
                    breakpoint = SemanticBreakpoint(
                        position=position,
                        break_type='semantic_gap',
                        confidence=confidence,
                        description=f"语义转折词: {word} ({break_type})"
                    )
                    breakpoints.append(breakpoint)
        
        # 检查语法不完整性
        self._check_syntactic_completeness(text, words, breakpoints)
        
        return breakpoints
    
    def _calculate_break_confidence(self, words: List[Tuple[str, str]], 
                                  position: int, break_type: str) -> float:
        """计算断点置信度"""
        base_confidence = 0.7
        
        # 根据上下文调整置信度
        if position > 0 and position < len(words) - 1:
            prev_word, prev_pos = words[position - 1]
            next_word, next_pos = words[position + 1]
            
            # 如果前后词性合理，提高置信度
            if prev_pos in ['n', 'v', 'a'] and next_pos in ['n', 'v', 'a']:
                base_confidence += 0.2
        
        return min(base_confidence, 1.0)
    
    def _check_syntactic_completeness(self, text: str, words: List[Tuple[str, str]], 
                                    breakpoints: List[SemanticBreakpoint]):
        """检查语法完整性"""
        # 检查句子是否以不完整的结构开始或结束
        if words:
            first_word, first_pos = words[0]
            if first_word in self.completeness_indicators['incomplete_start']:
                breakpoint = SemanticBreakpoint(
                    position=0,
                    break_type='syntax_break',
                    confidence=0.8,
                    description=f"不完整的句子开始: {first_word}"
                )
                breakpoints.append(breakpoint)
            
            last_word, last_pos = words[-1]
            if last_word in self.completeness_indicators['incomplete_end']:
                position = len(text) - len(last_word)
                breakpoint = SemanticBreakpoint(
                    position=position,
                    break_type='syntax_break',
                    confidence=0.8,
                    description=f"不完整的句子结尾: {last_word}"
                )
                breakpoints.append(breakpoint)


class SemanticSegmentationParser:
    """语义分割与句法解析器"""
    
    def __init__(self):
        self.split_detector = SplitCharacterDetector()
        self.continuity_analyzer = SemanticContinuityAnalyzer()
        
        # 初始化jieba
        jieba.initialize()
    
    def parse_sentence(self, text: str, max_chunks: int = None) -> Dict:
        """
        解析句子，识别语义断点并进行切块
        
        Args:
            text: 输入文本
            max_chunks: 最大返回的语义块数量
            
        Returns:
            解析结果字典
        """
        print(f"🔍 开始解析句子: {text}")
        
        # 1. 检测拆分字错误
        split_errors = self.split_detector.detect_split_errors(text)
        print(f"📝 检测到 {len(split_errors)} 个拆分字错误")
        
        # 2. 分析语义连续性
        semantic_breakpoints = self.continuity_analyzer.analyze_semantic_continuity(text)
        print(f"🔗 检测到 {len(semantic_breakpoints)} 个语义断点")
        
        # 3. 合并所有断点
        all_breakpoints = []
        
        # 添加拆分字错误断点
        for start, end, error_text, correct_text in split_errors:
            breakpoint = SemanticBreakpoint(
                position=start,
                break_type='split_error',
                confidence=0.9,
                description=f"拆分字错误: {error_text} → {correct_text}",
                error_tokens=[error_text, correct_text]
            )
            all_breakpoints.append(breakpoint)
        
        # 添加语义断点
        all_breakpoints.extend(semantic_breakpoints)
        
        # 4. 按位置排序断点
        all_breakpoints.sort(key=lambda x: x.position)
        
        # 5. 基于断点进行文本切块
        chunks = self._segment_text(text, all_breakpoints)
        
        # 6. 对每个块进行句法分析
        for chunk in chunks:
            chunk.pos_tags = list(pseg.cut(chunk.text))
            chunk.syntax_tree = self._build_syntax_tree(chunk.text, chunk.pos_tags)
        
        # 7. 限制返回的块数量
        if max_chunks and len(chunks) > max_chunks:
            chunks = chunks[:max_chunks]
            print(f"📊 限制输出前 {max_chunks} 个语义块")
        
        # 8. 构建结果
        result = {
            'original_text': text,
            'split_errors': [
                {
                    'position': (start, end),
                    'error_text': error_text,
                    'correct_text': correct_text
                }
                for start, end, error_text, correct_text in split_errors
            ],
            'semantic_breakpoints': [
                {
                    'position': bp.position,
                    'type': bp.break_type,
                    'confidence': bp.confidence,
                    'description': bp.description,
                    'error_tokens': bp.error_tokens
                }
                for bp in all_breakpoints
            ],
            'semantic_chunks': [
                {
                    'chunk_id': i,
                    'text': chunk.text,
                    'position': (chunk.start_pos, chunk.end_pos),
                    'type': chunk.chunk_type,
                    'confidence': chunk.confidence,
                    'pos_tags': [(word, pos) for word, pos in chunk.pos_tags] if chunk.pos_tags else [],
                    'syntax_tree': chunk.syntax_tree
                }
                for i, chunk in enumerate(chunks)
            ],
            'analysis_summary': {
                'total_chunks': len(chunks),
                'split_errors_count': len(split_errors),
                'semantic_breaks_count': len(semantic_breakpoints),
                'avg_chunk_length': np.mean([len(chunk.text) for chunk in chunks]) if chunks else 0
            }
        }
        
        print(f"✅ 解析完成，生成 {len(chunks)} 个语义块")
        return result
    
    def _segment_text(self, text: str, breakpoints: List[SemanticBreakpoint]) -> List[SemanticChunk]:
        """基于断点分割文本"""
        if not breakpoints:
            # 没有断点，整个文本作为一个块
            chunk = SemanticChunk(
                text=text,
                start_pos=0,
                end_pos=len(text),
                chunk_type='normal',
                confidence=1.0
            )
            return [chunk]
        
        chunks = []
        start_pos = 0
        
        for breakpoint in breakpoints:
            if breakpoint.position > start_pos:
                # 创建断点前的块
                chunk_text = text[start_pos:breakpoint.position]
                chunk_type = 'error_containing' if breakpoint.break_type == 'split_error' else 'normal'
                
                chunk = SemanticChunk(
                    text=chunk_text,
                    start_pos=start_pos,
                    end_pos=breakpoint.position,
                    chunk_type=chunk_type,
                    confidence=self._calculate_chunk_confidence(chunk_text, breakpoint)
                )
                chunks.append(chunk)
            
            start_pos = breakpoint.position
        
        # 添加最后一个块
        if start_pos < len(text):
            chunk_text = text[start_pos:]
            chunk = SemanticChunk(
                text=chunk_text,
                start_pos=start_pos,
                end_pos=len(text),
                chunk_type='normal',
                confidence=0.8
            )
            chunks.append(chunk)
        
        return chunks
    
    def _calculate_chunk_confidence(self, chunk_text: str, breakpoint: SemanticBreakpoint) -> float:
        """计算语义块的置信度"""
        base_confidence = 0.8
        
        # 根据断点类型调整
        if breakpoint.break_type == 'split_error':
            base_confidence = 0.6  # 包含错误的块置信度较低
        elif breakpoint.break_type == 'syntax_break':
            base_confidence = 0.5  # 语法不完整的块置信度更低
        
        # 根据块长度调整
        if len(chunk_text) < 3:
            base_confidence *= 0.7  # 太短的块置信度降低
        elif len(chunk_text) > 20:
            base_confidence *= 1.1  # 适中长度的块置信度提高
        
        return min(base_confidence, 1.0)
    
    def _build_syntax_tree(self, text: str, pos_tags: List[Tuple[str, str]]) -> Dict:
        """构建简单的句法树"""
        # 简化的句法分析
        tree = {
            'type': 'sentence',
            'text': text,
            'children': []
        }
        
        # 按词性分组
        current_phrase = None
        current_phrase_type = None
        
        for word, pos in pos_tags:
            # 简单的短语识别
            if pos.startswith('n'):  # 名词短语
                if current_phrase_type != 'NP':
                    if current_phrase:
                        tree['children'].append(current_phrase)
                    current_phrase = {'type': 'NP', 'words': []}
                    current_phrase_type = 'NP'
                current_phrase['words'].append((word, pos))
            
            elif pos.startswith('v'):  # 动词短语
                if current_phrase_type != 'VP':
                    if current_phrase:
                        tree['children'].append(current_phrase)
                    current_phrase = {'type': 'VP', 'words': []}
                    current_phrase_type = 'VP'
                current_phrase['words'].append((word, pos))
            
            elif pos.startswith('a'):  # 形容词短语
                if current_phrase_type != 'AP':
                    if current_phrase:
                        tree['children'].append(current_phrase)
                    current_phrase = {'type': 'AP', 'words': []}
                    current_phrase_type = 'AP'
                current_phrase['words'].append((word, pos))
            
            else:  # 其他词性
                if current_phrase:
                    tree['children'].append(current_phrase)
                    current_phrase = None
                    current_phrase_type = None
                
                tree['children'].append({
                    'type': 'WORD',
                    'word': word,
                    'pos': pos
                })
        
        # 添加最后一个短语
        if current_phrase:
            tree['children'].append(current_phrase)
        
        return tree


def demo_semantic_parsing():
    """演示语义分割和句法解析"""
    parser = SemanticSegmentationParser()
    
    # 测试句子
    test_sentences = [
        "乌兰巴托不到4百年白勺历史。",
        "我为什么喜欢阿拉木图，我觉得有几个牛寺点。",
        "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。",
        "这是一个测试句子，但是它包含了一些语义断点，因此需要分割。",
        "如果天气好的话，我们就去公园散步，否则就在家里看书。"
    ]
    
    for i, sentence in enumerate(test_sentences):
        print(f"\n{'='*60}")
        print(f"测试句子 {i+1}: {sentence}")
        print(f"{'='*60}")
        
        result = parser.parse_sentence(sentence, max_chunks=3)
        
        print(f"\n📊 分析结果:")
        print(f"  拆分字错误: {len(result['split_errors'])} 个")
        print(f"  语义断点: {len(result['semantic_breakpoints'])} 个")
        print(f"  语义块: {len(result['semantic_chunks'])} 个")
        
        print(f"\n🔍 拆分字错误:")
        for error in result['split_errors']:
            print(f"  位置 {error['position']}: {error['error_text']} → {error['correct_text']}")
        
        print(f"\n🔗 语义断点:")
        for bp in result['semantic_breakpoints']:
            print(f"  位置 {bp['position']}: {bp['type']} - {bp['description']} (置信度: {bp['confidence']:.2f})")
        
        print(f"\n📝 语义块:")
        for chunk in result['semantic_chunks']:
            print(f"  块 {chunk['chunk_id']}: \"{chunk['text']}\"")
            print(f"    位置: {chunk['position']}, 类型: {chunk['type']}, 置信度: {chunk['confidence']:.2f}")
            print(f"    词性标注: {chunk['pos_tags']}")
            print(f"    句法树: {json.dumps(chunk['syntax_tree'], ensure_ascii=False, indent=2)}")


if __name__ == '__main__':
    demo_semantic_parsing()
