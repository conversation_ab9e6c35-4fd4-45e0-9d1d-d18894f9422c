#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
句法分析树可视化工具
支持多种可视化方式：文本图、图形图、网络图等
"""

import os
import sys
import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import networkx as nx
from typing import Dict, List, Tuple
import numpy as np

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class SyntaxTreeVisualizer:
    """句法分析树可视化器"""
    
    def __init__(self):
        # 节点类型颜色映射
        self.node_colors = {
            'S': '#FF6B6B',      # 句子 - 红色
            'NP': '#4ECDC4',     # 名词短语 - 青色
            'VP': '#45B7D1',     # 动词短语 - 蓝色
            'AP': '#96CEB4',     # 形容词短语 - 绿色
            'WORD': '#FFEAA7',   # 词汇 - 黄色
            'default': '#DDA0DD' # 默认 - 紫色
        }
        
        # 词性标签颜色
        self.pos_colors = {
            'n': '#FFB6C1',   # 名词 - 浅粉色
            'v': '#87CEEB',   # 动词 - 天蓝色
            'a': '#98FB98',   # 形容词 - 浅绿色
            'r': '#F0E68C',   # 代词 - 卡其色
            'p': '#DDA0DD',   # 介词 - 紫色
            'c': '#FFA07A',   # 连词 - 浅橙色
            'default': '#E6E6FA'  # 默认 - 薰衣草色
        }
    
    def create_ascii_tree(self, tree: Dict, prefix: str = "", is_last: bool = True) -> str:
        """创建ASCII艺术风格的树形图"""
        if not tree:
            return ""
        
        lines = []
        
        # 根节点
        if tree['type'] == 'sentence':
            lines.append(f"📄 [S] {tree.get('text', '')}")
            children = tree.get('children', [])
        elif tree['type'] in ['NP', 'VP', 'AP']:
            phrase_text = " ".join([word for word, pos in tree.get('words', [])])
            lines.append(f"{prefix}{'└── ' if is_last else '├── '}📦 [{tree['type']}] {phrase_text}")
            children = []
        elif tree['type'] == 'WORD':
            lines.append(f"{prefix}{'└── ' if is_last else '├── '}🔤 [{tree['pos']}] {tree['word']}")
            children = []
        else:
            lines.append(f"{prefix}{'└── ' if is_last else '├── '}❓ [{tree['type']}] {tree.get('word', '')}")
            children = []
        
        # 递归处理子节点
        for i, child in enumerate(children):
            is_child_last = (i == len(children) - 1)
            child_prefix = prefix + ("    " if is_last else "│   ")
            lines.append(self.create_ascii_tree(child, child_prefix, is_child_last))
        
        return "\n".join(filter(None, lines))
    
    def create_graphical_tree(self, tree: Dict, save_path: str = None) -> None:
        """创建图形化的树形图"""
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        
        # 构建NetworkX图
        G = nx.DiGraph()
        pos_dict = {}
        labels = {}
        node_colors = []
        
        # 递归添加节点和边
        self._add_nodes_to_graph(tree, G, labels, node_colors, parent_id=None)
        
        # 计算布局
        pos = nx.spring_layout(G, k=3, iterations=50)
        
        # 调整布局为树形结构
        pos = self._adjust_tree_layout(G, pos)
        
        # 绘制边
        nx.draw_networkx_edges(G, pos, edge_color='gray', arrows=True, 
                              arrowsize=20, arrowstyle='->', width=2, alpha=0.6)
        
        # 绘制节点
        for i, (node, (x, y)) in enumerate(pos.items()):
            color = node_colors[i] if i < len(node_colors) else self.node_colors['default']
            
            # 创建圆角矩形节点
            bbox = FancyBboxPatch((x-0.1, y-0.05), 0.2, 0.1,
                                 boxstyle="round,pad=0.01",
                                 facecolor=color, edgecolor='black',
                                 linewidth=1.5, alpha=0.8)
            ax.add_patch(bbox)
            
            # 添加文本标签
            ax.text(x, y, labels[node], ha='center', va='center',
                   fontsize=10, fontweight='bold', color='black')
        
        # 设置图形属性
        ax.set_xlim(-1.5, 1.5)
        ax.set_ylim(-1.5, 1.5)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # 添加标题
        title = f"句法分析树: {tree.get('text', '')}"
        plt.title(title, fontsize=16, fontweight='bold', pad=20)
        
        # 添加图例
        self._add_legend(ax)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"图形化树形图已保存: {save_path}")
        
        plt.show()
    
    def _add_nodes_to_graph(self, tree: Dict, G: nx.DiGraph, labels: Dict, 
                           node_colors: List, parent_id: str = None, node_counter: List = [0]):
        """递归添加节点到图中"""
        if not tree:
            return
        
        # 生成唯一节点ID
        current_id = f"node_{node_counter[0]}"
        node_counter[0] += 1
        
        # 添加节点
        G.add_node(current_id)
        
        # 设置标签和颜色
        if tree['type'] == 'sentence':
            labels[current_id] = f"[S]\n{tree.get('text', '')[:10]}..."
            node_colors.append(self.node_colors['S'])
        elif tree['type'] in ['NP', 'VP', 'AP']:
            phrase_text = " ".join([word for word, pos in tree.get('words', [])])
            labels[current_id] = f"[{tree['type']}]\n{phrase_text[:8]}..."
            node_colors.append(self.node_colors.get(tree['type'], self.node_colors['default']))
        elif tree['type'] == 'WORD':
            labels[current_id] = f"[{tree['pos']}]\n{tree['word']}"
            pos_prefix = tree['pos'][0] if tree['pos'] else 'default'
            node_colors.append(self.pos_colors.get(pos_prefix, self.pos_colors['default']))
        else:
            labels[current_id] = f"[{tree['type']}]\n{tree.get('word', '')}"
            node_colors.append(self.node_colors['default'])
        
        # 添加边
        if parent_id:
            G.add_edge(parent_id, current_id)
        
        # 递归处理子节点
        children = tree.get('children', [])
        for child in children:
            self._add_nodes_to_graph(child, G, labels, node_colors, current_id, node_counter)
    
    def _adjust_tree_layout(self, G: nx.DiGraph, pos: Dict) -> Dict:
        """调整布局为更好的树形结构"""
        # 找到根节点（入度为0的节点）
        root_nodes = [n for n in G.nodes() if G.in_degree(n) == 0]
        if not root_nodes:
            return pos
        
        root = root_nodes[0]
        
        # 使用层次化布局
        levels = {}
        queue = [(root, 0)]
        visited = set()
        
        while queue:
            node, level = queue.pop(0)
            if node in visited:
                continue
            visited.add(node)
            
            if level not in levels:
                levels[level] = []
            levels[level].append(node)
            
            # 添加子节点到队列
            for child in G.successors(node):
                queue.append((child, level + 1))
        
        # 重新计算位置
        new_pos = {}
        max_level = max(levels.keys()) if levels else 0
        
        for level, nodes in levels.items():
            y = 1 - (level / max_level) * 2  # 从上到下
            
            if len(nodes) == 1:
                new_pos[nodes[0]] = (0, y)
            else:
                x_positions = np.linspace(-1, 1, len(nodes))
                for i, node in enumerate(nodes):
                    new_pos[node] = (x_positions[i], y)
        
        return new_pos
    
    def _add_legend(self, ax):
        """添加图例"""
        legend_elements = []
        
        # 节点类型图例
        for node_type, color in self.node_colors.items():
            if node_type != 'default':
                legend_elements.append(
                    patches.Patch(color=color, label=f'{node_type} - {self._get_type_description(node_type)}')
                )
        
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
    
    def _get_type_description(self, node_type: str) -> str:
        """获取节点类型描述"""
        descriptions = {
            'S': '句子',
            'NP': '名词短语',
            'VP': '动词短语',
            'AP': '形容词短语',
            'WORD': '词汇'
        }
        return descriptions.get(node_type, node_type)
    
    def create_detailed_tree_diagram(self, tree: Dict, save_path: str = None) -> None:
        """创建详细的树形图，包含词性信息"""
        fig, ax = plt.subplots(1, 1, figsize=(16, 12))
        
        # 递归绘制树
        self._draw_detailed_tree(ax, tree, x=0, y=0.9, width=1.0, level=0)
        
        # 设置图形属性
        ax.set_xlim(-0.1, 1.1)
        ax.set_ylim(-0.1, 1.0)
        ax.axis('off')
        
        # 添加标题
        title = f"详细句法分析树: {tree.get('text', '')}"
        plt.title(title, fontsize=16, fontweight='bold', pad=20)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"详细树形图已保存: {save_path}")
        
        plt.show()
    
    def _draw_detailed_tree(self, ax, tree: Dict, x: float, y: float, 
                           width: float, level: int) -> None:
        """递归绘制详细的树形图"""
        if not tree:
            return
        
        # 绘制当前节点
        if tree['type'] == 'sentence':
            # 句子节点
            rect = FancyBboxPatch((x - width/2, y - 0.03), width, 0.06,
                                 boxstyle="round,pad=0.01",
                                 facecolor=self.node_colors['S'],
                                 edgecolor='black', linewidth=2)
            ax.add_patch(rect)
            ax.text(x, y, f"[S] {tree.get('text', '')}", ha='center', va='center',
                   fontsize=12, fontweight='bold')
            
            # 绘制子节点
            children = tree.get('children', [])
            if children:
                child_width = width / len(children)
                child_y = y - 0.15
                
                for i, child in enumerate(children):
                    child_x = x - width/2 + child_width/2 + i * child_width
                    
                    # 绘制连接线
                    ax.plot([x, child_x], [y - 0.03, child_y + 0.03], 
                           'k-', linewidth=1.5, alpha=0.7)
                    
                    # 递归绘制子节点
                    self._draw_detailed_tree(ax, child, child_x, child_y, 
                                           child_width * 0.8, level + 1)
        
        elif tree['type'] in ['NP', 'VP', 'AP']:
            # 短语节点
            phrase_text = " ".join([word for word, pos in tree.get('words', [])])
            color = self.node_colors.get(tree['type'], self.node_colors['default'])
            
            rect = FancyBboxPatch((x - width/2, y - 0.025), width, 0.05,
                                 boxstyle="round,pad=0.01",
                                 facecolor=color, edgecolor='black', linewidth=1.5)
            ax.add_patch(rect)
            ax.text(x, y, f"[{tree['type']}] {phrase_text}", ha='center', va='center',
                   fontsize=10, fontweight='bold')
            
            # 绘制词汇子节点
            words = tree.get('words', [])
            if words:
                word_width = width / len(words)
                word_y = y - 0.1
                
                for i, (word, pos) in enumerate(words):
                    word_x = x - width/2 + word_width/2 + i * word_width
                    
                    # 绘制连接线
                    ax.plot([x, word_x], [y - 0.025, word_y + 0.02], 
                           'k-', linewidth=1, alpha=0.7)
                    
                    # 绘制词汇节点
                    pos_color = self.pos_colors.get(pos[0] if pos else 'default', 
                                                   self.pos_colors['default'])
                    
                    word_rect = FancyBboxPatch((word_x - word_width/2, word_y - 0.02), 
                                              word_width, 0.04,
                                              boxstyle="round,pad=0.005",
                                              facecolor=pos_color, 
                                              edgecolor='gray', linewidth=1)
                    ax.add_patch(word_rect)
                    ax.text(word_x, word_y, f"{word}\n[{pos}]", ha='center', va='center',
                           fontsize=8)
        
        elif tree['type'] == 'WORD':
            # 单独的词汇节点
            pos_color = self.pos_colors.get(tree['pos'][0] if tree['pos'] else 'default',
                                           self.pos_colors['default'])
            
            rect = FancyBboxPatch((x - width/2, y - 0.025), width, 0.05,
                                 boxstyle="round,pad=0.01",
                                 facecolor=pos_color, edgecolor='black', linewidth=1.5)
            ax.add_patch(rect)
            ax.text(x, y, f"{tree['word']}\n[{tree['pos']}]", ha='center', va='center',
                   fontsize=9, fontweight='bold')


def visualize_trees_from_json(json_file: str, output_dir: str = None):
    """从JSON文件中读取句法树并可视化"""
    if output_dir is None:
        output_dir = "ChineseErrorCorrector/semantic_parser/tree_visualizations"
    
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📂 从文件读取句法树: {json_file}")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    visualizer = SyntaxTreeVisualizer()
    
    # 处理结果中的每个样本
    results = data.get('detailed_results', [])
    
    for i, result in enumerate(results):
        if 'semantic_parsing' not in result:
            continue
        
        sample_info = result['sample_info']
        semantic_chunks = result['semantic_parsing']['semantic_chunks']
        
        print(f"\n🌳 处理样本 {sample_info['id']}: {sample_info['source']}")
        
        # 为每个语义块创建可视化
        for j, chunk in enumerate(semantic_chunks):
            if 'syntax_tree' not in chunk:
                continue
            
            chunk_id = chunk['chunk_id']
            chunk_text = chunk['text']
            syntax_tree = chunk['syntax_tree']
            
            print(f"  📝 语义块 {chunk_id}: {chunk_text}")
            
            # 创建ASCII树
            ascii_tree = visualizer.create_ascii_tree(syntax_tree)
            print(f"  ASCII树形图:")
            print(ascii_tree)
            
            # 保存图形化可视化
            base_filename = f"sample_{sample_info['id']}_chunk_{chunk_id}"
            
            # 图形化树形图
            graph_path = os.path.join(output_dir, f"{base_filename}_graph.png")
            visualizer.create_graphical_tree(syntax_tree, graph_path)
            
            # 详细树形图
            detail_path = os.path.join(output_dir, f"{base_filename}_detailed.png")
            visualizer.create_detailed_tree_diagram(syntax_tree, detail_path)
            
            print(f"  💾 可视化已保存:")
            print(f"    - 图形化: {graph_path}")
            print(f"    - 详细图: {detail_path}")


def demo_tree_visualization():
    """演示句法树可视化"""
    print("🎨 句法树可视化演示")
    print("=" * 50)
    
    # 示例句法树
    example_tree = {
        "type": "sentence",
        "text": "小明在公园里踢足球。",
        "children": [
            {
                "type": "NP",
                "words": [["小明", "nr"]]
            },
            {
                "type": "WORD",
                "word": "在",
                "pos": "p"
            },
            {
                "type": "NP",
                "words": [["公园", "n"]]
            },
            {
                "type": "WORD",
                "word": "里",
                "pos": "f"
            },
            {
                "type": "VP",
                "words": [["踢", "v"], ["足球", "n"]]
            },
            {
                "type": "WORD",
                "word": "。",
                "pos": "x"
            }
        ]
    }
    
    visualizer = SyntaxTreeVisualizer()
    
    # ASCII可视化
    print("\n📄 ASCII树形图:")
    ascii_tree = visualizer.create_ascii_tree(example_tree)
    print(ascii_tree)
    
    # 图形化可视化
    print("\n🎨 生成图形化可视化...")
    output_dir = "ChineseErrorCorrector/semantic_parser/demo_visualizations"
    os.makedirs(output_dir, exist_ok=True)
    
    graph_path = os.path.join(output_dir, "demo_graph.png")
    detail_path = os.path.join(output_dir, "demo_detailed.png")
    
    visualizer.create_graphical_tree(example_tree, graph_path)
    visualizer.create_detailed_tree_diagram(example_tree, detail_path)
    
    print(f"✅ 演示完成！可视化文件已保存到: {output_dir}")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description="句法分析树可视化工具")
    parser.add_argument('--mode', choices=['demo', 'json'], default='demo',
                       help='运行模式: demo演示 或 json文件处理')
    parser.add_argument('--json_file', type=str,
                       default='ChineseErrorCorrector/semantic_parser/split_semantic_analysis_results.json',
                       help='JSON结果文件路径')
    parser.add_argument('--output_dir', type=str,
                       help='输出目录路径')
    
    args = parser.parse_args()
    
    if args.mode == 'demo':
        demo_tree_visualization()
    elif args.mode == 'json':
        visualize_trees_from_json(args.json_file, args.output_dir)
