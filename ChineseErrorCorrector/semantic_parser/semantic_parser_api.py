#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语义分割与句法解析API
提供简洁的接口用于识别拆分字错误和语义断点，并生成句法解析树
"""

import os
import sys
import json
from typing import List, Dict, Optional

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.semantic_parser.semantic_segmentation_parser import SemanticSegmentationParser


class SemanticParserAPI:
    """语义解析器API"""
    
    def __init__(self):
        self.parser = SemanticSegmentationParser()
    
    def parse_with_syntax_trees(self, text: str, max_chunks: int = 3) -> Dict:
        """
        解析文本并返回前n个语义块的句法解析树
        
        Args:
            text: 输入文本
            max_chunks: 返回的最大语义块数量
            
        Returns:
            包含语义分析和句法树的结果字典
        """
        # 使用核心解析器
        result = self.parser.parse_sentence(text, max_chunks)
        
        # 简化输出格式，专注于句法树
        simplified_result = {
            'input_text': text,
            'split_errors': result['split_errors'],
            'semantic_chunks': []
        }
        
        # 为每个语义块生成详细的句法树信息
        for i, chunk in enumerate(result['semantic_chunks']):
            chunk_info = {
                'chunk_id': i + 1,
                'text': chunk['text'],
                'position': chunk['position'],
                'chunk_type': chunk['type'],
                'confidence': round(chunk['confidence'], 3),
                'word_segmentation': chunk['pos_tags'],
                'syntax_tree': chunk['syntax_tree'],
                'tree_visualization': self._visualize_syntax_tree(chunk['syntax_tree'])
            }
            simplified_result['semantic_chunks'].append(chunk_info)
        
        # 添加摘要信息
        simplified_result['summary'] = {
            'total_chunks': len(simplified_result['semantic_chunks']),
            'split_errors_found': len(result['split_errors']),
            'has_semantic_breaks': len(result['semantic_breakpoints']) > len(result['split_errors'])
        }
        
        return simplified_result
    
    def _visualize_syntax_tree(self, tree: Dict, indent: int = 0) -> str:
        """将句法树可视化为文本格式"""
        if not tree:
            return ""
        
        lines = []
        prefix = "  " * indent
        
        if tree['type'] == 'sentence':
            lines.append(f"{prefix}[S] {tree.get('text', '')}")
            for child in tree.get('children', []):
                lines.append(self._visualize_syntax_tree(child, indent + 1))
        
        elif tree['type'] in ['NP', 'VP', 'AP']:
            phrase_text = " ".join([word for word, pos in tree.get('words', [])])
            lines.append(f"{prefix}[{tree['type']}] {phrase_text}")
        
        elif tree['type'] == 'WORD':
            lines.append(f"{prefix}[{tree['pos']}] {tree['word']}")
        
        return "\n".join(lines)
    
    def detect_split_errors_only(self, text: str) -> List[Dict]:
        """仅检测拆分字错误"""
        split_errors = self.parser.split_detector.detect_split_errors(text)
        
        return [
            {
                'position': (start, end),
                'error_text': error_text,
                'correct_text': correct_text,
                'error_type': 'split_character'
            }
            for start, end, error_text, correct_text in split_errors
        ]
    
    def segment_by_semantic_breaks(self, text: str) -> List[Dict]:
        """按语义断点分割文本"""
        result = self.parser.parse_sentence(text)
        
        return [
            {
                'chunk_id': i + 1,
                'text': chunk['text'],
                'start_position': chunk['position'][0],
                'end_position': chunk['position'][1],
                'chunk_type': chunk['type'],
                'confidence': round(chunk['confidence'], 3)
            }
            for i, chunk in enumerate(result['semantic_chunks'])
        ]


def quick_parse(text: str, max_chunks: int = 3) -> None:
    """快速解析并打印结果"""
    api = SemanticParserAPI()
    result = api.parse_with_syntax_trees(text, max_chunks)
    
    print(f"\n🔍 输入文本: {text}")
    print(f"📊 分析摘要: {result['summary']}")
    
    if result['split_errors']:
        print(f"\n❌ 拆分字错误:")
        for error in result['split_errors']:
            print(f"  位置 {error['position']}: {error['error_text']} → {error['correct_text']}")
    
    print(f"\n📝 语义块与句法树:")
    for chunk in result['semantic_chunks']:
        print(f"\n--- 语义块 {chunk['chunk_id']} ---")
        print(f"文本: \"{chunk['text']}\"")
        print(f"位置: {chunk['position']}")
        print(f"类型: {chunk['chunk_type']} (置信度: {chunk['confidence']})")
        print(f"词性标注: {chunk['word_segmentation']}")
        print(f"句法树:")
        print(chunk['tree_visualization'])


def batch_parse_from_file(input_file: str, output_file: str, max_chunks: int = 3) -> None:
    """批量处理文件中的句子"""
    api = SemanticParserAPI()
    
    print(f"📂 从文件读取: {input_file}")
    
    # 读取输入文件
    if input_file.endswith('.json'):
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 假设是测试数据格式
        sentences = []
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'source' in item:
                    sentences.append(item['source'])
                elif isinstance(item, str):
                    sentences.append(item)
        else:
            sentences = [data.get('text', str(data))]
    else:
        # 纯文本文件，每行一个句子
        with open(input_file, 'r', encoding='utf-8') as f:
            sentences = [line.strip() for line in f if line.strip()]
    
    print(f"📝 处理 {len(sentences)} 个句子...")
    
    # 批量处理
    results = []
    for i, sentence in enumerate(sentences):
        print(f"处理 {i+1}/{len(sentences)}: {sentence[:30]}...")
        
        try:
            result = api.parse_with_syntax_trees(sentence, max_chunks)
            results.append(result)
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            results.append({
                'input_text': sentence,
                'error': str(e),
                'split_errors': [],
                'semantic_chunks': []
            })
    
    # 保存结果
    output_data = {
        'processing_info': {
            'input_file': input_file,
            'total_sentences': len(sentences),
            'max_chunks_per_sentence': max_chunks,
            'successful_parses': len([r for r in results if 'error' not in r])
        },
        'results': results
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 结果已保存到: {output_file}")
    
    # 统计信息
    total_split_errors = sum(len(r.get('split_errors', [])) for r in results)
    total_chunks = sum(len(r.get('semantic_chunks', [])) for r in results)
    
    print(f"\n📊 处理统计:")
    print(f"  总句子数: {len(sentences)}")
    print(f"  成功解析: {len([r for r in results if 'error' not in r])}")
    print(f"  拆分字错误总数: {total_split_errors}")
    print(f"  语义块总数: {total_chunks}")


def integrate_with_split_test():
    """与现有的split测试系统集成"""
    # 示例：处理split测试数据
    split_data_path = "ChineseErrorCorrector/data/split_data/split_errors_data.json"
    output_path = "ChineseErrorCorrector/semantic_parser/split_semantic_analysis.json"
    
    if os.path.exists(split_data_path):
        print(f"🔗 集成处理split测试数据...")
        batch_parse_from_file(split_data_path, output_path, max_chunks=2)
    else:
        print(f"⚠️ Split数据文件不存在: {split_data_path}")
        
        # 使用示例数据
        example_sentences = [
            "乌兰巴托不到4百年白勺历史。",
            "我为什么喜欢阿拉木图，我觉得有几个牛寺点。",
            "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。"
        ]
        
        print(f"📝 使用示例数据进行演示...")
        api = SemanticParserAPI()
        
        results = []
        for sentence in example_sentences:
            result = api.parse_with_syntax_trees(sentence, max_chunks=2)
            results.append(result)
        
        # 保存示例结果
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump({
                'processing_info': {
                    'data_type': 'example_data',
                    'total_sentences': len(example_sentences)
                },
                'results': results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"💾 示例结果已保存到: {output_path}")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description="语义分割与句法解析API")
    parser.add_argument('--mode', choices=['quick', 'batch', 'integrate'], 
                       default='quick', help='运行模式')
    parser.add_argument('--text', type=str, 
                       default="乌兰巴托不到4百年白勺历史。",
                       help='要解析的文本（quick模式）')
    parser.add_argument('--input_file', type=str, help='输入文件路径（batch模式）')
    parser.add_argument('--output_file', type=str, help='输出文件路径（batch模式）')
    parser.add_argument('--max_chunks', type=int, default=3, help='最大语义块数量')
    
    args = parser.parse_args()
    
    if args.mode == 'quick':
        print("🚀 快速解析模式")
        quick_parse(args.text, args.max_chunks)
    
    elif args.mode == 'batch':
        if not args.input_file or not args.output_file:
            print("❌ batch模式需要指定input_file和output_file参数")
            sys.exit(1)
        
        print("📦 批量处理模式")
        batch_parse_from_file(args.input_file, args.output_file, args.max_chunks)
    
    elif args.mode == 'integrate':
        print("🔗 集成模式")
        integrate_with_split_test()
    
    print("\n✅ 处理完成!")
