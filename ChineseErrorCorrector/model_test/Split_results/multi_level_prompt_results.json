{"test_info": {"test_time": "2025-07-31T17:22:11.255540", "strategies_tested": ["basic", "detailed", "hierarchical", "contextual", "expert", "chain_of_thought", "few_shot"], "total_strategies": 7}, "strategy_results": {"basic": {"strategy_name": "basic", "total_samples": 20, "correct_count": 1, "accuracy": 0.05, "modified_count": 17, "modification_rate": 0.85, "total_time": 51.71961784362793, "avg_time_per_sample": 2.5859808921813965, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了 。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，尤其像芭蕾舞演员在跳舞一木羊是那么的优美。秋天时，一片片叶子从树落下来，尤其像芭蕾舞演员在跳舞一木羊是那么的优美。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛点。我为什么喜欢阿拉木图，我觉得有几个优点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点。我为什么喜欢阿拉木图，我觉得有几点", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。乌兰巴托不到4百年白勺历史。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非无情也不容易对他来说，越来越不容易了。生活非", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。2005年口客山举了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.人类学的人是必须研究其他人和其他", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "意思是迎接新年节日。", "error_type": "拆分字错误", "is_correct": true, "is_modified": true}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺工具。在工作上它也是一个用户白勺", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具。.手机是一个关健白勺工具", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们还好月月有。我们高中的在一个班学习到现在我们", "error_type": "成分赘余", "is_correct": false, "is_modified": true}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系很好。因为我跟他的爱好一样所以我们的关系", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "这是我的房间：靠土啬[Zc]摆着我的床。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我上课。从星期一到星期五我", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "今天我很悲伤.今天我很伤心.今天我很痛苦.今天我很哀痛.今天我很悲痛.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.今天我很哀悼.", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}, "detailed": {"strategy_name": "detailed", "total_samples": 20, "correct_count": 0, "accuracy": 0.0, "modified_count": 20, "modification_rate": 1.0, "total_time": 67.28606247901917, "avg_time_per_sample": 3.3643031239509584, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了 。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "乌兰巴托不到4百年白勺历史 。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非信亡也不容易对他来说，越来越不容易了。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "原句：意思是迎接新年节日。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子 原句：意思是迎接新年节日。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子 原句：意思是迎接新年节日。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子 �", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "这一天，全家人会一起吃年夜饭，还有送牛勿。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作它也是一个用者白勺工具。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具 。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月有。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "成分赘余", "is_correct": false, "is_modified": true}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "因为我跟他的爱好一木羊所以我们的关系很好。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "这是我的房间：靠土啬[Zc]摆着我的床。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星其月一到星月五我上课。要求：1. 识别并纠正拆分字错误。2. 保持句子的原意不变。3. 只输出纠正后的句子。纠错：从星其月一到星月五我上课。要求：1. 识别并纠正拆分字错误。2. 保持句子的原意不变。3. 只输出纠正后的句子。纠错：从星其月一到星月五我上课。要求：1. 识别并纠正拆分字错误。2. 保持句子的原意不变。3", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "B：今天我很伤心.要求：1. 识别并纠正拆分字错误 2. 保持句子的原意不变 3. 只输出纠正后的句子", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}, "hierarchical": {"strategy_name": "hierarchical", "total_samples": 20, "correct_count": 1, "accuracy": 0.05, "modified_count": 5, "modification_rate": 0.25, "total_time": 19.10741877555847, "avg_time_per_sample": 0.9553709387779236, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流畅了 。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "乌兰巴托不到4百年白勺历史。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非忄亡也不容易对他来说，越来越不容易了。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "意思是迎接新年节日。", "error_type": "拆分字错误", "is_correct": true, "is_modified": true}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作它也是一个用者白勺工具。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月有。2. 识别可能的拆分字错误", "error_type": "成分赘余", "is_correct": false, "is_modified": true}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "因为我跟他的爱好一木羊所以我们的关系很好。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "这是我的房间：靠土啬[Zc]摆着我的床。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星其月一到星月五我上课。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "B：今天我很伤心.", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}, "contextual": {"strategy_name": "contextual", "total_samples": 20, "correct_count": 0, "accuracy": 0.0, "modified_count": 18, "modification_rate": 0.9, "total_time": 56.14813041687012, "avg_time_per_sample": 2.807406520843506, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了 。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。分析要点：理解句子的整体语义，识别不符合语境的字符组合，考虑汉字的标准写法，保持语义连贯性。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。分析要点：- 理解句子的整体语义- 识别不符合语境的字符组合- 考虑汉字的标准写法- 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "无兰巴托不到4百年白勺历史。分析要点：理解句子的整体语义 识别不符合语境的字符组合 考虑汉字的标准写法 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非信亡也不容易对他来说，越来越不容易了。分析要点：理解句子的整体语义 识别不符合语境的字符组合 考虑汉字的标准写法 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.分析要点：- 理解句子的整体语义- 识别不符合语境的字符组合- 考虑汉字的标准写法- 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "迎接春节年节日。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "这一天，全家人会一起吃年夜饭，还有送牛勿。分析要点：理解句子的整体语义，识别不符合语境的字符组合，考虑汉字的标准写法，保持语义连贯性。原句：这一天，全家人会一起吃年夜饭，还有送牛勿。这一天，全家人会一起吃年夜饭，还有送牛勿。分析要点：理解句子的整体语义，识别不符合语境的字符组合，考虑汉字的标准写法，保持语义连贯性。原句：这一天，全家人会一起吃年夜饭，还有送牛勿", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作它也是一个用户白勺工具。分析要点：- 理解句子的整体语义- 识别不符合语境的字符组合- 考虑汉字的标准写法- 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具。分析要点：- 理解句子的整体语义- 识别不符合语境的字符组合- 考虑汉字的标准写法- 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月有。分析要点：- 理解句子的整体语义- 识别不符合语境的字符组合- 考虑汉字的标准写法- 保持语义连贯性", "error_type": "成分赘余", "is_correct": false, "is_modified": true}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "因为我跟他的爱好一木羊所以我们的关系很好。分析要点：- 理解句子的整体语义- 识别不符合语境的字符组合- 考虑汉字的标准写法- 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "靠土啬[Zc]摆着我的床。分析要点：理解句子的整体语义 识别不符合语境的字符组合 考虑汉字的标准写法 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。分析要点：理解句子的整体语义、识别不符合语境的字符组合、考虑汉字的标准写法、保持语义连贯性。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.分析要点：理解句子的整体语义识别不符合语境的字符组合考虑汉字的标准写法保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星其月一到星月五我上课。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "B：今天我很伤心.分析要点：- 理解句子的整体语义 - 识别不符合语境的字符组合 - 考虑汉字的标准写法 - 保持语义连贯性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}, "expert": {"strategy_name": "expert", "total_samples": 20, "correct_count": 1, "accuracy": 0.05, "modified_count": 14, "modification_rate": 0.7, "total_time": 41.32539343833923, "avg_time_per_sample": 2.0662696719169618, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "原句的拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非忄亡也不容易对他来说，越来越不容易了。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.专业分析：1. 拆分字错误特征：将完整汉字的偏旁部首错误分离 2. 币见模式：形近字混淆、部件拆分、结构错误 3. 纠正原则：恢复汉字的标准形态，保持语义准确性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "拆分字错误特征：将完整汉字的偏旁部首错误分离  �见模式：形近字混淆、部件拆分、结构错误  纠正原则：恢复汉字的标准形态，保持语义准确性  拆分字错误特征：将完整汉字的偏旁部首错误分离  �见模式：形近字混淆、部件拆分、结构错误  纠正原则：恢复汉字的标准形态，保持语义准确性  拆分字错误特征：将完整汉字的偏旁部首错误分离  �见模式：", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "这一天，全家人会一起吃年夜饭，还有送牛勿。专业分析：1. 拆分字错误特征：将完整汉字的偏旁部首错误分离 2. 币见模式：形近字混淆、部件拆分、结构错误 3. 纠正原则：恢复汉字的标准形态，保持语义准确性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作它也是一个用者白勺工具。专业分析：1. 拆分字错误特征：将完整汉字的偏旁部首错误分离 2. 市见模式：形近字混淆、部件拆分、结构错误 3. 纠正原则：恢复汉字的标准形态，保持语义准确性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月月友我有一个好月月友但不在中国。我的月", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月有。专业分析：1. 拆分字错误特征：将完整汉字的偏旁部首错误分离 2. 币见模式：形近字混淆、部件拆分、结构错误 3. 纠正原则：恢复汉字的标准形态，保持语义准确性", "error_type": "成分赘余", "is_correct": false, "is_modified": true}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "原句：因为我跟他的爱好一木羊所以我们的关系很好。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "这是我的房间：靠土啬[Zc]摆着我的床。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "原句的拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星期一到星期五我上课。", "error_type": "拆分字错误", "is_correct": true, "is_modified": true}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "B：今天我很悲伤.专业分析：1. 拆分字错误特征：将完整汉字的偏旁部首错误分离 2. 币见模式：形近字混淆、部件拆分、结构错误 3. 纠正原则：恢复汉字的标准形态，保持语义准确性", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}, "chain_of_thought": {"strategy_name": "chain_of_thought", "total_samples": 20, "correct_count": 0, "accuracy": 0.0, "modified_count": 7, "modification_rate": 0.35, "total_time": 8.75440526008606, "avg_time_per_sample": 0.437720263004303, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流畅了。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "乌兰巴托不到4百年白勺历史。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非信亡也不容易对他来说，越来越不容易了。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "意思是迎接亲斤年节日。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "……", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作它也是一个用者白勺工具。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月友。", "error_type": "成分赘余", "is_correct": false, "is_modified": false}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "因为我跟他的爱好一致所以我们的关系很好。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "这是我的房间：靠桌子[Zc]摆着我的床。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星其月一到星月五我上课。", "error_type": "拆分字错误", "is_correct": false, "is_modified": false}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "B：今天我很伤心.", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}, "few_shot": {"strategy_name": "few_shot", "total_samples": 20, "correct_count": 0, "accuracy": 0.0, "modified_count": 20, "modification_rate": 1.0, "total_time": 50.14584159851074, "avg_time_per_sample": 2.507292079925537, "results": [{"id": 0, "source": "你先择几个个性重点培养，最终形成自己独特的男人鬼未力。", "target": "你选择几个个性重点培养，最终形成自己独特的男人魅力。", "prediction": "你选择几个个性重点培养，最终形成自己独特的男人魅力。说明：将\"鬼未力\"纠正为\"魅力\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 1, "source": "在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了。", "target": "在老师的帮助和鼓励力下，我终于着用汉语来表达自己的思想写得多了，也不那么怕写错了，笔下也越来越流畅了。", "prediction": "在老师的帮助和鼓励力力下，我终于试着用汉语来表达自己的思想写得多了，也就不那么怕写错了，笔下也越来越流申了 。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 2, "source": "秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。", "target": "秋天时，一片片叶了从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。", "prediction": "秋天时，一片片叶子从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 3, "source": "我为什么喜欢阿拉木图，我觉得有几个牛寺点。", "target": "我为什么喜欢阿拉木，我觉得有几个特点。", "prediction": "我为什么喜欢阿拉木图，我觉得有几个牛气点。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 4, "source": "乌兰巴托不到4百年白勺历史。", "target": "乌兰巴托不到4百年的历史。", "prediction": "乌兰巴托不到4百年白勺历史。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 5, "source": "生活非忄亡也不容易对他来说，越来越不容易了。", "target": "生活非常忙也不容易对他来说，越来越不容易了。", "prediction": "生活非抑亡也不容易对他来说，越来越不容易了。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 6, "source": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "target": "2005年喀山举办了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。", "prediction": "2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2千公顷的城堡，但是今天喀山城市的面积是500多平方公里，人口为130万人。2005年口客山举为了1000年的城市庆典，原来喀山只不过是个刚超过2", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 7, "source": "人类学的人是必须石开究其他人和其他民族我的研究题目是关于汉人文化.", "target": "人类学人是必须研究其他人和其他族我研究题目是关于汉人文化.", "prediction": "人类学的人是必须研究其他人和其他民族我的研究题目是关于汉人文化.说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 8, "source": "意思是迎接亲斤年节日。", "target": "意思是迎接新年节日。", "prediction": "意思是迎接新年节日。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 9, "source": "这一天，全家人会一起吃年夜饭，还有送牛勿。", "target": "这一天，全家人会一起吃年夜饭，有物。", "prediction": "这一天，全家人会一起吃年夜饭，还有送牛物。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 10, "source": "在工作它也是一个用者白勺工具。", "target": "在工作它是一个有用的工。", "prediction": "在工作上它也是一个用者白勺工具。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 11, "source": "手机是一个关健白勺工具。", "target": "手机是一个关健的工。", "prediction": "手机是一个关健白勺工具。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 12, "source": "我的月月友我有一个好月月友但不在中国。", "target": "我的朋友我有一个好朋友但不在中国。", "prediction": "我的月月友我有一个好月月友但不在中国。说明：将\"月月友\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 13, "source": "我们高中的在一个班学习到现在我们还好月月友。", "target": "我们高中在一个班学习到现在我们还是好朋友。", "prediction": "我们高中的在一个班学习到现在我们还好月月有。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "成分赘余", "is_correct": false, "is_modified": true}, {"id": 14, "source": "因为我跟他的爱好一木羊所以我们的关系很好。", "target": "因为我跟他的爱好一样所以我们的关系很好。", "prediction": "因为我跟他的爱好一样所以我们的关系很好。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 15, "source": "这是我的房间：靠土啬[Zc]摆着我的床。", "target": "这是我的房间：靠墙[Zc]摆着我的床。", "prediction": "这是我的房间：靠土啬[Zc]摆着我的床。说明：将\"鬼未\"纠正为\"魅\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 16, "source": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。", "target": "桌子上放着两本书、桌子下放着一个垃圾箱、房间里还放着一个衣柜。", "prediction": "桌子上放着两本书、桌子下放着一个土立土及箱、房间里还放着一个衣柜。纠错：将原句中的“鬼未”纠正为“魅”，这是拆分字错误。纠错：将原句中的“鬼未”纠正为“魅”，这是拆分字错误。纠错：将原句中的“鬼未”纠正为“魅”，这是拆分字错误。纠错：将原句中的“鬼未”纠正为“魅”，这是拆分字错误。纠错：将原句中的“鬼未”纠正为“魅”，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 17, "source": "衣木巨装着衣服我有一张北京地图在我的房间我有一个新衣木巨在我的房间.", "target": "衣柜里装着衣服我有一张北京地图在我的房间我有一个新衣柜在我的房间.", "prediction": "我的鬼未力很强", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 18, "source": "从星其月一到星月五我上课。", "target": "从星期一到星期五我上课。", "prediction": "从星其月一到星月五我上课。说明：将\"星其月一到星月五\"纠正为\"星月五\"，这是拆分字错误。", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}, {"id": 19, "source": "B：今天我很忄亡.", "target": "B：今天我很忙.", "prediction": "B：今天我很悲伤.说明：将\"很忄亡\"纠正为\"很悲伤\"，这是拆分字错误", "error_type": "拆分字错误", "is_correct": false, "is_modified": true}]}}, "analysis": {"strategy_ranking": [{"strategy": "basic", "accuracy": 0.05, "modification_rate": 0.85, "avg_time": 2.5859808921813965}, {"strategy": "hierarchical", "accuracy": 0.05, "modification_rate": 0.25, "avg_time": 0.9553709387779236}, {"strategy": "expert", "accuracy": 0.05, "modification_rate": 0.7, "avg_time": 2.0662696719169618}, {"strategy": "detailed", "accuracy": 0.0, "modification_rate": 1.0, "avg_time": 3.3643031239509584}, {"strategy": "contextual", "accuracy": 0.0, "modification_rate": 0.9, "avg_time": 2.807406520843506}, {"strategy": "chain_of_thought", "accuracy": 0.0, "modification_rate": 0.35, "avg_time": 0.437720263004303}, {"strategy": "few_shot", "accuracy": 0.0, "modification_rate": 1.0, "avg_time": 2.507292079925537}], "performance_improvements": {"detailed": {"absolute_improvement": -0.05, "relative_improvement": -1.0}, "hierarchical": {"absolute_improvement": 0.0, "relative_improvement": 0.0}, "contextual": {"absolute_improvement": -0.05, "relative_improvement": -1.0}, "expert": {"absolute_improvement": 0.0, "relative_improvement": 0.0}, "chain_of_thought": {"absolute_improvement": -0.05, "relative_improvement": -1.0}, "few_shot": {"absolute_improvement": -0.05, "relative_improvement": -1.0}}, "error_type_analysis": {"拆分字错误": {"basic": {"total": 19, "correct": 1, "accuracy": 0.05263157894736842}, "detailed": {"total": 19, "correct": 0, "accuracy": 0.0}, "hierarchical": {"total": 19, "correct": 1, "accuracy": 0.05263157894736842}, "contextual": {"total": 19, "correct": 0, "accuracy": 0.0}, "expert": {"total": 19, "correct": 1, "accuracy": 0.05263157894736842}, "chain_of_thought": {"total": 19, "correct": 0, "accuracy": 0.0}, "few_shot": {"total": 19, "correct": 0, "accuracy": 0.0}}, "成分赘余": {"basic": {"total": 1, "correct": 0, "accuracy": 0.0}, "detailed": {"total": 1, "correct": 0, "accuracy": 0.0}, "hierarchical": {"total": 1, "correct": 0, "accuracy": 0.0}, "contextual": {"total": 1, "correct": 0, "accuracy": 0.0}, "expert": {"total": 1, "correct": 0, "accuracy": 0.0}, "chain_of_thought": {"total": 1, "correct": 0, "accuracy": 0.0}, "few_shot": {"total": 1, "correct": 0, "accuracy": 0.0}}}, "best_strategy": "basic", "worst_strategy": "few_shot", "max_improvement": 0.0}}