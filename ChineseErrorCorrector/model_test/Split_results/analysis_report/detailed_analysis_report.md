# 多层次提示词测试详细分析报告

**生成时间**: 2025-07-31 17:32:40

## 1. 测试概述

- **测试策略数量**: 7
- **测试样本数量**: 15
- **最佳策略**: constraint_based
- **最大准确率提升**: 0.1333

## 2. 各策略详细分析

### baseline

- **准确率**: 0.0000
- **修改率**: 0.7333
- **平均处理时间**: 1.607 秒/样本
- **完全匹配数**: 0/15

### explicit_instruction

- **准确率**: 0.0667
- **修改率**: 0.6000
- **平均处理时间**: 0.678 秒/样本
- **完全匹配数**: 1/15
- **相对基线改进**: 0.0667

### pattern_aware

- **准确率**: 0.0000
- **修改率**: 0.6667
- **平均处理时间**: 0.705 秒/样本
- **完全匹配数**: 0/15
- **相对基线改进**: 0.0000

### step_by_step

- **准确率**: 0.0000
- **修改率**: 0.5333
- **平均处理时间**: 0.445 秒/样本
- **完全匹配数**: 0/15
- **相对基线改进**: 0.0000

### example_guided

- **准确率**: 0.0667
- **修改率**: 0.8667
- **平均处理时间**: 0.511 秒/样本
- **完全匹配数**: 1/15
- **相对基线改进**: 0.0667

### constraint_based

- **准确率**: 0.1333
- **修改率**: 0.8000
- **平均处理时间**: 1.296 秒/样本
- **完全匹配数**: 2/15
- **相对基线改进**: 0.1333

### meta_cognitive

- **准确率**: 0.0000
- **修改率**: 0.6667
- **平均处理时间**: 0.570 秒/样本
- **完全匹配数**: 0/15
- **相对基线改进**: 0.0000

## 3. 性能排名

### 按准确率排名

| 排名 | 策略 | 准确率 | 修改率 | 平均时间(秒) |
|------|------|--------|--------|-------------|
| 1 | constraint_based | 0.1333 | 0.8000 | 1.296 |
| 2 | explicit_instruction | 0.0667 | 0.6000 | 0.678 |
| 3 | example_guided | 0.0667 | 0.8667 | 0.511 |
| 4 | baseline | 0.0000 | 0.7333 | 1.607 |
| 5 | pattern_aware | 0.0000 | 0.6667 | 0.705 |
| 6 | step_by_step | 0.0000 | 0.5333 | 0.445 |
| 7 | meta_cognitive | 0.0000 | 0.6667 | 0.570 |

## 4. 关键发现

1. **最佳策略**: `constraint_based` 策略表现最佳，准确率达到 0.1333

2. **显著提升**: 多层次提示词带来了显著的性能提升，最大提升幅度为 0.1333

3. **修改率与准确率相关性**: 0.528
   - 中等相关性，修改行为对准确性有一定影响

## 5. 建议

- **强烈推荐**: 使用 `constraint_based` 策略，可以显著提升模型性能
- **进一步优化**: 可以基于最佳策略进行更细致的提示词调优

---

*本报告由多层次提示词测试系统自动生成*
