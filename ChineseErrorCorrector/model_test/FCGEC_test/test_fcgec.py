#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FCGEC数据集测试脚本
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from typing import List, Dict, Tuple, Optional

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

try:
    from ChineseErrorCorrector.model_test.FCGEC_test.fcgec_data_loader import FCGECDataLoader
    from ChineseErrorCorrector.main import ErrorCorrect
    from ChineseErrorCorrector.config import TextCorrectConfig
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在正确的项目目录下运行此脚本")
    sys.exit(1)

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FCGECTester:
    """FCGEC测试器"""

    def __init__(self, model_path: str = None, use_vllm: bool = False, data_dir: str = None, batch_size: int = 16):
        self.model_path = model_path or TextCorrectConfig.DEFAULT_CKPT_PATH
        self.use_vllm = use_vllm
        self.batch_size = batch_size
        self.corrector = None
        self.data_dir = data_dir or "ChineseErrorCorrector/data/FCGEC_data"
        self.data_loader = FCGECDataLoader(data_dir=self.data_dir)

        # 设置配置
        if model_path:
            TextCorrectConfig.DEFAULT_CKPT_PATH = model_path
        TextCorrectConfig.USE_VLLM = use_vllm
        
    def initialize_model(self):
        """初始化模型"""
        logger.info(f"🔧 初始化模型: {self.model_path}")
        logger.info(f"使用VLLM: {self.use_vllm}")
        logger.info(f"数据目录: {self.data_dir}")

        try:
            # 检查模型路径是否存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型路径不存在: {self.model_path}")

            # 检查数据目录是否存在
            if not os.path.exists(self.data_dir):
                raise FileNotFoundError(f"数据目录不存在: {self.data_dir}")

            self.corrector = ErrorCorrect()
            logger.info("✅ 模型初始化成功")
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            logger.error(f"请检查模型路径和数据目录是否正确")
            raise
    
    def prepare_test_data(self, data: List[Dict]) -> "Tuple[List[str], List[str], List[str]]":
        """准备测试数据，参照NaCGEC的实现"""
        test_sentences = []
        ground_truth = []
        error_types = []

        for item in data:
            if isinstance(item, dict):
                # FCGEC格式
                test_sentences.append(item['sentence'])
                ground_truth.append(item['target'])
                error_types.append(item.get('error_type', 'unknown'))
            else:
                logger.warning(f"数据格式不识别: {type(item)}")
                continue

        logger.info(f"准备了 {len(test_sentences)} 条测试数据")

        # 统计错误类型分布
        from collections import Counter
        error_type_counts = Counter(error_types)
        logger.info("错误类型分布:")
        for error_type, count in error_type_counts.most_common():
            logger.info(f"  {error_type}: {count}")

        return test_sentences, ground_truth, error_types

    def run_batch_inference(self, sentences: List[str]) -> List[str]:
        """批处理推理，参照NaCGEC的实现"""
        predictions = []
        total_batches = (len(sentences) + self.batch_size - 1) // self.batch_size

        logger.info(f"开始批处理推理，批大小: {self.batch_size}, 总批数: {total_batches}")

        for i in range(0, len(sentences), self.batch_size):
            batch_sentences = sentences[i:i + self.batch_size]
            batch_idx = i // self.batch_size + 1

            if batch_idx % 10 == 0 or batch_idx == total_batches:
                logger.info(f"处理批次: {batch_idx}/{total_batches}")

            try:
                # 批处理推理
                if self.use_vllm:
                    batch_results = self.corrector.vllm_infer(batch_sentences)
                else:
                    batch_results = self.corrector.hf_infer(batch_sentences)

                # 提取预测结果
                for j, result in enumerate(batch_results):
                    if result and isinstance(result, dict):
                        # res_format返回的格式是 {'source': a, 'target': b, 'errors': errors}
                        if 'target' in result:
                            predictions.append(result['target'])
                        elif 'corrected_text' in result:
                            predictions.append(result['corrected_text'])
                        else:
                            predictions.append(batch_sentences[j])
                    else:
                        # 如果推理失败，返回原句
                        predictions.append(batch_sentences[j])

            except Exception as e:
                logger.warning(f"批次 {batch_idx} 推理失败: {e}")
                # 推理失败时，返回原句
                predictions.extend(batch_sentences)

        return predictions

    def _calculate_error_type_stats(self, results: List[Dict]) -> Dict:
        """计算错误类型统计"""
        error_type_stats = {}
        for result in results:
            error_type = result['error_type']
            if error_type not in error_type_stats:
                error_type_stats[error_type] = {
                    'total': 0,
                    'correct': 0,
                    'accuracy': 0.0,
                    'samples': []
                }
            error_type_stats[error_type]['total'] += 1

            is_correct = result['prediction'] == result['ground_truth']
            if is_correct:
                error_type_stats[error_type]['correct'] += 1

            # 保存样本用于分析
            if len(error_type_stats[error_type]['samples']) < 5:
                error_type_stats[error_type]['samples'].append({
                    'id': result['id'],
                    'source': result['source'],
                    'prediction': result['prediction'],
                    'ground_truth': result['ground_truth'],
                    'correct': is_correct
                })

        # 计算各错误类型的准确率
        for error_type in error_type_stats:
            stats = error_type_stats[error_type]
            stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0

        return error_type_stats

    def _calculate_edit_distance_stats(self, results: List[Dict]) -> Dict:
        """计算编辑距离统计"""
        try:
            import Levenshtein
        except ImportError:
            logger.warning("未安装python-Levenshtein，跳过编辑距离计算")
            return {}

        edit_distances = []
        for result in results:
            distance = Levenshtein.distance(result['prediction'], result['ground_truth'])
            edit_distances.append(distance)

        if edit_distances:
            return {
                'mean_edit_distance': sum(edit_distances) / len(edit_distances),
                'min_edit_distance': min(edit_distances),
                'max_edit_distance': max(edit_distances),
                'zero_edit_distance_count': edit_distances.count(0),
                'zero_edit_distance_rate': edit_distances.count(0) / len(edit_distances)
            }
        return {}

    def test_on_validation_set(self, max_samples: int = 100) -> Dict:
        """在验证集上测试（有标注数据）"""
        logger.info(f"📊 在FCGEC验证集上测试 (最大样本数: {max_samples})")

        try:
            # 加载验证数据
            valid_data = self.data_loader.load_validation_data()

            # 创建测试子集
            if max_samples and len(valid_data) > max_samples:
                test_data = self.data_loader.create_test_subset(valid_data, max_samples)
            else:
                test_data = valid_data

            logger.info(f"测试样本数: {len(test_data)}")

        except Exception as e:
            logger.error(f"❌ 加载验证数据失败: {e}")
            raise
        
        # 准备测试数据
        test_sentences, ground_truth, error_types = self.prepare_test_data(test_data)

        if len(test_sentences) == 0:
            raise ValueError("没有有效的测试数据")

        # 开始批处理推理
        start_time = time.time()
        predictions = self.run_batch_inference(test_sentences)
        end_time = time.time()

        # 构建结果
        results = []
        for i, (sentence, prediction, truth, error_type) in enumerate(zip(test_sentences, predictions, ground_truth, error_types)):
            sample = test_data[i]
            result = {
                'id': sample['id'],
                'source': sentence,
                'prediction': prediction,
                'ground_truth': truth,
                'error_flag': sample['error_flag'],
                'error_type': error_type,
                'operations': sample['operations']
            }
            results.append(result)
        
        # 计算基本统计
        total_time = end_time - start_time
        avg_time = total_time / len(results) if results else 0
        throughput = len(results) / total_time if total_time > 0 else 0

        # 计算完全匹配率
        exact_matches = sum(1 for r in results if r['prediction'] == r['ground_truth'])
        exact_match_rate = exact_matches / len(results) if results else 0

        # 按错误类型统计
        error_type_stats = self._calculate_error_type_stats(results)

        # 计算编辑距离统计
        edit_distance_stats = self._calculate_edit_distance_stats(results)
        
        test_summary = {
            'test_time': datetime.now().isoformat(),
            'model_path': self.model_path,
            'use_vllm': self.use_vllm,
            'batch_size': self.batch_size,
            'data_dir': self.data_dir,
            'total_samples': len(results),
            'total_time_seconds': total_time,
            'average_time_per_sample': avg_time,
            'throughput_samples_per_second': throughput,
            'exact_match_count': exact_matches,
            'exact_match_rate': exact_match_rate,
            'error_type_statistics': error_type_stats,
            'edit_distance_statistics': edit_distance_stats,
            'results': results
        }
        
        return test_summary
    
    def test_on_test_set(self, max_samples: int = 100) -> Dict:
        """在测试集上测试（无标注数据）"""
        logger.info(f"📊 在FCGEC测试集上测试 (最大样本数: {max_samples})")

        try:
            # 加载测试数据
            test_data = self.data_loader.load_test_data()

            # 创建测试子集
            if max_samples and len(test_data) > max_samples:
                test_subset = self.data_loader.create_test_subset(test_data, max_samples)
            else:
                test_subset = test_data

            logger.info(f"测试样本数: {len(test_subset)}")

        except Exception as e:
            logger.error(f"❌ 加载测试数据失败: {e}")
            raise

        # 准备测试数据（测试集没有ground_truth）
        test_sentences = []
        for item in test_subset:
            test_sentences.append(item['sentence'])

        # 开始批处理推理
        start_time = time.time()
        predictions = self.run_batch_inference(test_sentences)
        end_time = time.time()

        # 构建结果
        results = []
        for i, (sentence, prediction) in enumerate(zip(test_sentences, predictions)):
            sample = test_subset[i]
            result = {
                'id': sample['id'],
                'source': sentence,
                'prediction': prediction,
                'version': sample['version']
            }
            results.append(result)
        
        # 计算基本统计
        total_time = end_time - start_time
        avg_time = total_time / len(results) if results else 0
        throughput = len(results) / total_time if total_time > 0 else 0

        # 计算修改率（有多少句子被修改了）
        modified_count = sum(1 for r in results if r['prediction'] != r['source'])
        modification_rate = modified_count / len(results) if results else 0

        test_summary = {
            'test_time': datetime.now().isoformat(),
            'model_path': self.model_path,
            'use_vllm': self.use_vllm,
            'batch_size': self.batch_size,
            'data_dir': self.data_dir,
            'total_samples': len(results),
            'total_time_seconds': total_time,
            'average_time_per_sample': avg_time,
            'throughput_samples_per_second': throughput,
            'modified_count': modified_count,
            'modification_rate': modification_rate,
            'results': results
        }
        
        return test_summary
    
    def save_results(self, results: Dict, output_file: str):
        """保存测试结果"""
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)

            # 保存结果
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ 测试结果已保存到: {output_file}")

        except Exception as e:
            logger.error(f"❌ 保存结果失败: {e}")
            raise
    
    def print_summary(self, results: Dict):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("FCGEC数据集测试报告")
        print("="*60)
        print(f"测试时间: {results['test_time']}")
        print(f"模型路径: {results['model_path']}")
        print(f"数据目录: {results['data_dir']}")
        print(f"使用VLLM: {results['use_vllm']}")
        print(f"批大小: {results['batch_size']}")
        print(f"测试样本数: {results['total_samples']}")
        print(f"总耗时: {results['total_time_seconds']:.2f} 秒")
        print(f"平均耗时: {results['average_time_per_sample']:.3f} 秒/样本")
        print(f"处理速度: {results['throughput_samples_per_second']:.2f} 样本/秒")

        # 如果有ground truth，显示准确率信息
        if 'exact_match_rate' in results:
            print(f"\n📈 准确率统计:")
            print(f"完全匹配数: {results['exact_match_count']}")
            print(f"完全匹配率: {results['exact_match_rate']:.4f}")

            # 编辑距离统计
            if 'edit_distance_statistics' in results and results['edit_distance_statistics']:
                edit_stats = results['edit_distance_statistics']
                print(f"\n📏 编辑距离统计:")
                print(f"平均编辑距离: {edit_stats['mean_edit_distance']:.2f}")
                print(f"零编辑距离率: {edit_stats['zero_edit_distance_rate']:.4f}")

            # 按错误类型显示统计
            if 'error_type_statistics' in results:
                print(f"\n📊 按错误类型统计:")
                error_stats = results['error_type_statistics']
                for error_type, stats in sorted(error_stats.items(), key=lambda x: x[1]['total'], reverse=True):
                    print(f"  {error_type}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.4f})")

                    # 显示样本示例
                    if stats['samples']:
                        print(f"    示例:")
                        for sample in stats['samples'][:2]:  # 只显示前2个
                            status = "✅" if sample['correct'] else "❌"
                            print(f"      {status} {sample['source'][:30]}... → {sample['prediction'][:30]}...")
        else:
            # 测试集（无ground truth）的统计
            if 'modification_rate' in results:
                print(f"\n📝 修改统计:")
                print(f"修改句子数: {results['modified_count']}")
                print(f"修改率: {results['modification_rate']:.4f}")

        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FCGEC数据集测试")
    parser.add_argument('--model_path', type=str, default=None,
                       help='模型路径')
    parser.add_argument('--data_dir', type=str,
                       default='ChineseErrorCorrector/data/FCGEC_data',
                       help='FCGEC数据集路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/model_test/FCGEC_results',
                       help='结果保存路径')
    parser.add_argument('--output_file', type=str, default='fcgec_evaluation_02.json',
                       help='指定输出文件名（可选，默认自动生成时间戳）')
    parser.add_argument('--use_vllm', action='store_true',
                       help='使用VLLM加速')
    parser.add_argument('--test_set', choices=['validation', 'test', 'both'], default='validation',
                       help='测试集选择')
    parser.add_argument('--max_samples', type=int, default=100,
                       help='最大测试样本数')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批处理大小')
    
    args = parser.parse_args()

    print("🚀 FCGEC数据集测试开始")
    print("=" * 50)
    print(f"模型路径: {args.model_path or TextCorrectConfig.DEFAULT_CKPT_PATH}")
    print(f"数据目录: {args.data_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"使用VLLM: {args.use_vllm}")
    print(f"测试集: {args.test_set}")
    print(f"最大样本数: {args.max_samples}")
    print(f"批大小: {args.batch_size}")

    try:
        # 创建测试器
        tester = FCGECTester(
            model_path=args.model_path,
            use_vllm=args.use_vllm,
            data_dir=args.data_dir,
            batch_size=args.batch_size
        )

        # 初始化模型
        tester.initialize_model()

    except Exception as e:
        logger.error(f"❌ 初始化失败: {e}")
        print(f"❌ 初始化失败: {e}")
        return
    
    # 执行测试
    try:
        if args.test_set in ['validation', 'both']:
            print(f"\n📊 开始验证集测试...")
            valid_results = tester.test_on_validation_set(max_samples=args.max_samples)

            # 生成输出文件名
            if args.output_file:
                valid_output_file = os.path.join(args.output_dir, args.output_file)
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                valid_output_file = os.path.join(args.output_dir, f'fcgec_validation_results_{timestamp}.json')

            # 保存结果
            tester.save_results(valid_results, valid_output_file)

            # 打印摘要
            tester.print_summary(valid_results)

        if args.test_set in ['test', 'both']:
            print(f"\n📊 开始测试集测试...")
            test_results = tester.test_on_test_set(max_samples=args.max_samples)

            # 生成输出文件名
            if args.output_file:
                # 如果指定了输出文件名，为测试集添加后缀
                base_name = os.path.splitext(args.output_file)[0]
                ext = os.path.splitext(args.output_file)[1] or '.json'
                test_output_file = os.path.join(args.output_dir, f'{base_name}_test{ext}')
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                test_output_file = os.path.join(args.output_dir, f'fcgec_test_results_{timestamp}.json')

            # 保存结果
            tester.save_results(test_results, test_output_file)

            # 打印摘要
            tester.print_summary(test_results)

        print(f"\n🎉 FCGEC测试完成！")

    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        print(f"❌ 测试失败: {e}")
        return


if __name__ == '__main__':
    main()
