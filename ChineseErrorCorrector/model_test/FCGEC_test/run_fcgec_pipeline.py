#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FCGEC完整测试流水线
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FCGECPipeline:
    """FCGEC测试流水线"""
    
    def __init__(self, model_path: str = None, use_vllm: bool = False, 
                 max_samples: int = 100, num_processes: int = 2):
        self.model_path = model_path
        self.use_vllm = use_vllm
        self.max_samples = max_samples
        self.num_processes = num_processes
        self.output_dir = "ChineseErrorCorrector/model_test/FCGEC_results"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def run_test(self, test_set: str = "validation") -> str:
        """运行FCGEC测试"""
        logger.info(f"🚀 开始FCGEC测试 - {test_set}集")
        
        # 构建测试命令
        cmd = [
            "python", "ChineseErrorCorrector/model_test/FCGEC_test/test_fcgec.py",
            "--test_set", test_set,
            "--max_samples", str(self.max_samples),
            "--output_dir", self.output_dir
        ]
        
        if self.model_path:
            cmd.extend(["--model_path", self.model_path])
        
        if self.use_vllm:
            cmd.append("--use_vllm")
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        # 运行测试
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
            
            if result.returncode == 0:
                logger.info("✅ FCGEC测试完成")
                
                # 查找生成的结果文件
                result_files = []
                for file in os.listdir(self.output_dir):
                    if file.startswith(f"fcgec_{test_set}_results_") and file.endswith(".json"):
                        result_files.append(os.path.join(self.output_dir, file))
                
                if result_files:
                    # 返回最新的结果文件
                    latest_file = max(result_files, key=os.path.getmtime)
                    logger.info(f"测试结果文件: {latest_file}")
                    return latest_file
                else:
                    raise RuntimeError("未找到测试结果文件")
            else:
                logger.error(f"FCGEC测试失败: {result.stderr}")
                raise RuntimeError(f"测试失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("FCGEC测试超时")
            raise RuntimeError("测试超时")
    
    def run_quick_test(self) -> str:
        """运行快速测试（使用模拟模型）"""
        logger.info(f"🚀 开始FCGEC快速测试")
        
        cmd = [
            "python", "ChineseErrorCorrector/model_test/FCGEC_test/quick_test_fcgec.py",
            "--max_samples", str(self.max_samples),
            "--output_dir", self.output_dir
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("✅ FCGEC快速测试完成")
                
                # 查找生成的结果文件
                result_files = []
                for file in os.listdir(self.output_dir):
                    if file.startswith("fcgec_quick_test_") and file.endswith(".json"):
                        result_files.append(os.path.join(self.output_dir, file))
                
                if result_files:
                    latest_file = max(result_files, key=os.path.getmtime)
                    logger.info(f"快速测试结果文件: {latest_file}")
                    return latest_file
                else:
                    raise RuntimeError("未找到快速测试结果文件")
            else:
                logger.error(f"FCGEC快速测试失败: {result.stderr}")
                raise RuntimeError(f"快速测试失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("FCGEC快速测试超时")
            raise RuntimeError("快速测试超时")
    
    def run_evaluation(self, results_file: str) -> str:
        """运行ChERRANT评估"""
        logger.info(f"📊 开始ChERRANT评估")
        
        eval_output_file = os.path.join(self.output_dir, f"fcgec_evaluation_{self.timestamp}.json")
        
        cmd = [
            "python", "ChineseErrorCorrector/model_test/FCGEC_test/evaluate_fcgec.py",
            "--results_file", results_file,
            "--output_file", eval_output_file,
            "--num_processes", str(self.num_processes)
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                logger.info("✅ ChERRANT评估完成")
                logger.info(f"评估结果文件: {eval_output_file}")
                return eval_output_file
            else:
                logger.error(f"ChERRANT评估失败: {result.stderr}")
                raise RuntimeError(f"评估失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("ChERRANT评估超时")
            raise RuntimeError("评估超时")
    
    def run_full_pipeline(self, use_real_model: bool = True, test_set: str = "validation"):
        """运行完整流水线"""
        logger.info("🚀 开始FCGEC完整测试流水线")
        logger.info("=" * 60)
        logger.info(f"使用真实模型: {use_real_model}")
        logger.info(f"测试集: {test_set}")
        logger.info(f"最大样本数: {self.max_samples}")
        logger.info(f"并行进程数: {self.num_processes}")
        logger.info(f"输出目录: {self.output_dir}")
        
        try:
            # 步骤1: 运行测试
            if use_real_model:
                results_file = self.run_test(test_set)
            else:
                results_file = self.run_quick_test()
            
            # 步骤2: 运行评估
            eval_file = self.run_evaluation(results_file)
            
            # 步骤3: 生成报告
            self.generate_summary_report(results_file, eval_file)
            
            logger.info("🎉 FCGEC完整测试流水线完成！")
            
        except Exception as e:
            logger.error(f"❌ 流水线执行失败: {e}")
            raise
    
    def generate_summary_report(self, results_file: str, eval_file: str):
        """生成汇总报告"""
        import json
        
        logger.info("📝 生成汇总报告")
        
        # 读取结果文件
        with open(results_file, 'r', encoding='utf-8') as f:
            test_results = json.load(f)
        
        with open(eval_file, 'r', encoding='utf-8') as f:
            eval_results = json.load(f)
        
        # 生成汇总报告
        summary_report = {
            'pipeline_time': datetime.now().isoformat(),
            'test_results_file': results_file,
            'evaluation_results_file': eval_file,
            'test_summary': {
                'total_samples': test_results.get('total_samples', 0),
                'exact_match_rate': test_results.get('exact_match_rate', 0),
                'throughput': test_results.get('throughput_samples_per_second', 0),
                'error_type_statistics': test_results.get('error_type_statistics', {})
            },
            'evaluation_summary': {
                'cherrant_metrics': eval_results.get('cherrant_metrics', {}),
                'exact_match_rate': eval_results.get('exact_match_rate', 0)
            }
        }
        
        # 保存汇总报告
        summary_file = os.path.join(self.output_dir, f"fcgec_summary_{self.timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"汇总报告已保存: {summary_file}")
        
        # 打印汇总信息
        print("\n" + "="*60)
        print("FCGEC测试流水线汇总报告")
        print("="*60)
        print(f"测试样本数: {summary_report['test_summary']['total_samples']}")
        print(f"完全匹配率: {summary_report['test_summary']['exact_match_rate']:.4f}")
        print(f"处理速度: {summary_report['test_summary']['throughput']:.2f} 样本/秒")
        
        if summary_report['evaluation_summary']['cherrant_metrics']:
            metrics = summary_report['evaluation_summary']['cherrant_metrics']
            print(f"\nChERRANT评估结果:")
            print(f"  精确率: {metrics.get('precision', 0):.4f}")
            print(f"  召回率: {metrics.get('recall', 0):.4f}")
            print(f"  F0.5分数: {metrics.get('f0.5', 0):.4f}")
            print(f"  F1分数: {metrics.get('f1', 0):.4f}")
        
        print(f"\n文件输出:")
        print(f"  测试结果: {results_file}")
        print(f"  评估结果: {eval_file}")
        print(f"  汇总报告: {summary_file}")
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FCGEC完整测试流水线")
    parser.add_argument('--model_path', type=str, default=None,
                       help='模型路径')
    parser.add_argument('--use_vllm', action='store_true',
                       help='使用VLLM加速')
    parser.add_argument('--test_set', choices=['validation', 'test'], default='validation',
                       help='测试集选择')
    parser.add_argument('--max_samples', type=int, default=100,
                       help='最大测试样本数')
    parser.add_argument('--num_processes', type=int, default=2,
                       help='评估并行进程数')
    parser.add_argument('--quick_test', action='store_true',
                       help='使用快速测试（模拟模型）')
    
    args = parser.parse_args()
    
    print("🚀 FCGEC测试流水线")
    print("=" * 50)
    
    # 创建流水线
    pipeline = FCGECPipeline(
        model_path=args.model_path,
        use_vllm=args.use_vllm,
        max_samples=args.max_samples,
        num_processes=args.num_processes
    )
    
    # 运行流水线
    pipeline.run_full_pipeline(
        use_real_model=not args.quick_test,
        test_set=args.test_set
    )


if __name__ == '__main__':
    main()
