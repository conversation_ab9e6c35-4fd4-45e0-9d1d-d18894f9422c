#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FCGEC数据集ChERRANT评估脚本
"""

import os
import sys
import json
import tempfile
import subprocess
import argparse
from datetime import datetime
from typing import Dict, List
from multiprocessing import Pool, cpu_count

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FCGECChERRANTEvaluator:
    """FCGEC ChERRANT评估器"""
    
    def __init__(self, cherrant_path: str = None):
        self.cherrant_path = cherrant_path or "ChineseErrorCorrector/model_test/evaluation_tools/ChERRANT"
        self.cherrant_abs_path = os.path.abspath(self.cherrant_path)
        
        # 检查ChERRANT工具
        self._check_cherrant_tools()
    
    def _check_cherrant_tools(self):
        """检查ChERRANT工具是否存在"""
        parallel_to_m2_script = os.path.join(self.cherrant_abs_path, 'parallel_to_m2.py')
        compare_m2_script = os.path.join(self.cherrant_abs_path, 'compare_m2_for_evaluation.py')
        
        if not os.path.exists(parallel_to_m2_script):
            raise FileNotFoundError(f"ChERRANT parallel_to_m2.py 不存在: {parallel_to_m2_script}")
        
        if not os.path.exists(compare_m2_script):
            raise FileNotFoundError(f"ChERRANT compare_m2_for_evaluation.py 不存在: {compare_m2_script}")
        
        logger.info(f"✅ ChERRANT工具检查通过: {self.cherrant_abs_path}")
    
    def load_test_results(self, results_file: str) -> List[Dict]:
        """加载测试结果"""
        if not os.path.exists(results_file):
            raise FileNotFoundError(f"结果文件不存在: {results_file}")
        
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取结果列表
        if 'results' in data:
            results = data['results']
        else:
            results = data
        
        # 过滤出有ground_truth的结果
        valid_results = []
        for result in results:
            if 'ground_truth' in result and result['ground_truth']:
                valid_results.append(result)
        
        logger.info(f"✅ 加载测试结果: {len(valid_results)} 条有效结果")
        return valid_results
    
    def create_parallel_files(self, results: List[Dict], temp_dir: str, is_reference: bool = True) -> str:
        """创建parallel格式文件"""
        filename = 'reference.para' if is_reference else 'hypothesis.para'
        para_file = os.path.join(temp_dir, filename)
        
        with open(para_file, 'w', encoding='utf-8') as f:
            for i, result in enumerate(results):
                # 清理BOM字符和特殊字符
                source = result['source'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                if is_reference:
                    target = result['ground_truth'].strip().replace('\ufeff', '')
                    target = target.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                else:
                    target = result['prediction'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                
                f.write(f"{i+1}\t{source}\t{target}\n")
        
        logger.info(f"✅ 创建{'参考' if is_reference else '假设'}文件: {para_file}")
        return para_file
    
    def generate_m2_file_chunk(self, args):
        """生成M2文件的单个块（用于并行处理）"""
        para_file, m2_file, cherrant_path, chunk_id = args
        
        try:
            cmd = [
                'python', 'parallel_to_m2.py',
                '-f', os.path.abspath(para_file),
                '-o', os.path.abspath(m2_file),
                '-g', 'char'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8',
                                  cwd=cherrant_path, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✅ {'参考' if 'reference' in para_file else '假设'}文件块 {chunk_id} 完成")
                return True, chunk_id, ""
            else:
                error_msg = f"M2生成失败: {result.stderr}"
                logger.error(f"❌ 块 {chunk_id} 失败: {error_msg}")
                return False, chunk_id, error_msg
                
        except Exception as e:
            error_msg = f"M2生成异常: {str(e)}"
            logger.error(f"❌ 块 {chunk_id} 异常: {error_msg}")
            return False, chunk_id, error_msg
    
    def run_cherrant_parallel_evaluation(self, results: List[Dict], num_processes: int = None) -> Dict:
        """运行ChERRANT并行评估"""
        if num_processes is None:
            num_processes = min(cpu_count(), 4)
        
        logger.info(f"🚀 使用 {num_processes} 个进程并行生成M2文件...")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 数据分块
            chunk_size = max(1, len(results) // num_processes) if num_processes > 1 else len(results)
            chunks = [results[i:i + chunk_size] for i in range(0, len(results), chunk_size)]
            
            logger.info(f"📊 数据分为 {len(chunks)} 块，每块约 {chunk_size} 个样本")
            
            # 生成参考M2文件
            logger.info("生成参考M2文件...")
            ref_tasks = []
            ref_m2_files = []
            
            for i, chunk in enumerate(chunks):
                chunk_para_file = os.path.join(temp_dir, f'ref_chunk_{i}.para')
                chunk_m2_file = os.path.join(temp_dir, f'ref_chunk_{i}.m2')
                
                # 创建chunk的parallel文件
                with open(chunk_para_file, 'w', encoding='utf-8') as f:
                    for j, result in enumerate(chunk):
                        source = result['source'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                        target = result['ground_truth'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                        f.write(f"{i * chunk_size + j + 1}\t{source}\t{target}\n")
                
                ref_tasks.append((chunk_para_file, chunk_m2_file, self.cherrant_abs_path, i))
                ref_m2_files.append(chunk_m2_file)
            
            # 并行生成参考M2文件
            with Pool(num_processes) as pool:
                ref_results = pool.map(self.generate_m2_file_chunk, ref_tasks)
            
            # 检查参考M2文件生成结果
            ref_success = all(success for success, _, _ in ref_results)
            if not ref_success:
                failed_chunks = [chunk_id for success, chunk_id, error in ref_results if not success]
                raise RuntimeError(f"参考M2文件生成失败，失败块: {failed_chunks}")
            
            # 生成假设M2文件
            logger.info("生成假设M2文件...")
            hyp_tasks = []
            hyp_m2_files = []
            
            for i, chunk in enumerate(chunks):
                chunk_para_file = os.path.join(temp_dir, f'hyp_chunk_{i}.para')
                chunk_m2_file = os.path.join(temp_dir, f'hyp_chunk_{i}.m2')
                
                # 创建chunk的parallel文件
                with open(chunk_para_file, 'w', encoding='utf-8') as f:
                    for j, result in enumerate(chunk):
                        source = result['source'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                        target = result['prediction'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                        f.write(f"{i * chunk_size + j + 1}\t{source}\t{target}\n")
                
                hyp_tasks.append((chunk_para_file, chunk_m2_file, self.cherrant_abs_path, i))
                hyp_m2_files.append(chunk_m2_file)
            
            # 并行生成假设M2文件
            with Pool(num_processes) as pool:
                hyp_results = pool.map(self.generate_m2_file_chunk, hyp_tasks)
            
            # 检查假设M2文件生成结果
            hyp_success = all(success for success, _, _ in hyp_results)
            if not hyp_success:
                failed_chunks = [chunk_id for success, chunk_id, error in hyp_results if not success]
                raise RuntimeError(f"假设M2文件生成失败，失败块: {failed_chunks}")
            
            # 合并M2文件
            logger.info("合并M2文件...")
            ref_m2_file = os.path.join(temp_dir, 'reference.m2')
            hyp_m2_file = os.path.join(temp_dir, 'hypothesis.m2')
            
            # 合并参考M2文件
            with open(ref_m2_file, 'w', encoding='utf-8') as outf:
                for m2_file in ref_m2_files:
                    if os.path.exists(m2_file):
                        with open(m2_file, 'r', encoding='utf-8') as inf:
                            outf.write(inf.read())
            
            # 合并假设M2文件
            with open(hyp_m2_file, 'w', encoding='utf-8') as outf:
                for m2_file in hyp_m2_files:
                    if os.path.exists(m2_file):
                        with open(m2_file, 'r', encoding='utf-8') as inf:
                            outf.write(inf.read())
            
            # 检查合并后的文件大小
            ref_size = os.path.getsize(ref_m2_file) if os.path.exists(ref_m2_file) else 0
            hyp_size = os.path.getsize(hyp_m2_file) if os.path.exists(hyp_m2_file) else 0
            
            logger.info(f"📊 参考M2文件大小: {ref_size} 字符")
            logger.info(f"📊 假设M2文件大小: {hyp_size} 字符")
            
            if ref_size == 0 or hyp_size == 0:
                raise RuntimeError("M2文件为空，无法进行评估")
            
            # 运行ChERRANT评估
            logger.info("计算评估指标...")
            cmd = [
                'python', 'compare_m2_for_evaluation.py',
                '-hyp', os.path.abspath(hyp_m2_file),
                '-ref', os.path.abspath(ref_m2_file),
                '-b', '0.5'
            ]
            
            logger.info(f"计算评估指标: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8',
                                  cwd=self.cherrant_abs_path, timeout=300)
            
            if result.returncode != 0:
                raise RuntimeError(f"ChERRANT评估失败: {result.stderr}")
            
            # 解析ChERRANT输出
            metrics = self.parse_cherrant_output(result.stdout)
            
            logger.info("✅ ChERRANT并行评估完成")
            return metrics
    
    def parse_cherrant_output(self, cherrant_output: str) -> Dict:
        """解析ChERRANT输出"""
        logger.info("=== 调试ChERRANT输出 ===")
        logger.info(f"原始输出: {repr(cherrant_output)}")
        
        lines = cherrant_output.strip().split('\n')
        logger.info(f"输出行数: {len(lines)}")
        
        # 查找标题行和数据行
        header_line = None
        data_line = None
        
        for i, line in enumerate(lines):
            logger.info(f"行 {i}: {repr(line)}")
            
            # 查找标题行（包含TP、FP、FN等）
            if line.startswith('TP') and 'FP' in line and 'FN' in line:
                header_line = line
                logger.info(f"找到标题行: {line}")
                
                # 查找下一行的数据
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line and not next_line.startswith('='):
                        data_line = next_line
                        logger.info(f"找到数据行: {data_line}")
                        break
        
        if header_line and data_line:
            try:
                # 解析数据行
                if '\t' in data_line:
                    # 制表符分隔
                    parts = data_line.split('\t')
                else:
                    # 空格分隔
                    parts = data_line.split()
                
                logger.info(f"数据部分: {parts}")
                
                if len(parts) >= 6:
                    tp = int(parts[0])
                    fp = int(parts[1])
                    fn = int(parts[2])
                    precision = float(parts[3])
                    recall = float(parts[4])
                    f_score_from_cherrant = float(parts[5])

                    # 正确计算F0.5分数
                    f0_5_calculated = (1.25 * precision * recall) / (0.25 * precision + recall) if (0.25 * precision + recall) > 0 else 0.0
                    
                    metrics = {
                        'tp': tp,
                        'fp': fp,
                        'fn': fn,
                        'precision': precision,
                        'recall': recall,
                        'f0.5': f0_5_calculated,
                        'f1': 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
                    }

                    logger.info(f"解析ChERRANT结果: TP={tp}, FP={fp}, FN={fn}, Prec={precision:.4f}, Rec={recall:.4f}")
                    logger.info(f"F0.5计算: ChERRANT原始={f_score_from_cherrant:.4f}, 重新计算={f0_5_calculated:.4f}")

                    return metrics
                else:
                    logger.error(f"数据行字段数量不足: {len(parts)}")
                    
            except (ValueError, IndexError) as e:
                logger.error(f"解析数据行失败: {e}")
                logger.error(f"尝试解析的值: {parts}")
        
        raise ValueError(f"无法解析ChERRANT输出，未找到有效的标题行和数据行")
    
    def evaluate_results(self, results_file: str, num_processes: int = None) -> Dict:
        """评估测试结果"""
        logger.info(f"📊 开始评估FCGEC测试结果: {results_file}")
        
        # 加载测试结果
        results = self.load_test_results(results_file)
        
        if not results:
            raise ValueError("没有找到有效的测试结果")
        
        # 计算简单指标
        exact_matches = sum(1 for r in results if r['prediction'] == r['ground_truth'])
        exact_match_rate = exact_matches / len(results)
        
        logger.info(f"简单评估: {exact_matches}/{len(results)} = {exact_match_rate:.4f}")
        
        # 运行ChERRANT评估
        try:
            cherrant_metrics = self.run_cherrant_parallel_evaluation(results, num_processes)
            logger.info(f"ChERRANT结果: P={cherrant_metrics['precision']:.4f}, R={cherrant_metrics['recall']:.4f}, F0.5={cherrant_metrics['f0.5']:.4f}")
        except Exception as e:
            logger.warning(f"ChERRANT评估失败: {e}")
            logger.info("回退到简单评估")
            cherrant_metrics = None
        
        # 组合评估结果
        evaluation_results = {
            'evaluation_time': datetime.now().isoformat(),
            'results_file': results_file,
            'total_samples': len(results),
            'exact_match_count': exact_matches,
            'exact_match_rate': exact_match_rate,
            'cherrant_metrics': cherrant_metrics
        }
        
        return evaluation_results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FCGEC ChERRANT评估")
    parser.add_argument('--results_file', type=str, 
                        default='ChineseErrorCorrector/model_test/FCGEC_results/fcgec_evaluation_02.json',
                       help='测试结果文件路径')
    parser.add_argument('--output_file', type=str,
                       default='ChineseErrorCorrector/model_test/FCGEC_results/fcgec_evaluation_result_02.json',
                       help='评估结果输出文件')
    parser.add_argument('--num_processes', type=int, default=None,
                       help='并行进程数')
    parser.add_argument('--cherrant_path', type=str, default=None,
                       help='ChERRANT工具路径')
    
    args = parser.parse_args()
    
    print("🚀 FCGEC ChERRANT评估开始")
    print("=" * 50)
    print(f"结果文件: {args.results_file}")
    print(f"输出文件: {args.output_file}")
    print(f"并行进程数: {args.num_processes or 'auto'}")
    
    # 创建评估器
    evaluator = FCGECChERRANTEvaluator(cherrant_path=args.cherrant_path)
    
    # 运行评估
    evaluation_results = evaluator.evaluate_results(args.results_file, args.num_processes)
    
    # 保存评估结果
    output_dir = os.path.dirname(args.output_file)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    with open(args.output_file, 'w', encoding='utf-8') as f:
        json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
    
    # 打印评估报告
    print("\n" + "="*60)
    print("FCGEC数据集评估报告")
    print("="*60)
    print(f"评估时间: {evaluation_results['evaluation_time']}")
    print(f"测试样本数: {evaluation_results['total_samples']}")
    print(f"完全匹配数: {evaluation_results['exact_match_count']}")
    print(f"完全匹配率: {evaluation_results['exact_match_rate']:.4f}")
    
    if evaluation_results['cherrant_metrics']:
        metrics = evaluation_results['cherrant_metrics']
        print(f"\n=== ChERRANT评估结果 ===")
        print(f"精确率 (Precision): {metrics['precision']:.4f}")
        print(f"召回率 (Recall): {metrics['recall']:.4f}")
        print(f"F0.5分数: {metrics['f0.5']:.4f}")
        print(f"F1分数: {metrics['f1']:.4f}")
        print(f"TP: {metrics['tp']}, FP: {metrics['fp']}, FN: {metrics['fn']}")
    
    print(f"\n评估报告已保存到: {args.output_file}")
    print("=== 评估完成 ===")


if __name__ == '__main__':
    main()
