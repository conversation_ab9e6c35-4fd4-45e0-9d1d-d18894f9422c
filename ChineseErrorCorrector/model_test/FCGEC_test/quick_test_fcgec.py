#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FCGEC快速测试脚本（不加载模型，用于验证流程）
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from typing import List, Dict

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.model_test.FCGEC_test.fcgec_data_loader import FCGECDataLoader
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockCorrector:
    """模拟纠错器，用于测试流程"""

    def __init__(self):
        self.correction_rules = {
            '降低': '缩小',
            '提高': '提升',
            '增加': '增大',
            '减少': '减小',
            '很好': '非常好',
            '不错': '很棒'
        }

    def hf_infer(self, texts: List[str]) -> List[Dict]:
        """模拟HF推理"""
        results = []
        for text in texts:
            corrected = self._correct_text(text)
            results.append({'corrected_text': corrected})
        return results

    def vllm_infer(self, texts: List[str]) -> List[Dict]:
        """模拟VLLM推理"""
        return self.hf_infer(texts)  # 使用相同的逻辑

    def _correct_text(self, text: str) -> str:
        """模拟文本纠错"""
        corrected = text

        # 应用简单的替换规则
        for wrong, correct in self.correction_rules.items():
            corrected = corrected.replace(wrong, correct)

        # 模拟一些随机修改
        if '的' in corrected and len(corrected) > 20:
            # 有时候删除一个"的"
            corrected = corrected.replace('的', '', 1)

        return corrected


class FCGECQuickTester:
    """FCGEC快速测试器"""

    def __init__(self, batch_size: int = 8):
        self.corrector = MockCorrector()
        self.data_loader = FCGECDataLoader()
        self.batch_size = batch_size
    
    def test_on_validation_set(self, max_samples: int = 50) -> Dict:
        """在验证集上快速测试"""
        logger.info(f"📊 FCGEC验证集快速测试 (最大样本数: {max_samples})")
        
        # 加载验证数据
        valid_data = self.data_loader.load_validation_data()
        
        # 创建测试子集
        test_data = self.data_loader.create_test_subset(valid_data, max_samples)
        
        logger.info(f"测试样本数: {len(test_data)}")
        
        # 准备测试数据
        test_sentences = [sample['sentence'] for sample in test_data]

        # 开始批处理测试
        results = []
        start_time = time.time()

        # 批处理推理
        total_batches = (len(test_sentences) + self.batch_size - 1) // self.batch_size
        logger.info(f"开始批处理推理，批大小: {self.batch_size}, 总批数: {total_batches}")

        predictions = []
        for i in range(0, len(test_sentences), self.batch_size):
            batch_sentences = test_sentences[i:i + self.batch_size]
            batch_idx = i // self.batch_size + 1

            if batch_idx % 5 == 0 or batch_idx == total_batches:
                logger.info(f"处理批次: {batch_idx}/{total_batches}")

            # 批处理推理
            batch_results = self.corrector.hf_infer(batch_sentences)
            for result in batch_results:
                predictions.append(result['corrected_text'])

        # 构建结果
        for i, (sample, prediction) in enumerate(zip(test_data, predictions)):
            result = {
                'id': sample['id'],
                'source': sample['sentence'],
                'prediction': prediction,
                'ground_truth': sample['target'],
                'error_flag': sample['error_flag'],
                'error_type': sample['error_type'],
                'operations': sample['operations']
            }
            results.append(result)
        
        end_time = time.time()
        
        # 计算统计信息
        total_time = end_time - start_time
        avg_time = total_time / len(results) if results else 0
        throughput = len(results) / total_time if total_time > 0 else 0
        
        # 计算完全匹配率
        exact_matches = sum(1 for r in results if r['prediction'] == r['ground_truth'])
        exact_match_rate = exact_matches / len(results) if results else 0
        
        # 按错误类型统计
        error_type_stats = {}
        for result in results:
            error_type = result['error_type']
            if error_type not in error_type_stats:
                error_type_stats[error_type] = {'total': 0, 'correct': 0}
            error_type_stats[error_type]['total'] += 1
            if result['prediction'] == result['ground_truth']:
                error_type_stats[error_type]['correct'] += 1
        
        # 计算各错误类型的准确率
        for error_type in error_type_stats:
            stats = error_type_stats[error_type]
            stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
        
        test_summary = {
            'test_time': datetime.now().isoformat(),
            'model_type': 'MockCorrector',
            'batch_size': self.batch_size,
            'total_samples': len(results),
            'total_time_seconds': total_time,
            'average_time_per_sample': avg_time,
            'throughput_samples_per_second': throughput,
            'exact_match_count': exact_matches,
            'exact_match_rate': exact_match_rate,
            'error_type_statistics': error_type_stats,
            'results': results
        }
        
        return test_summary
    
    def save_results(self, results: Dict, output_file: str):
        """保存测试结果"""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 测试结果已保存到: {output_file}")
    
    def print_summary(self, results: Dict):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("FCGEC数据集快速测试报告")
        print("="*60)
        print(f"测试时间: {results['test_time']}")
        print(f"模型类型: {results['model_type']}")
        print(f"批大小: {results['batch_size']}")
        print(f"测试样本数: {results['total_samples']}")
        print(f"总耗时: {results['total_time_seconds']:.2f} 秒")
        print(f"平均耗时: {results['average_time_per_sample']:.3f} 秒/样本")
        print(f"处理速度: {results['throughput_samples_per_second']:.2f} 样本/秒")
        print(f"完全匹配数: {results['exact_match_count']}")
        print(f"完全匹配率: {results['exact_match_rate']:.4f}")
        
        # 按错误类型显示统计
        print(f"\n📊 按错误类型统计:")
        error_stats = results['error_type_statistics']
        for error_type, stats in sorted(error_stats.items(), key=lambda x: x[1]['total'], reverse=True):
            print(f"  {error_type}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.4f})")
        
        # 显示一些样本结果
        print(f"\n📝 样本结果 (前5个):")
        for i, result in enumerate(results['results'][:5]):
            print(f"\n样本 {i+1}:")
            print(f"  原句: {result['source'][:50]}...")
            print(f"  预测: {result['prediction'][:50]}...")
            print(f"  标准: {result['ground_truth'][:50]}...")
            print(f"  匹配: {'✅' if result['prediction'] == result['ground_truth'] else '❌'}")
            print(f"  错误类型: {result['error_type']}")
        
        print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FCGEC数据集快速测试")
    parser.add_argument('--max_samples', type=int, default=50,
                       help='最大测试样本数')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='批处理大小')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/model_test/FCGEC_results',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🚀 FCGEC数据集快速测试开始")
    print("=" * 50)
    print(f"最大样本数: {args.max_samples}")
    print(f"批大小: {args.batch_size}")
    print(f"输出目录: {args.output_dir}")

    # 创建测试器
    tester = FCGECQuickTester(batch_size=args.batch_size)
    
    # 执行测试
    print(f"\n📊 开始验证集测试...")
    results = tester.test_on_validation_set(max_samples=args.max_samples)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f'fcgec_quick_test_{timestamp}.json')
    tester.save_results(results, output_file)
    
    # 打印摘要
    tester.print_summary(results)
    
    print(f"\n🎉 FCGEC快速测试完成！")
    print(f"结果文件: {output_file}")


if __name__ == '__main__':
    main()
