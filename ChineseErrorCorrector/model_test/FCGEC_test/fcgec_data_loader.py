#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FCGEC数据加载器
"""

import json
import os
import re
from typing import Dict, List, Tuple, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FCGECDataLoader:
    """FCGEC数据加载器"""
    
    def __init__(self, data_dir: str = "ChineseErrorCorrector/data/FCGEC_data"):
        self.data_dir = data_dir
        self.test_file = os.path.join(data_dir, "FCGEC_test.json")
        self.valid_file = os.path.join(data_dir, "FCGEC_valid.json")
        
    def load_test_data(self) -> List[Dict]:
        """加载测试数据"""
        if not os.path.exists(self.test_file):
            raise FileNotFoundError(f"测试文件不存在: {self.test_file}")
        
        with open(self.test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        test_samples = []
        for sample_id, sample_data in data.items():
            test_samples.append({
                'id': sample_id,
                'sentence': sample_data['sentence'],
                'version': sample_data.get('version', 'unknown')
            })
        
        logger.info(f"✅ 加载FCGEC测试数据: {len(test_samples)} 条")
        return test_samples
    
    def load_validation_data(self) -> List[Dict]:
        """加载验证数据（包含标注信息）"""
        if not os.path.exists(self.valid_file):
            raise FileNotFoundError(f"验证文件不存在: {self.valid_file}")
        
        with open(self.valid_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        valid_samples = []
        for sample_id, sample_data in data.items():
            # 解析操作信息
            operations = self._parse_operations(sample_data.get('operation', '[]'))
            
            # 生成目标句子
            target_sentence = self._apply_operations(
                sample_data['sentence'], 
                operations
            )
            
            valid_samples.append({
                'id': sample_id,
                'sentence': sample_data['sentence'],
                'target': target_sentence,
                'error_flag': sample_data.get('error_flag', 0),
                'error_type': sample_data.get('error_type', '*'),
                'operations': operations,
                'version': sample_data.get('version', 'unknown')
            })
        
        logger.info(f"✅ 加载FCGEC验证数据: {len(valid_samples)} 条")
        return valid_samples
    
    def _parse_operations(self, operation_str: str) -> List[Dict]:
        """解析操作字符串"""
        try:
            if not operation_str or operation_str == '[]':
                return []
            
            operations = json.loads(operation_str)
            return operations if isinstance(operations, list) else [operations]
        except json.JSONDecodeError:
            logger.warning(f"无法解析操作字符串: {operation_str}")
            return []
    
    def _apply_operations(self, sentence: str, operations: List[Dict]) -> str:
        """应用编辑操作生成目标句子"""
        if not operations:
            return sentence
        
        # 将句子转换为字符列表
        chars = list(sentence)
        
        for operation in operations:
            if 'Switch' in operation:
                # 交换操作
                switch_order = operation['Switch']
                if len(switch_order) == len(chars):
                    new_chars = [''] * len(chars)
                    for i, new_pos in enumerate(switch_order):
                        if 0 <= new_pos < len(chars):
                            new_chars[i] = chars[new_pos]
                    chars = new_chars
            
            elif 'Delete' in operation:
                # 删除操作
                delete_positions = sorted(operation['Delete'], reverse=True)
                for pos in delete_positions:
                    if 0 <= pos < len(chars):
                        chars.pop(pos)
            
            elif 'Insert' in operation:
                # 插入操作
                inserts = sorted(operation['Insert'], key=lambda x: x['pos'], reverse=True)
                for insert_op in inserts:
                    pos = insert_op['pos']
                    labels = insert_op.get('label', [])
                    if labels and 0 <= pos <= len(chars):
                        # 插入第一个标签
                        chars.insert(pos, labels[0])
            
            elif 'Modify' in operation:
                # 修改操作 - 改进为词级别替换
                modifies = operation['Modify']
                for modify_op in modifies:
                    pos = modify_op['pos']
                    labels = modify_op.get('label', [])
                    tag = modify_op.get('tag', '')

                    if labels and 0 <= pos < len(chars):
                        # 根据标签长度和上下文智能替换
                        replacement = labels[0]

                        # 如果替换词比原字符长，需要特殊处理
                        if len(replacement) > 1:
                            # 尝试找到完整的词进行替换
                            sentence_str = ''.join(chars)

                            # 常见的词级别替换模式
                            word_replacements = {
                                '降低': ['缩小', '减小', '缩减', '减缩'],
                                '提高': ['提升', '增强', '改善'],
                                '增加': ['增大', '增多', '提升'],
                                '减少': ['减小', '降低', '缩减'],
                            }

                            # 查找需要替换的词
                            for original_word, possible_replacements in word_replacements.items():
                                if replacement in possible_replacements and original_word in sentence_str:
                                    # 执行词级别替换
                                    sentence_str = sentence_str.replace(original_word, replacement, 1)
                                    chars = list(sentence_str)
                                    break
                            else:
                                # 如果没有找到匹配的词，回退到字符替换
                                chars[pos] = replacement
                        else:
                            # 单字符替换
                            chars[pos] = replacement
        
        return ''.join(chars)
    
    def get_error_type_statistics(self, data: List[Dict]) -> Dict[str, int]:
        """获取错误类型统计"""
        error_stats = {}
        for sample in data:
            error_type = sample.get('error_type', '*')
            error_stats[error_type] = error_stats.get(error_type, 0) + 1
        
        return error_stats
    
    def filter_by_error_type(self, data: List[Dict], error_types: List[str]) -> List[Dict]:
        """根据错误类型过滤数据"""
        filtered_data = []
        for sample in data:
            if sample.get('error_type', '*') in error_types:
                filtered_data.append(sample)
        
        return filtered_data
    
    def create_test_subset(self, data: List[Dict], max_samples: int = 100) -> List[Dict]:
        """创建测试子集"""
        if len(data) <= max_samples:
            return data
        
        # 均匀采样
        step = len(data) // max_samples
        subset = []
        for i in range(0, len(data), step):
            if len(subset) < max_samples:
                subset.append(data[i])
        
        logger.info(f"创建测试子集: {len(subset)} 条样本")
        return subset


def main():
    """测试数据加载器"""
    print("🔍 FCGEC数据加载器测试")
    print("=" * 50)
    
    # 创建数据加载器
    loader = FCGECDataLoader()
    
    try:
        # 加载验证数据
        print("📖 加载验证数据...")
        valid_data = loader.load_validation_data()
        
        # 显示前几个样本
        print(f"\n📊 验证数据样本 (前3条):")
        for i, sample in enumerate(valid_data[:3]):
            print(f"\n样本 {i+1}:")
            print(f"  ID: {sample['id']}")
            print(f"  原句: {sample['sentence']}")
            print(f"  目标: {sample['target']}")
            print(f"  错误标志: {sample['error_flag']}")
            print(f"  错误类型: {sample['error_type']}")
            print(f"  操作数量: {len(sample['operations'])}")
        
        # 错误类型统计
        print(f"\n📈 错误类型统计:")
        error_stats = loader.get_error_type_statistics(valid_data)
        for error_type, count in sorted(error_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {error_type}: {count} 条")
        
        # 加载测试数据
        print(f"\n📖 加载测试数据...")
        test_data = loader.load_test_data()
        print(f"测试数据样本数: {len(test_data)}")
        
        # 创建小测试集
        print(f"\n🎯 创建小测试集...")
        test_subset = loader.create_test_subset(test_data, max_samples=50)
        print(f"测试子集样本数: {len(test_subset)}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")


if __name__ == '__main__':
    main()
