# FCGEC数据集测试

本目录包含对FCGEC (Fine-grained Chinese Grammar Error Correction) 数据集的测试和评估工具。

## 📊 **数据集介绍**

FCGEC是一个细粒度的中文语法纠错数据集，包含：
- **训练集**: FCGEC_train.json
- **验证集**: FCGEC_valid.json (包含标注)
- **测试集**: FCGEC_test.json (无标注)

### 数据格式
```json
{
    "sample_id": {
        "sentence": "原始句子",
        "error_flag": 1,  // 是否有错误 (仅验证集)
        "error_type": "IWC",  // 错误类型 (仅验证集)
        "operation": "[{\"Modify\": [...]}]",  // 编辑操作 (仅验证集)
        "version": "FCGEC EMNLP 2022"
    }
}
```

### 支持的编辑操作
- **Switch**: 交换操作
- **Delete**: 删除操作  
- **Insert**: 插入操作
- **Modify**: 修改操作

## 🛠️ **工具组件**

### 1. fcgec_data_loader.py
数据加载和预处理工具
- 加载FCGEC测试和验证数据
- 解析编辑操作并生成目标句子
- 提供错误类型统计和数据过滤功能

### 2. test_fcgec.py
模型测试脚本
- 在验证集和测试集上运行模型
- 计算基本性能指标
- 支持VLLM加速推理

### 3. evaluate_fcgec.py
ChERRANT评估脚本
- 使用ChERRANT工具进行精确评估
- 支持并行处理加速
- 计算P/R/F0.5等详细指标

## 🚀 **使用方法**

### 基本测试
```bash
# 在验证集上测试 (有标注数据)
python ChineseErrorCorrector/model_test/FCGEC_test/test_fcgec.py \
    --data_dir ChineseErrorCorrector/data/FCGEC_data \
    --output_dir ChineseErrorCorrector/model_test/FCGEC_results \
    --test_set validation \
    --max_samples 100

# 在测试集上测试 (无标注数据)
python ChineseErrorCorrector/model_test/FCGEC_test/test_fcgec.py \
    --data_dir ChineseErrorCorrector/data/FCGEC_data \
    --output_dir ChineseErrorCorrector/model_test/FCGEC_results \
    --test_set test \
    --max_samples 100

# 指定输出文件名
python ChineseErrorCorrector/model_test/FCGEC_test/test_fcgec.py \
    --data_dir ChineseErrorCorrector/data/FCGEC_data \
    --output_dir ChineseErrorCorrector/model_test/FCGEC_results \
    --output_file my_fcgec_results.json \
    --test_set validation \
    --max_samples 50
```

### 使用VLLM加速
```bash
python ChineseErrorCorrector/model_test/FCGEC_test/test_fcgec.py \
    --use_vllm \
    --test_set validation \
    --max_samples 200
```

### 自定义模型路径
```bash
python ChineseErrorCorrector/model_test/FCGEC_test/test_fcgec.py \
    --model_path /path/to/your/model \
    --test_set validation \
    --max_samples 100
```

### ChERRANT评估
```bash
# 评估测试结果
python ChineseErrorCorrector/model_test/FCGEC_test/evaluate_fcgec.py \
    --results_file ChineseErrorCorrector/model_test/FCGEC_results/fcgec_validation_results_20240101_120000.json \
    --output_file ChineseErrorCorrector/model_test/FCGEC_results/fcgec_evaluation.json

# 使用多进程加速
python ChineseErrorCorrector/model_test/FCGEC_test/evaluate_fcgec.py \
    --results_file results.json \
    --num_processes 4
```

## 📁 **输出文件**

### 测试结果文件
```json
{
    "test_time": "2024-01-01T12:00:00",
    "model_path": "/path/to/model",
    "use_vllm": false,
    "total_samples": 100,
    "total_time_seconds": 120.5,
    "average_time_per_sample": 1.205,
    "throughput_samples_per_second": 0.83,
    "exact_match_count": 75,
    "exact_match_rate": 0.75,
    "error_type_statistics": {
        "IWC": {"total": 30, "correct": 25, "accuracy": 0.833},
        "CR": {"total": 20, "correct": 15, "accuracy": 0.75}
    },
    "results": [...]
}
```

### 评估结果文件
```json
{
    "evaluation_time": "2024-01-01T12:30:00",
    "results_file": "test_results.json",
    "total_samples": 100,
    "exact_match_count": 75,
    "exact_match_rate": 0.75,
    "cherrant_metrics": {
        "tp": 85,
        "fp": 15,
        "fn": 25,
        "precision": 0.85,
        "recall": 0.773,
        "f0.5": 0.823,
        "f1": 0.810
    }
}
```

## 📊 **评估指标**

### 基本指标
- **完全匹配率**: 预测结果与标准答案完全一致的比例
- **处理速度**: 每秒处理的样本数 (支持批处理加速)
- **修改率**: 被模型修改的句子比例
- **按错误类型统计**: 不同错误类型的准确率

### 高级指标
- **编辑距离统计**: 预测与标准答案的字符级差异
- **零编辑距离率**: 完全匹配的样本比例
- **样本分析**: 典型错误样本展示

### ChERRANT指标
- **Precision**: 预测修改中正确的比例
- **Recall**: 应该修改的地方被正确修改的比例
- **F0.5**: 偏向精确率的F分数
- **F1**: 精确率和召回率的调和平均

## 🔧 **参数说明**

### test_fcgec.py 参数
- `--model_path`: 模型路径 (默认使用配置文件中的路径)
- `--data_dir`: FCGEC数据集路径 (默认: ChineseErrorCorrector/data/FCGEC_data)
- `--output_dir`: 结果保存路径 (默认: ChineseErrorCorrector/model_test/FCGEC_results)
- `--output_file`: 指定输出文件名 (可选，默认自动生成时间戳)
- `--use_vllm`: 是否使用VLLM加速
- `--test_set`: 测试集选择 (validation/test/both)
- `--max_samples`: 最大测试样本数
- `--batch_size`: 批处理大小 (默认: 16)

### evaluate_fcgec.py 参数
- `--results_file`: 测试结果文件路径 (必需)
- `--output_file`: 评估结果输出文件
- `--num_processes`: 并行进程数
- `--cherrant_path`: ChERRANT工具路径

## 📈 **性能基准**

### 预期性能指标
基于ChineseErrorCorrector3-4B模型：

| 数据集 | 样本数 | 完全匹配率 | F0.5分数 | 处理速度 |
|--------|--------|------------|----------|----------|
| FCGEC验证集 | 1,400 | 65-75% | 0.70-0.80 | 8-12 样本/秒 |
| FCGEC测试集 | 1,200 | - | - | 8-12 样本/秒 |

### 错误类型分布
常见错误类型及其在验证集中的分布：
- **IWC** (Inappropriate Word Choice): ~30%
- **CR** (Collocation Redundancy): ~20%
- **MS** (Missing Structure): ~15%
- **其他**: ~35%

## 🔍 **数据质量检查**

### 数据加载器功能
```python
from ChineseErrorCorrector.model_test.FCGEC_test.fcgec_data_loader import FCGECDataLoader

loader = FCGECDataLoader()

# 加载验证数据
valid_data = loader.load_validation_data()

# 错误类型统计
error_stats = loader.get_error_type_statistics(valid_data)

# 按错误类型过滤
iwc_data = loader.filter_by_error_type(valid_data, ['IWC'])

# 创建测试子集
test_subset = loader.create_test_subset(valid_data, max_samples=100)
```

## ⚠️ **注意事项**

1. **数据依赖**: 确保FCGEC数据文件存在于正确路径
2. **ChERRANT工具**: 评估需要ChERRANT工具正确安装
3. **内存使用**: 大规模测试可能需要较多内存
4. **并行处理**: 使用多进程时注意系统资源

## 🐛 **常见问题**

### Q: 数据文件不存在
A: 检查数据文件路径，确保FCGEC数据已正确放置在data目录

### Q: ChERRANT评估失败
A: 检查ChERRANT工具安装，确保parallel_to_m2.py和compare_m2_for_evaluation.py存在

### Q: 内存不足
A: 减少max_samples参数或使用更少的并行进程

### Q: 处理速度慢
A: 考虑使用--use_vllm参数或增加并行进程数

## 📚 **相关资源**

- [FCGEC论文](https://arxiv.org/abs/2210.12364)
- [ChERRANT工具](https://github.com/HillZhang1999/ChERRANT)
- [FCGEC数据集](https://github.com/xlxwalex/FCGEC)

## 🤝 **贡献指南**

欢迎提交Issue和Pull Request来改进FCGEC测试工具：
1. 报告Bug或提出改进建议
2. 添加新的评估指标
3. 优化性能和用户体验
4. 完善文档和示例
