#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多层次提示词测试分析报告生成器
"""

import os
import sys
import json
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


class PromptAnalysisReporter:
    """提示词分析报告生成器"""
    
    def __init__(self):
        self.results_data = None
        self.analysis_data = None
    
    def load_results(self, results_path: str):
        """加载测试结果"""
        print(f"📂 加载测试结果: {results_path}")
        
        with open(results_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.results_data = data['strategy_results']
        self.analysis_data = data['analysis']
        
        print(f"✅ 加载了 {len(self.results_data)} 个策略的测试结果")
    
    def generate_performance_comparison(self, output_dir: str):
        """生成性能对比图表"""
        print("📊 生成性能对比图表...")
        
        strategies = list(self.results_data.keys())
        accuracies = [self.results_data[s]['accuracy'] for s in strategies]
        modification_rates = [self.results_data[s]['modification_rate'] for s in strategies]
        avg_times = [self.results_data[s]['avg_time_per_sample'] for s in strategies]
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('多层次提示词策略性能对比分析', fontsize=16, fontweight='bold')
        
        # 1. 准确率对比
        bars1 = ax1.bar(strategies, accuracies, color='skyblue', alpha=0.8)
        ax1.set_title('各策略准确率对比', fontweight='bold')
        ax1.set_ylabel('准确率')
        ax1.set_ylim(0, max(accuracies) * 1.2 if max(accuracies) > 0 else 0.2)
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, acc in zip(bars1, accuracies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{acc:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 2. 修改率对比
        bars2 = ax2.bar(strategies, modification_rates, color='lightcoral', alpha=0.8)
        ax2.set_title('各策略修改率对比', fontweight='bold')
        ax2.set_ylabel('修改率')
        ax2.set_ylim(0, 1.1)
        ax2.tick_params(axis='x', rotation=45)
        
        for bar, rate in zip(bars2, modification_rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{rate:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 3. 平均处理时间对比
        bars3 = ax3.bar(strategies, avg_times, color='lightgreen', alpha=0.8)
        ax3.set_title('各策略平均处理时间对比', fontweight='bold')
        ax3.set_ylabel('平均时间 (秒/样本)')
        ax3.tick_params(axis='x', rotation=45)
        
        for bar, time in zip(bars3, avg_times):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                    f'{time:.2f}', ha='center', va='bottom', fontsize=9)
        
        # 4. 准确率vs修改率散点图
        colors = plt.cm.Set3(np.linspace(0, 1, len(strategies)))
        scatter = ax4.scatter(modification_rates, accuracies, c=colors, s=100, alpha=0.7)
        ax4.set_xlabel('修改率')
        ax4.set_ylabel('准确率')
        ax4.set_title('准确率 vs 修改率关系', fontweight='bold')
        
        # 添加策略标签
        for i, strategy in enumerate(strategies):
            ax4.annotate(strategy, (modification_rates[i], accuracies[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(output_dir, 'performance_comparison.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 性能对比图表已保存: {chart_path}")
    
    def generate_improvement_analysis(self, output_dir: str):
        """生成改进分析图表"""
        print("📈 生成改进分析图表...")
        
        # 提取改进数据
        baseline_accuracy = self.results_data['baseline']['accuracy']
        
        strategies = []
        accuracy_gains = []
        relative_gains = []
        
        for strategy, gains in self.analysis_data['performance_gains'].items():
            strategies.append(strategy)
            accuracy_gains.append(gains['accuracy_gain'])
            relative_gains.append(gains['relative_accuracy_gain'])
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('提示词策略改进效果分析', fontsize=16, fontweight='bold')
        
        # 1. 绝对改进
        colors = ['green' if gain > 0 else 'red' for gain in accuracy_gains]
        bars1 = ax1.bar(strategies, accuracy_gains, color=colors, alpha=0.7)
        ax1.set_title('相对基线策略的准确率改进', fontweight='bold')
        ax1.set_ylabel('准确率改进')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        for bar, gain in zip(bars1, accuracy_gains):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., 
                    height + (0.005 if height >= 0 else -0.01),
                    f'{gain:.3f}', ha='center', 
                    va='bottom' if height >= 0 else 'top', fontsize=9)
        
        # 2. 策略排名
        ranking_data = self.analysis_data['accuracy_ranking']
        strategies_ranked = [item['strategy'] for item in ranking_data]
        accuracies_ranked = [item['accuracy'] for item in ranking_data]
        
        bars2 = ax2.barh(strategies_ranked, accuracies_ranked, color='steelblue', alpha=0.7)
        ax2.set_title('策略准确率排名', fontweight='bold')
        ax2.set_xlabel('准确率')
        
        for bar, acc in zip(bars2, accuracies_ranked):
            width = bar.get_width()
            ax2.text(width + 0.005, bar.get_y() + bar.get_height()/2.,
                    f'{acc:.3f}', ha='left', va='center', fontsize=9)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(output_dir, 'improvement_analysis.png')
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 改进分析图表已保存: {chart_path}")
    
    def generate_detailed_report(self, output_dir: str):
        """生成详细分析报告"""
        print("📝 生成详细分析报告...")
        
        report_path = os.path.join(output_dir, 'detailed_analysis_report.md')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 多层次提示词测试详细分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 测试概述
            f.write("## 1. 测试概述\n\n")
            f.write(f"- **测试策略数量**: {len(self.results_data)}\n")
            f.write(f"- **测试样本数量**: {self.results_data['baseline']['total_samples']}\n")
            f.write(f"- **最佳策略**: {self.analysis_data['best_accuracy_strategy']}\n")
            f.write(f"- **最大准确率提升**: {self.analysis_data['max_accuracy_gain']:.4f}\n\n")
            
            # 策略详细分析
            f.write("## 2. 各策略详细分析\n\n")
            
            for strategy_name, results in self.results_data.items():
                f.write(f"### {strategy_name}\n\n")
                f.write(f"- **准确率**: {results['accuracy']:.4f}\n")
                f.write(f"- **修改率**: {results['modification_rate']:.4f}\n")
                f.write(f"- **平均处理时间**: {results['avg_time_per_sample']:.3f} 秒/样本\n")
                f.write(f"- **完全匹配数**: {results['exact_matches']}/{results['total_samples']}\n")
                
                if strategy_name != 'baseline':
                    gains = self.analysis_data['performance_gains'][strategy_name]
                    f.write(f"- **相对基线改进**: {gains['accuracy_gain']:.4f}\n")
                
                f.write("\n")
            
            # 性能排名
            f.write("## 3. 性能排名\n\n")
            f.write("### 按准确率排名\n\n")
            f.write("| 排名 | 策略 | 准确率 | 修改率 | 平均时间(秒) |\n")
            f.write("|------|------|--------|--------|-------------|\n")
            
            for i, item in enumerate(self.analysis_data['accuracy_ranking'], 1):
                f.write(f"| {i} | {item['strategy']} | {item['accuracy']:.4f} | "
                       f"{item['modification_rate']:.4f} | {item['avg_time']:.3f} |\n")
            
            f.write("\n")
            
            # 关键发现
            f.write("## 4. 关键发现\n\n")
            
            best_strategy = self.analysis_data['best_accuracy_strategy']
            max_gain = self.analysis_data['max_accuracy_gain']
            
            f.write(f"1. **最佳策略**: `{best_strategy}` 策略表现最佳，准确率达到 {self.results_data[best_strategy]['accuracy']:.4f}\n\n")
            
            if max_gain > 0.1:
                f.write(f"2. **显著提升**: 多层次提示词带来了显著的性能提升，最大提升幅度为 {max_gain:.4f}\n\n")
            elif max_gain > 0.05:
                f.write(f"2. **明显提升**: 多层次提示词带来了明显的性能提升，最大提升幅度为 {max_gain:.4f}\n\n")
            else:
                f.write(f"2. **有限提升**: 多层次提示词的性能提升有限，最大提升幅度仅为 {max_gain:.4f}\n\n")
            
            # 计算修改率vs准确率的相关性
            modification_rates = [self.results_data[s]['modification_rate'] for s in self.results_data.keys()]
            accuracies = [self.results_data[s]['accuracy'] for s in self.results_data.keys()]
            correlation = np.corrcoef(modification_rates, accuracies)[0, 1]
            
            f.write(f"3. **修改率与准确率相关性**: {correlation:.3f}\n")
            if abs(correlation) > 0.7:
                f.write("   - 强相关性，说明修改行为与准确性密切相关\n\n")
            elif abs(correlation) > 0.3:
                f.write("   - 中等相关性，修改行为对准确性有一定影响\n\n")
            else:
                f.write("   - 弱相关性，修改行为与准确性关系不明显\n\n")
            
            # 建议
            f.write("## 5. 建议\n\n")
            
            if max_gain > 0.1:
                f.write(f"- **强烈推荐**: 使用 `{best_strategy}` 策略，可以显著提升模型性能\n")
                f.write("- **进一步优化**: 可以基于最佳策略进行更细致的提示词调优\n")
            elif max_gain > 0.05:
                f.write(f"- **推荐使用**: `{best_strategy}` 策略有明显的性能提升\n")
                f.write("- **持续优化**: 继续探索更有效的提示词设计方法\n")
            else:
                f.write("- **有限效果**: 当前的多层次提示词策略效果有限\n")
                f.write("- **考虑其他方法**: 建议考虑模型微调、数据增强等其他优化方法\n")
            
            f.write("\n---\n\n")
            f.write("*本报告由多层次提示词测试系统自动生成*\n")
        
        print(f"✅ 详细分析报告已保存: {report_path}")
    
    def generate_summary_table(self, output_dir: str):
        """生成汇总表格"""
        print("📋 生成汇总表格...")
        
        # 创建汇总数据
        summary_data = []
        for strategy_name, results in self.results_data.items():
            row = {
                '策略': strategy_name,
                '准确率': f"{results['accuracy']:.4f}",
                '修改率': f"{results['modification_rate']:.4f}",
                '平均时间(秒)': f"{results['avg_time_per_sample']:.3f}",
                '完全匹配': f"{results['exact_matches']}/{results['total_samples']}"
            }
            
            if strategy_name != 'baseline':
                gains = self.analysis_data['performance_gains'][strategy_name]
                row['相对基线改进'] = f"{gains['accuracy_gain']:.4f}"
            else:
                row['相对基线改进'] = "基线"
            
            summary_data.append(row)
        
        # 保存为JSON格式
        summary_path = os.path.join(output_dir, 'summary_table.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 汇总表格已保存: {summary_path}")
    
    def generate_complete_report(self, results_path: str, output_dir: str):
        """生成完整的分析报告"""
        print("🚀 开始生成完整分析报告...")
        print("=" * 50)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载结果
        self.load_results(results_path)
        
        # 生成各种分析
        self.generate_performance_comparison(output_dir)
        self.generate_improvement_analysis(output_dir)
        self.generate_detailed_report(output_dir)
        self.generate_summary_table(output_dir)
        
        print("=" * 50)
        print("🎉 完整分析报告生成完成!")
        print(f"📁 报告保存位置: {output_dir}")
        print("📊 包含内容:")
        print("  - performance_comparison.png: 性能对比图表")
        print("  - improvement_analysis.png: 改进分析图表")
        print("  - detailed_analysis_report.md: 详细分析报告")
        print("  - summary_table.json: 汇总表格")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="多层次提示词测试分析报告生成器")
    parser.add_argument('--results_path', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/enhanced_prompt_results.json',
                       help='测试结果文件路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/analysis_report',
                       help='报告输出目录')
    
    args = parser.parse_args()
    
    try:
        reporter = PromptAnalysisReporter()
        reporter.generate_complete_report(args.results_path, args.output_dir)
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
