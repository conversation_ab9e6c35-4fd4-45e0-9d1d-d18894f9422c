#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaCGEC在Split数据集上的完整测试流水线
包括测试、评估、结果分类
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime


def run_command(cmd, description):
    """运行命令并处理结果"""
    print(f"\n🚀 {description}")
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功完成")
            if result.stdout:
                print("输出:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="NaCGEC Split数据集完整测试流水线")
    parser.add_argument('--data_path', type=str,
                       default='ChineseErrorCorrector/data/split_data/split_errors_data.json',
                       help='Split数据集路径')
    parser.add_argument('--max_samples', type=int, default=100,
                       help='最大测试样本数')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批处理大小')
    parser.add_argument('--skip_test', action='store_true',
                       help='跳过测试，直接进行评估和分类')
    parser.add_argument('--skip_evaluation', action='store_true',
                       help='跳过评估')
    parser.add_argument('--skip_classification', action='store_true',
                       help='跳过结果分类')
    
    args = parser.parse_args()
    
    print("🎯 NaCGEC在Split数据集上的完整测试流水线")
    print("=" * 80)
    print(f"开始时间: {datetime.now().isoformat()}")
    print(f"数据路径: {args.data_path}")
    print(f"最大样本数: {args.max_samples}")
    print(f"批大小: {args.batch_size}")
    print("=" * 80)
    
    # 定义文件路径
    test_results_file = "ChineseErrorCorrector/model_test/Split_results/nacgec_split_test_results.json"
    evaluation_results_file = "ChineseErrorCorrector/model_test/Split_results/nacgec_split_evaluation_results.json"
    classification_dir = "ChineseErrorCorrector/model_test/Split_results/nacgec_classified_results"
    
    success_count = 0
    total_steps = 3
    
    # 步骤1: 运行测试
    if not args.skip_test:
        cmd_test = [
            "python", "ChineseErrorCorrector/model_test/Split_test/split_test_nacgec.py",
            "--data_path", args.data_path,
            "--output_path", test_results_file,
            "--max_samples", str(args.max_samples),
            "--batch_size", str(args.batch_size)
        ]
        
        if run_command(cmd_test, "NaCGEC模型测试"):
            success_count += 1
        else:
            print("❌ 测试失败，终止流水线")
            return
    else:
        print("⏭️ 跳过测试步骤")
        success_count += 1
    
    # 检查测试结果文件是否存在
    if not os.path.exists(test_results_file):
        print(f"❌ 测试结果文件不存在: {test_results_file}")
        return
    
    # 步骤2: 运行评估
    if not args.skip_evaluation:
        cmd_evaluate = [
            "python", "ChineseErrorCorrector/model_test/Split_test/split_evaluate.py",
            "--results_file", test_results_file,
            "--output_file", evaluation_results_file,
            "--num_processes", "4"
        ]
        
        if run_command(cmd_evaluate, "NaCGEC模型评估"):
            success_count += 1
        else:
            print("⚠️ 评估失败，但继续进行分类")
    else:
        print("⏭️ 跳过评估步骤")
        success_count += 1
    
    # 步骤3: 结果分类
    if not args.skip_classification:
        cmd_classify = [
            "python", "ChineseErrorCorrector/model_test/Split_test/classify_inference_results.py",
            "--results_file", test_results_file,
            "--output_dir", classification_dir
        ]
        
        if run_command(cmd_classify, "推理结果分类"):
            success_count += 1
        else:
            print("⚠️ 分类失败")
    else:
        print("⏭️ 跳过分类步骤")
        success_count += 1
    
    # 总结
    print("\n" + "=" * 80)
    print("🎉 流水线执行完成")
    print(f"完成时间: {datetime.now().isoformat()}")
    print(f"成功步骤: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("✅ 所有步骤都成功完成!")
        
        print(f"\n📁 生成的文件:")
        if os.path.exists(test_results_file):
            print(f"  测试结果: {test_results_file}")
        if os.path.exists(evaluation_results_file):
            print(f"  评估结果: {evaluation_results_file}")
        if os.path.exists(classification_dir):
            print(f"  分类结果: {classification_dir}/")
            
            # 列出分类结果文件
            if os.path.isdir(classification_dir):
                files = os.listdir(classification_dir)
                for file in sorted(files):
                    print(f"    - {file}")
        
        print(f"\n💡 后续分析建议:")
        print(f"1. 查看分类结果了解模型表现: {classification_dir}/sample_display.md")
        print(f"2. 分析错误推理找出改进方向: {classification_dir}/incorrect_results.json")
        print(f"3. 研究部分正确案例: {classification_dir}/partial_results.json")
        print(f"4. 对比完全正确案例: {classification_dir}/correct_results.json")
        
    else:
        print("⚠️ 部分步骤失败，请检查错误信息")
    
    print("=" * 80)


if __name__ == '__main__':
    main()
