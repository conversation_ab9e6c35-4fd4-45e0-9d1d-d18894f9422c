#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分类保存模型推理结果
将正确推理和错误推理分别保存到不同文件
"""

import json
import os
import argparse
from collections import defaultdict
from datetime import datetime


def load_test_results(results_file):
    """加载测试结果"""
    print(f"加载测试结果: {results_file}")
    
    if not os.path.exists(results_file):
        raise FileNotFoundError(f"结果文件不存在: {results_file}")
    
    with open(results_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data


def classify_results(results):
    """分类推理结果"""
    print("🔍 分类推理结果...")
    print(f"📊 总共需要分类 {len(results)} 个结果")

    correct_results = []      # 完全正确
    incorrect_results = []    # 错误推理
    no_change_results = []    # 未修改
    partial_results = []      # 部分正确

    for i, result in enumerate(results):
        source = result['source']
        target = result['target']
        prediction = result['prediction']
        
        if prediction == target:
            # 完全正确
            correct_results.append(result)
        elif prediction == source:
            # 未修改
            no_change_results.append(result)
        else:
            # 进行了修改但不完全正确
            # 使用简单的相似度判断是否为部分正确
            from difflib import SequenceMatcher

            sim_pred_target = SequenceMatcher(None, prediction, target).ratio()
            sim_pred_source = SequenceMatcher(None, prediction, source).ratio()

            if sim_pred_target > sim_pred_source and sim_pred_target > 0.7:
                # 部分正确：预测更接近目标且相似度较高
                partial_results.append(result)
            else:
                # 错误推理
                incorrect_results.append(result)

        # 显示进度
        if (i + 1) % 10 == 0 or i == len(results) - 1:
            print(f"  ✅ 已分类 {i + 1}/{len(results)} 个结果")

    classification = {
        'correct': correct_results,
        'incorrect': incorrect_results,
        'no_change': no_change_results,
        'partial': partial_results
    }

    print(f"\n📊 分类结果:")
    print(f"  完全正确: {len(correct_results)}")
    print(f"  错误推理: {len(incorrect_results)}")
    print(f"  未修改: {len(no_change_results)}")
    print(f"  部分正确: {len(partial_results)}")

    return classification


def analyze_by_error_type(classification):
    """按错误类型分析"""
    print("\n按错误类型分析...")
    
    error_type_analysis = defaultdict(lambda: {
        'correct': 0,
        'incorrect': 0,
        'no_change': 0,
        'partial': 0,
        'total': 0
    })
    
    for category, results in classification.items():
        for result in results:
            error_type = result.get('error_type', 'unknown')
            error_type_analysis[error_type][category] += 1
            error_type_analysis[error_type]['total'] += 1
    
    print("错误类型分析:")
    for error_type, stats in sorted(error_type_analysis.items(), key=lambda x: x[1]['total'], reverse=True):
        total = stats['total']
        print(f"\n  {error_type} (总数: {total}):")
        print(f"    完全正确: {stats['correct']} ({stats['correct']/total:.2%})")
        print(f"    错误推理: {stats['incorrect']} ({stats['incorrect']/total:.2%})")
        print(f"    未修改: {stats['no_change']} ({stats['no_change']/total:.2%})")
        print(f"    部分正确: {stats['partial']} ({stats['partial']/total:.2%})")
    
    return error_type_analysis


def save_classified_results(classification, test_info, output_dir):
    """保存分类结果"""
    print(f"\n保存分类结果到: {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存各类结果
    for category, results in classification.items():
        if not results:
            continue
            
        output_file = os.path.join(output_dir, f"{category}_results.json")
        
        # 构建输出数据
        output_data = {
            'classification_info': {
                'category': category,
                'count': len(results),
                'classification_time': datetime.now().isoformat(),
                'original_test_info': test_info
            },
            'results': results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"  {category}: {len(results)} 个结果 → {output_file}")


def save_analysis_summary(classification, error_type_analysis, test_info, output_dir):
    """保存分析摘要"""
    summary_file = os.path.join(output_dir, "classification_summary.json")
    
    # 计算总体统计
    total_samples = sum(len(results) for results in classification.values())
    
    summary = {
        'summary_info': {
            'classification_time': datetime.now().isoformat(),
            'total_samples': total_samples,
            'original_test_info': test_info
        },
        'overall_statistics': {
            'correct_count': len(classification['correct']),
            'correct_rate': len(classification['correct']) / total_samples if total_samples > 0 else 0,
            'incorrect_count': len(classification['incorrect']),
            'incorrect_rate': len(classification['incorrect']) / total_samples if total_samples > 0 else 0,
            'no_change_count': len(classification['no_change']),
            'no_change_rate': len(classification['no_change']) / total_samples if total_samples > 0 else 0,
            'partial_count': len(classification['partial']),
            'partial_rate': len(classification['partial']) / total_samples if total_samples > 0 else 0
        },
        'error_type_analysis': dict(error_type_analysis)
    }
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"  分析摘要 → {summary_file}")


def create_sample_display(classification, output_dir, max_samples=5):
    """创建样本展示文件"""
    display_file = os.path.join(output_dir, "sample_display.md")
    
    with open(display_file, 'w', encoding='utf-8') as f:
        f.write("# 推理结果样本展示\n\n")
        
        for category, results in classification.items():
            if not results:
                continue
                
            category_names = {
                'correct': '完全正确',
                'incorrect': '错误推理',
                'no_change': '未修改',
                'partial': '部分正确'
            }
            
            f.write(f"## {category_names.get(category, category)} ({len(results)}个)\n\n")
            
            for i, result in enumerate(results[:max_samples]):
                f.write(f"### 样本 {i+1}\n\n")
                f.write(f"- **错误类型**: {result.get('error_type', 'unknown')}\n")
                f.write(f"- **原句**: {result['source']}\n")
                f.write(f"- **目标**: {result['target']}\n")
                f.write(f"- **预测**: {result['prediction']}\n\n")
            
            if len(results) > max_samples:
                f.write(f"... 还有 {len(results) - max_samples} 个样本\n\n")
    
    print(f"  样本展示 → {display_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分类保存推理结果")
    parser.add_argument('--results_file', type=str, required=True,
                       help='测试结果文件路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/classified_results',
                       help='分类结果输出目录')
    
    args = parser.parse_args()
    
    print("🔍 推理结果分类分析")
    print("=" * 50)
    print(f"结果文件: {args.results_file}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 50)
    
    try:
        # 加载测试结果
        data = load_test_results(args.results_file)
        results = data['results']
        test_info = data.get('test_info', {})
        
        # 分类结果
        classification = classify_results(results)
        
        # 按错误类型分析
        error_type_analysis = analyze_by_error_type(classification)
        
        # 保存分类结果
        save_classified_results(classification, test_info, args.output_dir)
        
        # 保存分析摘要
        save_analysis_summary(classification, error_type_analysis, test_info, args.output_dir)
        
        # 创建样本展示
        create_sample_display(classification, args.output_dir)
        
        print(f"\n🎉 分类完成!")
        print(f"结果已保存到: {args.output_dir}")
        
        # 显示总体统计
        total = len(results)
        print(f"\n📊 总体统计:")
        print(f"  总样本数: {total}")
        print(f"  完全正确: {len(classification['correct'])} ({len(classification['correct'])/total:.2%})")
        print(f"  错误推理: {len(classification['incorrect'])} ({len(classification['incorrect'])/total:.2%})")
        print(f"  未修改: {len(classification['no_change'])} ({len(classification['no_change'])/total:.2%})")
        print(f"  部分正确: {len(classification['partial'])} ({len(classification['partial'])/total:.2%})")
        
    except Exception as e:
        print(f"\n❌ 分类失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
