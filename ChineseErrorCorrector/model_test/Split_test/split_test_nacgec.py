#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaCGEC模型在Split数据集上的测试脚本
基于split_test.py修改，专门用于测试NaCGEC模型
"""

import os
import sys
import json
import time
import argparse
import re
from datetime import datetime

# 设置环境变量来抑制transformers的警告信息
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['VLLM_DISABLE'] = '1'  # 禁用VLLM

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


def clean_prediction(text):
    """清理预测文本中的<think>标签和提示词"""
    if not isinstance(text, str):
        return text

    # 移除<think>...</think>标签及其内容
    cleaned = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

    # 移除提示词模板（如果输出包含完整的提示词）
    if '你是一个专业的中文文本纠错专家' in cleaned:
        # 尝试提取最后一行作为纠错结果
        lines = cleaned.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line and not line.startswith('你是') and not line.startswith('请纠正') and not line.startswith('具体要求') and not line.startswith('-'):
                cleaned = line
                break
        else:
            # 如果没找到合适的行，返回空字符串
            cleaned = ""

    # 移除多余的空白字符
    cleaned = cleaned.strip()
    
    return cleaned


def load_split_data(data_path):
    """加载Split数据集"""
    print(f"加载Split数据集: {data_path}")
    
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"加载了 {len(data)} 条数据")
    
    # 统计错误类型分布
    error_types = {}
    for item in data:
        error_type = item.get('error_type', 'unknown')
        error_types[error_type] = error_types.get(error_type, 0) + 1
    
    print("错误类型分布:")
    for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
        print(f"  {error_type}: {count}")
    
    return data


def load_nacgec_model():
    """加载NaCGEC模型"""
    print("加载NaCGEC模型...")

    # 使用简化的推理方法，避免VLLM问题
    try:
        # 导入必要的模块
        from transformers import AutoTokenizer, AutoModelForCausalLM, set_seed
        import torch

        # 设置随机种子
        set_seed(42)

        # 使用项目中的模型路径
        model_path = "/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B"

        print(f"从路径加载模型: {model_path}")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        # 使用AutoModelForCausalLM而不是AutoModelForSeq2SeqLM
        model = AutoModelForCausalLM.from_pretrained(model_path)

        # 移动到GPU（如果可用）
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
        model.eval()

        print(f"模型加载成功，使用设备: {device}")
        return model, tokenizer, device

    except Exception as e:
        print(f"模型加载失败: {e}")
        print("使用简化的文本处理方法...")
        return None, None, None


def nacgec_inference(model, tokenizer, device, texts, batch_size=16, show_progress=True):
    """NaCGEC模型推理"""
    if model is None or tokenizer is None:
        # 简化处理：返回原文本
        print("⚠️ 模型未加载，返回原文本")
        return texts

    results = []
    total_batches = (len(texts) + batch_size - 1) // batch_size

    if show_progress:
        print(f"🚀 开始推理 {len(texts)} 个样本，分 {total_batches} 个批次处理")
        print(f"📊 批大小: {batch_size}, 设备: {device}")

    try:
        import torch

        # 设置pad_token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        for batch_idx in range(0, len(texts), batch_size):
            batch_num = batch_idx // batch_size + 1
            batch_texts = texts[batch_idx:batch_idx + batch_size]

            if show_progress:
                print(f"\n📦 处理批次 {batch_num}/{total_batches} (样本 {batch_idx+1}-{min(batch_idx+len(batch_texts), len(texts))})")

            # 为CausalLM构建提示词
            prompts = []
            for text in batch_texts:
                prompt = f"纠正句子：{text}\n纠正："
                prompts.append(prompt)

            if show_progress:
                print(f"🔤 编码输入...")

            # 编码
            inputs = tokenizer(
                prompts,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=256
            ).to(device)

            if show_progress:
                print(f"🧠 模型推理中...")

            # 推理
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=128,
                    num_beams=2,
                    early_stopping=True,
                    do_sample=False,
                    pad_token_id=tokenizer.pad_token_id
                )

            if show_progress:
                print(f"📝 解码输出...")

            # 解码并提取纠正结果
            batch_results = tokenizer.batch_decode(outputs, skip_special_tokens=True)

            for j, (prompt, full_output) in enumerate(zip(prompts, batch_results)):
                sample_idx = batch_idx + j
                original_text = batch_texts[j]

                # 提取生成的部分（去掉prompt）
                if full_output.startswith(prompt):
                    generated = full_output[len(prompt):].strip()
                else:
                    generated = full_output.strip()

                # 进一步清理：只取第一行，避免重复
                if '\n' in generated:
                    generated = generated.split('\n')[0].strip()

                # 移除可能的重复提示词
                if '纠正：' in generated:
                    generated = generated.split('纠正：')[-1].strip()

                # 如果生成结果为空或与原文相同，返回原文本
                if not generated or generated == original_text:
                    generated = original_text

                results.append(generated)

                if show_progress:
                    print(f"  样本 {sample_idx+1}:")
                    print(f"    原句: {original_text}")
                    print(f"    预测: {generated}")
                    if generated != original_text:
                        print(f"    状态: ✏️ 已修改")
                    else:
                        print(f"    状态: ➡️ 未修改")

        if show_progress:
            print(f"\n✅ 推理完成! 共处理 {len(results)} 个样本")

        return results

    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return texts


def test_nacgec_on_split(data_path, output_path, max_samples=None, batch_size=16):
    """测试NaCGEC在Split数据集上的表现"""
    
    # 加载数据
    data = load_split_data(data_path)
    
    # 限制测试样本数
    if max_samples and max_samples < len(data):
        data = data[:max_samples]
        print(f"限制测试样本数为: {max_samples}")
    
    # 加载模型
    model, tokenizer, device = load_nacgec_model()

    # 设置随机种子
    try:
        from transformers import set_seed
        set_seed(42)
    except ImportError:
        import random
        import numpy as np
        random.seed(42)
        np.random.seed(42)
    
    # 开始测试
    results = []
    start_time = time.time()

    print(f"\n🎯 开始测试 {len(data)} 个样本...")
    print(f"📋 测试配置:")
    print(f"  - 批大小: {batch_size}")
    print(f"  - 设备: {device if device else 'CPU'}")
    print(f"  - 模型: {'已加载' if model else '未加载'}")
    print("=" * 60)

    # 批处理推理
    sources = [item['source'] for item in data]

    try:
        print(f"🚀 开始模型推理...")
        predictions = nacgec_inference(model, tokenizer, device, sources, batch_size, show_progress=True)

        print(f"\n📊 处理推理结果...")
        # 处理结果
        for i, (item, prediction) in enumerate(zip(data, predictions)):
            # 清理预测结果
            cleaned_prediction = clean_prediction(prediction)

            result = {
                'id': i,
                'source': item['source'],
                'target': item['target'][0] if item['target'] else item['source'],
                'prediction': cleaned_prediction,
                'error_type': item.get('error_type', 'unknown'),
                'metadata': item.get('metadata', {})
            }
            results.append(result)

            # 显示处理进度
            if (i + 1) % 10 == 0 or i == len(data) - 1:
                print(f"  ✅ 已处理 {i + 1}/{len(data)} 个结果")
            
    except Exception as e:
        print(f"❌ 批处理推理失败，改用逐个处理: {e}")
        print("🔄 切换到逐个样本处理模式...")

        # 逐个处理
        for i, item in enumerate(data):
            print(f"\n📝 处理样本 {i+1}/{len(data)}")
            print(f"  原句: {item['source']}")

            try:
                pred_results = nacgec_inference(model, tokenizer, device, [item['source']], 1, show_progress=False)

                if pred_results and len(pred_results) > 0:
                    prediction = pred_results[0]
                else:
                    prediction = item['source']

                # 清理预测结果
                cleaned_prediction = clean_prediction(prediction)

                result = {
                    'id': i,
                    'source': item['source'],
                    'target': item['target'][0] if item['target'] else item['source'],
                    'prediction': cleaned_prediction,
                    'error_type': item.get('error_type', 'unknown'),
                    'metadata': item.get('metadata', {})
                }
                results.append(result)

                print(f"  预测: {cleaned_prediction}")
                if cleaned_prediction != item['source']:
                    print(f"  状态: ✏️ 已修改")
                else:
                    print(f"  状态: ➡️ 未修改")

            except Exception as e:
                print(f"❌ 处理样本 {i+1} 失败: {e}")
                result = {
                    'id': i,
                    'source': item['source'],
                    'target': item['target'][0] if item['target'] else item['source'],
                    'prediction': item['source'],  # 失败时返回原句
                    'error_type': item.get('error_type', 'unknown'),
                    'metadata': item.get('metadata', {}),
                    'error': str(e)
                }
                results.append(result)
                print(f"  状态: ❌ 处理失败，返回原句")
    
    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n📊 计算统计指标...")

    # 计算基本统计
    exact_matches = sum(1 for r in results if r['prediction'] == r['target'])
    exact_match_rate = exact_matches / len(results) if results else 0

    # 计算修改统计
    modified_count = sum(1 for r in results if r['prediction'] != r['source'])
    modification_rate = modified_count / len(results) if results else 0

    # 按错误类型统计
    error_type_stats = {}
    for result in results:
        error_type = result['error_type']
        if error_type not in error_type_stats:
            error_type_stats[error_type] = {'total': 0, 'correct': 0, 'modified': 0}
        error_type_stats[error_type]['total'] += 1
        if result['prediction'] == result['target']:
            error_type_stats[error_type]['correct'] += 1
        if result['prediction'] != result['source']:
            error_type_stats[error_type]['modified'] += 1

    # 计算各错误类型的准确率
    for error_type in error_type_stats:
        stats = error_type_stats[error_type]
        stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
        stats['modification_rate'] = stats['modified'] / stats['total'] if stats['total'] > 0 else 0
    
    # 构建最终结果
    final_result = {
        'test_info': {
            'test_time': datetime.now().isoformat(),
            'data_path': data_path,
            'model_name': 'NaCGEC',
            'batch_size': batch_size,
            'max_samples': max_samples,
            'total_samples': len(results),
            'total_time_seconds': total_time,
            'average_time_per_sample': total_time / len(results) if results else 0,
            'throughput_samples_per_second': len(results) / total_time if total_time > 0 else 0
        },
        'metrics': {
            'exact_match_count': exact_matches,
            'exact_match_rate': exact_match_rate,
            'error_type_statistics': error_type_stats
        },
        'results': results
    }
    
    # 保存结果
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n" + "=" * 60)
    print(f"🎉 测试完成!")
    print(f"=" * 60)
    print(f"📊 基本统计:")
    print(f"  总样本数: {len(results)}")
    print(f"  总耗时: {total_time:.2f} 秒")
    print(f"  平均耗时: {total_time/len(results):.3f} 秒/样本")
    print(f"  处理速度: {len(results)/total_time:.2f} 样本/秒")

    print(f"\n📈 准确性统计:")
    print(f"  完全匹配数: {exact_matches}")
    print(f"  完全匹配率: {exact_match_rate:.4f}")
    print(f"  修改样本数: {modified_count}")
    print(f"  修改率: {modification_rate:.4f}")

    print(f"\n📁 输出文件:")
    print(f"  结果已保存到: {output_path}")

    # 显示错误类型统计
    print(f"\n📋 按错误类型统计:")
    for error_type, stats in sorted(error_type_stats.items(), key=lambda x: x[1]['total'], reverse=True):
        print(f"  {error_type}:")
        print(f"    总数: {stats['total']}")
        print(f"    正确: {stats['correct']} (准确率: {stats['accuracy']:.4f})")
        print(f"    修改: {stats['modified']} (修改率: {stats['modification_rate']:.4f})")

    print(f"=" * 60)
    
    return final_result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="NaCGEC在Split数据集上的测试")
    parser.add_argument('--data_path', type=str, 
                       default='ChineseErrorCorrector/data/split_data/split_errors_data.json',
                       help='Split数据集路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/nacgec_split_test_results.json',
                       help='输出结果路径')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='最大测试样本数')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批处理大小')
    
    args = parser.parse_args()
    
    print("🚀 NaCGEC在Split数据集上的测试开始")
    print("=" * 50)
    print(f"数据路径: {args.data_path}")
    print(f"输出路径: {args.output_path}")
    print(f"最大样本数: {args.max_samples}")
    print(f"批大小: {args.batch_size}")
    print("=" * 50)
    
    try:
        test_nacgec_on_split(
            data_path=args.data_path,
            output_path=args.output_path,
            max_samples=args.max_samples,
            batch_size=args.batch_size
        )
        print("\n🎉 测试成功完成!")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
