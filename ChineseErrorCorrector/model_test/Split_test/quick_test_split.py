#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Split数据集快速测试脚本
用于验证测试系统是否正常工作
"""

import os
import sys
import json
import subprocess
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


def check_data_availability():
    """检查数据可用性"""
    print("🔍 检查数据可用性...")
    
    data_path = "ChineseErrorCorrector/data/split_data/split_errors_data.json"
    
    if not os.path.exists(data_path):
        print(f"❌ Split数据集不存在: {data_path}")
        return False
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Split数据集加载成功: {len(data)} 条数据")
        
        # 显示前3个样本
        print("📋 数据样本预览:")
        for i, item in enumerate(data[:3]):
            print(f"  样本 {i+1}:")
            print(f"    原句: {item['source']}")
            print(f"    目标: {item['target'][0] if item['target'] else 'N/A'}")
            print(f"    错误类型: {item.get('error_type', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False


def check_model_availability():
    """检查模型可用性"""
    print("\n🔍 检查模型可用性...")

    try:
        # 避免直接导入可能引起GUI问题的模块
        config_path = "ChineseErrorCorrector/config.py"
        if os.path.exists(config_path):
            print("✅ 配置文件存在")
            # 简单检查，不实际导入
            return True
        else:
            print("❌ 配置文件不存在")
            return False

    except Exception as e:
        print(f"❌ 模型检查失败: {e}")
        return False


def check_cherrant_availability():
    """检查ChERRANT工具可用性"""
    print("\n🔍 检查ChERRANT工具可用性...")
    
    cherrant_path = "ChineseErrorCorrector/model_test/evaluation_tools/ChERRANT"
    
    if not os.path.exists(cherrant_path):
        print(f"❌ ChERRANT路径不存在: {cherrant_path}")
        return False
    
    # 检查关键文件
    key_files = [
        "parallel_to_m2.py",
        "compare_m2_for_evaluation.py"
    ]
    
    for file_name in key_files:
        file_path = os.path.join(cherrant_path, file_name)
        if os.path.exists(file_path):
            print(f"✅ {file_name} 存在")
        else:
            print(f"❌ {file_name} 不存在")
            return False
    
    return True


def run_quick_test():
    """运行快速测试"""
    print("\n🚀 运行快速测试...")
    
    # 确保输出目录存在
    output_dir = "ChineseErrorCorrector/model_test/Split_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 运行测试
    test_cmd = [
        "python", "ChineseErrorCorrector/model_test/Split_test/split_test.py",
        "--data_path", "ChineseErrorCorrector/data/split_data/split_errors_data.json",
        "--output_path", f"{output_dir}/quick_test_results.json",
        "--max_samples", "5"
    ]
    
    print(f"执行命令: {' '.join(test_cmd)}")
    
    try:
        result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 测试执行成功")
            print("测试输出:")
            print(result.stdout)
            
            # 检查结果文件
            result_file = f"{output_dir}/quick_test_results.json"
            if os.path.exists(result_file):
                print(f"✅ 结果文件生成成功: {result_file}")
                
                # 显示结果摘要
                with open(result_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                test_info = data.get('test_info', {})
                metrics = data.get('metrics', {})
                
                print("📊 测试结果摘要:")
                print(f"  测试样本数: {test_info.get('total_samples', 0)}")
                print(f"  总耗时: {test_info.get('total_time_seconds', 0):.2f} 秒")
                print(f"  完全匹配率: {metrics.get('exact_match_rate', 0):.4f}")
                
                return True
            else:
                print("❌ 结果文件未生成")
                return False
        else:
            print("❌ 测试执行失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False


def run_quick_evaluation():
    """运行快速评估"""
    print("\n🚀 运行快速评估...")
    
    result_file = "ChineseErrorCorrector/model_test/Split_results/quick_test_results.json"
    
    if not os.path.exists(result_file):
        print("❌ 测试结果文件不存在，跳过评估")
        return False
    
    # 运行评估（跳过ChERRANT）
    eval_cmd = [
        "python", "ChineseErrorCorrector/model_test/Split_test/split_evaluate.py",
        "--results_file", result_file,
        "--output_file", "ChineseErrorCorrector/model_test/Split_results/quick_evaluation_results.json",
        "--skip_cherrant"
    ]
    
    print(f"执行命令: {' '.join(eval_cmd)}")
    
    try:
        result = subprocess.run(eval_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 评估执行成功")
            print("评估输出:")
            print(result.stdout)
            return True
        else:
            print("❌ 评估执行失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 评估超时")
        return False
    except Exception as e:
        print(f"❌ 评估执行异常: {e}")
        return False


def main():
    """主函数"""
    print("🎯 Split数据集测试系统快速验证")
    print("=" * 60)
    print(f"验证时间: {datetime.now().isoformat()}")
    print("=" * 60)
    
    # 检查列表
    checks = [
        ("数据可用性", check_data_availability),
        ("模型可用性", check_model_availability),
        ("ChERRANT工具", check_cherrant_availability),
    ]
    
    # 执行检查
    passed_checks = 0
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        if check_func():
            passed_checks += 1
            print(f"✅ {check_name}检查通过")
        else:
            print(f"❌ {check_name}检查失败")
    
    print(f"\n检查结果: {passed_checks}/{len(checks)} 通过")
    
    # 如果基本检查通过，运行快速测试
    if passed_checks >= 2:  # 至少数据和模型可用
        print("\n" + "=" * 60)
        print("🧪 执行功能测试")
        print("=" * 60)
        
        test_passed = run_quick_test()
        eval_passed = run_quick_evaluation()
        
        print("\n" + "=" * 60)
        print("📊 最终结果")
        print("=" * 60)
        
        if test_passed and eval_passed:
            print("🎉 所有测试通过！Split测试系统工作正常")
            print("\n可以使用以下命令进行完整测试:")
            print("python ChineseErrorCorrector/model_test/Split_test/split_test.py --max_samples 100")
            print("python ChineseErrorCorrector/model_test/Split_test/split_evaluate.py \\")
            print("    --results_file ChineseErrorCorrector/model_test/Split_results/split_test_results.json")
        elif test_passed:
            print("⚠️ 测试通过，但评估失败")
            print("可以进行测试，但评估功能可能有问题")
        else:
            print("❌ 测试失败，请检查配置和依赖")
    else:
        print("\n❌ 基本检查未通过，无法进行功能测试")
        print("请检查数据文件和模型配置")
    
    print("=" * 60)


if __name__ == '__main__':
    main()
