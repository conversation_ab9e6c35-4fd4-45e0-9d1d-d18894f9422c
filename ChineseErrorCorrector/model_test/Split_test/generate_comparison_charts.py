#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate Comparison Charts for Original vs Multi-Level Prompt Results
Creates visualization charts in English to prevent encoding issues
"""

import json
import matplotlib.pyplot as plt
import numpy as np
import os
from typing import Dict, List, Any

# Set matplotlib to use a font that supports English
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['font.size'] = 10
plt.rcParams['figure.figsize'] = (12, 8)


class ComparisonChartGenerator:
    """Generate comparison charts for model performance analysis"""
    
    def __init__(self, results_path: str):
        """Initialize with results data"""
        with open(results_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        self.strategy_results = self.data['strategy_results']
        self.comparison_analysis = self.data['comparison_analysis']
    
    def create_performance_comparison_chart(self, output_path: str):
        """Create performance comparison bar chart"""
        strategies = []
        accuracies = []
        modification_rates = []
        avg_times = []
        
        # Extract data for visualization
        for strategy_name, results in self.strategy_results.items():
            strategies.append(strategy_name.replace('_', ' ').title())
            accuracies.append(results['accuracy'] * 100)  # Convert to percentage
            modification_rates.append(results['modification_rate'] * 100)
            avg_times.append(results['avg_time_per_sample'])
        
        # Create subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Original vs Multi-Level Prompt Performance Comparison', fontsize=16, fontweight='bold')
        
        # Colors for different strategies
        colors = ['#ff7f7f', '#7fbf7f', '#7f7fff', '#ffbf7f']
        
        # 1. Accuracy Comparison
        bars1 = ax1.bar(strategies, accuracies, color=colors, alpha=0.8)
        ax1.set_title('Accuracy Comparison (%)', fontweight='bold')
        ax1.set_ylabel('Accuracy (%)')
        ax1.set_ylim(0, max(accuracies) * 1.2)
        
        # Add value labels on bars
        for bar, acc in zip(bars1, accuracies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 2. Modification Rate Comparison
        bars2 = ax2.bar(strategies, modification_rates, color=colors, alpha=0.8)
        ax2.set_title('Modification Rate Comparison (%)', fontweight='bold')
        ax2.set_ylabel('Modification Rate (%)')
        ax2.set_ylim(0, 100)
        
        # Add value labels on bars
        for bar, mod in zip(bars2, modification_rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{mod:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 3. Processing Time Comparison
        bars3 = ax3.bar(strategies, avg_times, color=colors, alpha=0.8)
        ax3.set_title('Average Processing Time (seconds)', fontweight='bold')
        ax3.set_ylabel('Time (seconds)')
        ax3.set_ylim(0, max(avg_times) * 1.2)
        
        # Add value labels on bars
        for bar, time in zip(bars3, avg_times):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{time:.3f}s', ha='center', va='bottom', fontweight='bold')
        
        # 4. Improvement vs Original
        original_accuracy = self.strategy_results['original']['accuracy'] * 100
        improvements = [acc - original_accuracy for acc in accuracies]
        
        # Color bars based on improvement (green for positive, red for negative/zero)
        improvement_colors = ['red' if imp <= 0 else 'green' for imp in improvements]
        
        bars4 = ax4.bar(strategies, improvements, color=improvement_colors, alpha=0.8)
        ax4.set_title('Accuracy Improvement vs Original (%)', fontweight='bold')
        ax4.set_ylabel('Improvement (%)')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # Add value labels on bars
        for bar, imp in zip(bars4, improvements):
            height = bar.get_height()
            y_pos = height + 0.2 if height >= 0 else height - 0.5
            ax4.text(bar.get_x() + bar.get_width()/2., y_pos,
                    f'{imp:+.1f}%', ha='center', va='bottom' if height >= 0 else 'top', 
                    fontweight='bold')
        
        # Rotate x-axis labels for better readability
        for ax in [ax1, ax2, ax3, ax4]:
            ax.tick_params(axis='x', rotation=45)
            ax.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Performance comparison chart saved to: {output_path}")
    
    def create_detailed_metrics_chart(self, output_path: str):
        """Create detailed metrics radar chart"""
        strategies = list(self.strategy_results.keys())
        strategy_names = [s.replace('_', ' ').title() for s in strategies]
        
        # Metrics to compare (normalized to 0-1 scale)
        metrics = ['Accuracy', 'Precision', 'Speed', 'Efficiency']
        
        # Prepare data
        data_matrix = []
        for strategy in strategies:
            results = self.strategy_results[strategy]
            
            # Normalize metrics to 0-1 scale
            accuracy = results['accuracy']
            precision = 1 - results['modification_rate']  # Lower modification rate = higher precision
            speed = 1 / results['avg_time_per_sample']  # Inverse of time = speed
            efficiency = results['avg_improvement_rate'] if results['avg_improvement_rate'] > 0 else 0
            
            # Normalize speed and efficiency to 0-1 scale
            data_matrix.append([accuracy, precision, speed/max([1/r['avg_time_per_sample'] for r in self.strategy_results.values()]), 
                               min(efficiency/100, 1) if efficiency > 0 else 0])
        
        # Create radar chart
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        colors = ['#ff7f7f', '#7fbf7f', '#7f7fff', '#ffbf7f']
        
        for i, (strategy_data, strategy_name, color) in enumerate(zip(data_matrix, strategy_names, colors)):
            values = strategy_data + strategy_data[:1]  # Complete the circle
            ax.plot(angles, values, 'o-', linewidth=2, label=strategy_name, color=color)
            ax.fill(angles, values, alpha=0.25, color=color)
        
        # Customize the chart
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
        ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
        ax.grid(True)
        
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        plt.title('Multi-Dimensional Performance Comparison\n(Normalized Metrics)', 
                 size=14, fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Detailed metrics chart saved to: {output_path}")
    
    def create_improvement_analysis_chart(self, output_path: str):
        """Create improvement analysis chart"""
        # Extract improvement data
        improvements = self.comparison_analysis['improvements']
        strategies = list(improvements.keys())
        strategy_names = [s.replace('_', ' ').title() for s in strategies]
        
        # Metrics
        accuracy_improvements = [improvements[s]['accuracy_improvement'] * 100 for s in strategies]
        time_efficiency_changes = [improvements[s]['time_efficiency_change'] for s in strategies]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('Improvement Analysis vs Original Model', fontsize=16, fontweight='bold')
        
        # 1. Accuracy Improvement
        colors = ['green' if x > 0 else 'red' for x in accuracy_improvements]
        bars1 = ax1.bar(strategy_names, accuracy_improvements, color=colors, alpha=0.8)
        ax1.set_title('Accuracy Improvement (%)', fontweight='bold')
        ax1.set_ylabel('Improvement (%)')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax1.grid(axis='y', alpha=0.3)
        
        # Add value labels
        for bar, imp in zip(bars1, accuracy_improvements):
            height = bar.get_height()
            y_pos = height + 0.2 if height >= 0 else height - 0.5
            ax1.text(bar.get_x() + bar.get_width()/2., y_pos,
                    f'{imp:+.1f}%', ha='center', va='bottom' if height >= 0 else 'top', 
                    fontweight='bold')
        
        # 2. Time Efficiency Change
        colors = ['green' if x > 0 else 'red' for x in time_efficiency_changes]
        bars2 = ax2.bar(strategy_names, time_efficiency_changes, color=colors, alpha=0.8)
        ax2.set_title('Time Efficiency Change (seconds)', fontweight='bold')
        ax2.set_ylabel('Time Saved (seconds)')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.grid(axis='y', alpha=0.3)
        
        # Add value labels
        for bar, eff in zip(bars2, time_efficiency_changes):
            height = bar.get_height()
            y_pos = height + 0.02 if height >= 0 else height - 0.05
            ax2.text(bar.get_x() + bar.get_width()/2., y_pos,
                    f'{eff:+.3f}s', ha='center', va='bottom' if height >= 0 else 'top', 
                    fontweight='bold')
        
        # Rotate x-axis labels
        for ax in [ax1, ax2]:
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Improvement analysis chart saved to: {output_path}")
    
    def generate_all_charts(self, output_dir: str):
        """Generate all comparison charts"""
        os.makedirs(output_dir, exist_ok=True)
        
        print("Generating comparison charts...")
        
        # Generate all charts
        self.create_performance_comparison_chart(
            os.path.join(output_dir, 'performance_comparison.png'))
        
        self.create_detailed_metrics_chart(
            os.path.join(output_dir, 'detailed_metrics_radar.png'))
        
        self.create_improvement_analysis_chart(
            os.path.join(output_dir, 'improvement_analysis.png'))
        
        print(f"All charts generated successfully in: {output_dir}")


def main():
    """Main function"""
    results_path = 'ChineseErrorCorrector/model_test/Split_results/comparison_original_vs_multilevel.json'
    output_dir = 'ChineseErrorCorrector/model_test/Split_results/charts'
    
    print("COMPARISON CHART GENERATOR")
    print("=" * 50)
    print(f"Results Path: {results_path}")
    print(f"Output Directory: {output_dir}")
    print("=" * 50)
    
    try:
        # Generate charts
        generator = ComparisonChartGenerator(results_path)
        generator.generate_all_charts(output_dir)
        
        print("\nChart generation completed successfully!")
        
    except Exception as e:
        print(f"\nChart generation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
