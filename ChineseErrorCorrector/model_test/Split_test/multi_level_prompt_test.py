#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多层次提示词测试系统
测试不同层次的提示词对模型性能的影响
"""

import os
import sys
import json
import time
import argparse
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict

# 设置环境变量
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['VLLM_DISABLE'] = '1'

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


class MultiLevelPromptTester:
    """多层次提示词测试器"""
    
    def __init__(self):
        self.prompt_strategies = {
            'basic': self._create_basic_prompt,
            'detailed': self._create_detailed_prompt,
            'hierarchical': self._create_hierarchical_prompt,
            'contextual': self._create_contextual_prompt,
            'expert': self._create_expert_prompt,
            'chain_of_thought': self._create_chain_of_thought_prompt,
            'few_shot': self._create_few_shot_prompt
        }
        
        # 示例数据用于few-shot学习
        self.few_shot_examples = [
            {
                'source': '你先择几个个性重点培养',
                'target': '你选择几个个性重点培养',
                'explanation': '将"先"纠正为"选"，这是拆分字错误'
            },
            {
                'source': '我的鬼未力很强',
                'target': '我的魅力很强',
                'explanation': '将"鬼未"纠正为"魅"，这是拆分字错误'
            }
        ]
    
    def _create_basic_prompt(self, text: str) -> str:
        """基础提示词"""
        return f"纠正句子：{text}\n纠正："
    
    def _create_detailed_prompt(self, text: str) -> str:
        """详细提示词"""
        return f"""请纠正以下句子中的错误：
原句：{text}
要求：
1. 识别并纠正拆分字错误
2. 保持句子的原意不变
3. 只输出纠正后的句子

纠正后的句子："""
    
    def _create_hierarchical_prompt(self, text: str) -> str:
        """层次化提示词"""
        return f"""你是一个专业的中文文本纠错专家。

任务：纠正拆分字错误
定义：拆分字错误是指将一个完整的汉字错误地拆分成多个部分，如"白勺"应为"的"。

步骤：
1. 仔细阅读句子：{text}
2. 识别可能的拆分字错误
3. 将拆分的部分合并为正确的汉字
4. 确保语义通顺

请输出纠正后的句子："""
    
    def _create_contextual_prompt(self, text: str) -> str:
        """上下文感知提示词"""
        return f"""作为中文纠错专家，请分析以下句子的语境并纠正拆分字错误。

原句：{text}

分析要点：
- 理解句子的整体语义
- 识别不符合语境的字符组合
- 考虑汉字的标准写法
- 保持语义连贯性

纠正结果："""
    
    def _create_expert_prompt(self, text: str) -> str:
        """专家级提示词"""
        return f"""你是一位资深的汉语言文字学专家，专门研究汉字规范化和错误纠正。

任务：识别并纠正拆分字错误
原句：{text}

专业分析：
1. 拆分字错误特征：将完整汉字的偏旁部首错误分离
2. 常见模式：形近字混淆、部件拆分、结构错误
3. 纠正原则：恢复汉字的标准形态，保持语义准确性

基于你的专业知识，请提供准确的纠正结果："""
    
    def _create_chain_of_thought_prompt(self, text: str) -> str:
        """思维链提示词"""
        return f"""请按照以下思维步骤纠正句子中的拆分字错误：

原句：{text}

思维过程：
<think>
1. 逐字分析句子中的每个字符
2. 识别可能被拆分的字符组合
3. 检查这些组合是否应该合并为单个汉字
4. 验证合并后的字符是否符合语境
5. 确认最终的纠正结果
</think>

基于以上分析，纠正后的句子是："""
    
    def _create_few_shot_prompt(self, text: str) -> str:
        """少样本学习提示词"""
        examples_text = ""
        for i, example in enumerate(self.few_shot_examples, 1):
            examples_text += f"""示例{i}：
原句：{example['source']}
纠正：{example['target']}
说明：{example['explanation']}

"""
        
        return f"""以下是一些拆分字错误纠正的示例：

{examples_text}现在请纠正以下句子：
原句：{text}
纠正："""
    
    def load_model(self):
        """加载模型"""
        print("🔧 加载NaCGEC模型...")
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM, set_seed
            import torch
            
            set_seed(42)
            model_path = "/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B"
            
            print(f"📂 从路径加载模型: {model_path}")
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path)
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model = model.to(device)
            model.eval()
            
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            print(f"✅ 模型加载成功，设备: {device}")
            return model, tokenizer, device
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return None, None, None
    
    def inference_with_prompt(self, model, tokenizer, device, text: str, 
                            prompt_strategy: str) -> str:
        """使用指定提示词策略进行推理"""
        if model is None or tokenizer is None:
            return text
        
        try:
            import torch
            
            # 生成提示词
            prompt = self.prompt_strategies[prompt_strategy](text)
            
            # 编码
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=512
            ).to(device)
            
            # 推理
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=128,
                    num_beams=2,
                    early_stopping=True,
                    do_sample=False,
                    pad_token_id=tokenizer.pad_token_id,
                    temperature=0.7
                )
            
            # 解码
            full_output = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 提取生成的部分
            if full_output.startswith(prompt):
                generated = full_output[len(prompt):].strip()
            else:
                generated = full_output.strip()
            
            # 清理输出
            generated = self._clean_output(generated, text)
            
            return generated
            
        except Exception as e:
            print(f"❌ 推理失败: {e}")
            return text
    
    def _clean_output(self, output: str, original: str) -> str:
        """清理模型输出"""
        if not output:
            return original
        
        # 移除<think>标签
        output = re.sub(r'<think>.*?</think>', '', output, flags=re.DOTALL)
        
        # 移除多余的提示词
        patterns_to_remove = [
            r'纠正后的句子[：:]',
            r'纠正结果[：:]',
            r'纠正[：:]',
            r'答案[：:]',
            r'结果[：:]'
        ]
        
        for pattern in patterns_to_remove:
            output = re.sub(pattern, '', output, flags=re.IGNORECASE)
        
        # 只取第一行
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('说明') and not line.startswith('解释'):
                output = line
                break
        
        output = output.strip()
        
        # 如果输出为空或与原文相同，返回原文
        if not output or output == original:
            return original
        
        return output
    
    def test_all_strategies(self, test_data: List[Dict], model, tokenizer, device, 
                          max_samples: int = None) -> Dict[str, Any]:
        """测试所有提示词策略"""
        print(f"🧪 开始测试所有提示词策略...")
        
        if max_samples:
            test_data = test_data[:max_samples]
            print(f"📊 限制测试样本数: {max_samples}")
        
        results = {}
        
        for strategy_name in self.prompt_strategies.keys():
            print(f"\n🔍 测试策略: {strategy_name}")
            print("-" * 40)
            
            strategy_results = []
            start_time = time.time()
            
            for i, item in enumerate(test_data):
                source = item['source']
                target = item['target'][0] if item['target'] else source
                
                # 使用当前策略进行推理
                prediction = self.inference_with_prompt(
                    model, tokenizer, device, source, strategy_name
                )
                
                result = {
                    'id': i,
                    'source': source,
                    'target': target,
                    'prediction': prediction,
                    'error_type': item.get('error_type', 'unknown'),
                    'is_correct': prediction == target,
                    'is_modified': prediction != source
                }
                
                strategy_results.append(result)
                
                # 显示进度
                if (i + 1) % 10 == 0 or i == len(test_data) - 1:
                    print(f"  ✅ 已处理 {i + 1}/{len(test_data)} 个样本")
            
            end_time = time.time()
            
            # 计算统计指标
            total_samples = len(strategy_results)
            correct_count = sum(1 for r in strategy_results if r['is_correct'])
            modified_count = sum(1 for r in strategy_results if r['is_modified'])
            
            accuracy = correct_count / total_samples if total_samples > 0 else 0
            modification_rate = modified_count / total_samples if total_samples > 0 else 0
            avg_time = (end_time - start_time) / total_samples if total_samples > 0 else 0
            
            results[strategy_name] = {
                'strategy_name': strategy_name,
                'total_samples': total_samples,
                'correct_count': correct_count,
                'accuracy': accuracy,
                'modified_count': modified_count,
                'modification_rate': modification_rate,
                'total_time': end_time - start_time,
                'avg_time_per_sample': avg_time,
                'results': strategy_results
            }
            
            print(f"  📊 准确率: {accuracy:.4f}")
            print(f"  📝 修改率: {modification_rate:.4f}")
            print(f"  ⏱️ 平均耗时: {avg_time:.3f}秒/样本")
        
        return results
    
    def analyze_results(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析测试结果"""
        print(f"\n📈 分析测试结果...")
        
        # 策略性能排名
        strategy_performance = []
        for strategy_name, results in all_results.items():
            strategy_performance.append({
                'strategy': strategy_name,
                'accuracy': results['accuracy'],
                'modification_rate': results['modification_rate'],
                'avg_time': results['avg_time_per_sample']
            })
        
        # 按准确率排序
        strategy_performance.sort(key=lambda x: x['accuracy'], reverse=True)
        
        # 计算性能提升
        baseline_accuracy = all_results['basic']['accuracy']
        performance_improvements = {}
        
        for strategy_name, results in all_results.items():
            if strategy_name != 'basic':
                improvement = results['accuracy'] - baseline_accuracy
                performance_improvements[strategy_name] = {
                    'absolute_improvement': improvement,
                    'relative_improvement': improvement / baseline_accuracy if baseline_accuracy > 0 else 0
                }
        
        # 错误类型分析
        error_type_analysis = defaultdict(lambda: defaultdict(dict))
        
        for strategy_name, results in all_results.items():
            error_types = defaultdict(lambda: {'total': 0, 'correct': 0})
            
            for result in results['results']:
                error_type = result['error_type']
                error_types[error_type]['total'] += 1
                if result['is_correct']:
                    error_types[error_type]['correct'] += 1
            
            for error_type, stats in error_types.items():
                accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
                error_type_analysis[error_type][strategy_name] = {
                    'total': stats['total'],
                    'correct': stats['correct'],
                    'accuracy': accuracy
                }
        
        analysis = {
            'strategy_ranking': strategy_performance,
            'performance_improvements': performance_improvements,
            'error_type_analysis': dict(error_type_analysis),
            'best_strategy': strategy_performance[0]['strategy'],
            'worst_strategy': strategy_performance[-1]['strategy'],
            'max_improvement': max(performance_improvements.values(), 
                                 key=lambda x: x['absolute_improvement'])['absolute_improvement'] if performance_improvements else 0
        }
        
        return analysis
    
    def save_results(self, all_results: Dict[str, Any], analysis: Dict[str, Any], 
                    output_path: str) -> None:
        """保存测试结果"""
        print(f"💾 保存测试结果到: {output_path}")
        
        final_results = {
            'test_info': {
                'test_time': datetime.now().isoformat(),
                'strategies_tested': list(all_results.keys()),
                'total_strategies': len(all_results)
            },
            'strategy_results': all_results,
            'analysis': analysis
        }
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存")
    
    def print_summary(self, analysis: Dict[str, Any]) -> None:
        """打印测试总结"""
        print(f"\n" + "=" * 60)
        print(f"🎯 多层次提示词测试总结")
        print(f"=" * 60)
        
        print(f"\n🏆 策略性能排名:")
        for i, strategy in enumerate(analysis['strategy_ranking'], 1):
            print(f"  {i}. {strategy['strategy']}: {strategy['accuracy']:.4f}")
        
        print(f"\n📊 性能提升分析:")
        print(f"  最佳策略: {analysis['best_strategy']}")
        print(f"  最大提升: {analysis['max_improvement']:.4f}")
        
        if analysis['performance_improvements']:
            print(f"\n  各策略相对基础策略的提升:")
            for strategy, improvement in analysis['performance_improvements'].items():
                print(f"    {strategy}: +{improvement['absolute_improvement']:.4f} "
                      f"({improvement['relative_improvement']:.2%})")
        
        print(f"\n💡 建议:")
        if analysis['max_improvement'] > 0.05:
            print(f"  ✅ 多层次提示词显著提升了模型性能")
            print(f"  🎯 推荐使用 '{analysis['best_strategy']}' 策略")
        elif analysis['max_improvement'] > 0.01:
            print(f"  📈 多层次提示词有一定的性能提升")
            print(f"  🔍 可以考虑使用 '{analysis['best_strategy']}' 策略")
        else:
            print(f"  ⚠️ 多层次提示词的性能提升有限")
            print(f"  🔧 建议进一步优化提示词设计")
        
        print(f"=" * 60)


def load_split_data(data_path: str) -> List[Dict]:
    """加载Split数据集"""
    print(f"📂 加载Split数据集: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"✅ 加载了 {len(data)} 条数据")
    return data


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="多层次提示词测试")
    parser.add_argument('--data_path', type=str,
                       default='ChineseErrorCorrector/data/split_data/split_errors_data.json',
                       help='测试数据路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/multi_level_prompt_results.json',
                       help='输出结果路径')
    parser.add_argument('--max_samples', type=int, default=50,
                       help='最大测试样本数')
    
    args = parser.parse_args()
    
    print("🚀 多层次提示词测试开始")
    print("=" * 50)
    print(f"数据路径: {args.data_path}")
    print(f"输出路径: {args.output_path}")
    print(f"最大样本数: {args.max_samples}")
    print("=" * 50)
    
    try:
        # 初始化测试器
        tester = MultiLevelPromptTester()
        
        # 加载数据
        test_data = load_split_data(args.data_path)
        
        # 加载模型
        model, tokenizer, device = tester.load_model()
        
        # 测试所有策略
        all_results = tester.test_all_strategies(
            test_data, model, tokenizer, device, args.max_samples
        )
        
        # 分析结果
        analysis = tester.analyze_results(all_results)
        
        # 保存结果
        tester.save_results(all_results, analysis, args.output_path)
        
        # 打印总结
        tester.print_summary(analysis)
        
        print(f"\n🎉 测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
