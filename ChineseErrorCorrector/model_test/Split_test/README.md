# Split数据集测试系统

## 📋 **项目简介**

这个目录包含了用于测试ChineseErrorCorrector在Split数据集上表现的完整测试系统，参照NaCGEC测试方式实现。

## 📁 **文件结构**

```
Split_test/
├── split_test.py          # 主测试脚本
├── split_evaluate.py      # 评估脚本
└── README.md              # 使用说明
```

## 🎯 **功能特性**

### split_test.py
- ✅ **数据加载**: 自动加载Split数据集
- ✅ **模型推理**: 支持HuggingFace和VLLM两种推理方式
- ✅ **批处理**: VLLM模式支持批处理加速
- ✅ **结果清理**: 自动清理预测结果中的提示词和标签
- ✅ **错误类型统计**: 按错误类型分析模型表现
- ✅ **性能统计**: 记录推理时间和吞吐量

### split_evaluate.py
- ✅ **简单指标**: 完全匹配率、错误类型准确率
- ✅ **ChERRANT评估**: 使用ChERRANT工具计算精确率、召回率、F分数
- ✅ **并行处理**: 支持多进程并行生成M2文件
- ✅ **详细报告**: 生成完整的评估报告

## 🚀 **使用方法**

### 1. 基本测试

```bash
# 基本测试（HuggingFace模式）
python ChineseErrorCorrector/model_test/Split_test/split_test.py \
    --data_path ChineseErrorCorrector/data/split_data/split_errors_data.json \
    --output_path ChineseErrorCorrector/model_test/Split_results/split_test_results.json \
    --max_samples 100

# 使用VLLM加速
python ChineseErrorCorrector/model_test/Split_test/split_test.py \
    --data_path ChineseErrorCorrector/data/split_data/split_errors_data.json \
    --output_path ChineseErrorCorrector/model_test/Split_results/split_test_results.json \
    --use_vllm \
    --batch_size 16 \
    --max_samples 500
```

### 2. 评估结果

```bash
# 完整评估（包含ChERRANT）
python ChineseErrorCorrector/model_test/Split_test/split_evaluate.py \
    --results_file ChineseErrorCorrector/model_test/Split_results/split_test_results.json \
    --output_file ChineseErrorCorrector/model_test/Split_results/split_evaluation_results.json \
    --num_processes 4

# 仅简单指标评估
python ChineseErrorCorrector/model_test/Split_test/split_evaluate.py \
    --results_file ChineseErrorCorrector/model_test/Split_results/split_test_results.json \
    --output_file ChineseErrorCorrector/model_test/Split_results/split_evaluation_results.json \
    --skip_cherrant
```

### 3. 完整流水线

```bash
# 步骤1: 运行测试
python ChineseErrorCorrector/model_test/Split_test/split_test.py \
    --max_samples 1000 \
    --use_vllm \
    --batch_size 32

# 步骤2: 评估结果
python ChineseErrorCorrector/model_test/Split_test/split_evaluate.py \
    --results_file ChineseErrorCorrector/model_test/Split_results/split_test_results.json
```

## 📊 **参数说明**

### split_test.py 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--data_path` | str | split_errors_data.json | Split数据集路径 |
| `--output_path` | str | split_test_results.json | 测试结果输出路径 |
| `--max_samples` | int | None | 最大测试样本数（None表示全部） |
| `--use_vllm` | bool | False | 是否使用VLLM加速 |
| `--batch_size` | int | 16 | 批处理大小（仅VLLM模式） |

### split_evaluate.py 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--results_file` | str | 必需 | 测试结果文件路径 |
| `--output_file` | str | split_evaluation_results.json | 评估结果输出路径 |
| `--cherrant_path` | str | ChERRANT工具路径 | ChERRANT工具目录 |
| `--num_processes` | int | 4 | 并行处理进程数 |
| `--skip_cherrant` | bool | False | 跳过ChERRANT评估 |

## 📈 **输出格式**

### 测试结果文件格式

```json
{
  "test_info": {
    "test_time": "2024-01-01T12:00:00",
    "data_path": "...",
    "model_path": "...",
    "use_vllm": true,
    "batch_size": 16,
    "total_samples": 1000,
    "total_time_seconds": 120.5,
    "average_time_per_sample": 0.12,
    "throughput_samples_per_second": 8.3
  },
  "metrics": {
    "exact_match_count": 850,
    "exact_match_rate": 0.85,
    "error_type_statistics": {
      "拆分字错误": {
        "total": 500,
        "correct": 425,
        "accuracy": 0.85
      }
    }
  },
  "results": [
    {
      "id": 0,
      "source": "原始句子",
      "target": "标准答案",
      "prediction": "模型预测",
      "error_type": "拆分字错误",
      "metadata": {...}
    }
  ]
}
```

### 评估结果文件格式

```json
{
  "evaluation_info": {
    "evaluation_time": "2024-01-01T12:30:00",
    "results_file": "...",
    "cherrant_path": "...",
    "num_processes": 4
  },
  "simple_metrics": {
    "total_samples": 1000,
    "exact_match_count": 850,
    "exact_match_rate": 0.85,
    "error_type_statistics": {...}
  },
  "cherrant_metrics": {
    "precision": 0.82,
    "recall": 0.78,
    "f0.5": 0.81,
    "f1": 0.80,
    "tp": 780,
    "fp": 170,
    "fn": 220
  }
}
```

## 🎯 **Split数据集特点**

Split数据集主要包含以下错误类型：
- **拆分字错误**: 如"先择"→"选择"
- **其他错误类型**: 根据数据集实际内容

每个样本包含：
- `source`: 原始错误句子
- `target`: 标准纠正答案（列表格式）
- `error_type`: 错误类型标签
- `metadata`: 包含详细的错误信息和元数据

## 📊 **评估指标**

### 简单指标
- **完全匹配率**: 预测结果与标准答案完全一致的比例
- **错误类型准确率**: 各错误类型的纠错准确率

### ChERRANT指标
- **精确率 (Precision)**: 预测修改中正确的比例
- **召回率 (Recall)**: 应该修改的地方被正确修改的比例
- **F0.5分数**: 偏向精确率的F分数
- **F1分数**: 精确率和召回率的调和平均

## ⚠️ **注意事项**

1. **数据路径**: 确保Split数据集文件存在
2. **模型路径**: 确认模型路径配置正确
3. **ChERRANT工具**: 评估前确保ChERRANT工具已正确安装
4. **内存使用**: VLLM模式需要足够的GPU内存
5. **并行处理**: 根据系统资源调整进程数

## 🔧 **故障排除**

### 常见问题

1. **数据加载失败**
   ```bash
   # 检查数据文件是否存在
   ls -la ChineseErrorCorrector/data/split_data/split_errors_data.json
   ```

2. **ChERRANT评估失败**
   ```bash
   # 检查ChERRANT工具是否可用
   python ChineseErrorCorrector/model_test/evaluation_tools/ChERRANT/parallel_to_m2.py --help
   ```

3. **内存不足**
   ```bash
   # 减少批大小或样本数
   --batch_size 8 --max_samples 100
   ```

## 📚 **参考资料**

- [NaCGEC测试系统](../NaCGEC_test/)
- [ChERRANT评估工具](../evaluation_tools/ChERRANT/)
- [Split数据集说明](../../data/split_data/)

## 🎉 **使用示例**

```bash
# 完整测试流程示例
cd /path/to/ChineseErrorCorrector

# 1. 小规模测试
python model_test/Split_test/split_test.py --max_samples 50

# 2. 评估结果
python model_test/Split_test/split_evaluate.py \
    --results_file model_test/Split_results/split_test_results.json

# 3. 大规模测试（如果资源充足）
python model_test/Split_test/split_test.py \
    --use_vllm --batch_size 32 --max_samples 5000

# 4. 完整评估
python model_test/Split_test/split_evaluate.py \
    --results_file model_test/Split_results/split_test_results.json \
    --num_processes 8
```
