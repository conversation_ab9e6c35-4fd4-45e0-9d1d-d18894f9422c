#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版多层次提示词测试系统
专门针对拆分字错误优化的提示词策略
"""

import os
import sys
import json
import time
import argparse
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict

# 设置环境变量
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['VLLM_DISABLE'] = '1'

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


class EnhancedPromptTester:
    """增强版提示词测试器"""
    
    def __init__(self):
        self.prompt_strategies = {
            'baseline': self._create_baseline_prompt,
            'explicit_instruction': self._create_explicit_instruction_prompt,
            'pattern_aware': self._create_pattern_aware_prompt,
            'step_by_step': self._create_step_by_step_prompt,
            'example_guided': self._create_example_guided_prompt,
            'constraint_based': self._create_constraint_based_prompt,
            'meta_cognitive': self._create_meta_cognitive_prompt
        }
        
        # 常见拆分字错误模式
        self.split_patterns = [
            ('白勺', '的'), ('牛寺', '特'), ('鬼未', '魅'),
            ('厉力', '励'), ('申', '畅'), ('了', '子'),
            ('京尤', '就'), ('木羊', '样'), ('先择', '选择')
        ]
    
    def _create_baseline_prompt(self, text: str) -> str:
        """基线提示词（简单直接）"""
        return f"请纠正这个句子：{text}"
    
    def _create_explicit_instruction_prompt(self, text: str) -> str:
        """明确指令提示词"""
        return f"""请纠正句子中的拆分字错误。拆分字错误是指将一个完整汉字错误地分成两个或多个部分。

原句：{text}

请输出纠正后的句子："""
    
    def _create_pattern_aware_prompt(self, text: str) -> str:
        """模式感知提示词"""
        patterns_text = "、".join([f"{split}→{correct}" for split, correct in self.split_patterns[:5]])
        
        return f"""请识别并纠正句子中的拆分字错误。

常见拆分字错误模式：{patterns_text}

原句：{text}

纠正后："""
    
    def _create_step_by_step_prompt(self, text: str) -> str:
        """分步骤提示词"""
        return f"""请按以下步骤纠正拆分字错误：

1. 仔细阅读句子：{text}
2. 找出看起来不像完整汉字的字符组合
3. 判断这些组合是否应该合并成一个汉字
4. 替换错误的拆分字符

最终纠正结果："""
    
    def _create_example_guided_prompt(self, text: str) -> str:
        """示例引导提示词"""
        return f"""参考以下拆分字错误纠正示例：

错误：我的鬼未力很强
正确：我的魅力很强
说明：将"鬼未"合并为"魅"

错误：你先择一个方案
正确：你选择一个方案  
说明：将"先择"合并为"选择"

现在请纠正：{text}

纠正后："""
    
    def _create_constraint_based_prompt(self, text: str) -> str:
        """约束条件提示词"""
        return f"""纠正句子中的拆分字错误，遵循以下约束：

1. 只修改拆分字错误，不改变其他内容
2. 确保修改后的句子语义通顺
3. 保持原句的语言风格
4. 如果不确定，保持原样

原句：{text}
纠正："""
    
    def _create_meta_cognitive_prompt(self, text: str) -> str:
        """元认知提示词"""
        return f"""作为中文纠错专家，请运用你的专业知识纠正拆分字错误。

思考过程：
- 拆分字错误通常出现在汉字的偏旁部首被错误分离的情况
- 需要识别哪些相邻字符实际上是一个汉字的组成部分
- 合并后的汉字应该符合现代汉语规范

原句：{text}

基于专业分析，纠正后的句子："""
    
    def load_model(self):
        """加载模型"""
        print("🔧 加载NaCGEC模型...")
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM, set_seed
            import torch
            
            set_seed(42)
            model_path = "/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B"
            
            print(f"📂 从路径加载模型: {model_path}")
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path)
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model = model.to(device)
            model.eval()
            
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            print(f"✅ 模型加载成功，设备: {device}")
            return model, tokenizer, device
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return None, None, None
    
    def inference_with_prompt(self, model, tokenizer, device, text: str, 
                            prompt_strategy: str) -> str:
        """使用指定提示词策略进行推理"""
        if model is None or tokenizer is None:
            return text
        
        try:
            import torch
            
            # 生成提示词
            prompt = self.prompt_strategies[prompt_strategy](text)
            
            # 编码
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=512
            ).to(device)
            
            # 推理
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=100,
                    num_beams=1,
                    do_sample=False,
                    pad_token_id=tokenizer.pad_token_id,
                    temperature=0.1,
                    repetition_penalty=1.1
                )
            
            # 解码
            full_output = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # 提取生成的部分
            if full_output.startswith(prompt):
                generated = full_output[len(prompt):].strip()
            else:
                generated = full_output.strip()
            
            # 清理输出
            generated = self._clean_output(generated, text)
            
            return generated
            
        except Exception as e:
            print(f"❌ 推理失败: {e}")
            return text
    
    def _clean_output(self, output: str, original: str) -> str:
        """清理模型输出"""
        if not output:
            return original
        
        # 移除常见的提示词残留
        patterns_to_remove = [
            r'纠正后[：:]?',
            r'纠正[：:]?',
            r'答案[：:]?',
            r'结果[：:]?',
            r'最终纠正结果[：:]?',
            r'基于专业分析，纠正后的句子[：:]?'
        ]
        
        for pattern in patterns_to_remove:
            output = re.sub(pattern, '', output, flags=re.IGNORECASE)
        
        # 只取第一行有效内容
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) > 5:  # 过滤太短的行
                output = line
                break
        
        output = output.strip()
        
        # 移除重复内容
        if output.count(output[:10]) > 1:  # 检测重复
            parts = output.split(output[:10])
            output = output[:10] + parts[1] if len(parts) > 1 else output[:10]
        
        # 如果输出为空或与原文相同，返回原文
        if not output or output == original:
            return original
        
        return output
    
    def calculate_edit_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self.calculate_edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def evaluate_prediction(self, source: str, target: str, prediction: str) -> Dict[str, Any]:
        """评估预测结果"""
        # 基本指标
        is_exact_match = prediction == target
        is_modified = prediction != source
        
        # 编辑距离
        edit_distance_to_target = self.calculate_edit_distance(prediction, target)
        edit_distance_to_source = self.calculate_edit_distance(prediction, source)
        
        # 改进程度（编辑距离减少）
        source_to_target_distance = self.calculate_edit_distance(source, target)
        improvement = source_to_target_distance - edit_distance_to_target
        improvement_rate = improvement / source_to_target_distance if source_to_target_distance > 0 else 0
        
        return {
            'is_exact_match': is_exact_match,
            'is_modified': is_modified,
            'edit_distance_to_target': edit_distance_to_target,
            'edit_distance_to_source': edit_distance_to_source,
            'improvement': improvement,
            'improvement_rate': improvement_rate
        }
    
    def test_all_strategies(self, test_data: List[Dict], model, tokenizer, device, 
                          max_samples: int = None) -> Dict[str, Any]:
        """测试所有提示词策略"""
        print(f"🧪 开始测试增强版提示词策略...")
        
        if max_samples:
            test_data = test_data[:max_samples]
            print(f"📊 限制测试样本数: {max_samples}")
        
        results = {}
        
        for strategy_name in self.prompt_strategies.keys():
            print(f"\n🔍 测试策略: {strategy_name}")
            print("-" * 40)
            
            strategy_results = []
            start_time = time.time()
            
            for i, item in enumerate(test_data):
                source = item['source']
                target = item['target'][0] if item['target'] else source
                
                # 使用当前策略进行推理
                prediction = self.inference_with_prompt(
                    model, tokenizer, device, source, strategy_name
                )
                
                # 评估预测结果
                evaluation = self.evaluate_prediction(source, target, prediction)
                
                result = {
                    'id': i,
                    'source': source,
                    'target': target,
                    'prediction': prediction,
                    'error_type': item.get('error_type', 'unknown'),
                    **evaluation
                }
                
                strategy_results.append(result)
                
                # 显示进度和示例
                if (i + 1) % 5 == 0 or i == len(test_data) - 1:
                    print(f"  ✅ 已处理 {i + 1}/{len(test_data)} 个样本")
                    if i < 3:  # 显示前几个样本的详细结果
                        print(f"    示例 {i+1}:")
                        print(f"      原句: {source}")
                        print(f"      目标: {target}")
                        print(f"      预测: {prediction}")
                        print(f"      匹配: {'✅' if evaluation['is_exact_match'] else '❌'}")
                        print(f"      改进: {evaluation['improvement_rate']:.2%}")
            
            end_time = time.time()
            
            # 计算统计指标
            total_samples = len(strategy_results)
            exact_matches = sum(1 for r in strategy_results if r['is_exact_match'])
            modifications = sum(1 for r in strategy_results if r['is_modified'])
            avg_improvement = sum(r['improvement_rate'] for r in strategy_results) / total_samples
            
            accuracy = exact_matches / total_samples if total_samples > 0 else 0
            modification_rate = modifications / total_samples if total_samples > 0 else 0
            avg_time = (end_time - start_time) / total_samples if total_samples > 0 else 0
            
            results[strategy_name] = {
                'strategy_name': strategy_name,
                'total_samples': total_samples,
                'exact_matches': exact_matches,
                'accuracy': accuracy,
                'modifications': modifications,
                'modification_rate': modification_rate,
                'avg_improvement_rate': avg_improvement,
                'total_time': end_time - start_time,
                'avg_time_per_sample': avg_time,
                'results': strategy_results
            }
            
            print(f"  📊 准确率: {accuracy:.4f}")
            print(f"  📝 修改率: {modification_rate:.4f}")
            print(f"  📈 平均改进: {avg_improvement:.2%}")
            print(f"  ⏱️ 平均耗时: {avg_time:.3f}秒/样本")
        
        return results
    
    def analyze_results(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析测试结果"""
        print(f"\n📈 分析测试结果...")
        
        # 策略性能排名（按准确率）
        accuracy_ranking = []
        # 策略性能排名（按改进率）
        improvement_ranking = []
        
        for strategy_name, results in all_results.items():
            accuracy_ranking.append({
                'strategy': strategy_name,
                'accuracy': results['accuracy'],
                'modification_rate': results['modification_rate'],
                'avg_improvement_rate': results['avg_improvement_rate'],
                'avg_time': results['avg_time_per_sample']
            })
            
            improvement_ranking.append({
                'strategy': strategy_name,
                'avg_improvement_rate': results['avg_improvement_rate'],
                'accuracy': results['accuracy']
            })
        
        # 排序
        accuracy_ranking.sort(key=lambda x: x['accuracy'], reverse=True)
        improvement_ranking.sort(key=lambda x: x['avg_improvement_rate'], reverse=True)
        
        # 计算相对基线的提升
        baseline_accuracy = all_results['baseline']['accuracy']
        baseline_improvement = all_results['baseline']['avg_improvement_rate']
        
        performance_gains = {}
        for strategy_name, results in all_results.items():
            if strategy_name != 'baseline':
                accuracy_gain = results['accuracy'] - baseline_accuracy
                improvement_gain = results['avg_improvement_rate'] - baseline_improvement
                
                performance_gains[strategy_name] = {
                    'accuracy_gain': accuracy_gain,
                    'improvement_gain': improvement_gain,
                    'relative_accuracy_gain': accuracy_gain / baseline_accuracy if baseline_accuracy > 0 else 0,
                    'relative_improvement_gain': improvement_gain / baseline_improvement if baseline_improvement > 0 else 0
                }
        
        analysis = {
            'accuracy_ranking': accuracy_ranking,
            'improvement_ranking': improvement_ranking,
            'performance_gains': performance_gains,
            'best_accuracy_strategy': accuracy_ranking[0]['strategy'],
            'best_improvement_strategy': improvement_ranking[0]['strategy'],
            'max_accuracy_gain': max(performance_gains.values(), 
                                   key=lambda x: x['accuracy_gain'])['accuracy_gain'] if performance_gains else 0,
            'max_improvement_gain': max(performance_gains.values(), 
                                      key=lambda x: x['improvement_gain'])['improvement_gain'] if performance_gains else 0
        }
        
        return analysis
    
    def save_results(self, all_results: Dict[str, Any], analysis: Dict[str, Any], 
                    output_path: str) -> None:
        """保存测试结果"""
        print(f"💾 保存测试结果到: {output_path}")
        
        final_results = {
            'test_info': {
                'test_time': datetime.now().isoformat(),
                'strategies_tested': list(all_results.keys()),
                'total_strategies': len(all_results),
                'test_type': 'enhanced_multi_level_prompt'
            },
            'strategy_results': all_results,
            'analysis': analysis
        }
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存")
    
    def print_summary(self, analysis: Dict[str, Any]) -> None:
        """打印测试总结"""
        print(f"\n" + "=" * 60)
        print(f"🎯 增强版多层次提示词测试总结")
        print(f"=" * 60)
        
        print(f"\n🏆 准确率排名:")
        for i, strategy in enumerate(analysis['accuracy_ranking'], 1):
            print(f"  {i}. {strategy['strategy']}: {strategy['accuracy']:.4f} "
                  f"(改进率: {strategy['avg_improvement_rate']:.2%})")
        
        print(f"\n📈 改进率排名:")
        for i, strategy in enumerate(analysis['improvement_ranking'], 1):
            print(f"  {i}. {strategy['strategy']}: {strategy['avg_improvement_rate']:.2%} "
                  f"(准确率: {strategy['accuracy']:.4f})")
        
        print(f"\n📊 性能提升分析:")
        print(f"  最佳准确率策略: {analysis['best_accuracy_strategy']}")
        print(f"  最佳改进率策略: {analysis['best_improvement_strategy']}")
        print(f"  最大准确率提升: {analysis['max_accuracy_gain']:.4f}")
        print(f"  最大改进率提升: {analysis['max_improvement_gain']:.2%}")
        
        if analysis['performance_gains']:
            print(f"\n  各策略相对基线的提升:")
            for strategy, gains in analysis['performance_gains'].items():
                print(f"    {strategy}:")
                print(f"      准确率: +{gains['accuracy_gain']:.4f} "
                      f"({gains['relative_accuracy_gain']:.1%})")
                print(f"      改进率: +{gains['improvement_gain']:.2%} "
                      f"({gains['relative_improvement_gain']:.1%})")
        
        print(f"\n💡 结论:")
        if analysis['max_accuracy_gain'] > 0.1:
            print(f"  ✅ 增强版多层次提示词显著提升了模型性能")
            print(f"  🎯 强烈推荐使用 '{analysis['best_accuracy_strategy']}' 策略")
        elif analysis['max_accuracy_gain'] > 0.05:
            print(f"  📈 增强版多层次提示词有明显的性能提升")
            print(f"  🔍 推荐使用 '{analysis['best_accuracy_strategy']}' 策略")
        elif analysis['max_improvement_gain'] > 0.1:
            print(f"  📊 虽然准确率提升有限，但改进率有显著提升")
            print(f"  🎯 推荐使用 '{analysis['best_improvement_strategy']}' 策略")
        else:
            print(f"  ⚠️ 提示词优化的效果仍然有限")
            print(f"  🔧 建议考虑模型微调或其他优化方法")
        
        print(f"=" * 60)


def load_split_data(data_path: str) -> List[Dict]:
    """加载Split数据集"""
    print(f"📂 加载Split数据集: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"✅ 加载了 {len(data)} 条数据")
    return data


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版多层次提示词测试")
    parser.add_argument('--data_path', type=str,
                       default='ChineseErrorCorrector/data/split_data/split_errors_data.json',
                       help='测试数据路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/enhanced_prompt_results.json',
                       help='输出结果路径')
    parser.add_argument('--max_samples', type=int, default=30,
                       help='最大测试样本数')
    
    args = parser.parse_args()
    
    print("🚀 增强版多层次提示词测试开始")
    print("=" * 50)
    print(f"数据路径: {args.data_path}")
    print(f"输出路径: {args.output_path}")
    print(f"最大样本数: {args.max_samples}")
    print("=" * 50)
    
    try:
        # 初始化测试器
        tester = EnhancedPromptTester()
        
        # 加载数据
        test_data = load_split_data(args.data_path)
        
        # 加载模型
        model, tokenizer, device = tester.load_model()
        
        # 测试所有策略
        all_results = tester.test_all_strategies(
            test_data, model, tokenizer, device, args.max_samples
        )
        
        # 分析结果
        analysis = tester.analyze_results(all_results)
        
        # 保存结果
        tester.save_results(all_results, analysis, args.output_path)
        
        # 打印总结
        tester.print_summary(analysis)
        
        print(f"\n🎉 测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
