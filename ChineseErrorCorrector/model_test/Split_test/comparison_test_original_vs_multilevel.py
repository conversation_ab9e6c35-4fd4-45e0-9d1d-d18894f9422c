#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comparison Test: Original 3-4B Model vs Multi-Level Prompt Enhanced Model
Tests the performance difference between original model and multi-level prompt strategies
All output in English to prevent encoding issues
"""

import os
import sys
import json
import time
import argparse
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict

# Set environment variables
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['VLLM_DISABLE'] = '1'

# Add project path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


class ModelComparisonTester:
    """Model Comparison Tester for Original vs Multi-Level Prompt Enhanced"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = None
        
        # Define prompt strategies
        self.prompt_strategies = {
            'original': self._create_original_prompt,
            'constraint_based': self._create_constraint_based_prompt,
            'explicit_instruction': self._create_explicit_instruction_prompt,
            'example_guided': self._create_example_guided_prompt
        }
    
    def _create_original_prompt(self, text: str) -> str:
        """Original simple prompt used in the baseline model"""
        return f"纠正句子：{text}\n纠正："
    
    def _create_constraint_based_prompt(self, text: str) -> str:
        """Best performing constraint-based prompt strategy"""
        return f"""纠正句子中的拆分字错误，遵循以下约束：

1. 只修改拆分字错误，不改变其他内容
2. 确保修改后的句子语义通顺
3. 保持原句的语言风格
4. 如果不确定，保持原样

原句：{text}
纠正："""
    
    def _create_explicit_instruction_prompt(self, text: str) -> str:
        """Explicit instruction prompt strategy"""
        return f"""请纠正句子中的拆分字错误。拆分字错误是指将一个完整汉字错误地分成两个或多个部分。

原句：{text}

请输出纠正后的句子："""
    
    def _create_example_guided_prompt(self, text: str) -> str:
        """Example-guided prompt strategy"""
        return f"""参考以下拆分字错误纠正示例：

错误：他的鬼未力很强
正确：他的魅力很强
说明：将"鬼未"合并为"魅"

错误：你先择一个方案
正确：你选择一个方案  
说明：将"先择"修改为"选择"

现在请纠正：{text}

纠正后："""
    
    def load_model(self):
        """Load the NaCGEC model"""
        print("🔧 Loading NaCGEC Model...")
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM, set_seed
            import torch
            
            set_seed(42)
            model_path = "/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B"
            
            print(f"📂 Loading model from path: {model_path}")
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path)
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model = model.to(device)
            model.eval()
            
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            self.model = model
            self.tokenizer = tokenizer
            self.device = device
            
            print(f"✅ Model loaded successfully, device: {device}")
            return True
            
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            return False
    
    def inference_with_prompt(self, text: str, prompt_strategy: str) -> str:
        """Perform inference with specified prompt strategy"""
        if self.model is None or self.tokenizer is None:
            return text
        
        try:
            import torch
            
            # Generate prompt
            prompt = self.prompt_strategies[prompt_strategy](text)
            
            # Encode
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=512
            ).to(self.device)
            
            # Inference
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=100,
                    num_beams=1,
                    do_sample=False,
                    pad_token_id=self.tokenizer.pad_token_id,
                    temperature=0.1,
                    repetition_penalty=1.1
                )
            
            # Decode
            full_output = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract generated part
            if full_output.startswith(prompt):
                generated = full_output[len(prompt):].strip()
            else:
                generated = full_output.strip()
            
            # Clean output
            generated = self._clean_output(generated, text)
            
            return generated
            
        except Exception as e:
            print(f"❌ Inference failed: {e}")
            return text
    
    def _clean_output(self, output: str, original: str) -> str:
        """Clean model output"""
        if not output:
            return original
        
        # Remove common prompt residuals
        patterns_to_remove = [
            r'纠正后[：:]?',
            r'纠正[：:]?',
            r'答案[：:]?',
            r'结果[：:]?',
            r'最终纠正结果[：:]?'
        ]
        
        for pattern in patterns_to_remove:
            output = re.sub(pattern, '', output, flags=re.IGNORECASE)
        
        # Take only the first valid line
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) > 5:  # Filter too short lines
                output = line
                break
        
        output = output.strip()
        
        # Remove repetitive content
        if output.count(output[:10]) > 1:  # Detect repetition
            parts = output.split(output[:10])
            output = output[:10] + parts[1] if len(parts) > 1 else output[:10]
        
        # If output is empty or same as original, return original
        if not output or output == original:
            return original
        
        return output
    
    def calculate_edit_distance(self, s1: str, s2: str) -> int:
        """Calculate edit distance"""
        if len(s1) < len(s2):
            return self.calculate_edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def evaluate_prediction(self, source: str, target: str, prediction: str) -> Dict[str, Any]:
        """Evaluate prediction results"""
        # Basic metrics
        is_exact_match = prediction == target
        is_modified = prediction != source
        
        # Edit distance
        edit_distance_to_target = self.calculate_edit_distance(prediction, target)
        edit_distance_to_source = self.calculate_edit_distance(prediction, source)
        
        # Improvement degree (edit distance reduction)
        source_to_target_distance = self.calculate_edit_distance(source, target)
        improvement = source_to_target_distance - edit_distance_to_target
        improvement_rate = improvement / source_to_target_distance if source_to_target_distance > 0 else 0
        
        return {
            'is_exact_match': is_exact_match,
            'is_modified': is_modified,
            'edit_distance_to_target': edit_distance_to_target,
            'edit_distance_to_source': edit_distance_to_source,
            'improvement': improvement,
            'improvement_rate': improvement_rate
        }
    
    def test_strategy(self, test_data: List[Dict], strategy_name: str, 
                     max_samples: int = None) -> Dict[str, Any]:
        """Test a specific prompt strategy"""
        print(f"\n🔍 Testing Strategy: {strategy_name}")
        print("-" * 40)
        
        if max_samples:
            test_data = test_data[:max_samples]
        
        strategy_results = []
        start_time = time.time()
        
        for i, item in enumerate(test_data):
            source = item['source']
            target = item['target'][0] if item['target'] else source
            
            # Perform inference with current strategy
            prediction = self.inference_with_prompt(source, strategy_name)
            
            # Evaluate prediction results
            evaluation = self.evaluate_prediction(source, target, prediction)
            
            result = {
                'id': i,
                'source': source,
                'target': target,
                'prediction': prediction,
                'error_type': item.get('error_type', 'unknown'),
                **evaluation
            }
            
            strategy_results.append(result)
            
            # Show progress and examples
            if (i + 1) % 5 == 0 or i == len(test_data) - 1:
                print(f"  ✅ Processed {i + 1}/{len(test_data)} samples")
                if i < 3:  # Show detailed results for first few samples
                    print(f"    Example {i+1}:")
                    print(f"      Original: {source}")
                    print(f"      Target: {target}")
                    print(f"      Prediction: {prediction}")
                    print(f"      Match: {'✅' if evaluation['is_exact_match'] else '❌'}")
                    print(f"      Improvement: {evaluation['improvement_rate']:.2%}")
        
        end_time = time.time()
        
        # Calculate statistics
        total_samples = len(strategy_results)
        exact_matches = sum(1 for r in strategy_results if r['is_exact_match'])
        modifications = sum(1 for r in strategy_results if r['is_modified'])
        avg_improvement = sum(r['improvement_rate'] for r in strategy_results) / total_samples
        
        accuracy = exact_matches / total_samples if total_samples > 0 else 0
        modification_rate = modifications / total_samples if total_samples > 0 else 0
        avg_time = (end_time - start_time) / total_samples if total_samples > 0 else 0
        
        results = {
            'strategy_name': strategy_name,
            'total_samples': total_samples,
            'exact_matches': exact_matches,
            'accuracy': accuracy,
            'modifications': modifications,
            'modification_rate': modification_rate,
            'avg_improvement_rate': avg_improvement,
            'total_time': end_time - start_time,
            'avg_time_per_sample': avg_time,
            'results': strategy_results
        }
        
        print(f"  📊 Accuracy: {accuracy:.4f}")
        print(f"  📝 Modification Rate: {modification_rate:.4f}")
        print(f"  📈 Average Improvement: {avg_improvement:.2%}")
        print(f"  ⏱️ Average Time: {avg_time:.3f}s/sample")
        
        return results

    def run_comparison_test(self, test_data: List[Dict], max_samples: int = None) -> Dict[str, Any]:
        """Run comparison test between all strategies"""
        print("=" * 60)
        print("COMPARISON TEST: Original vs Multi-Level Prompt Enhanced Model")
        print("=" * 60)
        print(f"Test Dataset: Split Character Error Correction")
        print(f"Total Samples: {len(test_data)}")
        print(f"Max Samples: {max_samples if max_samples else 'All'}")
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        # Test all strategies
        all_results = {}
        strategy_order = ['original', 'constraint_based', 'explicit_instruction', 'example_guided']

        for strategy in strategy_order:
            all_results[strategy] = self.test_strategy(test_data, strategy, max_samples)

        # Generate comparison analysis
        analysis = self._analyze_comparison_results(all_results)

        return {
            'test_info': {
                'test_time': datetime.now().isoformat(),
                'total_samples': max_samples if max_samples else len(test_data),
                'strategies_tested': strategy_order,
                'test_type': 'original_vs_multilevel_comparison'
            },
            'strategy_results': all_results,
            'comparison_analysis': analysis
        }

    def _analyze_comparison_results(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze comparison results between strategies"""
        print(f"\n" + "=" * 60)
        print("COMPARISON ANALYSIS")
        print("=" * 60)

        # Extract metrics for comparison
        original_results = all_results['original']

        comparison_data = []
        improvements = {}

        for strategy_name, results in all_results.items():
            data = {
                'strategy': strategy_name,
                'accuracy': results['accuracy'],
                'modification_rate': results['modification_rate'],
                'avg_improvement_rate': results['avg_improvement_rate'],
                'avg_time': results['avg_time_per_sample'],
                'exact_matches': results['exact_matches'],
                'total_samples': results['total_samples']
            }
            comparison_data.append(data)

            # Calculate improvement relative to original
            if strategy_name != 'original':
                improvements[strategy_name] = {
                    'accuracy_improvement': results['accuracy'] - original_results['accuracy'],
                    'modification_rate_change': results['modification_rate'] - original_results['modification_rate'],
                    'avg_improvement_rate_change': results['avg_improvement_rate'] - original_results['avg_improvement_rate'],
                    'time_efficiency_change': original_results['avg_time_per_sample'] - results['avg_time_per_sample']
                }

        # Sort by accuracy
        comparison_data.sort(key=lambda x: x['accuracy'], reverse=True)

        # Find best performing strategy
        best_strategy = comparison_data[0]['strategy']
        best_accuracy = comparison_data[0]['accuracy']

        # Print comparison table
        print("\nPERFORMANCE COMPARISON TABLE")
        print("-" * 60)
        print(f"{'Rank':<4} {'Strategy':<20} {'Accuracy':<10} {'Mod.Rate':<10} {'Avg.Time':<10}")
        print("-" * 60)

        for i, data in enumerate(comparison_data, 1):
            print(f"{i:<4} {data['strategy']:<20} {data['accuracy']:<10.4f} "
                  f"{data['modification_rate']:<10.4f} {data['avg_time']:<10.3f}")

        print("-" * 60)

        # Print improvement analysis
        print(f"\nIMPROVEMENT ANALYSIS (vs Original)")
        print("-" * 60)

        for strategy, improvement in improvements.items():
            print(f"\n{strategy.upper()}:")
            print(f"  Accuracy Improvement: {improvement['accuracy_improvement']:+.4f}")
            print(f"  Modification Rate Change: {improvement['modification_rate_change']:+.4f}")
            print(f"  Avg Improvement Rate Change: {improvement['avg_improvement_rate_change']:+.2%}")
            print(f"  Time Efficiency Change: {improvement['time_efficiency_change']:+.3f}s")

            # Determine if improvement is significant
            if improvement['accuracy_improvement'] > 0.05:
                print(f"  Assessment: SIGNIFICANT IMPROVEMENT")
            elif improvement['accuracy_improvement'] > 0.01:
                print(f"  Assessment: MODERATE IMPROVEMENT")
            elif improvement['accuracy_improvement'] > 0:
                print(f"  Assessment: SLIGHT IMPROVEMENT")
            else:
                print(f"  Assessment: NO IMPROVEMENT")

        # Overall conclusion
        print(f"\n" + "=" * 60)
        print("OVERALL CONCLUSION")
        print("=" * 60)

        max_improvement = max(improvements.values(), key=lambda x: x['accuracy_improvement'])['accuracy_improvement']

        if max_improvement > 0.1:
            conclusion = "Multi-level prompts provide SIGNIFICANT performance improvements"
            recommendation = f"STRONGLY RECOMMEND using {best_strategy} strategy"
        elif max_improvement > 0.05:
            conclusion = "Multi-level prompts provide MODERATE performance improvements"
            recommendation = f"RECOMMEND using {best_strategy} strategy"
        elif max_improvement > 0.01:
            conclusion = "Multi-level prompts provide SLIGHT performance improvements"
            recommendation = f"CONSIDER using {best_strategy} strategy"
        else:
            conclusion = "Multi-level prompts provide LIMITED performance improvements"
            recommendation = "Consider other optimization approaches"

        print(f"Conclusion: {conclusion}")
        print(f"Best Strategy: {best_strategy} (Accuracy: {best_accuracy:.4f})")
        print(f"Max Improvement: +{max_improvement:.4f}")
        print(f"Recommendation: {recommendation}")
        print("=" * 60)

        return {
            'comparison_data': comparison_data,
            'improvements': improvements,
            'best_strategy': best_strategy,
            'best_accuracy': best_accuracy,
            'max_improvement': max_improvement,
            'conclusion': conclusion,
            'recommendation': recommendation
        }

    def save_results(self, results: Dict[str, Any], output_path: str) -> None:
        """Save comparison results to file"""
        print(f"\nSaving results to: {output_path}")

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"Results saved successfully")


def load_split_data(data_path: str) -> List[Dict]:
    """Load Split dataset"""
    print(f"Loading Split dataset: {data_path}")

    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"Loaded {len(data)} samples")
    return data


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Original vs Multi-Level Prompt Comparison Test")
    parser.add_argument('--data_path', type=str,
                       default='ChineseErrorCorrector/data/split_data/split_errors_data.json',
                       help='Test data path')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/comparison_original_vs_multilevel.json',
                       help='Output results path')
    parser.add_argument('--max_samples', type=int, default=20,
                       help='Maximum test samples')

    args = parser.parse_args()

    print("ORIGINAL vs MULTI-LEVEL PROMPT COMPARISON TEST")
    print("=" * 50)
    print(f"Data Path: {args.data_path}")
    print(f"Output Path: {args.output_path}")
    print(f"Max Samples: {args.max_samples}")
    print("=" * 50)

    try:
        # Initialize tester
        tester = ModelComparisonTester()

        # Load model
        if not tester.load_model():
            print("Failed to load model, exiting...")
            sys.exit(1)

        # Load data
        test_data = load_split_data(args.data_path)

        # Run comparison test
        results = tester.run_comparison_test(test_data, args.max_samples)

        # Save results
        tester.save_results(results, args.output_path)

        print(f"\nComparison test completed successfully!")

    except Exception as e:
        print(f"\nComparison test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
