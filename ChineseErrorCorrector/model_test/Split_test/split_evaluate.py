#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Split数据集评估脚本
使用ChERRANT工具进行详细评估
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
import multiprocessing
from datetime import datetime
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed


def load_test_results(results_path):
    """加载测试结果"""
    print(f"加载测试结果: {results_path}")
    
    if not os.path.exists(results_path):
        raise FileNotFoundError(f"结果文件不存在: {results_path}")
    
    with open(results_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = data['results']
    print(f"加载了 {len(results)} 条结果")
    
    return results


def process_chunk_to_m2(chunk_data):
    """处理数据块生成M2文件的工作函数"""
    chunk_id, chunk_results, cherrant_path, is_reference = chunk_data

    try:
        # 确保使用绝对路径
        cherrant_abs_path = os.path.abspath(cherrant_path)

        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建临时parallel文件
            para_file = os.path.join(temp_dir, f'chunk_{chunk_id}.para')

            with open(para_file, 'w', encoding='utf-8') as f:
                for i, result in enumerate(chunk_results):
                    source = result['source'].strip().replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    
                    if is_reference:
                        target = result['target'].strip().replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    else:
                        target = result['prediction'].strip().replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    
                    f.write(f"{source}\t{target}\n")

            # 生成M2文件
            m2_file = os.path.join(temp_dir, f'chunk_{chunk_id}.m2')
            
            cmd = [
                'python',
                'parallel_to_m2.py',
                '-f', para_file,
                '-o', m2_file,
                '-g', 'char'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, cwd=cherrant_abs_path)
            
            if result.returncode != 0:
                print(f"ChERRANT处理块 {chunk_id} 失败:")
                print(f"命令: {' '.join(cmd)}")
                print(f"错误输出: {result.stderr}")
                return chunk_id, None
            
            # 读取生成的M2文件
            if os.path.exists(m2_file):
                with open(m2_file, 'r', encoding='utf-8') as f:
                    m2_content = f.read()
                return chunk_id, m2_content
            else:
                print(f"M2文件未生成: {m2_file}")
                return chunk_id, None
                
    except Exception as e:
        print(f"处理块 {chunk_id} 时发生异常: {e}")
        return chunk_id, None


def generate_m2_files_parallel(results, cherrant_path, num_processes=4):
    """并行生成M2文件"""
    print(f"使用 {num_processes} 个进程并行生成M2文件...")
    
    # 分块处理
    chunk_size = max(1, len(results) // num_processes)
    chunks = [results[i:i + chunk_size] for i in range(0, len(results), chunk_size)]
    
    # 准备任务数据
    reference_tasks = [(i, chunk, cherrant_path, True) for i, chunk in enumerate(chunks)]
    hypothesis_tasks = [(i, chunk, cherrant_path, False) for i, chunk in enumerate(chunks)]
    
    reference_m2_parts = {}
    hypothesis_m2_parts = {}
    
    # 处理参考文件
    print("生成参考M2文件...")
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        future_to_chunk = {executor.submit(process_chunk_to_m2, task): task[0] for task in reference_tasks}
        
        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                chunk_id, m2_content = future.result()
                if m2_content is not None:
                    reference_m2_parts[chunk_id] = m2_content
                else:
                    print(f"参考M2块 {chunk_id} 生成失败")
            except Exception as e:
                print(f"参考M2块 {chunk_id} 处理异常: {e}")
    
    # 处理假设文件
    print("生成假设M2文件...")
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        future_to_chunk = {executor.submit(process_chunk_to_m2, task): task[0] for task in hypothesis_tasks}
        
        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                chunk_id, m2_content = future.result()
                if m2_content is not None:
                    hypothesis_m2_parts[chunk_id] = m2_content
                else:
                    print(f"假设M2块 {chunk_id} 生成失败")
            except Exception as e:
                print(f"假设M2块 {chunk_id} 处理异常: {e}")
    
    # 合并M2文件内容
    reference_m2 = ""
    hypothesis_m2 = ""
    
    for i in range(len(chunks)):
        if i in reference_m2_parts:
            reference_m2 += reference_m2_parts[i]
        if i in hypothesis_m2_parts:
            hypothesis_m2 += hypothesis_m2_parts[i]
    
    return reference_m2, hypothesis_m2


def evaluate_with_cherrant(results, cherrant_path, num_processes=4):
    """使用ChERRANT评估结果"""
    print(f"使用ChERRANT评估 {len(results)} 条结果...")
    
    try:
        # 并行生成M2文件
        reference_m2, hypothesis_m2 = generate_m2_files_parallel(results, cherrant_path, num_processes)
        
        if not reference_m2 or not hypothesis_m2:
            raise Exception("M2文件生成失败")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 保存M2文件
            ref_m2_file = os.path.join(temp_dir, 'reference.m2')
            hyp_m2_file = os.path.join(temp_dir, 'hypothesis.m2')
            
            with open(ref_m2_file, 'w', encoding='utf-8') as f:
                f.write(reference_m2)
            
            with open(hyp_m2_file, 'w', encoding='utf-8') as f:
                f.write(hypothesis_m2)
            
            # 运行ChERRANT评估
            cmd = [
                'python',
                'compare_m2_for_evaluation.py',
                '-hyp', hyp_m2_file,
                '-ref', ref_m2_file
            ]

            print(f"运行ChERRANT评估命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=cherrant_path)
            
            if result.returncode != 0:
                print(f"ChERRANT评估失败:")
                print(f"错误输出: {result.stderr}")
                return None
            
            # 解析评估结果
            output_lines = result.stdout.strip().split('\n')
            cherrant_metrics = {}

            print("ChERRANT原始输出:")
            for line in output_lines:
                print(f"  {line}")

            # 解析指标 - 改进的解析逻辑
            header_found = False
            for line in output_lines:
                line = line.strip()

                # 查找表格标题行
                if 'TP' in line and 'FP' in line and 'FN' in line and 'Prec' in line:
                    header_found = True
                    print(f"  找到标题行: {line}")
                    continue

                # 如果找到标题行后的下一行是数据行
                if header_found and '\t' in line:
                    parts = line.split('\t')
                    print(f"  解析数据行: {parts}")

                    try:
                        if len(parts) >= 6:  # TP FP FN Prec Rec F0.5
                            cherrant_metrics['tp'] = int(parts[0])
                            cherrant_metrics['fp'] = int(parts[1])
                            cherrant_metrics['fn'] = int(parts[2])
                            cherrant_metrics['precision'] = float(parts[3])
                            cherrant_metrics['recall'] = float(parts[4])
                            cherrant_metrics['f0.5'] = float(parts[5])
                            print(f"  成功解析指标: TP={cherrant_metrics['tp']}, FP={cherrant_metrics['fp']}, FN={cherrant_metrics['fn']}")
                            print(f"  成功解析指标: Prec={cherrant_metrics['precision']}, Rec={cherrant_metrics['recall']}, F0.5={cherrant_metrics['f0.5']}")
                            break
                    except (ValueError, IndexError) as e:
                        print(f"  解析数据行失败: {e}")
                        pass

                # 备用解析方法：冒号分隔的格式
                elif 'Precision' in line and ':' in line:
                    try:
                        cherrant_metrics['precision'] = float(line.split(':')[1].strip())
                    except (ValueError, IndexError):
                        pass
                elif 'Recall' in line and ':' in line:
                    try:
                        cherrant_metrics['recall'] = float(line.split(':')[1].strip())
                    except (ValueError, IndexError):
                        pass
                elif 'F0.5' in line and ':' in line:
                    try:
                        cherrant_metrics['f0.5'] = float(line.split(':')[1].strip())
                    except (ValueError, IndexError):
                        pass
                elif 'F1' in line and 'F0.5' not in line and ':' in line:
                    try:
                        cherrant_metrics['f1'] = float(line.split(':')[1].strip())
                    except (ValueError, IndexError):
                        pass

            print("\n解析的ChERRANT指标:")
            for key, value in cherrant_metrics.items():
                print(f"  {key.upper()}: {value}")

            # 修正ChERRANT指标的逻辑错误
            if 'tp' in cherrant_metrics and 'fp' in cherrant_metrics and 'fn' in cherrant_metrics:
                tp = cherrant_metrics['tp']
                fp = cherrant_metrics['fp']
                fn = cherrant_metrics['fn']

                print(f"  ChERRANT统计: TP={tp}, FP={fp}, FN={fn}")

                # 当TP=FP=FN=0时，说明没有检测到任何编辑操作
                if tp == 0 and fp == 0 and fn == 0:
                    print(f"  ⚠️ 检测到异常情况：TP=FP=FN=0，这通常表示：")
                    print(f"     1. 模型预测与原句完全相同（未进行任何修改）")
                    print(f"     2. ChERRANT无法识别数据中的编辑操作")
                    print(f"     3. 数据格式可能不适合ChERRANT处理")

                    # 在这种情况下，将指标设为0而不是1
                    cherrant_metrics['precision'] = 0.0
                    cherrant_metrics['recall'] = 0.0
                    cherrant_metrics['f1'] = 0.0
                    cherrant_metrics['f0.5'] = 0.0
                    print(f"  修正指标为0（更符合实际情况）")
                else:
                    # 正常情况下的计算
                    if 'precision' not in cherrant_metrics or cherrant_metrics['precision'] == 1.0:
                        if (tp + fp) > 0:
                            cherrant_metrics['precision'] = tp / (tp + fp)
                            print(f"  重新计算 PRECISION: {cherrant_metrics['precision']:.4f}")
                        else:
                            cherrant_metrics['precision'] = 0.0
                            print(f"  PRECISION设为0（无预测编辑）")

                    if 'recall' not in cherrant_metrics or cherrant_metrics['recall'] == 1.0:
                        if (tp + fn) > 0:
                            cherrant_metrics['recall'] = tp / (tp + fn)
                            print(f"  重新计算 RECALL: {cherrant_metrics['recall']:.4f}")
                        else:
                            cherrant_metrics['recall'] = 0.0
                            print(f"  RECALL设为0（无需要编辑）")

                    # 重新计算F分数
                    p = cherrant_metrics['precision']
                    r = cherrant_metrics['recall']

                    if (p + r) > 0:
                        cherrant_metrics['f1'] = 2 * p * r / (p + r)
                        print(f"  重新计算 F1: {cherrant_metrics['f1']:.4f}")
                    else:
                        cherrant_metrics['f1'] = 0.0

                    if (0.25 * p + r) > 0:
                        cherrant_metrics['f0.5'] = 1.25 * p * r / (0.25 * p + r)
                        print(f"  重新计算 F0.5: {cherrant_metrics['f0.5']:.4f}")
                    else:
                        cherrant_metrics['f0.5'] = 0.0

            return cherrant_metrics
            
    except Exception as e:
        print(f"ChERRANT评估失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def calculate_simple_metrics(results):
    """计算简单评估指标"""
    print("计算简单评估指标...")

    total_samples = len(results)
    exact_matches = sum(1 for r in results if r['prediction'] == r['target'])
    exact_match_rate = exact_matches / total_samples if total_samples > 0 else 0

    # 统计模型修改情况
    no_change_count = sum(1 for r in results if r['prediction'] == r['source'])
    modified_count = total_samples - no_change_count
    modification_rate = modified_count / total_samples if total_samples > 0 else 0

    # 按错误类型统计
    error_type_stats = defaultdict(lambda: {'total': 0, 'correct': 0, 'modified': 0, 'accuracy': 0.0, 'modification_rate': 0.0})

    for result in results:
        error_type = result.get('error_type', 'unknown')
        error_type_stats[error_type]['total'] += 1

        if result['prediction'] == result['target']:
            error_type_stats[error_type]['correct'] += 1

        if result['prediction'] != result['source']:
            error_type_stats[error_type]['modified'] += 1

    # 计算各错误类型的准确率和修改率
    for error_type in error_type_stats:
        stats = error_type_stats[error_type]
        stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
        stats['modification_rate'] = stats['modified'] / stats['total'] if stats['total'] > 0 else 0

    simple_metrics = {
        'total_samples': total_samples,
        'exact_match_count': exact_matches,
        'exact_match_rate': exact_match_rate,
        'no_change_count': no_change_count,
        'modified_count': modified_count,
        'modification_rate': modification_rate,
        'error_type_statistics': dict(error_type_stats)
    }

    print("简单评估指标:")
    print(f"  总样本数: {total_samples}")
    print(f"  完全匹配数: {exact_matches}")
    print(f"  完全匹配率: {exact_match_rate:.4f}")
    print(f"  未修改样本数: {no_change_count}")
    print(f"  已修改样本数: {modified_count}")
    print(f"  修改率: {modification_rate:.4f}")

    print("  按错误类型统计:")
    for error_type, stats in sorted(error_type_stats.items(), key=lambda x: x[1]['total'], reverse=True):
        print(f"    {error_type}:")
        print(f"      总数: {stats['total']}")
        print(f"      正确: {stats['correct']} (准确率: {stats['accuracy']:.4f})")
        print(f"      修改: {stats['modified']} (修改率: {stats['modification_rate']:.4f})")

    return simple_metrics


def calculate_edit_distance_metrics(results):
    """计算基于编辑距离的评估指标"""
    print("计算编辑距离评估指标...")

    try:
        from difflib import SequenceMatcher
    except ImportError:
        print("⚠️ 无法导入difflib，跳过编辑距离评估")
        return None

    total_char_edits_needed = 0  # 需要的总字符编辑数
    total_char_edits_made = 0    # 实际进行的总字符编辑数
    total_correct_edits = 0      # 正确的编辑数

    for result in results:
        source = result['source']
        target = result['target']
        prediction = result['prediction']

        # 计算需要的编辑数（source -> target）
        matcher_ref = SequenceMatcher(None, source, target)
        edits_needed = sum(abs(b2-b1) + abs(a2-a1) for tag, a1, a2, b1, b2 in matcher_ref.get_opcodes() if tag != 'equal')

        # 计算实际的编辑数（source -> prediction）
        matcher_hyp = SequenceMatcher(None, source, prediction)
        edits_made = sum(abs(b2-b1) + abs(a2-a1) for tag, a1, a2, b1, b2 in matcher_hyp.get_opcodes() if tag != 'equal')

        # 计算正确的编辑数（prediction -> target的相似度）
        matcher_correct = SequenceMatcher(None, prediction, target)
        similarity = matcher_correct.ratio()
        correct_edits = edits_made * similarity if edits_made > 0 else 0

        total_char_edits_needed += edits_needed
        total_char_edits_made += edits_made
        total_correct_edits += correct_edits

    # 计算指标
    precision = total_correct_edits / total_char_edits_made if total_char_edits_made > 0 else 0
    recall = total_correct_edits / total_char_edits_needed if total_char_edits_needed > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    f05 = 1.25 * precision * recall / (0.25 * precision + recall) if (0.25 * precision + recall) > 0 else 0

    edit_metrics = {
        'total_edits_needed': total_char_edits_needed,
        'total_edits_made': total_char_edits_made,
        'total_correct_edits': total_correct_edits,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'f0.5': f05
    }

    print("编辑距离评估指标:")
    print(f"  需要的总编辑数: {total_char_edits_needed}")
    print(f"  实际的总编辑数: {total_char_edits_made}")
    print(f"  正确的编辑数: {total_correct_edits:.2f}")
    print(f"  精确率: {precision:.4f}")
    print(f"  召回率: {recall:.4f}")
    print(f"  F1分数: {f1:.4f}")
    print(f"  F0.5分数: {f05:.4f}")

    return edit_metrics


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Split数据集评估")
    parser.add_argument('--results_file', type=str, 
                        default='ChineseErrorCorrector/model_test/Split_results/split_test_results_01.json',
                       help='测试结果文件路径')
    parser.add_argument('--output_file', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/split_evaluation_results_01.json',
                       help='评估结果输出路径')
    parser.add_argument('--cherrant_path', type=str,
                       default='ChineseErrorCorrector/model_test/evaluation_tools/ChERRANT',
                       help='ChERRANT工具路径')
    parser.add_argument('--num_processes', type=int, default=4,
                       help='并行处理进程数')
    parser.add_argument('--skip_cherrant', action='store_true',
                       help='跳过ChERRANT评估，只计算简单指标')
    
    args = parser.parse_args()
    
    print("🚀 Split数据集评估开始")
    print("=" * 50)
    print(f"结果文件: {args.results_file}")
    print(f"输出文件: {args.output_file}")
    print(f"ChERRANT路径: {args.cherrant_path}")
    print(f"进程数: {args.num_processes}")
    print(f"跳过ChERRANT: {args.skip_cherrant}")
    print("=" * 50)
    
    try:
        # 加载测试结果
        results = load_test_results(args.results_file)
        
        # 计算简单指标
        simple_metrics = calculate_simple_metrics(results)

        # 计算编辑距离指标
        edit_distance_metrics = calculate_edit_distance_metrics(results)

        # ChERRANT评估
        cherrant_metrics = None
        if not args.skip_cherrant:
            if os.path.exists(args.cherrant_path):
                cherrant_metrics = evaluate_with_cherrant(results, args.cherrant_path, args.num_processes)

                # 如果ChERRANT返回全0结果，给出警告
                if cherrant_metrics and all(cherrant_metrics.get(k, 0) == 0 for k in ['tp', 'fp', 'fn']):
                    print("⚠️ ChERRANT检测到0个编辑操作，这可能表示:")
                    print("   1. 模型预测与原句完全相同（未进行纠错）")
                    print("   2. ChERRANT无法正确处理这种类型的数据")
                    print("   3. 建议查看编辑距离指标作为参考")
            else:
                print(f"⚠️ ChERRANT路径不存在: {args.cherrant_path}")
                print("跳过ChERRANT评估")
        
        # 构建最终评估结果
        evaluation_results = {
            'evaluation_info': {
                'evaluation_time': datetime.now().isoformat(),
                'results_file': args.results_file,
                'cherrant_path': args.cherrant_path,
                'num_processes': args.num_processes,
                'skip_cherrant': args.skip_cherrant
            },
            'simple_metrics': simple_metrics,
            'edit_distance_metrics': edit_distance_metrics,
            'cherrant_metrics': cherrant_metrics
        }
        
        # 保存评估结果
        os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 评估完成!")
        print(f"评估结果已保存到: {args.output_file}")

        # 显示详细评估结果
        print(f"\n" + "="*60)
        print(f"📊 Split数据集评估结果")
        print(f"="*60)

        # 基本统计信息
        print(f"📋 基本统计:")
        print(f"  总样本数: {simple_metrics['total_samples']}")
        print(f"  完全匹配数: {simple_metrics['exact_match_count']}")
        print(f"  完全匹配率: {simple_metrics['exact_match_rate']:.4f}")
        print(f"  未修改样本数: {simple_metrics['no_change_count']}")
        print(f"  已修改样本数: {simple_metrics['modified_count']}")
        print(f"  修改率: {simple_metrics['modification_rate']:.4f}")

        # 按错误类型统计
        print(f"\n📊 按错误类型统计:")
        error_stats = simple_metrics['error_type_statistics']
        for error_type, stats in sorted(error_stats.items(), key=lambda x: x[1]['total'], reverse=True):
            print(f"  {error_type}:")
            print(f"    样本数: {stats['total']}")
            print(f"    正确数: {stats['correct']} (准确率: {stats['accuracy']:.4f})")
            print(f"    修改数: {stats['modified']} (修改率: {stats['modification_rate']:.4f})")

        # 编辑距离指标
        if edit_distance_metrics:
            print(f"\n📏 编辑距离评估指标:")
            print(f"  需要的总编辑数: {edit_distance_metrics['total_edits_needed']}")
            print(f"  实际的总编辑数: {edit_distance_metrics['total_edits_made']}")
            print(f"  正确的编辑数: {edit_distance_metrics['total_correct_edits']:.2f}")
            print(f"  精确率: {edit_distance_metrics['precision']:.4f}")
            print(f"  召回率: {edit_distance_metrics['recall']:.4f}")
            print(f"  F1分数: {edit_distance_metrics['f1']:.4f}")
            print(f"  F0.5分数: {edit_distance_metrics['f0.5']:.4f}")

        # ChERRANT评估结果
        if cherrant_metrics:
            tp = cherrant_metrics.get('tp', 0)
            fp = cherrant_metrics.get('fp', 0)
            fn = cherrant_metrics.get('fn', 0)
            precision = cherrant_metrics.get('precision', 0)
            recall = cherrant_metrics.get('recall', 0)
            f1 = cherrant_metrics.get('f1', 0)
            f05 = cherrant_metrics.get('f0.5', 0)

            print(f"\n🎯 ChERRANT评估指标:")

            # 检查是否为异常情况
            if tp == 0 and fp == 0 and fn == 0:
                print(f"  ⚠️ 异常情况：未检测到任何编辑操作")
                print(f"  精确率 (Precision): {precision:.4f} (无编辑操作)")
                print(f"  召回率 (Recall):    {recall:.4f} (无编辑操作)")
                print(f"  F1分数:            {f1:.4f} (无编辑操作)")
                print(f"  F0.5分数:          {f05:.4f} (无编辑操作)")

                print(f"\n📈 详细统计:")
                print(f"  真正例 (TP): {tp}")
                print(f"  假正例 (FP): {fp}")
                print(f"  假负例 (FN): {fn}")
                print(f"  预测编辑总数: 0")
                print(f"  参考编辑总数: 0")

                print(f"\n💡 说明:")
                print(f"  • 这种情况通常表示模型没有进行任何纠错")
                print(f"  • 或者ChERRANT无法识别数据中的编辑操作")
                print(f"  • 建议参考编辑距离指标进行评估")
            else:
                print(f"  精确率 (Precision): {precision:.4f}")
                print(f"  召回率 (Recall):    {recall:.4f}")
                print(f"  F1分数:            {f1:.4f}")
                print(f"  F0.5分数:          {f05:.4f}")

                print(f"\n📈 详细统计:")
                print(f"  真正例 (TP): {tp}")
                print(f"  假正例 (FP): {fp}")
                print(f"  假负例 (FN): {fn}")

                # 计算总编辑数
                total_predicted_edits = tp + fp
                total_reference_edits = tp + fn
                print(f"  预测编辑总数: {total_predicted_edits}")
                print(f"  参考编辑总数: {total_reference_edits}")
        else:
            print(f"\n⚠️ ChERRANT评估未执行")
            if not args.skip_cherrant:
                print(f"   可能原因: ChERRANT工具不可用或处理失败")

        print(f"="*60)
        
    except Exception as e:
        print(f"\n❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
