#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Split数据集测试脚本
用于测试ChineseErrorCorrector在Split数据集上的表现
"""

import os
import sys
import json
import time
import argparse
import re
from tqdm import tqdm
from datetime import datetime

# 设置环境变量来抑制transformers的警告信息
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.main import ErrorCorrect
from ChineseErrorCorrector.config import TextCorrectConfig
from transformers import set_seed
import asyncio


def clean_prediction(text):
    """清理预测文本中的<think>标签和提示词"""
    if not isinstance(text, str):
        return text

    # 移除<think>...</think>标签及其内容
    cleaned = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

    # 移除提示词模板（如果输出包含完整的提示词）
    if '你是一个专业的中文文本纠错专家' in cleaned:
        # 尝试提取最后一行作为纠错结果
        lines = cleaned.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line and not line.startswith('你是') and not line.startswith('请纠正') and not line.startswith('具体要求') and not line.startswith('-'):
                cleaned = line
                break
        else:
            # 如果没找到合适的行，返回空字符串
            cleaned = ""

    # 移除多余的空白字符
    cleaned = cleaned.strip()
    
    return cleaned


def load_split_data(data_path):
    """加载Split数据集"""
    print(f"加载Split数据集: {data_path}")
    
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"加载了 {len(data)} 条数据")
    
    # 统计错误类型分布
    error_types = {}
    for item in data:
        error_type = item.get('error_type', 'unknown')
        error_types[error_type] = error_types.get(error_type, 0) + 1
    
    print("错误类型分布:")
    for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
        print(f"  {error_type}: {count}")
    
    return data


def test_split_dataset(data_path, output_path, max_samples=None, use_vllm=False, batch_size=16):
    """测试Split数据集"""
    
    # 加载数据
    data = load_split_data(data_path)
    
    # 限制测试样本数
    if max_samples and max_samples < len(data):
        data = data[:max_samples]
        print(f"限制测试样本数为: {max_samples}")
    
    # 初始化模型
    print("初始化模型...")
    corrector = ErrorCorrect()
    
    # 设置随机种子
    set_seed(42)
    
    # 开始测试
    results = []
    start_time = time.time()
    
    print(f"开始测试 {len(data)} 个样本...")
    
    if use_vllm:
        # VLLM批处理
        print(f"使用VLLM批处理，批大小: {batch_size}")
        
        for i in range(0, len(data), batch_size):
            batch_data = data[i:i + batch_size]
            batch_sources = [item['source'] for item in batch_data]
            
            print(f"处理批次 {i//batch_size + 1}/{(len(data) + batch_size - 1)//batch_size}")
            
            try:
                # VLLM推理
                batch_predictions = corrector.vllm_infer(batch_sources)
                
                # 处理结果
                for j, (item, pred_result) in enumerate(zip(batch_data, batch_predictions)):
                    if isinstance(pred_result, dict) and 'target' in pred_result:
                        prediction = pred_result['target']
                    else:
                        prediction = str(pred_result) if pred_result else item['source']
                    
                    # 清理预测结果
                    prediction = clean_prediction(prediction)
                    
                    result = {
                        'id': i + j,
                        'source': item['source'],
                        'target': item['target'][0] if item['target'] else item['source'],
                        'prediction': prediction,
                        'error_type': item.get('error_type', 'unknown'),
                        'metadata': item.get('metadata', {})
                    }
                    results.append(result)
                    
            except Exception as e:
                print(f"批次处理失败: {e}")
                # 添加失败的结果
                for j, item in enumerate(batch_data):
                    result = {
                        'id': i + j,
                        'source': item['source'],
                        'target': item['target'][0] if item['target'] else item['source'],
                        'prediction': item['source'],  # 失败时返回原句
                        'error_type': item.get('error_type', 'unknown'),
                        'metadata': item.get('metadata', {}),
                        'error': str(e)
                    }
                    results.append(result)
    else:
        # HuggingFace逐个处理
        print("使用HuggingFace逐个处理")
        
        for i, item in enumerate(tqdm(data, desc="处理进度")):
            try:
                # HF推理
                hf_results = corrector.hf_infer([item['source']])
                
                if hf_results and len(hf_results) > 0:
                    pred_result = hf_results[0]
                    if isinstance(pred_result, dict) and 'target' in pred_result:
                        prediction = pred_result['target']
                    else:
                        prediction = str(pred_result) if pred_result else item['source']
                else:
                    prediction = item['source']
                
                # 清理预测结果
                prediction = clean_prediction(prediction)
                
                result = {
                    'id': i,
                    'source': item['source'],
                    'target': item['target'][0] if item['target'] else item['source'],
                    'prediction': prediction,
                    'error_type': item.get('error_type', 'unknown'),
                    'metadata': item.get('metadata', {})
                }
                results.append(result)
                
            except Exception as e:
                print(f"处理样本 {i} 失败: {e}")
                result = {
                    'id': i,
                    'source': item['source'],
                    'target': item['target'][0] if item['target'] else item['source'],
                    'prediction': item['source'],  # 失败时返回原句
                    'error_type': item.get('error_type', 'unknown'),
                    'metadata': item.get('metadata', {}),
                    'error': str(e)
                }
                results.append(result)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 计算基本统计
    exact_matches = sum(1 for r in results if r['prediction'] == r['target'])
    exact_match_rate = exact_matches / len(results) if results else 0
    
    # 按错误类型统计
    error_type_stats = {}
    for result in results:
        error_type = result['error_type']
        if error_type not in error_type_stats:
            error_type_stats[error_type] = {'total': 0, 'correct': 0}
        error_type_stats[error_type]['total'] += 1
        if result['prediction'] == result['target']:
            error_type_stats[error_type]['correct'] += 1
    
    # 计算各错误类型的准确率
    for error_type in error_type_stats:
        stats = error_type_stats[error_type]
        stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
    
    # 构建最终结果
    final_result = {
        'test_info': {
            'test_time': datetime.now().isoformat(),
            'data_path': data_path,
            'model_path': TextCorrectConfig.DEFAULT_CKPT_PATH,
            'use_vllm': use_vllm,
            'batch_size': batch_size if use_vllm else 1,
            'max_samples': max_samples,
            'total_samples': len(results),
            'total_time_seconds': total_time,
            'average_time_per_sample': total_time / len(results) if results else 0,
            'throughput_samples_per_second': len(results) / total_time if total_time > 0 else 0
        },
        'metrics': {
            'exact_match_count': exact_matches,
            'exact_match_rate': exact_match_rate,
            'error_type_statistics': error_type_stats
        },
        'results': results
    }
    
    # 保存结果
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试完成!")
    print(f"总样本数: {len(results)}")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"平均耗时: {total_time/len(results):.3f} 秒/样本")
    print(f"处理速度: {len(results)/total_time:.2f} 样本/秒")
    print(f"完全匹配数: {exact_matches}")
    print(f"完全匹配率: {exact_match_rate:.4f}")
    print(f"结果已保存到: {output_path}")
    
    # 显示错误类型统计
    print(f"\n按错误类型统计:")
    for error_type, stats in sorted(error_type_stats.items(), key=lambda x: x[1]['total'], reverse=True):
        print(f"  {error_type}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.4f})")
    
    return final_result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Split数据集测试")
    parser.add_argument('--data_path', type=str, 
                       default='ChineseErrorCorrector/data/split_data/split_errors_data.json',
                       help='Split数据集路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/Split_results/split_test_results_01.json',
                       help='输出结果路径')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='最大测试样本数')
    parser.add_argument('--use_vllm', action='store_true',
                       help='使用VLLM加速')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批处理大小（仅VLLM模式）')
    
    args = parser.parse_args()
    
    print("🚀 Split数据集测试开始")
    print("=" * 50)
    print(f"数据路径: {args.data_path}")
    print(f"输出路径: {args.output_path}")
    print(f"最大样本数: {args.max_samples}")
    print(f"使用VLLM: {args.use_vllm}")
    print(f"批大小: {args.batch_size}")
    print("=" * 50)
    
    try:
        result = test_split_dataset(
            data_path=args.data_path,
            output_path=args.output_path,
            max_samples=args.max_samples,
            use_vllm=args.use_vllm,
            batch_size=args.batch_size
        )
        print("\n🎉 测试成功完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
