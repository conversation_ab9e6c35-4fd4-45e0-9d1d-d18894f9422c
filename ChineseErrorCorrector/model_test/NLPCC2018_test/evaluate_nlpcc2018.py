#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NLPCC2018数据集评估脚本
使用ChERRANT工具进行F0.5评估，支持并行处理
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
import multiprocessing
from datetime import datetime
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


def load_test_results(results_path):
    """加载测试结果"""
    print(f"加载测试结果: {results_path}")
    
    if not os.path.exists(results_path):
        raise FileNotFoundError(f"结果文件不存在: {results_path}")
    
    with open(results_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = data['results']
    print(f"加载了 {len(results)} 条结果")
    
    return results, data.get('metadata', {})


def process_chunk_to_m2(chunk_data):
    """处理数据块生成M2文件的工作函数"""
    chunk_id, chunk_results, cherrant_path, is_reference = chunk_data
    
    try:
        # 确保使用绝对路径
        cherrant_abs_path = os.path.abspath(cherrant_path)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建临时parallel文件
            para_file = os.path.join(temp_dir, f'chunk_{chunk_id}.para')
            
            with open(para_file, 'w', encoding='utf-8') as f:
                for i, result in enumerate(chunk_results):
                    # 清理BOM字符和其他特殊字符
                    source = result['source'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    if is_reference:
                        target = result['ground_truth'].strip().replace('\ufeff', '')
                        target = target.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    else:
                        target = result['prediction'].strip().replace('\ufeff', '').replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')

                    f.write(f"{chunk_id * 1000 + i + 1}\t{source}\t{target}\n")
            
            # 生成M2文件
            m2_file = os.path.join(temp_dir, f'chunk_{chunk_id}.m2')
            parallel_to_m2_script = 'parallel_to_m2.py'  # 使用相对路径

            cmd = [
                'python', parallel_to_m2_script,
                '-f', os.path.abspath(para_file),  # 使用绝对路径
                '-o', os.path.abspath(m2_file),    # 使用绝对路径
                '-g', 'char'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8',
                                  cwd=cherrant_abs_path, timeout=300)
            
            if result.returncode != 0:
                raise RuntimeError(f"Chunk {chunk_id} M2生成失败: {result.stderr}")
            
            # 读取生成的M2内容
            with open(m2_file, 'r', encoding='utf-8') as f:
                m2_content = f.read()
            
            return chunk_id, m2_content
            
    except Exception as e:
        return chunk_id, f"ERROR: {str(e)}"


def parallel_generate_m2_files(results, cherrant_path, num_processes=None):
    """并行生成M2文件"""
    if num_processes is None:
        num_processes = min(multiprocessing.cpu_count(), 8)
    
    print(f"🚀 使用 {num_processes} 个进程并行生成M2文件...")
    
    # 将数据分块
    chunk_size = max(100, len(results) // num_processes)
    chunks = []
    for i in range(0, len(results), chunk_size):
        chunk = results[i:i + chunk_size]
        chunks.append(chunk)
    
    print(f"📊 数据分为 {len(chunks)} 块，每块约 {chunk_size} 个样本")
    
    # 并行处理参考文件和假设文件
    ref_chunks = [(i, chunk, cherrant_path, True) for i, chunk in enumerate(chunks)]
    hyp_chunks = [(i, chunk, cherrant_path, False) for i, chunk in enumerate(chunks)]
    
    ref_m2_parts = {}
    hyp_m2_parts = {}
    
    # 处理参考文件
    print("生成参考M2文件...")
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        future_to_chunk = {executor.submit(process_chunk_to_m2, chunk_data): chunk_data[0] 
                          for chunk_data in ref_chunks}
        
        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                chunk_id, m2_content = future.result()
                if m2_content.startswith("ERROR:"):
                    raise RuntimeError(m2_content)
                ref_m2_parts[chunk_id] = m2_content
                print(f"✅ 参考文件块 {chunk_id} 完成")
            except Exception as e:
                print(f"❌ 参考文件块 {chunk_id} 失败: {e}")
                raise
    
    # 处理假设文件
    print("生成假设M2文件...")
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        future_to_chunk = {executor.submit(process_chunk_to_m2, chunk_data): chunk_data[0] 
                          for chunk_data in hyp_chunks}
        
        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                chunk_id, m2_content = future.result()
                if m2_content.startswith("ERROR:"):
                    raise RuntimeError(m2_content)
                hyp_m2_parts[chunk_id] = m2_content
                print(f"✅ 假设文件块 {chunk_id} 完成")
            except Exception as e:
                print(f"❌ 假设文件块 {chunk_id} 失败: {e}")
                raise
    
    # 合并M2文件
    print("合并M2文件...")
    ref_m2_content = ""
    hyp_m2_content = ""
    
    for i in range(len(chunks)):
        if i in ref_m2_parts:
            ref_m2_content += ref_m2_parts[i]
        if i in hyp_m2_parts:
            hyp_m2_content += hyp_m2_parts[i]
    
    return ref_m2_content, hyp_m2_content


def run_cherrant_evaluation_parallel(results, cherrant_path, num_processes=None):
    """使用并行处理运行ChERRANT评估"""
    print("运行ChERRANT并行评估...")

    # 检查ChERRANT脚本路径
    parallel_to_m2_script = os.path.abspath(os.path.join(cherrant_path, 'parallel_to_m2.py'))
    compare_m2_script = os.path.abspath(os.path.join(cherrant_path, 'compare_m2_for_evaluation.py'))

    print(f"📁 ChERRANT路径: {cherrant_path}")
    print(f"📄 parallel_to_m2脚本: {parallel_to_m2_script}")
    print(f"📄 compare_m2脚本: {compare_m2_script}")

    if not os.path.exists(parallel_to_m2_script):
        raise FileNotFoundError(f"parallel_to_m2.py脚本不存在: {parallel_to_m2_script}")
    if not os.path.exists(compare_m2_script):
        raise FileNotFoundError(f"compare_m2_for_evaluation.py脚本不存在: {compare_m2_script}")

    # 并行生成M2文件
    ref_m2_content, hyp_m2_content = parallel_generate_m2_files(results, cherrant_path, num_processes)

    # 创建临时目录用于存储最终文件
    with tempfile.TemporaryDirectory() as temp_eval_dir:
        # 写入合并后的M2文件
        ref_m2_file = os.path.join(temp_eval_dir, 'reference.m2')
        hyp_m2_file = os.path.join(temp_eval_dir, 'hypothesis.m2')
        
        with open(ref_m2_file, 'w', encoding='utf-8') as f:
            f.write(ref_m2_content)
        
        with open(hyp_m2_file, 'w', encoding='utf-8') as f:
            f.write(hyp_m2_content)

        print(f"📊 参考M2文件大小: {len(ref_m2_content)} 字符")
        print(f"📊 假设M2文件大小: {len(hyp_m2_content)} 字符")

        # 计算评估指标 (F0.5)
        cmd = [
            'python', compare_m2_script,
            '-hyp', hyp_m2_file,
            '-ref', ref_m2_file,
            '-b', '0.5'  # F0.5评估
        ]

        print(f"计算评估指标: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8',
                                  cwd=os.path.abspath(cherrant_path), timeout=600)
        except subprocess.TimeoutExpired:
            print("⚠️ 评估计算超时 (10分钟)")
            raise RuntimeError("ChERRANT评估计算超时")

        if result.returncode != 0:
            print(f"评估计算失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            raise RuntimeError("ChERRANT评估计算失败")

        return result.stdout


def parse_cherrant_output(cherrant_output):
    """解析ChERRANT输出"""
    print("=== 调试ChERRANT输出 ===")
    print(f"原始输出: {repr(cherrant_output)}")

    lines = cherrant_output.strip().split('\n')
    print(f"输出行数: {len(lines)}")

    # 查找标题行和数据行
    header_line = None
    data_line = None

    for i, line in enumerate(lines):
        print(f"行 {i}: {repr(line)}")

        # 查找标题行（包含TP、FP、FN等）
        if line.startswith('TP') and 'FP' in line and 'FN' in line:
            header_line = line
            print(f"找到标题行: {line}")

            # 查找下一行的数据
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and not next_line.startswith('='):
                    data_line = next_line
                    print(f"找到数据行: {data_line}")
                    break

    if header_line and data_line:
        try:
            # 解析数据行
            if '\t' in data_line:
                # 制表符分隔
                parts = data_line.split('\t')
            else:
                # 空格分隔
                parts = data_line.split()

            print(f"数据部分: {parts}")

            if len(parts) >= 6:
                tp = int(parts[0])
                fp = int(parts[1])
                fn = int(parts[2])
                precision = float(parts[3])
                recall = float(parts[4])
                f_score_from_cherrant = float(parts[5])

                # 正确计算F0.5分数
                f0_5_calculated = (1.25 * precision * recall) / (0.25 * precision + recall) if (0.25 * precision + recall) > 0 else 0.0

                metrics = {
                    'tp': tp,
                    'fp': fp,
                    'fn': fn,
                    'precision': precision,
                    'recall': recall,
                    'f0.5': f0_5_calculated,
                    'f1': 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
                }

                print(f"解析ChERRANT结果: TP={tp}, FP={fp}, FN={fn}, Prec={precision:.4f}, Rec={recall:.4f}")
                print(f"F0.5计算: ChERRANT原始={f_score_from_cherrant:.4f}, 重新计算={f0_5_calculated:.4f}")

                return metrics
            else:
                print(f"数据行字段数量不足: {len(parts)}")

        except (ValueError, IndexError) as e:
            print(f"解析数据行失败: {e}")
            print(f"尝试解析的值: {parts}")

    raise ValueError(f"无法解析ChERRANT输出，未找到有效的标题行和数据行")


def calculate_simple_metrics(results):
    """计算简单指标"""
    total = len(results)
    exact_matches = 0
    
    for result in results:
        pred = result['prediction'].strip().replace(' ', '')
        gt = result['ground_truth'].strip().replace(' ', '')
        if pred == gt:
            exact_matches += 1
    
    return {
        'exact_match_accuracy': exact_matches / total if total > 0 else 0,
        'exact_matches': exact_matches,
        'total_samples': total
    }


def print_evaluation_report(results, metadata, metrics, simple_metrics):
    """打印评估报告"""
    print("=" * 60)
    print("NLPCC2018数据集评估报告")
    print("=" * 60)
    
    # 基本信息
    print(f"测试时间: {metadata.get('timestamp', '未知')}")
    print(f"测试样本数: {len(results)}")
    
    model_config = metadata.get('model_config', {})
    print(f"模型配置:")
    print(f"  - 使用VLLM: {model_config.get('use_vllm', '未知')}")
    print(f"  - 模型路径: {model_config.get('model_path', '未知')}")
    print()
    
    # 评估结果
    print("=== 评估结果摘要 ===")
    print(f"精确率 (Precision): {metrics['precision']:.4f}")
    print(f"召回率 (Recall): {metrics['recall']:.4f}")
    print(f"F0.5分数: {metrics['f0.5']:.4f}")
    print(f"F1分数: {metrics['f1']:.4f}")
    print(f"完全匹配率: {simple_metrics['exact_match_accuracy']:.4f}")
    print()
    
    # 与官方基准对比（如果有的话）
    print("=== 与基准对比 ===")
    print(f"当前F0.5分数: {metrics['f0.5']:.4f}")
    print(f"完全匹配准确率: {simple_metrics['exact_match_accuracy']:.4f}")
    print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='NLPCC2018数据集评估')
    parser.add_argument('--results_path', type=str,
                       default='ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_test_original_4B_02.json',
                       help='测试结果文件路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_evaluation_02.json',
                       help='评估报告输出路径')
    parser.add_argument('--cherrant_path', type=str,
                       default='ChineseErrorCorrector/model_test/evaluation_tools/ChERRANT',
                       help='ChERRANT工具路径')
    parser.add_argument('--num_processes', type=int, default=4,
                       help='并行处理进程数 (默认: 4)')
    parser.add_argument('--disable_parallel', action='store_true',
                       help='禁用并行处理，使用单线程评估')
    
    args = parser.parse_args()
    
    print("=== NLPCC2018数据集评估开始 ===")
    print(f"结果文件: {args.results_path}")
    print(f"输出路径: {args.output_path}")
    print(f"ChERRANT路径: {args.cherrant_path}")
    print(f"并行进程数: {args.num_processes}")
    
    try:
        # 1. 加载测试结果
        results, metadata = load_test_results(args.results_path)
        
        # 2. 计算简单指标
        simple_metrics = calculate_simple_metrics(results)
        
        # 3. 尝试使用ChERRANT评估
        cherrant_success = False
        
        try:
            print("尝试使用ChERRANT评估...")

            # 使用并行评估
            if not args.disable_parallel:
                try:
                    print("使用并行评估...")
                    cherrant_output = run_cherrant_evaluation_parallel(results, args.cherrant_path, args.num_processes)
                    metrics = parse_cherrant_output(cherrant_output)
                    print("✅ ChERRANT并行评估完成")
                    print(f"ChERRANT结果: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F0.5={metrics['f0.5']:.4f}")
                    cherrant_success = True
                except Exception as parallel_error:
                    print(f"⚠️ 并行评估失败: {parallel_error}")
                    print("ChERRANT评估失败，使用简单指标")
            else:
                print("并行评估已禁用")

        except Exception as e:
            print(f"⚠️ ChERRANT评估完全失败: {e}")
            
        # 如果ChERRANT评估失败，使用简化方法
        if not cherrant_success:
            print("使用简化指标作为备选...")
            metrics = {
                'precision': simple_metrics['exact_match_accuracy'],
                'recall': simple_metrics['exact_match_accuracy'],
                'f0.5': simple_metrics['exact_match_accuracy'],
                'f1': simple_metrics['exact_match_accuracy'],
                'tp': simple_metrics['exact_matches'],
                'fp': simple_metrics['total_samples'] - simple_metrics['exact_matches'],
                'fn': simple_metrics['total_samples'] - simple_metrics['exact_matches']
            }
        
        # 4. 打印评估报告
        print_evaluation_report(results, metadata, metrics, simple_metrics)
        
        # 5. 保存评估报告
        evaluation_report = {
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata,
            'metrics': metrics,
            'simple_metrics': simple_metrics,
            'cherrant_success': cherrant_success
        }
        
        os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
        with open(args.output_path, 'w', encoding='utf-8') as f:
            json.dump(evaluation_report, f, ensure_ascii=False, indent=2)
        
        print(f"评估报告已保存到: {args.output_path}")
        print("=== 评估完成 ===")
        
    except Exception as e:
        print(f"评估失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
