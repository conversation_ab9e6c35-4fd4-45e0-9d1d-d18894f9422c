#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NLPCC2018数据集测试脚本
使用ChineseErrorCorrector模型对NLPCC2018测试集进行纠错测试
"""

import os
import sys
import json
import argparse
import time
from datetime import datetime
from tqdm import tqdm
import torch

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.main import ErrorCorrect
from ChineseErrorCorrector.config import TextCorrectConfig
from transformers import set_seed


def load_nlpcc2018_data(data_dir):
    """加载NLPCC2018数据"""
    source_file = os.path.join(data_dir, 'source.txt')

    if not os.path.exists(source_file):
        raise FileNotFoundError(f"源文件不存在: {source_file}")

    # 读取源文件，清理BOM字符
    with open(source_file, 'r', encoding='utf-8') as f:
        sources = [line.strip().replace('\ufeff', '') for line in f if line.strip()]

    print(f"✅ 加载NLPCC2018数据: {len(sources)} 条")
    return sources


def parse_gold_file(gold_file):
    """解析gold文件，应用修改操作得到正确的目标句子"""
    targets = []
    current_source = []
    current_edits = []

    with open(gold_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                # 处理当前句子
                if current_source:
                    target = apply_edits_to_sentence(current_source, current_edits)
                    targets.append(target)
                    current_source = []
                    current_edits = []
            elif line.startswith('S '):
                # 句子行，去掉'S '前缀，获取分词后的源句子
                sentence = line[2:].strip()
                current_source = sentence.split()
            elif line.startswith('A '):
                # 修改行，解析编辑操作
                parts = line.split('|||')
                if len(parts) >= 6:
                    # 解析位置信息: A start end|||type|||replacement|||REQUIRED|||-NONE-|||edit_id
                    pos_info = parts[0].split()[1:]  # 去掉'A'
                    if len(pos_info) >= 2:
                        start_pos = int(pos_info[0])
                        end_pos = int(pos_info[1])
                        edit_type = parts[1]
                        replacement = parts[2]
                        edit_id = int(parts[5]) if len(parts) > 5 and parts[5].isdigit() else 0

                        current_edits.append({
                            'start': start_pos,
                            'end': end_pos,
                            'type': edit_type,
                            'replacement': replacement,
                            'edit_id': edit_id
                        })

    # 处理最后一个句子
    if current_source:
        target = apply_edits_to_sentence(current_source, current_edits)
        targets.append(target)

    return targets


def apply_edits_to_sentence(source_tokens, edits):
    """将编辑操作应用到源句子上"""
    if not edits:
        return ''.join(source_tokens)

    # 按edit_id分组，选择第一个edit_id的编辑（通常是主要的修改方案）
    edit_groups = {}
    for edit in edits:
        edit_id = edit.get('edit_id', 0)
        if edit_id not in edit_groups:
            edit_groups[edit_id] = []
        edit_groups[edit_id].append(edit)

    # 选择最优的编辑组：优先选择edit_id最大的（通常是更好的修改方案）
    selected_edit_id = max(edit_groups.keys())
    selected_edits = edit_groups[selected_edit_id]

    # 按位置倒序排序，从后往前应用编辑，避免位置偏移
    selected_edits = sorted(selected_edits, key=lambda x: -x['start'])

    result_tokens = source_tokens.copy()

    for edit in selected_edits:
        start = edit['start']
        end = edit['end']
        edit_type = edit['type']
        replacement = edit['replacement']

        # 确保索引在有效范围内
        if start < 0 or start > len(result_tokens):
            continue
        if end < start or end > len(result_tokens):
            end = len(result_tokens)

        if edit_type == 'S':  # 替换 (Substitution)
            if replacement != '-NONE-':
                # 替换指定范围的词
                replacement_tokens = replacement.split()
                result_tokens[start:end] = replacement_tokens
            else:
                # 删除指定范围的词
                del result_tokens[start:end]
        elif edit_type == 'M':  # 缺失，插入 (Missing)
            if replacement != '-NONE-':
                # 在指定位置插入词
                insert_tokens = replacement.split()
                result_tokens[start:start] = insert_tokens
        elif edit_type == 'W':  # 词序错误，替换 (Word order)
            if replacement != '-NONE-':
                replacement_tokens = replacement.split()
                result_tokens[start:end] = replacement_tokens
            else:
                del result_tokens[start:end]
        elif edit_type == 'R':  # 冗余，删除 (Redundant)
            del result_tokens[start:end]

    # 将分词结果连接成正常句子（去掉空格）
    return ''.join(result_tokens)


def load_nlpcc2018_targets(data_dir, gold_file='gold.01'):
    """加载NLPCC2018目标数据"""
    gold_path = os.path.join(data_dir, 'gold', gold_file)
    
    if not os.path.exists(gold_path):
        raise FileNotFoundError(f"目标文件不存在: {gold_path}")
    
    targets = parse_gold_file(gold_path)
    print(f"✅ 加载NLPCC2018目标数据: {len(targets)} 条")
    return targets


def clean_prediction(text):
    """清理预测结果"""
    if not text:
        return ""
    
    # 移除多余的空格和换行
    text = ' '.join(text.split())
    
    # 移除可能的特殊标记
    text = text.replace('<unk>', '').replace('<pad>', '').replace('<s>', '').replace('</s>', '')
    
    return text.strip()


def run_nlpcc2018_test(data_dir, output_path, max_samples=None, batch_size=8, gold_file='gold.01'):
    """运行NLPCC2018测试"""
    print("🚀 开始NLPCC2018数据集测试")
    print("=" * 60)
    
    # 1. 加载数据
    print("📂 加载测试数据...")
    sources = load_nlpcc2018_data(data_dir)
    targets = load_nlpcc2018_targets(data_dir, gold_file)
    
    # 检查数据一致性
    if len(sources) != len(targets):
        print(f"⚠️ 数据长度不一致: 源文件{len(sources)}条, 目标文件{len(targets)}条")
        min_len = min(len(sources), len(targets))
        sources = sources[:min_len]
        targets = targets[:min_len]
        print(f"📊 使用前{min_len}条数据")
    
    # 限制样本数量
    if max_samples and max_samples < len(sources):
        sources = sources[:max_samples]
        targets = targets[:max_samples]
        print(f"📊 限制测试样本数: {max_samples}")
    
    test_sentences = sources
    ground_truths = targets
    
    print(f"📊 测试配置:")
    print(f"  - 样本数量: {len(test_sentences)}")
    print(f"  - 批处理大小: {batch_size}")
    print(f"  - 使用VLLM: {TextCorrectConfig.USE_VLLM}")
    print(f"  - 模型路径: {TextCorrectConfig.DEFAULT_CKPT_PATH}")
    
    # 2. 初始化纠错器
    print("🔧 初始化模型...")
    try:
        corrector = ErrorCorrect()
        print("✓ 模型初始化成功")
    except Exception as e:
        print(f"✗ 模型初始化失败: {e}")
        raise

    # GPU优化设置
    if torch.cuda.is_available():
        print("🚀 GPU优化设置...")
        torch.backends.cudnn.benchmark = True
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        
        # 预分配GPU内存
        total_memory = torch.cuda.get_device_properties(0).total_memory
        torch.cuda.set_per_process_memory_fraction(0.95)

        allocated = torch.cuda.memory_allocated(0) / 1024**3
        reserved = torch.cuda.memory_reserved(0) / 1024**3
        print(f"📊 GPU内存使用: {allocated:.2f}GB 已分配, {reserved:.2f}GB 已保留")

    # 3. 批量推理
    predictions = []
    total_batches = (len(test_sentences) + batch_size - 1) // batch_size
    start_time = time.time()

    print("🔄 开始批量推理...")
    with tqdm(total=len(test_sentences), desc="推理进度", unit="句", leave=True,
              dynamic_ncols=False, ascii=True) as pbar:
        for i in range(0, len(test_sentences), batch_size):
            batch = test_sentences[i:i + batch_size]

            try:
                # 执行批量推理
                if TextCorrectConfig.USE_VLLM:
                    batch_results = corrector.vllm_infer(batch)
                else:
                    batch_results = corrector.hf_infer(batch)

                # 提取纠错后的句子并清理输出
                for result in batch_results:
                    if isinstance(result, dict):
                        if 'target' in result:
                            cleaned_target = clean_prediction(result['target'])
                        elif 'corrected_text' in result:
                            cleaned_target = clean_prediction(result['corrected_text'])
                        elif 'prediction' in result:
                            cleaned_target = clean_prediction(result['prediction'])
                        else:
                            cleaned_target = clean_prediction(result.get('source', ''))
                        predictions.append(cleaned_target)
                    else:
                        cleaned_result = clean_prediction(str(result))
                        predictions.append(cleaned_result)

                pbar.update(len(batch))

            except Exception as e:
                print(f"批次 {i//batch_size + 1} 推理失败: {e}")
                predictions.extend([''] * len(batch))
                pbar.update(len(batch))

    end_time = time.time()
    total_time = end_time - start_time

    print(f"✅ 推理完成!")
    print(f"📊 总耗时: {total_time:.2f}秒")
    print(f"📊 平均速度: {len(test_sentences)/total_time:.2f}句/秒")

    # 4. 保存结果
    print("💾 保存测试结果...")
    
    # 构建结果数据
    results = []
    for i, (source, prediction, ground_truth) in enumerate(zip(test_sentences, predictions, ground_truths)):
        results.append({
            'id': i,
            'source': source,
            'prediction': prediction,
            'ground_truth': ground_truth
        })

    # 构建元数据
    metadata = {
        'timestamp': datetime.now().isoformat(),
        'total_samples': len(test_sentences),
        'batch_size': batch_size,
        'total_time_seconds': total_time,
        'avg_speed_sentences_per_second': len(test_sentences) / total_time,
        'model_config': {
            'use_vllm': TextCorrectConfig.USE_VLLM,
            'model_path': TextCorrectConfig.DEFAULT_CKPT_PATH,
            'max_length': TextCorrectConfig.MAX_LENGTH
        },
        'data_config': {
            'data_dir': data_dir,
            'gold_file': gold_file,
            'max_samples': max_samples
        }
    }

    # 保存到JSON文件
    output_data = {
        'metadata': metadata,
        'results': results
    }

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    print(f"✅ 结果已保存到: {output_path}")
    
    # 5. 简单统计
    exact_matches = sum(1 for pred, gt in zip(predictions, ground_truths) 
                       if pred.replace(' ', '') == gt.replace(' ', ''))
    accuracy = exact_matches / len(predictions) if predictions else 0

    print(f"📊 简单统计:")
    print(f"  - 完全匹配: {exact_matches}/{len(predictions)} ({accuracy:.2%})")
    
    return output_path


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='NLPCC2018数据集测试')
    parser.add_argument('--data_dir', type=str,
                       default='ChineseErrorCorrector/data/NLPCC2018_data',
                       help='NLPCC2018数据目录')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_test_original_4B_02.json',
                       help='输出文件路径')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='最大测试样本数（用于快速测试）')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='批处理大小')
    parser.add_argument('--gold_file', type=str, default='gold.01',
                       help='使用的gold文件名')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')

    args = parser.parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    print(f"🎯 NLPCC2018测试配置:")
    print(f"  - 数据目录: {args.data_dir}")
    print(f"  - 输出路径: {args.output_path}")
    print(f"  - 最大样本数: {args.max_samples}")
    print(f"  - 批处理大小: {args.batch_size}")
    print(f"  - Gold文件: {args.gold_file}")
    print(f"  - 随机种子: {args.seed}")
    
    try:
        output_path = run_nlpcc2018_test(
            data_dir=args.data_dir,
            output_path=args.output_path,
            max_samples=args.max_samples,
            batch_size=args.batch_size,
            gold_file=args.gold_file
        )
        
        print("🎉 NLPCC2018测试完成!")
        print(f"📁 结果文件: {output_path}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
