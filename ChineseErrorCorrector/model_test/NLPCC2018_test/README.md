# NLPCC2018测试工具

本目录包含用于NLPCC2018数据集的测试和评估工具，使用与NaCGEC相同的评估框架。

## 📁 文件结构

```
NLPCC2018_test/
├── test_nlpcc2018.py          # NLPCC2018测试脚本
├── evaluate_nlpcc2018.py      # NLPCC2018评估脚本
└── README.md                  # 使用说明

../NLPCC2018_results/          # 测试结果目录
├── nlpcc2018_test_original_4B_01.json      # 测试结果
└── nlpcc2018_evaluation_01.json            # 评估报告
```

## 🚀 使用方法

### 1. 运行NLPCC2018测试

#### 基本测试
```bash
# 完整测试
python ChineseErrorCorrector/model_test/NLPCC2018_test/test_nlpcc2018.py

# 小样本测试（快速验证）
python ChineseErrorCorrector/model_test/NLPCC2018_test/test_nlpcc2018.py --max_samples 100

# 自定义配置
python ChineseErrorCorrector/model_test/NLPCC2018_test/test_nlpcc2018.py \
    --data_dir ChineseErrorCorrector/data/NLPCC2018_data \
    --output_path ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_test_original_4B_01.json \
    --batch_size 16 \
    --gold_file gold.01
```

#### 参数说明
- `--data_dir`: NLPCC2018数据目录（默认: `ChineseErrorCorrector/data/NLPCC2018_data`）
- `--output_path`: 输出文件路径
- `--max_samples`: 最大测试样本数（用于快速测试）
- `--batch_size`: 批处理大小（默认: 8）
- `--gold_file`: 使用的gold文件名（默认: gold.01）
- `--seed`: 随机种子（默认: 42）

### 2. 运行NLPCC2018评估

#### 基本评估
```bash
# 使用默认配置评估
python ChineseErrorCorrector/model_test/NLPCC2018_test/evaluate_nlpcc2018.py

# 自定义配置评估
python ChineseErrorCorrector/model_test/NLPCC2018_test/evaluate_nlpcc2018.py \
    --results_path ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_test_original_4B_01.json \
    --output_path ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_evaluation_01.json \
    --num_processes 4
```

#### 并行评估选项
```bash
# 使用8个进程并行评估（推荐）
python evaluate_nlpcc2018.py --num_processes 8

# 禁用并行评估（如果遇到问题）
python evaluate_nlpcc2018.py --disable_parallel

# 使用2个进程（适合内存较小的系统）
python evaluate_nlpcc2018.py --num_processes 2
```

## 📊 数据格式

### NLPCC2018数据结构
```
NLPCC2018_data/
├── source.txt          # 源文件（每行一个句子）
└── gold/
    ├── gold.01         # 标准答案文件1
    ├── gold.02         # 标准答案文件2（如果有）
    └── ...
```

### 测试结果格式
```json
{
  "metadata": {
    "timestamp": "2024-01-01T12:00:00",
    "total_samples": 1000,
    "batch_size": 8,
    "model_config": {
      "use_vllm": false,
      "model_path": "...",
      "max_length": 512
    }
  },
  "results": [
    {
      "id": 0,
      "source": "原始句子",
      "prediction": "模型预测",
      "ground_truth": "标准答案"
    }
  ]
}
```

### 评估报告格式
```json
{
  "timestamp": "2024-01-01T12:00:00",
  "metrics": {
    "precision": 0.8322,
    "recall": 0.8084,
    "f0.5": 0.8274,
    "f1": 0.8202
  },
  "simple_metrics": {
    "exact_match_accuracy": 0.7277,
    "exact_matches": 727,
    "total_samples": 1000
  }
}
```

## 🔧 技术特性

### 1. 并行处理
- 支持多进程并行生成M2文件
- 显著提升ChERRANT评估速度
- 自动错误恢复和回退机制

### 2. 灵活配置
- 支持不同的gold文件
- 可配置批处理大小和进程数
- 支持小样本快速测试

### 3. 兼容性
- 与NaCGEC评估工具共享核心代码
- 支持相同的ChERRANT评估标准
- 结果格式统一，便于对比

## 📈 性能优化

### 推荐配置
```bash
# 对于大多数系统的最佳配置
python test_nlpcc2018.py --batch_size 16
python evaluate_nlpcc2018.py --num_processes 8
```

### 内存优化
```bash
# 内存受限的系统
python test_nlpcc2018.py --batch_size 4
python evaluate_nlpcc2018.py --num_processes 2
```

### 快速测试
```bash
# 快速验证（100个样本）
python test_nlpcc2018.py --max_samples 100 --batch_size 8
python evaluate_nlpcc2018.py --num_processes 2
```

## 🎯 评估指标

### 主要指标
- **F0.5分数**: 偏向精确率的F分数（ChERRANT标准）
- **精确率**: 正确修改的比例
- **召回率**: 发现错误的比例
- **完全匹配率**: 句子级别的准确率

### 指标解释
- **F0.5 > 0.8**: 优秀性能
- **F0.5 > 0.7**: 良好性能
- **F0.5 > 0.6**: 可接受性能
- **完全匹配率**: 更直观的性能指标

## ⚠️ 注意事项

### 系统要求
- Python 3.7+
- 至少8GB RAM（用于并行处理）
- 足够的磁盘空间（临时文件）

### 常见问题
1. **ChERRANT评估失败**: 会自动回退到简单评估
2. **内存不足**: 减少进程数或批处理大小
3. **权限问题**: 确保有临时目录写入权限

### 故障排除
```bash
# 如果并行评估失败
python evaluate_nlpcc2018.py --disable_parallel

# 如果内存不足
python evaluate_nlpcc2018.py --num_processes 2

# 查看详细错误信息
python test_nlpcc2018.py --max_samples 10
```

## 📝 示例工作流

```bash
# 1. 运行完整测试
python ChineseErrorCorrector/model_test/NLPCC2018_test/test_nlpcc2018.py \
    --batch_size 16

# 2. 运行并行评估
python ChineseErrorCorrector/model_test/NLPCC2018_test/evaluate_nlpcc2018.py \
    --num_processes 8

# 3. 查看结果
cat ChineseErrorCorrector/model_test/NLPCC2018_results/nlpcc2018_evaluation_01.json
```

这套工具提供了完整的NLPCC2018测试和评估流程，与NaCGEC工具保持一致的评估标准。
