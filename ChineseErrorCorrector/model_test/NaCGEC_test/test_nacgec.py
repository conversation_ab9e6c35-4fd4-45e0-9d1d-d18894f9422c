#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaCGEC数据集测试脚本
用于测试ChineseErrorCorrector在NaCGEC数据集上的表现
"""

import os
import sys
import json
import time
import argparse
import re
from tqdm import tqdm
from datetime import datetime

# 设置环境变量来抑制transformers的警告信息
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.main import ErrorCorrect
from ChineseErrorCorrector.config import TextCorrectConfig
from transformers import set_seed
import asyncio


def clean_prediction(text):
    """清理预测文本中的<think>标签和提示词"""
    if not isinstance(text, str):
        return text

    # 移除<think>...</think>标签及其内容
    cleaned = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

    # 移除提示词模板（如果输出包含完整的提示词）
    if '你是一个专业的中文文本纠错专家' in cleaned:
        # 尝试提取最后一行作为纠错结果
        lines = cleaned.split('\n')
        for line in reversed(lines):
            line = line.strip()
            if line and not line.startswith('你是') and not line.startswith('请纠正') and not line.startswith('具体要求') and not line.startswith('-'):
                cleaned = line
                break
        else:
            # 如果没找到合适的行，返回空字符串
            cleaned = ""

    # 清理多余的空白字符
    cleaned = re.sub(r'\n+', '\n', cleaned)
    cleaned = cleaned.strip()

    # 如果结果为空或过长，返回原始输入（表示纠错失败）
    if not cleaned or len(cleaned) > 200:
        return ""

    return cleaned


def load_nacgec_data(data_path):
    """加载NaCGEC测试数据"""
    print(f"加载数据: {data_path}")
    
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    if os.path.getsize(data_path) == 0:
        raise ValueError(f"数据文件为空: {data_path}")
    
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"成功加载 {len(data)} 条数据")
    return data


def prepare_test_data(data, max_samples=None):
    """准备测试数据"""
    test_sentences = []
    ground_truth = []
    error_types = []

    for i, item in enumerate(data):
        if max_samples and i >= max_samples:
            break

        # 根据数据格式提取源句子和目标句子
        if isinstance(item, dict):
            if 'source' in item and 'target' in item:
                test_sentences.append(item['source'])
                # 处理target可能是列表的情况，取第一个作为主要参考答案
                if isinstance(item['target'], list):
                    ground_truth.append(item['target'][0] if item['target'] else item['source'])
                    # 保存所有可能的正确答案用于后续评估
                    item['all_targets'] = item['target']
                else:
                    ground_truth.append(item['target'])
                    item['all_targets'] = [item['target']]

                # 保存错误类型信息
                error_types.append(item.get('error_type', 'unknown'))
            elif 'input' in item and 'output' in item:
                test_sentences.append(item['input'])
                ground_truth.append(item['output'])
                error_types.append(item.get('error_type', 'unknown'))
            elif 'original' in item and 'corrected' in item:
                test_sentences.append(item['original'])
                ground_truth.append(item['corrected'])
                error_types.append(item.get('error_type', 'unknown'))
            else:
                print(f"警告: 第{i}条数据格式不识别: {list(item.keys())}")
                continue
        else:
            print(f"警告: 第{i}条数据不是字典格式")
            continue

    print(f"准备了 {len(test_sentences)} 条测试数据")

    # 统计错误类型分布
    from collections import Counter
    error_type_counts = Counter(error_types)
    print("错误类型分布:")
    for error_type, count in error_type_counts.most_common():
        print(f"  {error_type}: {count}")

    return test_sentences, ground_truth, error_types


def run_inference(test_sentences, use_vllm=False, batch_size=8):
    """运行推理"""
    print(f"开始推理，使用{'VLLM' if use_vllm else 'HF'}模式")
    print(f"批次大小: {batch_size}")

    # 设置随机种子
    set_seed(42)

    # 初始化纠错器
    print("正在初始化模型...")
    try:
        corrector = ErrorCorrect()
        print("✓ 模型初始化成功")
    except Exception as e:
        print(f"✗ 模型初始化失败: {e}")
        raise

    # GPU全性能优化设置
    import torch
    if torch.cuda.is_available():
        # 清理GPU缓存
        torch.cuda.empty_cache()

        # GPU性能优化设置
        torch.backends.cudnn.benchmark = True      # 启用cuDNN自动调优
        torch.backends.cudnn.deterministic = False # 允许非确定性算法以提升性能
        torch.backends.cuda.matmul.allow_tf32 = True    # 启用TF32加速
        torch.backends.cudnn.allow_tf32 = True           # 启用TF32加速

        # 预分配GPU内存
        total_memory = torch.cuda.get_device_properties(0).total_memory
        torch.cuda.set_per_process_memory_fraction(0.95)  # 使用95%GPU内存

        allocated = torch.cuda.memory_allocated(0) / 1024**3
        reserved = torch.cuda.memory_reserved(0) / 1024**3
        print(f"📊 GPU内存使用: {allocated:.2f}GB 已分配, {reserved:.2f}GB 已保留")
        print(f"🚀 GPU性能优化: cuDNN自动调优已启用, TF32加速已启用")

    predictions = []
    total_batches = (len(test_sentences) + batch_size - 1) // batch_size

    start_time = time.time()

    # 创建单一进度条，禁用动态刷新
    with tqdm(total=len(test_sentences), desc="推理进度", unit="句", leave=True,
              dynamic_ncols=False, ascii=True) as pbar:
        for i in range(0, len(test_sentences), batch_size):
            batch = test_sentences[i:i + batch_size]

            try:
                # 执行批量推理
                if TextCorrectConfig.USE_VLLM:
                    # 使用VLLM推理
                    batch_results = corrector.vllm_infer(batch)
                else:
                    # 使用HuggingFace推理
                    batch_results = corrector.hf_infer(batch)

                # 提取纠错后的句子并清理输出
                for result in batch_results:
                    if isinstance(result, dict):
                        # 从结果中提取纠错文本
                        if 'target' in result:
                            cleaned_target = clean_prediction(result['target'])
                        elif 'corrected_text' in result:
                            cleaned_target = clean_prediction(result['corrected_text'])
                        elif 'prediction' in result:
                            cleaned_target = clean_prediction(result['prediction'])
                        else:
                            # 如果没有找到预期的键，使用原文
                            cleaned_target = clean_prediction(result.get('source', ''))
                        predictions.append(cleaned_target)
                    else:
                        cleaned_result = clean_prediction(str(result))
                        predictions.append(cleaned_result)

                # 更新进度条
                pbar.update(len(batch))

            except Exception as e:
                print(f"批次 {i//batch_size + 1} 推理失败: {e}")
                # 添加空预测以保持索引一致
                predictions.extend([''] * len(batch))
                # 更新进度条
                pbar.update(len(batch))

    end_time = time.time()
    total_time = end_time - start_time

    print(f"推理完成，耗时: {total_time:.2f}秒")
    print(f"平均每句耗时: {total_time/len(test_sentences):.3f}秒")

    # 显示最终GPU内存使用情况
    if torch.cuda.is_available():
        final_allocated = torch.cuda.memory_allocated(0) / 1024**3
        final_reserved = torch.cuda.memory_reserved(0) / 1024**3
        max_allocated = torch.cuda.max_memory_allocated(0) / 1024**3
        print(f"📊 最终GPU内存: {final_allocated:.2f}GB 已分配, {final_reserved:.2f}GB 已保留")
        print(f"📈 峰值GPU内存: {max_allocated:.2f}GB")

        # 计算GPU利用率
        throughput = len(test_sentences) / total_time
        print(f"🚀 GPU吞吐量: {throughput:.2f}句/秒")

    return predictions


def save_results(test_sentences, ground_truth, predictions, error_types, output_path):
    """保存测试结果"""
    results = {
        'metadata': {
            'timestamp': datetime.now().isoformat(),
            'total_samples': len(test_sentences),
            'model_config': {
                'use_vllm': TextCorrectConfig.USE_VLLM,
                'model_path': TextCorrectConfig.DEFAULT_CKPT_PATH,
                'max_length': TextCorrectConfig.MAX_LENGTH
            }
        },
        'results': []
    }

    for i, (source, target, pred, error_type) in enumerate(zip(test_sentences, ground_truth, predictions, error_types)):
        results['results'].append({
            'id': i,
            'source': source,
            'ground_truth': target,
            'prediction': pred,
            'error_type': error_type
        })

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"结果已保存到: {output_path}")


def main():
    parser = argparse.ArgumentParser(description='NaCGEC数据集测试')
    parser.add_argument('--data_path', type=str,
                       default='ChineseErrorCorrector/data/paper_data/nacgec_all_type.json',
                       help='NaCGEC测试数据路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_original_4B_03.json',
                       help='结果输出路径')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='最大测试样本数（用于快速测试）')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批次大小（GPU全性能优化：建议32-64）')
    parser.add_argument('--use_vllm', default=False, action='store_true',
                       help='使用VLLM进行推理')

    args = parser.parse_args()

    # 创建输出目录
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    print("=== NaCGEC数据集测试开始 ===")
    print(f"数据路径: {args.data_path}")
    print(f"输出路径: {args.output_path}")
    print(f"最大样本数: {args.max_samples}")
    print(f"批次大小: {args.batch_size}")
    print(f"使用VLLM: {args.use_vllm}")

    # 显示GPU信息
    import torch
    if torch.cuda.is_available():
        print(f"🚀 GPU加速: {torch.cuda.get_device_name(0)}")
        print(f"📊 GPU内存: {torch.cuda.get_device_properties(0).total_memory // 1024**3}GB")
        print(f"⚡ CUDA版本: {torch.version.cuda}")
    else:
        print("⚠️ GPU不可用，将使用CPU")
    
    try:
        # 1. 加载数据
        data = load_nacgec_data(args.data_path)
        
        # 2. 准备测试数据
        test_sentences, ground_truth, error_types = prepare_test_data(data, args.max_samples)

        if len(test_sentences) == 0:
            raise ValueError("没有有效的测试数据")

        # 3. 运行推理
        predictions = run_inference(test_sentences, args.use_vllm, args.batch_size)

        # 4. 保存结果
        save_results(test_sentences, ground_truth, predictions, error_types, args.output_path)
        
        print("=== 测试完成 ===")
        print(f"测试样本数: {len(test_sentences)}")
        print(f"结果文件: {args.output_path}")
        print("接下来可以运行评估脚本: python evaluate_nacgec.py")
        
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
