#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaCGEC Comparison Test: Original vs Enhanced Prompt Strategies
Tests the performance difference between original model and enhanced prompt strategies on full NaCGEC dataset
All output in English to prevent encoding issues
"""

import os
import sys
import json
import time
import argparse
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any
from tqdm import tqdm

# Set environment variables
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'
os.environ['VLLM_DISABLE'] = '1'

# Add project path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)


class NaCGECComparisonTester:
    """NaCGEC Comparison Tester for Original vs Enhanced Prompt Strategies"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = None
        
        # Define prompt strategies
        self.prompt_strategies = {
            'original': self._create_original_prompt,
            'constraint_based': self._create_constraint_based_prompt
        }
    
    def _create_original_prompt(self, text: str) -> str:
        """Original simple prompt used in the baseline model"""
        return f"纠正句子：{text}\n纠正："
    
    def _create_constraint_based_prompt(self, text: str) -> str:
        """Best performing constraint-based prompt strategy from previous tests"""
        return f"""纠正句子中的错误，遵循以下约束：

1. 只修改确实存在的错误，不改变正确的内容
2. 确保修改后的句子语义通顺
3. 保持原句的语言风格和表达习惯
4. 如果不确定是否有错误，保持原样

原句：{text}
纠正："""
    
    def load_model(self):
        """Load the NaCGEC model"""
        print("Loading NaCGEC Model...")
        
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM, set_seed
            import torch
            
            set_seed(42)
            model_path = "/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B"
            
            print(f"Loading model from path: {model_path}")
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            model = AutoModelForCausalLM.from_pretrained(model_path)
            
            device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            model = model.to(device)
            model.eval()
            
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            self.model = model
            self.tokenizer = tokenizer
            self.device = device
            
            print(f"Model loaded successfully, device: {device}")
            return True
            
        except Exception as e:
            print(f"Model loading failed: {e}")
            return False
    
    def inference_with_prompt(self, text: str, prompt_strategy: str) -> str:
        """Perform inference with specified prompt strategy"""
        if self.model is None or self.tokenizer is None:
            return text
        
        try:
            import torch
            
            # Generate prompt
            prompt = self.prompt_strategies[prompt_strategy](text)
            
            # Encode
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=512
            ).to(self.device)
            
            # Inference
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=100,
                    num_beams=1,
                    do_sample=False,
                    pad_token_id=self.tokenizer.pad_token_id,
                    temperature=0.1,
                    repetition_penalty=1.1
                )
            
            # Decode
            full_output = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract generated part
            if full_output.startswith(prompt):
                generated = full_output[len(prompt):].strip()
            else:
                generated = full_output.strip()
            
            # Clean output
            generated = self._clean_output(generated, text)
            
            return generated
            
        except Exception as e:
            print(f"Inference failed: {e}")
            return text
    
    def _clean_output(self, output: str, original: str) -> str:
        """Clean model output"""
        if not output:
            return original
        
        # Remove common prompt residuals
        patterns_to_remove = [
            r'纠正后[：:]?',
            r'纠正[：:]?',
            r'答案[：:]?',
            r'结果[：:]?',
            r'最终纠正结果[：:]?'
        ]
        
        for pattern in patterns_to_remove:
            output = re.sub(pattern, '', output, flags=re.IGNORECASE)
        
        # Take only the first valid line
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) > 5:  # Filter too short lines
                output = line
                break
        
        output = output.strip()
        
        # Remove repetitive content
        if len(output) > 10 and output.count(output[:10]) > 1:  # Detect repetition
            parts = output.split(output[:10])
            output = output[:10] + parts[1] if len(parts) > 1 else output[:10]
        
        # If output is empty or same as original, return original
        if not output or output == original:
            return original
        
        return output
    
    def calculate_edit_distance(self, s1: str, s2: str) -> int:
        """Calculate edit distance"""
        if len(s1) < len(s2):
            return self.calculate_edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def evaluate_prediction(self, source: str, targets: List[str], prediction: str) -> Dict[str, Any]:
        """Evaluate prediction results against multiple possible targets"""
        # Check exact match with any target
        is_exact_match = prediction in targets
        is_modified = prediction != source
        
        # Find the best target (minimum edit distance)
        best_target = targets[0]
        min_edit_distance = float('inf')
        
        for target in targets:
            edit_distance = self.calculate_edit_distance(prediction, target)
            if edit_distance < min_edit_distance:
                min_edit_distance = edit_distance
                best_target = target
        
        # Calculate metrics using best target
        edit_distance_to_source = self.calculate_edit_distance(prediction, source)
        source_to_target_distance = self.calculate_edit_distance(source, best_target)
        improvement = source_to_target_distance - min_edit_distance
        improvement_rate = improvement / source_to_target_distance if source_to_target_distance > 0 else 0
        
        return {
            'is_exact_match': is_exact_match,
            'is_modified': is_modified,
            'edit_distance_to_best_target': min_edit_distance,
            'edit_distance_to_source': edit_distance_to_source,
            'improvement': improvement,
            'improvement_rate': improvement_rate,
            'best_target': best_target,
            'all_targets': targets
        }
    
    def test_strategy_batch(self, test_data: List[Dict], strategy_name: str, 
                           batch_size: int = 32) -> Dict[str, Any]:
        """Test a specific prompt strategy with batch processing"""
        print(f"\nTesting Strategy: {strategy_name}")
        print("-" * 50)
        
        strategy_results = []
        start_time = time.time()
        
        # Process in batches for efficiency
        total_batches = (len(test_data) + batch_size - 1) // batch_size
        
        with tqdm(total=len(test_data), desc=f"Processing {strategy_name}") as pbar:
            for batch_idx in range(total_batches):
                batch_start = batch_idx * batch_size
                batch_end = min(batch_start + batch_size, len(test_data))
                batch_data = test_data[batch_start:batch_end]
                
                # Process batch
                for i, item in enumerate(batch_data):
                    source = item['source']
                    targets = item['target'] if isinstance(item['target'], list) else [item['target']]
                    
                    # Perform inference with current strategy
                    prediction = self.inference_with_prompt(source, strategy_name)
                    
                    # Evaluate prediction results
                    evaluation = self.evaluate_prediction(source, targets, prediction)
                    
                    result = {
                        'id': batch_start + i,
                        'source': source,
                        'targets': targets,
                        'prediction': prediction,
                        'error_type': item.get('error_type', 'unknown'),
                        **evaluation
                    }
                    
                    strategy_results.append(result)
                    pbar.update(1)
                
                # Show progress every 10 batches
                if (batch_idx + 1) % 10 == 0:
                    current_accuracy = sum(1 for r in strategy_results if r['is_exact_match']) / len(strategy_results)
                    print(f"  Batch {batch_idx + 1}/{total_batches} - Current Accuracy: {current_accuracy:.4f}")
        
        end_time = time.time()
        
        # Calculate statistics
        total_samples = len(strategy_results)
        exact_matches = sum(1 for r in strategy_results if r['is_exact_match'])
        modifications = sum(1 for r in strategy_results if r['is_modified'])
        avg_improvement = sum(r['improvement_rate'] for r in strategy_results) / total_samples
        
        accuracy = exact_matches / total_samples if total_samples > 0 else 0
        modification_rate = modifications / total_samples if total_samples > 0 else 0
        avg_time = (end_time - start_time) / total_samples if total_samples > 0 else 0
        
        results = {
            'strategy_name': strategy_name,
            'total_samples': total_samples,
            'exact_matches': exact_matches,
            'accuracy': accuracy,
            'modifications': modifications,
            'modification_rate': modification_rate,
            'avg_improvement_rate': avg_improvement,
            'total_time': end_time - start_time,
            'avg_time_per_sample': avg_time,
            'results': strategy_results
        }
        
        print(f"  Final Results:")
        print(f"    Accuracy: {accuracy:.4f} ({exact_matches}/{total_samples})")
        print(f"    Modification Rate: {modification_rate:.4f}")
        print(f"    Average Improvement: {avg_improvement:.2%}")
        print(f"    Total Time: {end_time - start_time:.2f}s")
        print(f"    Average Time: {avg_time:.3f}s/sample")
        
        return results

    def run_comparison_test(self, test_data: List[Dict], batch_size: int = 32) -> Dict[str, Any]:
        """Run comparison test between original and enhanced strategies"""
        print("=" * 80)
        print("NACGEC COMPARISON TEST: Original vs Enhanced Prompt Strategies")
        print("=" * 80)
        print(f"Test Dataset: NaCGEC Chinese Error Correction")
        print(f"Total Samples: {len(test_data)}")
        print(f"Batch Size: {batch_size}")
        print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # Test both strategies
        all_results = {}
        strategy_order = ['original', 'constraint_based']

        for strategy in strategy_order:
            all_results[strategy] = self.test_strategy_batch(test_data, strategy, batch_size)

        # Generate comparison analysis
        analysis = self._analyze_comparison_results(all_results)

        return {
            'test_info': {
                'test_time': datetime.now().isoformat(),
                'total_samples': len(test_data),
                'batch_size': batch_size,
                'strategies_tested': strategy_order,
                'test_type': 'nacgec_original_vs_enhanced_comparison'
            },
            'strategy_results': all_results,
            'comparison_analysis': analysis
        }

    def _analyze_comparison_results(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze comparison results between strategies"""
        print(f"\n" + "=" * 80)
        print("COMPARISON ANALYSIS")
        print("=" * 80)

        # Extract metrics for comparison
        original_results = all_results['original']
        enhanced_results = all_results['constraint_based']

        # Calculate improvements
        accuracy_improvement = enhanced_results['accuracy'] - original_results['accuracy']
        modification_rate_change = enhanced_results['modification_rate'] - original_results['modification_rate']
        avg_improvement_rate_change = enhanced_results['avg_improvement_rate'] - original_results['avg_improvement_rate']
        time_efficiency_change = original_results['avg_time_per_sample'] - enhanced_results['avg_time_per_sample']

        # Print comparison table
        print("\nPERFORMANCE COMPARISON TABLE")
        print("-" * 80)
        print(f"{'Metric':<25} {'Original':<15} {'Enhanced':<15} {'Improvement':<15}")
        print("-" * 80)
        print(f"{'Accuracy':<25} {original_results['accuracy']:<15.4f} {enhanced_results['accuracy']:<15.4f} {accuracy_improvement:+.4f}")
        print(f"{'Exact Matches':<25} {original_results['exact_matches']:<15} {enhanced_results['exact_matches']:<15} {enhanced_results['exact_matches'] - original_results['exact_matches']:+}")
        print(f"{'Modification Rate':<25} {original_results['modification_rate']:<15.4f} {enhanced_results['modification_rate']:<15.4f} {modification_rate_change:+.4f}")
        print(f"{'Avg Improvement Rate':<25} {original_results['avg_improvement_rate']:<15.2%} {enhanced_results['avg_improvement_rate']:<15.2%} {avg_improvement_rate_change:+.2%}")
        print(f"{'Avg Time (s/sample)':<25} {original_results['avg_time_per_sample']:<15.3f} {enhanced_results['avg_time_per_sample']:<15.3f} {time_efficiency_change:+.3f}")
        print(f"{'Total Time (s)':<25} {original_results['total_time']:<15.1f} {enhanced_results['total_time']:<15.1f} {enhanced_results['total_time'] - original_results['total_time']:+.1f}")
        print("-" * 80)

        # Determine significance
        if accuracy_improvement > 0.05:
            significance = "HIGHLY SIGNIFICANT"
            recommendation = "STRONGLY RECOMMEND enhanced strategy"
        elif accuracy_improvement > 0.02:
            significance = "SIGNIFICANT"
            recommendation = "RECOMMEND enhanced strategy"
        elif accuracy_improvement > 0.01:
            significance = "MODERATE"
            recommendation = "CONSIDER enhanced strategy"
        elif accuracy_improvement > 0:
            significance = "SLIGHT"
            recommendation = "MARGINAL benefit from enhanced strategy"
        else:
            significance = "NO IMPROVEMENT"
            recommendation = "Enhanced strategy not beneficial"

        # Calculate relative improvement
        relative_accuracy_improvement = (accuracy_improvement / original_results['accuracy']) * 100 if original_results['accuracy'] > 0 else 0

        print(f"\nIMPROVEMENT ANALYSIS")
        print("-" * 80)
        print(f"Absolute Accuracy Improvement: {accuracy_improvement:+.4f}")
        print(f"Relative Accuracy Improvement: {relative_accuracy_improvement:+.2f}%")
        print(f"Additional Correct Predictions: {enhanced_results['exact_matches'] - original_results['exact_matches']:+}")
        print(f"Significance Level: {significance}")
        print(f"Recommendation: {recommendation}")

        # Error type analysis
        print(f"\nERROR TYPE ANALYSIS")
        print("-" * 80)
        self._analyze_error_types(all_results)

        # Overall conclusion
        print(f"\n" + "=" * 80)
        print("OVERALL CONCLUSION")
        print("=" * 80)

        if accuracy_improvement > 0.01:
            conclusion = f"Enhanced prompt strategy provides {significance.lower()} performance improvements"
        else:
            conclusion = "Enhanced prompt strategy provides limited benefits"

        print(f"Conclusion: {conclusion}")
        print(f"Best Strategy: {'Enhanced (constraint-based)' if accuracy_improvement > 0 else 'Original'}")
        print(f"Accuracy Improvement: {accuracy_improvement:+.4f} ({relative_accuracy_improvement:+.2f}%)")
        print(f"Recommendation: {recommendation}")
        print("=" * 80)

        return {
            'accuracy_improvement': accuracy_improvement,
            'relative_accuracy_improvement': relative_accuracy_improvement,
            'modification_rate_change': modification_rate_change,
            'avg_improvement_rate_change': avg_improvement_rate_change,
            'time_efficiency_change': time_efficiency_change,
            'additional_correct_predictions': enhanced_results['exact_matches'] - original_results['exact_matches'],
            'significance': significance,
            'recommendation': recommendation,
            'conclusion': conclusion
        }

    def _analyze_error_types(self, all_results: Dict[str, Any]):
        """Analyze performance by error type"""
        from collections import defaultdict

        # Group results by error type
        original_by_type = defaultdict(list)
        enhanced_by_type = defaultdict(list)

        for result in all_results['original']['results']:
            original_by_type[result['error_type']].append(result)

        for result in all_results['constraint_based']['results']:
            enhanced_by_type[result['error_type']].append(result)

        # Calculate accuracy by error type
        error_types = set(original_by_type.keys()) | set(enhanced_by_type.keys())

        print(f"{'Error Type':<20} {'Original Acc':<12} {'Enhanced Acc':<12} {'Improvement':<12}")
        print("-" * 60)

        for error_type in sorted(error_types):
            orig_results = original_by_type[error_type]
            enh_results = enhanced_by_type[error_type]

            orig_acc = sum(1 for r in orig_results if r['is_exact_match']) / len(orig_results) if orig_results else 0
            enh_acc = sum(1 for r in enh_results if r['is_exact_match']) / len(enh_results) if enh_results else 0
            improvement = enh_acc - orig_acc

            print(f"{error_type:<20} {orig_acc:<12.4f} {enh_acc:<12.4f} {improvement:+.4f}")

    def save_results(self, results: Dict[str, Any], output_path: str) -> None:
        """Save comparison results to file"""
        print(f"\nSaving results to: {output_path}")

        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"Results saved successfully")


def load_nacgec_data(data_path: str) -> List[Dict]:
    """Load NaCGEC dataset"""
    print(f"Loading NaCGEC dataset: {data_path}")

    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"Loaded {len(data)} samples")
    return data


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="NaCGEC Original vs Enhanced Prompt Comparison Test")
    parser.add_argument('--data_path', type=str,
                       default='ChineseErrorCorrector/data/paper_data/nacgec_all_type.json',
                       help='NaCGEC test data path')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/NaCGEC_results/comparison_original_vs_enhanced_full.json',
                       help='Output results path')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='Maximum test samples (for testing, use None for full dataset)')
    parser.add_argument('--batch_size', type=int, default=32,
                       help='Batch size for processing')

    args = parser.parse_args()

    print("NACGEC ORIGINAL vs ENHANCED PROMPT COMPARISON TEST")
    print("=" * 60)
    print(f"Data Path: {args.data_path}")
    print(f"Output Path: {args.output_path}")
    print(f"Max Samples: {args.max_samples if args.max_samples else 'Full dataset'}")
    print(f"Batch Size: {args.batch_size}")
    print("=" * 60)

    try:
        # Initialize tester
        tester = NaCGECComparisonTester()

        # Load model
        if not tester.load_model():
            print("Failed to load model, exiting...")
            sys.exit(1)

        # Load data
        test_data = load_nacgec_data(args.data_path)

        # Limit samples if specified
        if args.max_samples:
            test_data = test_data[:args.max_samples]
            print(f"Limited to {len(test_data)} samples for testing")

        # Run comparison test
        results = tester.run_comparison_test(test_data, args.batch_size)

        # Save results
        tester.save_results(results, args.output_path)

        print(f"\nComparison test completed successfully!")
        print(f"Total samples processed: {len(test_data)}")
        print(f"Results saved to: {args.output_path}")

    except Exception as e:
        print(f"\nComparison test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
