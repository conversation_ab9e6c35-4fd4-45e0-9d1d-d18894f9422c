#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaCGEC数据集简化评估脚本
不依赖ChERRANT工具，使用简单指标进行评估
"""

import os
import sys
import json
import argparse
from datetime import datetime
from collections import defaultdict


def load_test_results(results_path):
    """加载测试结果"""
    print(f"加载测试结果: {results_path}")
    
    if not os.path.exists(results_path):
        raise FileNotFoundError(f"结果文件不存在: {results_path}")
    
    with open(results_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = data['results']
    print(f"加载了 {len(results)} 条结果")
    
    return results, data.get('metadata', {})


def calculate_edit_distance(s1, s2):
    """计算编辑距离"""
    if s1 == s2:
        return 0
    
    m, n = len(s1), len(s2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    
    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j
    
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if s1[i-1] == s2[j-1]:
                dp[i][j] = dp[i-1][j-1]
            else:
                dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
    
    return dp[m][n]


def calculate_sentence_accuracy(results):
    """计算句子级别准确率"""
    correct = 0
    total = len(results)
    
    for result in results:
        pred = result['prediction'].strip()
        gt = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()
        if pred == gt:
            correct += 1
    
    return correct / total if total > 0 else 0


def calculate_character_level_metrics(results):
    """计算字符级别的精确率、召回率和F分数"""
    total_tp = 0  # True Positives
    total_fp = 0  # False Positives  
    total_fn = 0  # False Negatives
    
    for result in results:
        source = result['source'].strip()
        prediction = result['prediction'].strip()
        ground_truth = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()
        
        # 计算编辑操作
        source_to_pred = calculate_edit_distance(source, prediction)
        source_to_gt = calculate_edit_distance(source, ground_truth)
        pred_to_gt = calculate_edit_distance(prediction, ground_truth)
        
        # 简化的TP/FP/FN计算
        if prediction == ground_truth:
            # 完全匹配
            total_tp += source_to_gt
        else:
            # 近似计算
            common_edits = max(0, (source_to_pred + source_to_gt - pred_to_gt) // 2)
            total_tp += common_edits
            total_fp += source_to_pred - common_edits
            total_fn += source_to_gt - common_edits
    
    # 计算指标
    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
    
    # 计算F分数
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    f05 = (1.25 * precision * recall) / (0.25 * precision + recall) if (0.25 * precision + recall) > 0 else 0
    
    return {
        'tp': total_tp,
        'fp': total_fp,
        'fn': total_fn,
        'precision': precision,
        'recall': recall,
        'f0.5': f05,
        'f1': f1
    }


def calculate_error_type_stats(results):
    """按错误类型计算统计信息"""
    error_stats = defaultdict(lambda: {'total': 0, 'correct': 0, 'edit_distances': []})
    
    for result in results:
        error_type = result.get('error_type', 'unknown')
        pred = result['prediction'].strip()
        gt = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()
        
        error_stats[error_type]['total'] += 1
        if pred == gt:
            error_stats[error_type]['correct'] += 1
        
        edit_dist = calculate_edit_distance(pred, gt)
        error_stats[error_type]['edit_distances'].append(edit_dist)
    
    # 计算每种错误类型的准确率和平均编辑距离
    for error_type, stats in error_stats.items():
        stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
        stats['avg_edit_distance'] = sum(stats['edit_distances']) / len(stats['edit_distances']) if stats['edit_distances'] else 0
    
    return error_stats


def print_evaluation_report(results, metadata, char_metrics, error_stats):
    """打印评估报告"""
    print("=" * 60)
    print("NaCGEC数据集评估报告 (简化版)")
    print("=" * 60)
    
    # 基本信息
    print(f"测试时间: {metadata.get('timestamp', '未知')}")
    print(f"测试样本数: {len(results)}")
    
    model_config = metadata.get('model_config', {})
    print(f"模型配置:")
    print(f"  - 使用VLLM: {model_config.get('use_vllm', '未知')}")
    print(f"  - 模型路径: {model_config.get('model_path', '未知')}")
    print()
    
    # 整体评估结果
    sentence_acc = calculate_sentence_accuracy(results)
    avg_edit_distance = sum(calculate_edit_distance(r['prediction'].strip(), 
                                                   r['ground_truth'].strip() if isinstance(r['ground_truth'], str) else r['ground_truth'][0].strip()) 
                           for r in results) / len(results)
    
    print("=== 评估结果摘要 ===")
    print(f"句子级别准确率: {sentence_acc:.4f} ({sentence_acc*100:.2f}%)")
    print(f"字符级别精确率: {char_metrics['precision']:.4f}")
    print(f"字符级别召回率: {char_metrics['recall']:.4f}")
    print(f"F0.5分数: {char_metrics['f0.5']:.4f}")
    print(f"F1分数: {char_metrics['f1']:.4f}")
    print(f"平均编辑距离: {avg_edit_distance:.2f}")
    print()
    
    # 按错误类型统计
    print("=== 各错误类型表现 ===")
    sorted_types = sorted(error_stats.items(), key=lambda x: x[1]['total'], reverse=True)
    for error_type, stats in sorted_types:
        print(f"{error_type}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.3f})")
    print()
    
    # 与官方基准对比
    print("=== 与官方基准对比 ===")
    official_f05 = 0.7402   # ChineseErrorCorrector3-4B官方F0.5分数
    current_f05 = char_metrics['f0.5']
    
    print(f"官方F0.5分数: {official_f05:.4f}")
    print(f"当前F0.5分数: {current_f05:.4f}")
    print(f"差异: {current_f05 - official_f05:+.4f}")
    
    if current_f05 >= official_f05 * 0.95:
        print("✓ 结果正常，与官方基准接近")
    else:
        print("⚠ 结果偏低，可能需要检查配置或数据")
    print()
    
    # 错误示例
    print("=== 错误示例 (前3个) ===")
    error_count = 0
    for result in results:
        pred = result['prediction'].strip()
        gt = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()
        if pred != gt and error_count < 3:
            print(f"示例 {error_count + 1} ({result.get('error_type', 'unknown')}):")
            print(f"  原文: {result['source']}")
            print(f"  标准答案: {gt}")
            print(f"  模型预测: {pred}")
            print(f"  编辑距离: {calculate_edit_distance(pred, gt)}")
            print()
            error_count += 1


def main():
    parser = argparse.ArgumentParser(description='NaCGEC数据集简化评估')
    parser.add_argument('--results_path', type=str,
                       default='ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_Original_4B_01.json',
                       help='测试结果文件路径')
    parser.add_argument('--output_path', type=str, default=None,
                       help='评估报告输出路径（可选）')
    
    args = parser.parse_args()
    
    print("=== NaCGEC数据集简化评估开始 ===")
    print(f"结果文件: {args.results_path}")
    
    try:
        # 1. 加载测试结果
        results, metadata = load_test_results(args.results_path)
        
        # 2. 计算字符级别指标
        char_metrics = calculate_character_level_metrics(results)
        
        # 3. 计算错误类型统计
        error_stats = calculate_error_type_stats(results)
        
        # 4. 打印评估报告
        print_evaluation_report(results, metadata, char_metrics, error_stats)
        
        # 5. 保存报告（如果指定了输出路径）
        if args.output_path:
            report = {
                'timestamp': datetime.now().isoformat(),
                'metadata': metadata,
                'metrics': char_metrics,
                'error_type_stats': {k: {
                    'total': v['total'],
                    'correct': v['correct'],
                    'accuracy': v['accuracy'],
                    'avg_edit_distance': v['avg_edit_distance']
                } for k, v in error_stats.items()},
                'sentence_accuracy': calculate_sentence_accuracy(results)
            }
            
            os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
            with open(args.output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"评估报告已保存到: {args.output_path}")
        
        print("=== 评估完成 ===")
        
    except Exception as e:
        print(f"评估失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
