#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NaCGEC数据集评估脚本
使用简单指标进行评估，不依赖ChERRANT工具
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
import multiprocessing
from datetime import datetime
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed


def load_test_results(results_path):
    """加载测试结果"""
    print(f"加载测试结果: {results_path}")
    
    if not os.path.exists(results_path):
        raise FileNotFoundError(f"结果文件不存在: {results_path}")
    
    with open(results_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = data['results']
    print(f"加载了 {len(results)} 条结果")
    
    return results


def process_chunk_to_m2(chunk_data):
    """处理数据块生成M2文件的工作函数"""
    chunk_id, chunk_results, cherrant_path, is_reference = chunk_data

    try:
        # 确保使用绝对路径
        cherrant_abs_path = os.path.abspath(cherrant_path)

        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建临时parallel文件
            para_file = os.path.join(temp_dir, f'chunk_{chunk_id}.para')

            with open(para_file, 'w', encoding='utf-8') as f:
                for i, result in enumerate(chunk_results):
                    source = result['source'].strip().replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    if is_reference:
                        target = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()
                        target = target.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
                    else:
                        target = result['prediction'].strip().replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')

                    f.write(f"{chunk_id * 1000 + i + 1}\t{source}\t{target}\n")

            # 生成M2文件
            m2_file = os.path.join(temp_dir, f'chunk_{chunk_id}.m2')
            parallel_to_m2_script = os.path.join(cherrant_abs_path, 'parallel_to_m2.py')

            cmd = [
                'python', parallel_to_m2_script,
                '-f', para_file,
                '-o', m2_file,
                '-g', 'char'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8',
                                  cwd=cherrant_abs_path, timeout=300)

            if result.returncode != 0:
                raise RuntimeError(f"Chunk {chunk_id} M2生成失败: {result.stderr}")

            # 读取生成的M2内容
            with open(m2_file, 'r', encoding='utf-8') as f:
                m2_content = f.read()

            return chunk_id, m2_content

    except Exception as e:
        return chunk_id, f"ERROR: {str(e)}"


def parallel_generate_m2_files(results, cherrant_path, num_processes=None):
    """并行生成M2文件"""
    if num_processes is None:
        num_processes = min(multiprocessing.cpu_count(), 8)  # 最多使用8个进程

    print(f"🚀 使用 {num_processes} 个进程并行生成M2文件...")

    # 将数据分块
    chunk_size = max(100, len(results) // num_processes)  # 每块至少100个样本
    chunks = []
    for i in range(0, len(results), chunk_size):
        chunk = results[i:i + chunk_size]
        chunks.append(chunk)

    print(f"📊 数据分为 {len(chunks)} 块，每块约 {chunk_size} 个样本")

    # 并行处理参考文件和假设文件
    ref_chunks = [(i, chunk, cherrant_path, True) for i, chunk in enumerate(chunks)]
    hyp_chunks = [(i, chunk, cherrant_path, False) for i, chunk in enumerate(chunks)]

    ref_m2_parts = {}
    hyp_m2_parts = {}

    # 处理参考文件
    print("生成参考M2文件...")
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        future_to_chunk = {executor.submit(process_chunk_to_m2, chunk_data): chunk_data[0]
                          for chunk_data in ref_chunks}

        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                chunk_id, m2_content = future.result()
                if m2_content.startswith("ERROR:"):
                    raise RuntimeError(m2_content)
                ref_m2_parts[chunk_id] = m2_content
                print(f"✅ 参考文件块 {chunk_id} 完成")
            except Exception as e:
                print(f"❌ 参考文件块 {chunk_id} 失败: {e}")
                raise

    # 处理假设文件
    print("生成假设M2文件...")
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        future_to_chunk = {executor.submit(process_chunk_to_m2, chunk_data): chunk_data[0]
                          for chunk_data in hyp_chunks}

        for future in as_completed(future_to_chunk):
            chunk_id = future_to_chunk[future]
            try:
                chunk_id, m2_content = future.result()
                if m2_content.startswith("ERROR:"):
                    raise RuntimeError(m2_content)
                hyp_m2_parts[chunk_id] = m2_content
                print(f"✅ 假设文件块 {chunk_id} 完成")
            except Exception as e:
                print(f"❌ 假设文件块 {chunk_id} 失败: {e}")
                raise

    # 合并M2文件
    print("合并M2文件...")
    ref_m2_content = ""
    hyp_m2_content = ""

    for i in range(len(chunks)):
        if i in ref_m2_parts:
            ref_m2_content += ref_m2_parts[i]
        if i in hyp_m2_parts:
            hyp_m2_content += hyp_m2_parts[i]

    return ref_m2_content, hyp_m2_content


def calculate_edit_distance(s1, s2):
    """计算编辑距离"""
    if s1 == s2:
        return 0

    m, n = len(s1), len(s2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]

    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j

    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if s1[i-1] == s2[j-1]:
                dp[i][j] = dp[i-1][j-1]
            else:
                dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])

    return dp[m][n]


def get_edit_operations(source, target):
    """获取从source到target的编辑操作序列"""
    m, n = len(source), len(target)
    dp = [[0] * (n + 1) for _ in range(m + 1)]

    # 初始化
    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j

    # 填充DP表
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if source[i-1] == target[j-1]:
                dp[i][j] = dp[i-1][j-1]
            else:
                dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])

    # 回溯获取编辑操作
    operations = []
    i, j = m, n
    while i > 0 or j > 0:
        if i > 0 and j > 0 and source[i-1] == target[j-1]:
            # 匹配
            i -= 1
            j -= 1
        elif i > 0 and j > 0 and dp[i][j] == dp[i-1][j-1] + 1:
            # 替换
            operations.append(('substitute', i-1, source[i-1], target[j-1]))
            i -= 1
            j -= 1
        elif i > 0 and dp[i][j] == dp[i-1][j] + 1:
            # 删除
            operations.append(('delete', i-1, source[i-1]))
            i -= 1
        elif j > 0 and dp[i][j] == dp[i][j-1] + 1:
            # 插入
            operations.append(('insert', i, target[j-1]))
            j -= 1

    return list(reversed(operations))


def calculate_character_level_metrics(results):
    """计算字符级别的精确率、召回率和F分数"""
    total_tp = 0  # True Positives: 正确的编辑操作
    total_fp = 0  # False Positives: 错误的编辑操作
    total_fn = 0  # False Negatives: 遗漏的编辑操作

    correct_sentences = 0
    total_sentences = len(results)

    for result in results:
        source = result['source'].strip()
        prediction = result['prediction'].strip()
        ground_truth = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()

        # 如果预测完全正确
        if prediction == ground_truth:
            correct_sentences += 1
            # 获取标准答案的编辑操作数量
            gt_operations = get_edit_operations(source, ground_truth)
            total_tp += len(gt_operations)
            continue

        # 获取编辑操作
        pred_operations = get_edit_operations(source, prediction)
        gt_operations = get_edit_operations(source, ground_truth)

        # 将操作转换为集合进行比较
        pred_ops_set = set()
        gt_ops_set = set()

        for op in pred_operations:
            if op[0] == 'substitute':
                pred_ops_set.add((op[0], op[1], op[3]))  # (操作类型, 位置, 新字符)
            elif op[0] == 'delete':
                pred_ops_set.add((op[0], op[1]))  # (操作类型, 位置)
            elif op[0] == 'insert':
                pred_ops_set.add((op[0], op[1], op[2]))  # (操作类型, 位置, 插入字符)

        for op in gt_operations:
            if op[0] == 'substitute':
                gt_ops_set.add((op[0], op[1], op[3]))
            elif op[0] == 'delete':
                gt_ops_set.add((op[0], op[1]))
            elif op[0] == 'insert':
                gt_ops_set.add((op[0], op[1], op[2]))

        # 计算TP, FP, FN
        tp = len(pred_ops_set & gt_ops_set)  # 交集：正确的编辑操作
        fp = len(pred_ops_set - gt_ops_set)  # 预测有但标准答案没有
        fn = len(gt_ops_set - pred_ops_set)  # 标准答案有但预测没有

        total_tp += tp
        total_fp += fp
        total_fn += fn

    # 计算指标
    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0

    # 计算F分数
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    f05 = (1.25 * precision * recall) / (0.25 * precision + recall) if (0.25 * precision + recall) > 0 else 0

    # 句子级别准确率
    sentence_accuracy = correct_sentences / total_sentences if total_sentences > 0 else 0

    return {
        'tp': total_tp,
        'fp': total_fp,
        'fn': total_fn,
        'precision': precision,
        'recall': recall,
        'f0.5': f05,
        'f1': f1,
        'sentence_accuracy': sentence_accuracy,
        'correct_sentences': correct_sentences,
        'total_sentences': total_sentences
    }


def prepare_cherrant_files(results, temp_dir):
    """准备ChERRANT评估所需的文件"""
    print("准备ChERRANT评估文件...")

    # 创建parallel格式文件 (id \t source \t target)
    ref_para_file = os.path.join(temp_dir, 'reference.para')
    hyp_para_file = os.path.join(temp_dir, 'hypothesis.para')

    valid_count = 0
    skipped_count = 0

    with open(ref_para_file, 'w', encoding='utf-8') as rf, \
         open(hyp_para_file, 'w', encoding='utf-8') as hf:

        for i, result in enumerate(results):
            # 跳过空的预测结果
            prediction = result['prediction'].strip()
            source = result['source'].strip()
            ground_truth = result['ground_truth'].strip() if isinstance(result['ground_truth'], str) else result['ground_truth'][0].strip()

            # 检查是否有空字段
            if not prediction or not source or not ground_truth:
                print(f"跳过无效结果 {i}: prediction={bool(prediction)}, source={bool(source)}, ground_truth={bool(ground_truth)}")
                skipped_count += 1
                continue

            # 清理特殊字符，避免ChERRANT解析错误
            source = source.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
            ground_truth = ground_truth.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')
            prediction = prediction.replace('\t', ' ').replace('\n', ' ').replace('\r', ' ')

            valid_count += 1
            # 参考文件格式: id \t source \t target
            rf.write(f"{valid_count}\t{source}\t{ground_truth}\n")
            # 假设文件格式: id \t source \t prediction
            hf.write(f"{valid_count}\t{source}\t{prediction}\n")

    print(f"有效样本数: {valid_count}/{len(results)} (跳过: {skipped_count})")
    return ref_para_file, hyp_para_file


def run_cherrant_evaluation_parallel(results, cherrant_path, num_processes=None):
    """使用并行处理运行ChERRANT评估"""
    print("运行ChERRANT并行评估...")

    # 检查ChERRANT脚本路径
    parallel_to_m2_script = os.path.abspath(os.path.join(cherrant_path, 'parallel_to_m2.py'))
    compare_m2_script = os.path.abspath(os.path.join(cherrant_path, 'compare_m2_for_evaluation.py'))

    print(f"📁 ChERRANT路径: {cherrant_path}")
    print(f"📄 parallel_to_m2脚本: {parallel_to_m2_script}")
    print(f"📄 compare_m2脚本: {compare_m2_script}")

    if not os.path.exists(parallel_to_m2_script):
        raise FileNotFoundError(f"parallel_to_m2.py脚本不存在: {parallel_to_m2_script}")
    if not os.path.exists(compare_m2_script):
        raise FileNotFoundError(f"compare_m2_for_evaluation.py脚本不存在: {compare_m2_script}")

    # 并行生成M2文件
    ref_m2_content, hyp_m2_content = parallel_generate_m2_files(results, cherrant_path, num_processes)

    # 创建临时目录用于存储最终文件
    with tempfile.TemporaryDirectory() as temp_eval_dir:
        # 写入合并后的M2文件
        ref_m2_file = os.path.join(temp_eval_dir, 'reference.m2')
        hyp_m2_file = os.path.join(temp_eval_dir, 'hypothesis.m2')

        with open(ref_m2_file, 'w', encoding='utf-8') as f:
            f.write(ref_m2_content)

        with open(hyp_m2_file, 'w', encoding='utf-8') as f:
            f.write(hyp_m2_content)

        print(f"📊 参考M2文件大小: {len(ref_m2_content)} 字符")
        print(f"📊 假设M2文件大小: {len(hyp_m2_content)} 字符")

        # 计算评估指标 (F0.5)
        cmd = [
            'python', compare_m2_script,
            '-hyp', hyp_m2_file,
            '-ref', ref_m2_file,
            '-b', '0.5'  # F0.5评估
        ]

        print(f"计算评估指标: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8',
                                  cwd=os.path.abspath(cherrant_path), timeout=600)  # 10分钟超时
        except subprocess.TimeoutExpired:
            print("⚠️ 评估计算超时 (10分钟)")
            raise RuntimeError("ChERRANT评估计算超时")

        if result.returncode != 0:
            print(f"评估计算失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            raise RuntimeError("ChERRANT评估计算失败")

        return result.stdout


def run_cherrant_evaluation(ref_para_file, hyp_para_file, cherrant_path):
    """运行ChERRANT评估（原始单线程版本，保留作为备选）"""
    print("运行ChERRANT单线程评估...")

    # 检查ChERRANT脚本路径
    parallel_to_m2_script = os.path.abspath(os.path.join(cherrant_path, 'parallel_to_m2.py'))
    compare_m2_script = os.path.abspath(os.path.join(cherrant_path, 'compare_m2_for_evaluation.py'))

    if not os.path.exists(parallel_to_m2_script):
        raise FileNotFoundError(f"parallel_to_m2.py脚本不存在: {parallel_to_m2_script}")
    if not os.path.exists(compare_m2_script):
        raise FileNotFoundError(f"compare_m2_for_evaluation.py脚本不存在: {compare_m2_script}")

    # 创建临时目录用于存储中间文件
    with tempfile.TemporaryDirectory() as temp_eval_dir:
        # 1. 生成参考M2文件
        ref_m2_file = os.path.join(temp_eval_dir, 'reference.m2')
        cmd1 = [
            'python', parallel_to_m2_script,
            '-f', ref_para_file,
            '-o', ref_m2_file,
            '-g', 'char'  # 字符级评估
        ]

        print(f"生成参考M2文件: {' '.join(cmd1)}")
        result1 = subprocess.run(cmd1, capture_output=True, text=True, encoding='utf-8',
                               cwd=os.path.abspath(cherrant_path), timeout=600)

        if result1.returncode != 0:
            raise RuntimeError("ChERRANT参考M2生成失败")

        # 2. 生成假设M2文件
        hyp_m2_file = os.path.join(temp_eval_dir, 'hypothesis.m2')
        cmd2 = [
            'python', parallel_to_m2_script,
            '-f', hyp_para_file,
            '-o', hyp_m2_file,
            '-g', 'char'  # 字符级评估
        ]

        print(f"生成假设M2文件: {' '.join(cmd2)}")
        result2 = subprocess.run(cmd2, capture_output=True, text=True, encoding='utf-8',
                               cwd=os.path.abspath(cherrant_path), timeout=600)

        if result2.returncode != 0:
            raise RuntimeError("ChERRANT假设M2生成失败")

        # 3. 计算评估指标 (F0.5)
        cmd3 = [
            'python', compare_m2_script,
            '-hyp', hyp_m2_file,
            '-ref', ref_m2_file,
            '-b', '0.5'  # F0.5评估
        ]

        print(f"计算评估指标: {' '.join(cmd3)}")
        result3 = subprocess.run(cmd3, capture_output=True, text=True, encoding='utf-8',
                               cwd=os.path.abspath(cherrant_path), timeout=600)

        if result3.returncode != 0:
            raise RuntimeError("ChERRANT评估计算失败")

        return result3.stdout


def parse_cherrant_output(output):
    """解析ChERRANT输出"""
    lines = output.strip().split('\n')
    metrics = {}

    # ChERRANT输出格式示例:
    # =========== Span-Based Correction ============
    # TP TN FP	FN	Prec	Rec	F0.5
    # 8	19	35	0.2963	0.186	0.2649
    # ==============================================

    for line in lines:
        line = line.strip()
        if not line or line.startswith('=') or line.startswith('TP\t'):
            continue

        # 查找包含数值的行（不是标题行）
        parts = line.split('\t')
        if len(parts) >= 6:
            try:
                # 尝试解析数值
                tp = int(parts[0])
                fp = int(parts[1])
                fn = int(parts[2])
                precision = float(parts[3])
                recall = float(parts[4])
                f_score_from_cherrant = float(parts[5])  # ChERRANT原始输出的F0.5（可能有误）

                # 正确计算F0.5分数：F_β = (1 + β²) * (precision * recall) / (β² * precision + recall)
                # 对于F0.5，β = 0.5，所以 β² = 0.25
                f0_5_calculated = (1.25 * precision * recall) / (0.25 * precision + recall) if (0.25 * precision + recall) > 0 else 0.0

                metrics = {
                    'tp': tp,
                    'fp': fp,
                    'fn': fn,
                    'precision': precision,
                    'recall': recall,
                    'f0.5': f0_5_calculated,  # 使用计算的F0.5而不是直接从ChERRANT输出解析
                    'f1': 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
                }
                print(f"解析ChERRANT结果: TP={tp}, FP={fp}, FN={fn}, Prec={precision:.4f}, Rec={recall:.4f}")
                print(f"F0.5计算: ChERRANT原始={f_score_from_cherrant:.4f}, 重新计算={f0_5_calculated:.4f}")
                break
            except (ValueError, IndexError) as e:
                print(f"解析行失败: {line}, 错误: {e}")
                continue

    # 如果没有找到标准格式，返回默认值
    if not metrics:
        print("警告: 无法解析ChERRANT输出，使用默认值")
        print(f"原始输出:\n{output}")
        metrics = {
            'tp': 0,
            'fp': 0,
            'fn': 0,
            'precision': 0.0,
            'recall': 0.0,
            'f0.5': 0.0,
            'f1': 0.0
        }

    return metrics


def calculate_simple_metrics(results):
    """计算简单的准确率指标"""
    total = len(results)
    exact_match = 0
    error_type_stats = {}

    for result in results:
        # 检查是否与任何一个正确答案匹配
        prediction = result['prediction']
        ground_truth = result['ground_truth']
        error_type = result.get('error_type', 'unknown')

        # 初始化错误类型统计
        if error_type not in error_type_stats:
            error_type_stats[error_type] = {'total': 0, 'correct': 0}
        error_type_stats[error_type]['total'] += 1

        # 检查完全匹配
        is_correct = False
        if prediction == ground_truth:
            is_correct = True

        if is_correct:
            exact_match += 1
            error_type_stats[error_type]['correct'] += 1

    exact_match_rate = exact_match / total if total > 0 else 0

    # 计算各错误类型的准确率
    for error_type in error_type_stats:
        stats = error_type_stats[error_type]
        stats['accuracy'] = stats['correct'] / stats['total'] if stats['total'] > 0 else 0

    return {
        'total_samples': total,
        'exact_match': exact_match,
        'exact_match_rate': exact_match_rate,
        'error_type_stats': error_type_stats
    }


def save_evaluation_report(metrics, simple_metrics, output_path):
    """保存评估报告"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'cherrant_metrics': metrics,
        'simple_metrics': simple_metrics,
        'summary': {
            'precision': metrics.get('precision', 0),
            'recall': metrics.get('recall', 0),
            'f0.5': metrics.get('f0.5', 0),
            'f1': metrics.get('f1', 0),
            'exact_match_rate': simple_metrics['exact_match_rate']
        }
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"评估报告已保存到: {output_path}")
    return report


def print_evaluation_summary(report):
    """打印评估摘要"""
    print("\n=== 评估结果摘要 ===")
    summary = report['summary']

    print(f"精确率 (Precision): {summary['precision']:.4f}")
    print(f"召回率 (Recall): {summary['recall']:.4f}")
    print(f"F0.5分数: {summary['f0.5']:.4f}")
    print(f"F1分数: {summary['f1']:.4f}")
    print(f"完全匹配率: {summary['exact_match_rate']:.4f}")

    # 显示错误类型统计
    if 'error_type_stats' in report['simple_metrics']:
        print("\n=== 各错误类型表现 ===")
        error_stats = report['simple_metrics']['error_type_stats']
        sorted_types = sorted(error_stats.items(), key=lambda x: x[1]['total'], reverse=True)

        for error_type, stats in sorted_types:
            print(f"{error_type}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.3f})")

    # 与官方基准对比
    print("\n=== 与官方基准对比 ===")
    # official_f05 = 0.5689  # ChineseErrorCorrector2-7B官方F0.5分数
    official_f05 = 0.7402   # ChineseErrorCorrector3-4B官方F0.5分数
    current_f05 = summary['f0.5']

    print(f"官方F0.5分数: {official_f05:.4f}")
    print(f"当前F0.5分数: {current_f05:.4f}")
    print(f"差异: {current_f05 - official_f05:+.4f}")

    if current_f05 >= official_f05 * 0.95:  # 95%以上认为正常
        print("结果正常，与官方基准接近")
    else:
        print("结果偏低，可能需要检查配置或数据")


def main():
    parser = argparse.ArgumentParser(description='NaCGEC数据集评估')
    parser.add_argument('--results_path', type=str,
                       default='ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_test_original_4B_03.json',
                       help='测试结果文件路径')
    parser.add_argument('--output_path', type=str,
                       default='ChineseErrorCorrector/model_test/NaCGEC_results/nacgec_evaluation_03.json',
                       help='评估报告输出路径')
    parser.add_argument('--cherrant_path', type=str,
                       default='ChineseErrorCorrector/model_test/evaluation_tools/ChERRANT',
                       help='ChERRANT工具路径')
    parser.add_argument('--num_processes', type=int, default=4,
                       help='并行处理进程数 (默认: CPU核心数，最多8个)')
    parser.add_argument('--disable_parallel', action='store_true',
                       help='禁用并行处理，使用单线程评估')

    args = parser.parse_args()
    
    print("=== NaCGEC数据集评估开始 ===")
    print(f"结果文件: {args.results_path}")
    print(f"输出路径: {args.output_path}")
    print(f"ChERRANT路径: {args.cherrant_path}")
    print(f"并行进程数: {args.num_processes}")
    
    try:
        # 1. 加载测试结果
        results = load_test_results(args.results_path)
        
        # 2. 计算简单指标
        simple_metrics = calculate_simple_metrics(results)
        
        # 3. 尝试使用ChERRANT评估，如果失败则使用简化方法
        cherrant_success = False

        try:
            print("尝试使用ChERRANT评估...")

            # 首先尝试并行评估（除非被禁用）
            if not args.disable_parallel:
                try:
                    print("使用并行评估...")
                    cherrant_output = run_cherrant_evaluation_parallel(results, args.cherrant_path, args.num_processes)
                    metrics = parse_cherrant_output(cherrant_output)
                    print("✅ ChERRANT并行评估完成")
                    print(f"ChERRANT结果: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F0.5={metrics['f0.5']:.4f}")
                    cherrant_success = True
                except Exception as parallel_error:
                    print(f"⚠️ 并行评估失败: {parallel_error}")
                    print("尝试单线程ChERRANT评估...")
            else:
                print("并行评估已禁用，使用单线程评估...")

            # 如果并行评估失败或被禁用，尝试单线程评估
            if not cherrant_success:
                with tempfile.TemporaryDirectory() as temp_dir:
                    ref_para_file, hyp_para_file = prepare_cherrant_files(results, temp_dir)

                    cherrant_output = run_cherrant_evaluation(
                        ref_para_file, hyp_para_file, args.cherrant_path
                    )
                    metrics = parse_cherrant_output(cherrant_output)
                    print("✅ ChERRANT单线程评估完成")
                    print(f"ChERRANT结果: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F0.5={metrics['f0.5']:.4f}")
                    cherrant_success = True

        except Exception as e:
            print(f"⚠️ ChERRANT评估完全失败: {e}")

        # 如果ChERRANT评估失败，使用简化方法
        if not cherrant_success:
            print("使用简化字符级别指标作为备选...")
            metrics = calculate_character_level_metrics(results)
            print(f"简化指标计算完成: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F0.5={metrics['f0.5']:.4f}")
        
        # 4. 保存评估报告
        report = save_evaluation_report(metrics, simple_metrics, args.output_path)
        
        # 5. 打印摘要
        print_evaluation_summary(report)
        
        print("\n=== 评估完成 ===")
        
    except Exception as e:
        print(f"评估失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
