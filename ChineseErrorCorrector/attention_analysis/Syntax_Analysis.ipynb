{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BERT's Attention and Dependency Syntax\n", "\n", "This notebook contains code for comparing BERT's attention to dependency syntax annotations (see Sections 4.2 and 5 of [What Does BERT Look At? An Analysis of BERT's Attention](https://arxiv.org/abs/1906.04341))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import collections\n", "import pickle\n", "import numpy as np\n", "import tensorflow as tf\n", "from matplotlib import pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading the data\n", "\n", "Download the data used in this notebook from [here](https://drive.google.com/open?id=1DEIBQIl0Q0az5ZuLoy4_lYabIfLSKBg-). However, note that since Penn Treebank annotations are not public, this is dummy data where all labels are ROOT. See the README for extracting attention maps on your own data."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('words:', ['Influential', 'members', 'of', 'the', 'House', 'Ways', 'and', 'Means', 'Committee', 'introduced', 'legislation', 'that', 'would', 'restrict', 'how', 'the', 'new', 'savings-and-loan', 'bailout', 'agency', 'can', 'raise', 'capital', ',', 'creating', 'another', 'potential', 'obstacle', 'to', 'the', 'government', \"'s\", 'sale', 'of', 'sick', 'thrifts', '.'])\n", "('heads:', [2, 10, 2, 6, 6, 3, 6, 9, 6, 0, 10, 14, 14, 11, 22, 20, 20, 20, 20, 22, 22, 14, 22, 14, 14, 28, 28, 25, 28, 31, 33, 31, 29, 33, 36, 34, 10])\n", "('relns:', ['amod', 'nsubj', 'prep', 'det', 'nn', 'pobj', 'cc', 'nn', 'conj', 'root', 'dobj', 'nsubj', 'aux', 'rcmod', 'advmod', 'det', 'amod', 'nn', 'nn', 'nsubj', 'aux', 'ccomp', 'dobj', 'punct', 'xcomp', 'det', 'amod', 'dobj', 'prep', 'det', 'poss', 'possessive', 'pobj', 'prep', 'amod', 'pobj', 'punct'])\n", "('attns: a tensor with shape', (12, 12, 39, 39))\n"]}], "source": ["def load_pickle(fname):\n", "  with open(fname, \"rb\") as f:\n", "    return pickle.load(f)  # add, encoding=\"latin1\") if using python3 and downloaded data\n", "    \n", "dev_data = load_pickle(\"./data/depparse/dev_attn.pkl\")\n", "\n", "# The data consists of a list of examples (dicts)\n", "# with the following keys/values\n", "# {\n", "#    \"words\": list of words in the sentence\n", "#    \"heads\": index of each word\"s syntactic head (0 for ROOT, 1 for the first \n", "#             word of the sentence, etc.)\n", "#    \"relns\": the relation between each word and its head\n", "#    \"attns\": [n_layers, n_heads, seq_len, seq_len] tensor of attention maps\n", "#             from BERT\n", "#}\n", "print(\"words:\", dev_data[0][\"words\"])\n", "print(\"heads:\", dev_data[0][\"heads\"])\n", "print(\"relns:\", dev_data[0][\"relns\"])\n", "# Attention maps are 9x9 because [CLS] and [SEP] are added\n", "print(\"attns: a tensor with shape\", dev_data[0][\"attns\"].shape)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('punct', 4703), ('prep', 3760), ('pobj', 3720), ('det', 3337), ('nn', 3192), ('nsubj', 2804), ('amod', 2505), ('root', 1696), ('dobj', 1591), ('advmod', 1241)]\n"]}], "source": ["# Find the most common relations in our data\n", "reln_counts = collections.Counter()\n", "for example in dev_data:\n", "  for reln in example[\"relns\"]:\n", "    reln_counts[reln] += 1\n", "print(reln_counts.most_common(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluating individual heads at dependency syntax (Section 4.2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Code for evaluating individual attention maps and baselines\n", "\n", "def evaluate_predictor(prediction_fn):\n", "  \"\"\"Compute accuracies for each relation for the given predictor.\"\"\"\n", "  n_correct, n_incorrect = collections.Counter(), collections.Counter()\n", "  for example in dev_data:\n", "    words = example[\"words\"]\n", "    predictions = prediction_fn(example)\n", "    for i, (p, y, r) in enumerate(zip(predictions, example[\"heads\"],\n", "                                      example[\"relns\"])):\n", "      is_correct = (p == y)\n", "      if r == \"poss\" and p < len(words):\n", "        # Special case for poss (see discussion in Section 4.2)\n", "        if i < len(words) and words[i + 1] == \"'s\" or words[i + 1] == \"s'\":\n", "          is_correct = (predictions[i + 1] == y)\n", "      if is_correct:\n", "        n_correct[r] += 1\n", "        n_correct[\"all\"] += 1\n", "      else:\n", "        n_incorrect[r] += 1\n", "        n_incorrect[\"all\"] += 1\n", "  return {k: n_correct[k] / float(n_correct[k] + n_incorrect[k])\n", "          for k in n_incorrect.keys()}\n", "\n", "def attn_head_predictor(layer, head, mode=\"normal\"):\n", "  \"\"\"Assign each word the most-attended-to other word as its head.\"\"\"\n", "  def predict(example):\n", "    attn = np.array(example[\"attns\"][layer][head])\n", "    if mode == \"transpose\":\n", "      attn = attn.T\n", "    elif mode == \"both\":\n", "      attn += attn.T\n", "    else:\n", "      assert mode == \"normal\"\n", "    # ignore attention to self and [CLS]/[SEP] tokens\n", "    attn[range(attn.shape[0]), range(attn.shape[0])] = 0\n", "    attn = attn[1:-1, 1:-1]\n", "    return np.argmax(attn, axis=-1) + 1  # +1 because ROOT is at index 0\n", "  return predict\n", "\n", "def offset_predictor(offset):\n", "  \"\"\"Simple baseline: assign each word the word a fixed offset from\n", "  it (e.g., the word to its right) as its head.\"\"\"\n", "  def predict(example):\n", "    return [max(0, min(i + offset + 1, len(example[\"words\"])))\n", "            for i in range(len(example[\"words\"]))]\n", "  return predict\n", "\n", "def get_scores(mode=\"normal\"):\n", "  \"\"\"Get the accuracies of every attention head.\"\"\"\n", "  scores = collections.defaultdict(dict)\n", "  for layer in range(12):\n", "    for head in range(12):\n", "      scores[layer][head] = evaluate_predictor(\n", "          attn_head_predictor(layer, head, mode))\n", "  return scores\n", "\n", "# attn_head_scores[direction][layer][head][dep_relation] = accuracy\n", "attn_head_scores = {\n", "    \"dep->head\": get_scores(\"normal\"),\n", "    \"head<-dep\": get_scores(\"transpose\")\n", "}\n", "# baseline_scores[offset][dep_relation] = accuracy\n", "baseline_scores = {\n", "    i: evaluate_predictor(offset_predictor(i)) for i in range(-3, 3)\n", "}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def get_all_scores(reln):\n", "  \"\"\"Get all attention head scores for a particular relation.\"\"\"\n", "  all_scores = []\n", "  for key, layer_head_scores in attn_head_scores.iteritems():\n", "    for layer, head_scores in layer_head_scores.iteritems():\n", "      for head, scores in head_scores.iteritems():\n", "        all_scores.append((scores[reln], layer, head, key))\n", "  return sorted(all_scores, reverse=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all      |     0 | attn: 34.7 | offset= 1: 26.7 | 6-5 dep->head\n", "prep     |  3760 | attn: 66.7 | offset=-1: 61.8 | 6-3 dep->head\n", "pobj     |  3720 | attn: 76.3 | offset=-2: 34.6 | 8-5 head<-dep\n", "det      |  3337 | attn: 94.3 | offset= 1: 51.7 | 7-10 dep->head\n", "nn       |  3192 | attn: 70.4 | offset= 1: 70.2 | 3-9 dep->head\n", "nsubj    |  2804 | attn: 58.5 | offset= 1: 45.5 | 7-1 dep->head\n", "amod     |  2505 | attn: 75.6 | offset= 1: 68.3 | 3-9 dep->head\n", "dobj     |  1591 | attn: 86.8 | offset=-2: 40.0 | 7-9 dep->head\n", "advmod   |  1241 | attn: 48.8 | offset= 1: 40.2 | 6-5 dep->head\n", "aux      |  1237 | attn: 81.1 | offset= 1: 71.5 | 3-9 dep->head\n", "num      |  1091 | attn: 67.4 | offset= 1: 56.4 | 7-10 dep->head\n", "cc       |   995 | attn: 47.8 | offset=-1: 41.2 | 6-4 dep->head\n", "conj     |   995 | attn: 53.3 | offset=-2: 26.8 | 6-0 dep->head\n", "poss     |   708 | attn: 80.5 | offset= 1: 47.7 | 6-5 dep->head\n", "dep      |   617 | attn: 18.3 | offset=-2: 15.9 | 10-10 dep->head\n", "ccomp    |   557 | attn: 48.8 | offset=-2: 12.4 | 7-0 dep->head\n", "number   |   481 | attn: 74.8 | offset= 1: 73.2 | 6-3 head<-dep\n", "xcomp    |   433 | attn: 57.3 | offset=-2: 47.1 | 4-6 dep->head\n", "possessi |   424 | attn: 98.8 | offset=-1: 98.8 | 6-11 dep->head\n", "mark     |   420 | attn: 50.7 | offset= 2: 14.5 | 7-1 dep->head\n", "cop      |   353 | attn: 72.2 | offset= 1: 40.5 | 6-5 dep->head\n", "auxpass  |   286 | attn: 94.1 | offset= 1: 82.5 | 3-9 dep->head\n", "rcmod    |   286 | attn: 40.9 | offset=-3: 26.6 | 8-10 dep->head\n", "advcl    |   285 | attn: 29.5 | offset=-3: 4.6 | 4-3 dep->head\n", "appos    |   272 | attn: 30.5 | offset=-3: 28.3 | 6-0 dep->head\n", "nsubjpas |   254 | attn: 57.5 | offset= 2: 43.7 | 6-5 dep->head\n", "tmod     |   243 | attn: 45.7 | offset=-1: 29.2 | 7-5 dep->head\n", "partmod  |   216 | attn: 59.3 | offset=-1: 56.5 | 7-4 dep->head\n", "pcomp    |   207 | attn: 79.7 | offset=-1: 79.2 | 5-9 head<-dep\n", "quantmod |   203 | attn: 68.0 | offset= 1: 60.6 | 0-3 head<-dep\n", "npadvmod |   186 | attn: 44.6 | offset=-2: 34.9 | 1-5 head<-dep\n", "neg      |   167 | attn: 72.5 | offset= 1: 68.3 | 3-9 dep->head\n", "prt      |   116 | attn: 99.1 | offset=-1: 91.4 | 5-6 dep->head\n"]}], "source": ["# Compare the best attention head to baselines across the most common relations.\n", "# This produces the scores in Table 1\n", "for row, (reln, _) in enumerate([(\"all\", 0)] + reln_counts.most_common()):\n", "  if reln == \"root\" or reln == \"punct\":\n", "    continue\n", "  if reln_counts[reln] < 100 and reln != \"all\":\n", "    break\n", "\n", "  uas, layer, head, direction = sorted(\n", "      s for s in get_all_scores(reln))[-1]\n", "  baseline_uas, baseline_offset = max(\n", "      (scores[reln], i) for i, scores in baseline_scores.iteritems())\n", "  print(\"{:8s} | {:5d} | attn: {:.1f} | offset={:2d}: {:.1f} | {:}-{:} {:}\".format(\n", "      reln[:8], reln_counts[reln], 100 * uas, baseline_offset, 100 * baseline_uas,\n", "      layer, head, direction))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Qualitative examples of attention (Figure 5)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAUMAAAEICAYAAADFrJaoAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi4wLCBodHRwOi8vbWF0cGxvdGxpYi5vcmcvFvnyVgAAIABJREFUeJzsnXl8nVWd/9/fu+RmT5o06Zo23eiS0oV9b0FUEBAQyqKjOKMijhsgKjMuBNQZHEcRRQdxdOCnKAKyiSB7oRQKtKVbuqX7krRJmqVZ73p+f3zPyb0JCbTQJcl9Pq/X87rPfbb7POf53u853+1zxBiDBw8ePKQ7fEf7Bjx48OBhIMBThh48ePCApww9ePDgAfCUoQcPHjwAnjL04MGDB8BThh48ePAAeMrQQz8QkbtF5HtH+z56Q0QqReSP77K/SkTmH8Fb8jBEEDjaN+DhyENEtgEjgBgQB9YC/w+4xxiTADDGXHeYfvuzwOeNMWccjusbYyo+6DVE5F5glzHmux/8jjwMFngjw/TFRcaYPGA8cDvwbeB3B3KiiHidqIchB08ZpjmMMS3GmCeAK4FrRGQm6OhIRH5o1+eLyC4R+baI7AH+z26/UERWiEiziLwmIrPcdUWkTEQeEZF6EdknIneJyHTgbuBUEWkTkea+7klERovIEyLSKCKbROQLvQ7JFJG/iEiriCwXkdkp524TkXPtuk9EbhaRzfYeHhSRopRjz7D33SwiO0XksyJyLfAp4Fv2Hv/2wVvZw2CApww9AGCMeRPYBZzZzyEjgSJ0JHmtiMwFfg98ESgGfgM8ISIhEfEDTwLbgXJgDPCAMWYdcB3wujEm1xhT2M9vPWDvZTRwOfAfInJOyv6LgYfs/fwJeExEgn1c56vAJcA8e60m4FcAIjIeeBr4JVACzAFWGGPuAe4H/sve40X93KOHIQZPGXpIRQ2qYPpCArjFGBM2xnQC1wK/Mca8YYyJG2PuA8LAKcBJqPL5pjGm3RjTZYx59UBuQETKgNOBb9vzVgD/C3wm5bBlxpiHjTFR4GdApv3d3rgO+I4xZpcxJgxUApdbM/+TwPPGmD8bY6LGmH32tzykKTzfj4dUjAEa+9lXb4zpSvk+HjWrv5qyLQNVgnFguzEm9j7uYTTQaIxpTdm2HTgh5ftOt2KMSYiIG0X2xnjgURFJpGyLo8GjMmDz+7g/D0MU3sjQAwAiciKqDPsbwfWmN9oJ/MgYU5iyZBtj/mz3jesn0PJeNEk1QJGI5KVsGwfsTvlelnLfPmCsPa83dgLn97rHTGPMbrtvUj/34FE5pSE8ZZjmEJF8EbkQ9dP90Riz+gBP/S1wnYicLIocEbnAKrE3gVrgdrs9U0ROt+ftBcaKSEZfFzXG7AReA/7TnjcL+ByQmlt4vIh8wirb61HzfEkfl7sb+JH1DyIiJSJysd13P3CuiFwhIgERKRaROSn3OPEA28HDEIGnDNMXfxORVnSE9B3U9/bPB3qyMWYp8AXgLjQwsQn4rN0XBy4CJgM70GDIlfbUF4EqYI+INPRz+avRwEsN8Cjqq3w+Zf/j9npNwKeBT1j/YW/cCTwBPGufdQlwsr3HHcDHgG+groEVgItK/w6YYaPMjx1Qg3gY9BCP3NXDUIKI7AD+yRjzytG+Fw+DC97I0MOQgYiUoGky247yrXgYhPCUoYchARsAqgZ+aU1gDx4OCp6Z7MGDBw94I0MPHjx4ADxl6MGDBw+Apww9ePDgAfCUoQcPHjwAnjL04MGDB8BThh48ePAAeMrQgwcPHgBPGXrw4MEDMICUoYiUi0iniKyw30eKyAOWsn2ZiDwlIsfY49b0cf4pIvKGpaFfJyKVdvuVljr+ySP8SB8IXnscfXjvIIm0aAtjzIBYUJaSNXZdgNeB61L2z0Yp6buP63X+BmC2XfcDM1L2zQeePNrP6LXH4Fq8d5BebTFgRoa9cDYQNcbc7TYYY1YaYxa9yzmlKIceRmno1/Z1kAgjRQYmV91o2ZnXz67D1h4eDhiHUyZPFBl4/0URfCJIH7uGpDwOuBdgMRNYdpDn3AFsEJFHReSLIpLZz3HzgetFCAygJZglHaPAzDsK7eHhwHA438FPgEki+AfQEsinYXIO+4cd4bY4ahioyvCgYYy5DZ0n41l0sp9/9HPoGmA6OsFR3C3GEDuSi/19g5ocWSE6Ruez/0BZpg9le3g4TDiId1CFKhhQmTBAwhjiR3JBZTKBymShkIgNp7HpCLfFUcNAVYZVwPEHe5IxZrMx5n+ADwGzRaS4j8M2onNoZKIvHWMwIohbPsB9vyvs9f0i3RNx+e19ZOaz3wynoa6fUw9ne3g4MBzOd7AW7aCBpPxZM7U/U/WQoA+5DwI5mbSa4TS3bDPlfdFaDUl5HKjK8EUgJDqhNwAiMktE+pvTFzv/hnuhU9AR3zsmKTeGCLAVmJo8F3F+VPf9UAlgLwXoIzkizUQFzwAZpdS1rWFOVz+XOWzt4eGAcTjfwWpgBqoInfIRY7qth27/3SGWy+7BgP3tIDrDoa+AlnACX1rJ44CcKtQYY0TkUuDnIvJtoAtlL77eHjJVdHpIhxuAy4A7RKQDiAGfMjoXR19YDcwCVroNvQSjx7be298L9jxBlZ/RRyJu9wVQRRi1x+QU0kgmkfYW+p5T/Qi0h4f3wGF+B2tJds5OIRoR/Ki57L4LdCsxczAyCf3Lc0pH7QN8I9gTzqXVl0Giz6leh6o8DkhlCGCMqQGu6Gd3sI9tDx3E5auACpI+O0j2wOIE5WAVo40IOkWYQH2RqdcI2XvvRNs+CPhGUWPayGl/N+E+zO3h4QBwuN6BMTSIEEbnfnZTojoZ8olgjEkqRege2fm6L9G/TPYrt3ZfkKSfMAhEgkTiYbKzWsjqa5It94NDTh4HkpkcBwpcUuehgohcCfwanUnNYQ1qlvQ4FJL+w97XSU1JSvWz9DKDBe3JYynC60ycbLS926G7Jw4AiTHsireSH+71k0eyPTz0jSP5DtYBx7pDsB11ikXR479qZbFfM9qtp8ptz3vAjyqtOCqLGUAEMPsoMtl0+hooTT1nyMtjWtL+W8W0HE0UjaTugqTCg75Hgf2YwYl+fsuZxRFjiIiQYc/JAqSAZjmH57IeZcHe/q7hYehDhK+jyumnJAcpCazv0Mqcj6TZ3Nc1XCf+rma0CEF7TBQN4mWj1op/GmtjMYK+LDoyVpnZbYfo8QYFBtLI8IjBGDpQc2SK3eSUm4Fkjwo9zQzb+/qx7WZHgHEnrL0d3CJkAiGgs5cidJ+MYRf7KI667x7SFlXANFI6WKxMpsijM5u7/7epcpcyCkzYfT2i0XY9A1WUEftb2UCH+61W8hJNFPg7yezTXziUkZbK0GIN78ztchG8HlG2XukwLv+rxyiulxntEyHHXqfDGOK2Nwa6/YoAiWPYKK3k9+ub8ZA2WIcqQ7/93sNVk6LsnNnsfzcz2CnFFMXofNQxY4hZhZqDKkIfYI5naXw3Zb4S6mmgxFOGQwUi0mY/y0Xkk30c4hJdXRt098L2M5DqB0R9Ju8ZwbNKLxs1izvtNnedGDpSjGMV8DD2+eopjbzTp/Oe9+9hEOEA3mcNajEMJ9k5Q4p8plgeqaPEd021sfud/EVIRqlzUNMYey25kCfjgOTQ7htNbaLndYa+PA5ZZZiCcjTjvTdWo4muqdFkF9Rw+YBuFNgjeNLXj1hBzUQFusMY3GjPh/b2UayStN8poc5ECZhdjHu3kWF/9+9hcKKcPt6n7QzXooG91A7aWSrdmQ8p8hiHHlHlHrDbg2gnHk3pcJ0cJlBZlHksjN9KpQ+QBH7WUtFfp9/n/Q8FpIMyvB0401IH3ZCy3WX9+1MW0NFbd6SuryheH75B54QGVYSJlO0BVPCcn9AJeGIWK31bmOR6+YO9fw+DE+/2Pp1Mpo7+uoMizoXTK12mW9Z6yWQAlT1X/ulGiTlA1BjCqMzHAAmTYQDfZKpppqDfIM173P+gRjoow5uBRcaYOcaYO0TIFqEEaAMagAkkzeDuXjjFaf2O0WCvSpUMNDIcNoau1HQakorQh5rHEVL8hVOo9jVR1C2oV8iDfb2PHvd/yFrFw9HCO96nCOOtjLkgSmpUGHoG9vpTiN1mc2q0uJdvOxu1drqsskwA/gU8GPsozxqAfFp8LRT2GRDs7/6HCtJBGfbGVcB3UWFZiyZfB+g5QvRDt8/QDxpASV3s9hxUyYVBe2O7BNF0GmeG5KJmsquH9gEBP3H/NsYngIwsOkYu5IzZR6YJPAwwPId2yutJWivOz+d8fqAy6bfberDMkAzMORlzytEdk2OPCaeMGgF8dZTKrVQGAF8zBe5aeZk0jwfyD/vTDxCkozJchia3xtGe2DHYuNFhDO09oyQTqB3LTNyuG2xJnTG02nPcPpfEGrbXCKCKMGYXA0QnUy2bmRDsJC8DyAkQzZ7AtgHH8ebhiGAZMBetmS9AO1lnJhuSVosL4sVT153vECt3qJyB9XlD90ixzV7HR9JiicxnoRs9BiNk5DYyPAvIKKS1Adh/WJ98ACEdlGErkEqauhYYj5q2rkC+N1Kjys5p7cwTZxZ3Wb9LD7MZ9Q3GbDqN337vJOkzDADZpewN7SfPKUd/OVv2ncaSvgIpve/fw+BGX+9zGXC8lbENJFO+IBlZ9qWayCkuHJc7KMYQSUmncSNDRwjiyj2deSyAGc+24K1U5gF5w2j0xfF3AeFi6tpjhDr68B0OWXlMB2W4CoiLyEoRucGO1jaiRA1rSNYo94YTuFTfSapg9cjDsscG0RFi3B6fjRaxZ6PKMAfttTunscFsY4IzbSLTWd91h7mxr0BKj/v/oI3h4aijr/e5jCQl1lqSfsMeFSXWD+1GdpBS0pmSvQB0y6Mrs+tw+a8p180HctrICaCjyVb3Q0BcelZmvdf9DwkMWKKGDwpjTK79jALn9Nq9AjjOGJaI0AmMBRzLhlOM3aNDtJ1CJEvqfCLvIHJwmf2xlKidK4LPQGuSfYDJpyU7QNRfw7g4kDiepZFOsnoweLzH/XsYZHiP97kcmGN9eVXoXCKQjCqn5hy6EWFqSR0i3SNH7PcAasG0A4kUi8Yp2K5bqOy6lUpQOQ0W0mQaKUrMY2E0TIZZwmnd8p0O8pgOI8O+4Hw00JNluBtuVIgqshA6wovafakpDy7R2glpJmpGZKL+lhia5e8c31nHsjq6lXLsOZGP8o/oLsoGFJ2RhyMHY2hBk66nkaxEgZ5VKKm5hi644uSxR9ZDSqpXJ6ro8uwSR0eAnag8g8p2AIhl0y4tDItsozxRy+i0Kw9NV2W4Ajg2JZ2hgqSwkWJSZJJkmnH5XN1VACn+mhCqNB0LiEGJKxNYQUs5piuHtsw6SozdHhtFjTRSnHbC56EHlqKm8iZgFJqBkEoxB8nOOYYqQl/vQgArt4Uksx18qOJrNYY2+z0xj4W+W6l01VARIFhIS3QMO+PbKZd2sqUPf+GQRroqwy3AMKCYJJ2XEzyxJkY2KnThlOL3VAEJosKWgwpTpy2/S010zUCFLR8V3g4gNJbdZhdjY0B0AQ/Gn+Uj9EOv7iF9sBwNosRQhTiVnorQpdukBu2MNY+7A3PAGLQTbjeGZpIld1FJsi2xnmkhVCYNEBzLzvheRhAg3k0Llm5IS2VoFdtq1FSuIhlRFnQEF0KFKAo9Kk4cL2E2yez9ZvtpUvK3uqypkklyZBgGQgU0Sw5t7GNEDIifxmti0lT4PPTAUuA4K2cuiAIkS+qgu6ROUhRblggFqD8wBNQBLSSVX4AkyXAA8E+mWvYy0inJEBDJpt0fJSO2nXJuoZIGSo/IQw8kpKUytHgbVYY1qLCNJJnf1UGyRM5Vj2Tb4yKookxAd8VJ9yx3JHvifHRUGEXNlEwgOJ+Fse2Mw+5LDKdBFjHf8xd6WA0cg8qJ8xs6RdidV5ii4PJRUzqBymsctU7CKT7tbl5ESZluoppjXOQ5iMpnYDLV0XatKJV7+SykYQed7spwll1fCxyHjt5cMqpjn3F+FWcGu+hwd+G7/cwkmVSdZ49ro2dVS3shzRl7GBVF/TbxLZT7+pv7xMOhg4hUishN7+M8x9YyWkQePkT3Ml9EnkzdZgxdaI7hLJI1ys4sdmlbOag/MIB2uvutTGbYa3SmXpKkVeIqojpvoTJeyS2gcu3yEUkg/gZKD9hEHujt+X4wZFNrDgAr0KLzbLQMahIpDNRoj9lJkvna0SW50WE3bDTZjwquK8PrIjkXhEtxyJjGuvijXBwDzJf5lTzFeR679SCAnfPj8sP8M8uBE4H7gHEk/YAZqDyGUTlycAE8gM6UZGzsuW4WxgSaFhZDbhXBpAb6fKezKFHHSDealBzauwkgDheOUHseFAbkyFBEviciG0TkVRH5s4jcJCJzRGSJiKwSkUdFZJg9dpKI/ENElonIIhGZZrcvEJE1Njn0lT5+Zq/9HI/6DY9FFVoEFbgI9KgsMWhvGkthtHG+myySdchRkmaNS1voAAIT2exrJ8fsZ1gCiIcJ+RZydj/TBRyRNhjSEJHviMhGEXkVO/vcu7TVCNumK+1yWq9rlYvIGrv+WRF5xF6nWkT+K+W4j4jI6yKyXEQeEpFcu/08EVkvIsuBT/Rzy8tRCyUG1KIyadB0mHZSfIYpSf5+VL5ckrVLrUllTMJekwrWuAGQc83E6in1tZEngFnAg6ylok+exEHYngeHbqK+AbKgPeMK6M7XqwZuQjPf59ljbgN+btdfAKbY9ZOBF+36amCMXS9MXt8ImDwwRWD+BOZKMFPALALjBxMA47OLpCwhu19SruVLuVaG3ZZjrxECU5xyXn4l38+6nAfywWSA8T/OhcFevxE4Em2QDguaprIaHVnloxHam96lrf4CXG/X/UCBXW+zn+XAGrv+WTQjocC+o+1AGUrM+gqQY4/7NvB9e8xOdJoJAR4EnkyRoxCYXDCnglkNJhvMj8FcbWXJ30tOxMpQHhh/L9n2WRkM2e9+MEEwvhmsCdrzfPa6geHs9Z/FSxmF7AuAkfFslZTzfGBCg6093+8yEM3k04HHjTFdQJeI/A3rKzHGvGyPuQ9wvcRpdt2dH7Kfi4F7ReRB4JGU638azSu8DY3gVdj9w9DGb6IndRL0nE4xINKDJDMH2Ic6qbPsdkHTdprQ3joXSLRQkLGOGQnAfwav8CeudhUFIWBmkI7jRbLvPgJtkA44E3jUGNMBICJPoH+i/trqHOAzAEbn8215j+u/YIxpsdd29e6FaGbCYnv9DOB1NBiy1RhTbY//I3CtriPAM8CX0cBJib1ONToyfIrkKM5ZEc58bgUy7aO4IF4uST9jFskgSeZuxjjLJ2DvLRYm5GukSJopAoi2kgsquyf5abvMh+9lyP7NYGnPD4KBqAwPBj6g2Rgzp/cOY8x1InIycAGwTESON8bsQwMnF6JCsQL4Kipk69Bo3mJ3CfsZxCZHm2SCqzONXSqDE1KXkpOHCqqbfcwA4VmszLyDG6NAvIAW/yLOzESV3Ew/kWHDaXi4xoxLJOXqsLVBuqLftnofSJ3aNU6yKuQ5Y8zVqQeKSL+/Z2VqFZofuBZYiQZP3gY+QjKzoZuWy/5WPXamvBRarxA21ctofbxjTEqMZ1tgO+UuOChA18m8zhuc6p/GetYwKwTMamTYccD4AJHEMBofr2fcg+/SBgOuPT8IBqLPcDFwkYhk2lHPhai/pElEXM3mp4GXjTH7ga0isgBAFLPt+iRjzBvGmO+jglNmz12P1iJnoMqwAlV4riwvNes/CGBMd/2nS2vItp/7TZI+yVUGuNGhm2gnAHR+hGcCuxnjeuvhuxlTWM/wPHS60rYR1L6QQaL+CLVBOuAV4BIRyRKRPOAi9J302Vaoufclu90vIgXv4zeXAKeLyGR7nRwROQaVuXIRmWSPu7rXeSsB9wdfivoNN6JmoGNgd3LpZrNLJXl1+a1RLJ2/rUTx2+MCn+XedxAv7GasL0hk+qNc8ingmz7ikwWz209syXTWPZBJ/HGTDMgMpvZ8XxhwytAY8xbwBOofexr1U7QA1wA/EZFVqODcZk/5FPA5EVmJKrSL7fafiMhq66R9DRU4p9g2ALOMoRFluz6GJJ2XE6IglhYpxZkcIml2xI3p0Zs5Z7YhOb+Eq0DJbmJY0b3883BAimhoqmF0cZSsmeiL3TCJLW2uCuVwt0E6wBizHPVbrUTb8C27q7+2+jpwtoisRmvX+6J2e6/frEf9X3+27+h1YJp1d1wL/N06/Ot6nboCmG0Vz3LUP9eOBvkmkBzNuUmcXBK1Gw06yyTmXGCkKMJbqIxUmkqn1AQY7SP28V2UfdcgZxlkI3CXYHYbAs2zWbEyj5bNneR0M+EMsvZ8XxiQk8iLSK4xpk1EstEe6Vr7Mg7R9bkBTZj+HxF+hTbycuAu1DRxwudoulweoUuizkVpkRxlv5sIypHAxqC7UiUIRG/l+533ck1kK5Mkm9aTANNB3iqA6VSFM4hEVpi53bRgh7sNPAwcWPl5CZiPJv8/h/rF/htYBPwdlb8I1uwlyc4eJUnL1T2HNyqPSjKMGMHko4G52UBQiL1tCLz9Ge5tfoArx0TImhYgvLWMXW2X8sj+h1jQsZPy7qlG0wED1Wd4j4jMQAXgvsOgBN4GrrTrK9BE1wdRQcxHh/9O6IKoYEVImsFh6OYsdKaLYxguJOlTbABiY9nJTFb7tzKpBJiVQ/vWOIGtHeT5AamgKvocH+ldhXK428DDAIHROUk2owGTZai8jSRJIrKQpHw5iwOSc+r4sMQNKWazDzBBInMEcxzqIlkNPDqcum0NlBog81EunRMhqwBYmktr8PP8tu2PfDqSwJ92NcoDUhkaYw73VISrgB9Yv8oK4DL0xVejwvc6dJO5+kjmcQXt9xhJv2J+ynGFJBWpY63xXcGDciM/qwCGh2h/ay5vtz3L+dhrdBqM9K5COQJt4GFgYRUwxxiWinSTva4DziWZcO1kMjWg51wxqQGW6W6J498BvAn8EQjPY6EPkJcpzfcTOzVKoA5YnEVH4bX8tnUb5bF1VESLaPCZXgTGQx0DUhkebhhDowiNwGSUtWYyOpJzEeXXSaYodNjTnCC2kUyHKUTTEHbb45wT21UMhIDQH/j0SRGCXcBLI6nzN1PkFGn0Gu5NLGeOL8VR7SE9sQINlIGODk8A7kHlMUKymsnNzeOIQdwIrhgNvJyCZjK8VcS+p/YxvM1WnMQA6SJk3uDUicAxubStG0VN7XpmDL+IJ5rHsSPxX/xbDEiMYK9PM83SBwMugHIEsQqYa7SecyvqS1mNjgyd0ouS9B9moz1wHpoLlosKaDVK4uoo2Z1CFDRd4qxx7KhJ4H8DMMNpCGxmgiuFis1hBXsZkTZ+GQ/9YiVJjk2nDFvsMpHkJE5AN99mDuoH/Ffgi2iH/gfgTuDVD/FCu2CcJcMIajPe4oST0GyKV2ewtnYr5cWFNDZ/mbsSd3NdHEiMYaes638S+SGLdFaGK4FZVvhWoWaFM5O7SKndRLvIYlTYYkCj+zSmW0Cd0nQmy4nApBHsef3f+dHWVvJ9QLCMHZF9lPqA+C1UxuL4fHWM8pRhmsMY9qKWSDlqrcyFbvKGY+gZKJmBRllvtMc/B/wEeBbYhvX1zWBtagS5uJ6SsxME29CAXNd2xhWFye74B+fFHuGS+BpmG5KR6bRThmlpJlusQHP1MlDFeBqaOjCOZHF7Lkm6/jqSiaE5JMkYsMc4J3YuSUF++XIe9j/Fx3xAYAKbOzcwzdF8JQR8d/PFw/2cHo4SRKQQ+KQx5tcHeMpK1Ff4DOpznooqxqloh30cGmSpR4OAf0Jlsjuy7KLJlsnadc7H+IhNTBBYicqxZNJZavB1fJ57OjKIyINcZeyxkk3HO/yF7+NZBh3SeWS4BR3tlZKcPzmOTgx1MqokW+3SgAqdK62LoywgLgLspgEdjSrVbWgeljmFJeZVzggAXSG6skzS1IkX0Ey9RvU8DE0UoibsgcIpvBia6nUK6ne+HLgKlcW7gP9DE5LDJDtiN68yxmDayHXpNacDwwtpfgUlfwAoEkz8LBa2nsSbgZv471gtYw1g5rHQtPU9E+jBPsugw5BQhiJyoyg7yxoRud4yYqwTkd+KSJWIPCsiWb1OC6IJz7NRxViGKrMqdA6K1MmzXS+ZSbInjljWGjcL2UyUBmwRGlCJAbKaY3P2MqILMEU0+tdTkQDMKbwm2ynztTAsTrKS4IM+k4eBhduBSSKyQkR+Ypc1NhH+ytQDbUndZtRNMxOVsX9BXTIB4Kdoio3zZUOSJ9PxaBoR5Ap50LeME0rQvMWGeSx8rZHhXdA9Raj/Qp5smMfLwUe5JLqZyd1VLpuYJLWMNr3l8WCeZbBi0CtDETke+Gd0NHcK8AWUdGEK8CtjTAVqclyWPIcsdFS4Hu2Jo2ivPBo1VWaQrEJxhJnOXwjJNAex55xij1uMVg4YwHcsqzJHs7uzmaJYHs2ZQWKddl/sRu7gVZ0R0r2DboX4fp7Jw4DEzcBmW7u7BK0amo2my/xEREZBtyI8Dk20nov6m59CrYj7UNkaQXIC+NQa5QhJhQjge4xLZqLm9JvAxhLqHQVdPjpabLmch/wRQjzNhaaQZpe/SIBY98RovRTiAT3LYMagV4bAGSibRrsxpg1lZzkTZbVYYY9ZhjqaEeFSlHqoC02hmYAqsBXoy12L9s65qBC6lJoEScYPV243HTgVNWmqUIH0oeZK7jXcF9nNmBiQNYEt8To1ieMAuxkjdZQ4wc4DTvER/rYVwIN6Jg+DAmcAfzbGxI0xe4GXgRPt+74H+BAawHsBzWp4BO2UgyRlEnqSgjhF6LZl+4nNi5KRjY4iG+exUB5mgQ/tzLOAhpHUmiw6gpVURgDalKkmAVCo5DICFAZom54j7SMO9Fk+aAMdbQzlAEpvFgxnUm4AzrfbtqMKrQjNMTwPeAh1WGehwpaN9pSd6OgsjvpP5qL8a4tQ0yWPZDF9AJDtjPf9hSt8QCBGMLqDMgOEi6nnHr6Qs4MJpWilQSnE8wLEng2bkHkX1pr+nsnDIIXNF7wP2IEGRtagiuVNu202StpwLCpr3fMc07M6qgyY5SOIjyWFAAAgAElEQVRRHVdzW4DgJiaDBvzy0eBJ8GIey/4zV0eaKcoZy86wNZNNDq25K5lTBGTn0BYXuhrbGN58hJriqGMojAwXoWwa2SKSA1xqt/WHjWiqTBz17e1A87jeRgWuFS2QL7THdKJ+my6SVP4n220vGUMNqgxdgnYC6DiepU3/xn921jEyMYrd+/LZH24nvwMoaCenYhOTZqO9eiOwPYfW54poXvU+n8nDwEQrdEcjFgFXWgaXEuAsVOGBBu1yUfl5E+2gHWnDHHRkOJlkek0HKosGlbvpwKRcWl+NkrHOHhedx8LIbsY6godaoPNa7jansqTrNU7vAKJx/IJ28hOiBAuB1hLqNl3EExvaGF6bQkZyoM8yaDHoR4bGmOUici/Jl/G/KKlqP8cTE2EDcKwxvC7CCtRhfS86CpyACt9cVEhbUaWVgSrNsaiQdpH0JzrT2EWV49/mx/I9bgsB4TAZxauZOQ7tnfcVs29nE4Wbo2SWAoFMOvbk09ZRy9j4+3kmDwMTxph9IrLYsgY9jfqlV6Iy8i1jzB576D5grB0lrkaVW4LkxPKv2G3ONHZZDAVoB94CvHI+T8ceNFcYEZhBldRTEkALBOqA2OU85JtJVfA5PhTew4gRQF49w12HvLWAFoFm/p3bozfw8x65rwfxLIMXh5pufTAslk79i3b9Y2B+aqnO/wLmEjBfBvOflj49A0wBmHPBnGbp1LPsPsv6YzLttnxLl541nxdHFFF/PJhz82g6O0DnZDAZk6j2l1I7HswxYMrBlE1jTWYB+7KPdrt4y1GTRwHzkSTFvnkUTAWYuWDetPvfAjPOyl8QzGQwF4EpA+Ofx0v+W7jFyaNMpDoIZpSVUx+YwAU8PrKIhqmF7JsJZgKYvCms99nrZU1ndegl5vnn8VL39AJHu22O5DIUzOT3g1Voj+rWZ6KjujWoo3ozmiYTRnvWD6O961KSUWRH3+Um3nEJruOBEzYx+dRW8iLA0vm89GaMzG2Aby+lk8KE3DSicaAujs/fQlFaFcV7SMIYDDryL7abVqK+wg2oPBWg1soEu/9ENLNgMWr++uazMHErlQDMoMq3h1HFqIwJMHocW2ZUM2VYmFDjVDZUAztns6Kjmql+IONj/D1yBq8lFjI/8TLzU+8rbZCuynAjMEaEXNRv6ENN4KUka5RnoNRes1Bi1CqS8ymnEmpmomkPM9AE14JCmrbfwq0vRgltmkR1bDdlUdQHOX4Mu+sjBF0Cd8M8FkbalMqrm0jTQ1piH0ll6Gjlomhgz/kNj0dzB2NoCV4zlq7rSS70Y9OztjBhVAc52WhQZdRw6qNf5J5texhVPYraliBRA8g+ih0LU8dxLPM9yiXdCjUdkZbK0Cjb9UZgpu391qLKbD3J2mSDpq68hgZU3IgwjPoK81Eh/Sg6eqxHUww2foRnmyexOQwwipr4BqaMAwrHsW1rlICvk9xs1E8TbaYg2EZOPN16YQ/vQD2a1QBJy0VIBlGa0EDFOru/e5Iog0SWcYIAxX6iJ3WRXYbK6HZg0/Oc27qNiZ37KZRsOgI7GWeArHz2x4HoS8z3/YPzEg1pXg2VlsrQYhXKEuLmPzkWDZjE0OjtauieTN6RvAZQ8+Rkknlfz6M5f432e+gG7ojcyzUJIHszkya3U9ABbI0QzGmgJB9VrnEgEaIr0Mqwd8xP4SHt0AYELeu1iy6PJDkxVAsaFa5HZcdNT5GVRcdYVC5H+InvRQNvjUD0e9yatYLZsd9ybRwIFNEo2ykPAZ0l1Mcu5yEBEjsZd0QfdiAi7ZUhmqu3Bo3WuQhyGBWoClQRlqGUSq5meSUqpNVotDlhzwkBkZXMznyAq8b4iYweQ81OVIAzBTPcR9zN1xC5gZ+xhYkuEu0hjZHiNyxFO+E1aPVREGWt+StqRufYbWOAySG6hneR1QHsyaZtb4TM7dg65Tu4Pj6KWv/N/CfYicyCRBJAdAZV8RzaZDMTTSWV7GUkJFma0s5fCOmtDDegZnAeak5UoMrsabSX3YsK45lorfJGtGJlNTaPC51HxU2+4wP4GndG/pfPj4iQmTeTtdsbKI4AWUG6SsvZtruZYldQL1XMCDYwwpCGdEke+sQ+1OUCGhj5MPAkdJePbkN9huWowtt8M7dvQeUpOIbdTU6JjWebmcfCjJc4u3MPYwCyimkI7yc/AfjmstQH8DbH95Y9Ny1p2mHIKEMRKRSRg2HVMKgSPINkGV4IFchzUGVXBryBjgIb0PZyEz8B3ZPvuDpR/6NcOqWNnE6g+kL+1tWobqBRJTQ020m8BU2IlSoq3Ex7vUkaDvZZPLxPiEiliNwkIreJyLlH+XbaUNP4ZKCGJMX/euASNMuhGNgE7LmFyuitVGYDOfNY2FDNVKfYfF/hF4EqKhIPcaXLdoh+lbs6GymKl7I3vouxgac06OKrp6S7HtmhD6IGeu4fUO12SDBklCEHQTFkWYJLUBP3JJS1phYt01uHCuSzaCBlBMlJrZ1J202XhI4KQ6jze+QlPFYbIFY/lXWxTUzy7WfYKKD2c/y+vVnnOYkDIQP+JgqdEPZmCRnydEkDDcaY7xtjnj9av2/Zj/LRKHInGoybiZrNb6FyuAStREkA3M11WWjaTV2YDIPtnC/lEf9HeN7/ae53RRURIDGDKlNMI/m0GPCHgcQ8FrJWO+Vu64YUc/m9cLTb7VBiKCnDg6EYmgh8DPXRlKOK7kV01FeFKsaJqCk9iSTtv5shz02+45hARgOB41laPYtVsTXMimfS6XuBD40CmsezrfVvXBDcz7BOe42wgL+DPB/JdJ3UdzHk6ZKOJkTkOyKyUUReRevQEZF7ReRyu367iKwVkVUi8t922wgReVREVtrlNEurtiblujeJSKVd/1rKNR6w2+bZd7pCRN4WnYzddc4no/mDu9HOtQWVvzxUCc5GO+rpAOPZ5t/LyGJg3y1UxpZwmg/gFF6TuSzLvIo/g/qxneVhhtHk8xOXKBkul9DJcZSkXHfP/23va8C226HGoC/HS8HNwExjzBwRuQy4DhWg4cBbIvKKMaZWhA+hE+/8A40En4aODPcC16NKbwWa0+WmanwNHdF1oYLqRoeFqH9xE9D6bX4s2xmfAHz7KB7ZTk4r0DKMxowoQTeNAPNYyF5KBR0BuAmkJmRJ65wu8v56oM9yuBpyKMPSo12FpqsE0NSVZSn7i9FsgmnGGCPK8AzwC+BlY8ylIuJHo73D3uWnbgYmGGPCKde4CfiyMWaxiOQCXdYi+E80oPcyGiAJGUODCG+jI8UXgDtQOR0D5NRRmgs03UJlZKFNkgaYxOb8VcyOr6PC8RcGgMQZvBJ/nnN8izkjPoMqZxLHw2S4lDHHqBQHpIwd5V/kzu/eJoGN3zc/vn2gtdu7nP++MZSUYSq6KYaAvSLiKIaeQKN0gvoBBY0en2K/d6ER5o0ov9yrwEXA42iqQhHaZgZVTCUk55zI/ynfCLWREwkSHmnwmU5y24BhLRSE9lLquAzZyVjfbsZkor1wLjAiSGdJBpFtnYberDXv9iweDh5novRoHQAi0rsdW1A5+J2IPIkGMED9yJ8BsO+iRUTe7U+9CrhfRB4DHrPbFgM/E5H7gUeMMbv0HngDlblOVL4mirATzVY4156fQE3kHX5i56DZDhk/53p/i7pbfGPZmbmLMf6XOWc/aj4D+LNpMxEC8jCXJQB22Rp4gDXMdBRgBu3cJ5/EG8fdynfOW8IJHa9x+k8HarsdagwlM/lAUYeOAlvQYMlS1CTej6bMjEV9NMegPd9k1Gxutse0oUITRl/cDru9+evcuX8zkwJZdLTM5+X1QFMhjR3n8/TeDvKageZhNIT3MTwnTJYTCAPsHcOuxfm0Lz0SDeChfxhjYqgf+WGSFkR/cPyVDpkp6xcAv0I71bdEJGCMuR34PBodXiwi0+yx69BOsRHYiVoLcZJy2I7K6VRgi0GKO8neM4+FLXNY0QZ0jqKm80b+uzVAohb1he8H2gpo3lfO9o4Leao5RKwZaBlG036gdR4L97eRH0ZL/j4MzPs11x3zBBeduY7pj93FDd981lxUNYDb7ZBiKCnDA6IYsqkHbUC+XXcsIUIy+XotqiBb0BdQgPpR8uz2RlSpujmVffN5yf9XLsvvIjt0NQ/s2alzSgRHszv+mvpzioGJMYIjugjFUAUbAtonUb01TrBzpxnnSvKGPF3SUcQrKD1alvU9XZS605phBcaYp4AbUPcEqKn6JXuMX0QK0E61VESKRSSEnfdYRHxAmTHmJeDbqPzkisgkY8xqY8yPUUXn/tS70QAJ6Cit0Z5TZ7+Xox3z6cBqg0wAKKXOvMz8ECCvcVpiGSfEX+BcN+rzAeZsXvLVUZo4k1fNWipkLDt92yn3ARMXccZ5wNVAyXEsXd5JRtE/cf/Jv+QrP/smP30pj/bUEdhAbLdDiiFjJpuDoxhqQQMfjWivPBFt+BWo3yOC+gGnkSzV24y2l/t0VOkC+BsYnldPSSZQM4K9/IYv+YHiJgozaynzoz31ro/zt9j9/NNYdHRZB7SXs9W3nmmODuxgn8XDQcAoPdpf0PasQ/9cqcgDHheRTPTd3mi3fx24R0Q+h47avmSMeV1EbkM7p91oCgxox/lH+8cX4BfGmGYR+YGInI3KTRX6bjGG/TZYkYt21K5OeY+9z2mo3H00SOSPUTIumEy1/yGuCAJmKcfHAsQC9/PpVLIPuZGfmt/xOUqodz7CETWMmooSPuzzE1+fIPDiHkpGlbDvOy3kbfkoz/3oLU7qKKYucwR7O/SvMTDb7VDDUv6kF2wJ3lSgyqiP7ucow/UKtKE/gwYtdqE9tqNm34P2uC7FxkXqimaxctgc3t72DB/NyyCSv5PxhRl0NU5nXdNK5rYDZiw7/E0Mm9ROnkFHholKKjv/xseGLeOkJmO8SpR0hQinAE3GsEGEQnRktQS4ApXV+4BXSqibXE/pqzm0fbSd3P0T2RR/lTMD/8z/RZ7hPBcdDpzEG4xjR/xhFhQGiE4Ec0yMDAJE1sfI2DiPhW3tZMvzfOj8PNqvqWP478eye3WcYHuIrsKR1LRtMxMbjlqDHAUMmZHhwcAYoiLEgCwROtHRYQWaYL0BjRCvQPMO30L9F7VowCOIOopdBK4AKC1mX/TPXD0xn/2xKMF2YP1JvBkvYH9sJXMBcvdRNDaBvxUdkfqAjg1MydjFGFf/7CF9UYuO2Dago8MAmmWwCfgEsF1I1NVTOkVI1LaTOxJo/Az/L/MObkg8w3mO+cg/iprM1VSUvcnJk4HSALENPhLPx8ioP4Zq1lLBT/hGTgVrr/cTG7Odcd+cyLYoVq4z6SJAvPVoNMLRxFDyGR4sWlFF5ibcmYb68BzzdTOqIF9BgyiO7boNVVx+NM3h5GE0kkGkI0po+yf4626DNAPhk3mTv6s7ZGSA8KgpVO8Mk7UfFfSOBTxIE8NCexkTTsdaUA89sBcoseayD5XPMah1UoqaoW+TpJib+lXu9F/Ek/Gf8C1H9FHuJ3zeXoZf1UluGepe+cPTnL+4g5z68WyTtVSYGkZMnc2Ku+L4mn/DdddPZJubsqITyC6ksS2X9rTj10zLkaFFC5os3Yr6IW5CFdw64IvA31EBrEfTaEagJXl5qD8n266vvIMbGhspkrc4kfVMCzZR3J5Fuyzm1ADa23ddxJObFzI/iOaRNQPmOJb5f8lXfCQnlveQpjCGLhFa0eqnBtTHPBrNPaz2E5sbJ7BUSMw1+N4W4tPP4cWnzuKVbDQAODFIuM1HfHuY7Jewo0SSlVICSBcZl/lILGgn55fD2P8aKtsRVBmGQnQFRrGncQmnpZ2lks4jw05UMWWgaTLb0fy9ZjS9Zi9JNptqdErQMjTw0YqmO2wG9n6IF2QnZZER7M2MEUwA/iIailcwuxwV7J2jqJUmivNQITe3UGkaKAnVUOYmAPfgYQ8qY26QEkU73VUGmYJGlI8DNmfRefo/cf8l7eR+DEjk0/zY8bz1tzDZLl82BpgZVHELlf77uXJYFVNv9RM/fRdjvmYVYTEqe/uxVVaFNLU1MNyko6WSzsrQscVko4KwG02rWYUK5TTUX3MOqhjHAlvRAvps1FxuuYVK4ycud3BjoIhGs4eRYWBMmMwRXeTuAJpP4TUe4ZIcVAEngMR8FvqeVj9P6vSfHtIbdShDUiFqHmcBPj+x5QY5Nkik0+CblU3biXH8E9vJfR24fzS73rySv7Qv4Qw39YRL209UUhl/jItnXMwTd4YJbb2ch/7tUh5vQKtAAmjk2pWEBiqo6thPQeK9iBqGIgalMhSRhSJywge8TCZqGpSgvpllqFlSgiZSn4WODMvRDPgyVHmOQUeFLYCp5FbfdsabAppCObSylUmTMuhMTGODqygwOyjLbyfP1YAmABZxRsYmJiVI1oQeyWf3cBghIq/Zz3IR+eSBnYOgFkcJqtBiqFUxBcgz+M5J4JvkI755FLXLw4S2AIEpbOAMXuW3fDHVJDaA7yxe9n2cx67Jof1bLeT9rJj9v3+cT4S3UJ6HWkUNqBIECOXS2hUlmKhjRF9MSgf9TIMNg1IZflDYSZzy0Z5xBKrYtqPO6WFoSsNw+zkLDapUoP6chD0+ASQ2Mcn/M270A4VvcHIZ0LCAv9aEiDjBzKlgbbiVApeX6P8M9wb/zoXBCFnORH5HUbyHwQtjzGl2tRw4UMURQkeEe9BO+VjgTB/xswVTIyQ2xQlsyKF9yW7GzATfWh+x6ZPZHHiDk91osNvP9zQfLn2Gj/5HgNiU1zjtKzPYuAKVtZwoGdmoheNM4Swgq4wdnTWMdpeQ1NHh+3ymQYUB/Qe0vdB6EblfRNaJyMMikt3rmLaU9ctF5xtGRCaJyBJRppcfph6HKroJ6OhQSGb7V6M95lJU+bm5ilvRkqU8NAXC2PPiT/GxrMe4eEwGkYKJbN0EtBTSKC9wbgLInk5VfDS1LkASB+KX8VezimOd8PrstXpwyB3GZ/dwmJHS3rcDZ1q2lRtEpEJE3rTfV4nIFD0ePyqP41A5uwxVjn/3kXhlJHuqgOX5tMzyE3u7i+zZwDo/8RkrmGO2M8EVARhAdjPi1DNZ/HMDb/yD8797Nq+0ZNMhQGYmHYVdZDeQpOlyshyZwNbEZqa4aHYPkteDfabBiAGtDC2mAr82xkxHHb0HyvN3J3CnMcbNbQKACGegSdUR1BxeBzQbQ4tdn4aSL+SiDuaVKL3SNvvdCYqZTHXO//Cl6VFC7Vfxl81vc3x0OHWJNzkFtLeNX8tvw/fxWT+WEPYCnuRl5gc7yYmRNJv9w9h34qU8+Mzl8lDocD27hyOOm4FFxpg5xpg70ET+O40xc9BpJHbZDvA7KKVcLho9fhO1SrYCoQ6ygwFiq30kjm0jdxlwQg6tG6IEp9Uy2iks82euDDaS96Xh7PvCJib/6Mfc/OALKO9qDH8QKC6i0c2h4hRoEAiVUNduCRwigPkadwbP4vnyk2SRKxM84Gc69M14ZDAYlOFOY8xiu/5HlMXlQHAqWlUC8KeU7Q3oC69CgyAdQJEIOWhZ0GxUKNeSnEP5JDS3ayYqPFnA6EaKKjLpqi1ny/5lHBcEMsayK7uK6QVAVjF15jd8IcNeLwCECmnK/gOfziaZp3gacEMFq36eRVfLX1mQmmZzqJ/dw9HF68C/i8i3gfHGmE4btd2KMrS8hMpgACiaQZUP6GikuHQK1RvbyJ0WI2Orj/iYCBm7wTcDzYaQ5zhn3Cd49M44/pInuPjrc1i16Vk+Enqcj/uLaAjVM2Ik0OwjkSBpEWVj+Qsnsykex5dxD5/PfoRLSnYy+thGCsd2kNd+sM906JvtyGAwKMPeIf53+57Je2MjyaTpMGoKu3lMlqM+kSBJU3kzGklehTqzo/Z76BM8suESHqsZx87oMJrbgEgRjdJBXgxovJCnIvspiKKBlPA8Foa3MT6jntKJwNkoN9wJBTSP+zDPbS2l/qZeKQ2H+tk9HEUYY/4EfByVh6dE5By7awlqdTiS1d1AyQIeivmJ7xvDrpyvcNemGMHiIhqyDayMEipDA3kl9RSfNY9Xbm+i4KmJbP/BAv7aBoRP5K3EFiabdrKLgPpbqGzPotMFZyKACRAJLOAvXT/gO7m/4br8LrKyr+fnxU9xUUMA3/I1Zs67ulje5ZkGHQaDMhwnIqfa9U+iHIOp2Csi0y3jxaUp25egvhdQUkoAjCGBkmQeYze5wEYW6htsQH03G1BltRh1Zm9GlaOjVNp5G9/v+gcfjY9ll38rEyimLrBdSRnaAJNLGzWMdW08YRlzP/QmJ5+L9srVWPaNb/CTbXECi37ON3Yczmf3cMSRyj6EiEwEthhjfoFyZM6yu2rQ4JxDrZ/Y8LXMkGE0tWbTUfADvpfwEVvTTm6Fwb8cOC6XlupnOPfmXFoX7GTszSOp/0crBc4P3U3OECTaCnQ8xAKpZmoQiJzLszk/4ubsa7mn9Gvc6dvO+Mg8Fpqv8cuCHZTXjmNbfS5tfZGoHugzDToMBmW4AfiyiKxDI73/02v/zSiR5GtocMPheuBGEVmFltO1pOzbbLc5dKKmrB9Np6mwx49Ck7Cb0MDKJFRZNn6NO+Nx/KaW0RLHn1hLRbCIxuwmilsBJrHJdz+fHIXSLl3gIzpiBHvWRwk9g5pBVwJrT2PxqilsHPYSZz/TR6Lr4Xh2D0cOq4C4KN39DSjpwhoRcSWf/w/AGNrRROlie14dUPQ2cwMPcFVnLaN8fmIhwawOk1kBvD2b5fP/wfkVexhVcjdfumES23aQEvS4hcrE/XyqGOjKo70dMCOoyfgFX8l8kfklV/NAwTpmZDzLh2v+g+/uv47flMTJyEKzKtpnUmVe5ay+iEMO6JkGIwZDOV7MGPNPvbbNdyvGmIdRQsne2A2cYinIr8LO2WCxFZ2YO4A6k8Oo/yQLDZhcATyFJl3PQIMnU1AlWg4sP5+n2U9+fB4vB6qZ7ANCp/BGxx+4phAYv4vREyNktNj7eP0Gfp74GxdlAwvQFIqHgMwf8e/Tn+T89Ys4u/eo8HA9u4fDDGNMrv2Mokn7qbi9n9NqgNHG0FApleEf8t32XNpGzeOV2gS+1v3kD4sTfBsSn3+a88Kt5M1bwimVN/Gzaaj8OiYlH8D/8KWSLkKR2axoWcCDodm8HYoQChXR1HE7N4ef4fxmbAXKPoZPjBJqQTt6M55t/iaGJVI75/f5TIMKg0EZvl8cD9wlIoKO7v7F7TCGsEg3S8h21KwdhfaqjaiZ3IGasvPR4MkINOgyA3gjQCxURUVXHaUZbzMnL4u20oe5fBzg9xHZXEL9s7sYHwYSJ/N6/O9cMHEj0z6FBmaeAkbdxve6wmSM+zOffJBDO5F8v8/uYcCiBjhJhNWGW/kV/1rXRebohczbkUN7Sz2lWV/lzt0T2Pbh41kWn8OK1hrKXkDdIalTffqBwslsDP6Yb+1vJ7ukjpFdSzg18js+37yH0X6SQcCcDML5BbTsbtL5vQ0gk6iWWkZH+7jHIY0BrQyNMdvQoff7OXcRSbbdvrAFNSFrUL9hJ5rjtQM1OU9DR1jHAY8A30BN0kkzqPLVU+L/LP83LpOuOV3kZGSzf6fB/ybQcjwrYtl0JHYxPhvoXMWxZ0cJzEOVoJtxb8fV/Om8O/nahhrG1fQ2kQ/zs3sYeGgCgsU05ADtJ7K06VVOL72PazLqKW36PpUfupnbL72dm+uOYeN9zRSXolVRglat7Pket/mXMnd0CQ05N3P73jpGxH/ILfWvcpZ/NitiezShOoia01OB+ot5fPvLzOuuXhnPNl8ZO82LfNgjakgjbEZpfN30iG66zgiqDKcDz9jPNajwVAOzdjDutK/xi48BMwLE1wOPXMrjyzrJaQD4JH/iZeb7UNP7c34Sx/qJ/9qePxnY+giXjqxlZNFvuHYlXn1y2sN2hrWt5I1pZFjwy9y1N0Ywbw0VGS3kfexf+P21G5j2u9uofLyZ4ikonddxQmxdGdtnL+aUYXm0jtlHUdY4dmx8kKsb5/NK107GCUCUoEFlPA+V6QZgZwl11OmMAwZgAltZxgmJdCRqGNAjw8OMFjSVwdUad6BZ/1NRQteT0d7aHbMPZbWpKKS5o4CWhcewsSuLzvYYAamnRADyaIk9ysXY6ywIEl52OQ8/fy//4uZP2ZpDK+fwwnHf4scbo2S5JFgPHmpyaJ0WJbDjAp6OnMySzvu45ic5tLdexQM/epoLtgAFkDi5mIY3gX8pof6tdnKmf48fLnlRq562ZRGNf5lfmfFsk+2UB0kyspehZnQjsGcGVfImJzlfIwBnssj8L59PO0UI6T0yDKAmsTNFE+hUoLvR1JQKNGjSjKanVKNVINuy6Mw0SFs522IvcG48l1Y2MC0BBMrYKYs486P2nAdu4GfPPcolfuw0j0DsVc4Yv5GpGffwpY14o0IPFt/htrpc2offyddJIHMfZsG/VlFRexav/PvTXLBjNLsK5/HS1tHUHDePlze1kTdrA1OX7qR88oucm4tyb0az6fD/ii+bKIEAECumLrCWiino6G8X2vnHK6hit1adCMAMqmQls0wto+Mea016IQs1FSaipkMU9eeVoYowH80vXIyOGl8CpviJbdhP/uSprA83UgSoabGdcpNFe+FGJl9nCJQBv7iGezfVMsrfQtFM1DfZWsGqwLGsOf6/+OZ6dOSZdozCgxnLRBYuO0ysQT/kFn8+Lfs+wx++KpjrNzD1Py7jr8su5+G8f+NHgQrWDJ/Ouj17GCGPsKA5TFarIdCO5vbtQ2XYTKGal5nvN6r88vZTMB5VgFvQFLF2IPCv/DpRy2iXUSHzeYl2ct3tHJAyHEosSmmpDEUIoGQNBs0vzECTXs9GR3D70GCJQXP4pqOR5LlBohvbyC3fw8hQLaNip/C6bxdlCWB6F6GvxAlUAfcCLbnsDz7M5ZNR4ag3WLYAACAASURBVKsBcp7jw5NWMLv9Ya7YjSXhtJNSpV1P7CEFIn5g1At8+FMRgtM6yL5hLDvXzmJV/ipm5a9nWu1znBe5m38NJwiuRDvslag7JoTlPpzHQlNEowGC+ygeARRm01GDmsYhLLvNKbwW+SnfSCULoYydvMC5/5+98w6Pqzz2/2fOdq16t2xZcpEtWzbYljFgY2wg1IReAoEkJNxcuElIwg2pJMEkv5uQfknhwk0hNxAgdAidBGxcKLbcq1wkucnqffue9/fHnLVkYxsDxrjo+zx6tHvO2VNmZ+edmXfm+6byhcedTh7TOUMReQr19PxoM/n/OruGoiUyqdak2ej6rs+jNYBtaHHp2WjP6FC0EHZYJt0b28m9NkDYu4viaBp93me48DygKpuO+zso2Az4s+iMPcklY8KkpzpeMj7Hn+xCWqd8jv9bjIbcSb3PdyrdAe59EIcBNSLl6ELoKXbpNSjBx8Bj/gfNIweAx6qNud3ZXo+uZnchOmlxZbUx62tEZqEkGqAD7enVxvSgJVBnADd5iT77JX4f/19uTKxjXGQJUze+xalBdDCNONdajXZHbUQ7olIEI0taKLD+zOeDHqJDYwQ6K9hQ10p+GjoxmOWcI/mf/JqvctfuWeQreUQe16algUZwd8mOHIQ8ZC95GEcesg95GGPWyz7kYYz5yBaiOtY9w88bY6pRNo2v6KLVTELXOClBcywvonnCjThrk6CeYi066trO6zHAWi8xsZFxLRS46hiVU8OUr9i4M4N0/vcQmupwWG38hMY4IcdWNGlt7uKrEzZSsf0lzutGQ+9EatbuSh6RG+TeIueHsc97/7CFNYh3YCxwd/X+WYNuqzZmKhqmzqoRGdiK1lptzBS0a+hWZ9utwJeqleFlJhB2PMIfAP8G3JlD198XM61pPOu8l/NELImnGY1iBJ30S0ON3xTUMJ3gvB8P0ERRQSNDhnqJNwFN7eT4nBpCCw2RuwGzgNPcjZSk6LuooNbE8dr01yt63MRO9Elk5N7yOACL0m1mgDxkL3mY/cjDDJTHR4hj2jNEjUiqZ7cUnRDpQpXoGdTQedCZ41RvchQNJ8JoaFGBGsORLhKrQqSNAPrWMe5sITEO+Cfwxmi2yA6GAfi8RApdJDO7yF3tnMv3Rz4Xt0hO+Bx/nocaXUFHYR/gjeLJzyFp6F/Iel/33vbhiGkQ+8G26j1Zg76y1/6rakT+Hf0dDUEN0kpn3xPO/xp0qU/Q/POvakT+BjxRbYzSXYk0APegOWzSCDV6iQ77DPe3zue0jlrGVI9mk6+bzO4OsocBPT2kDyuiZXkzBRO9xO5J4J6ZRmi4l5DnTN7enE2nWcJJ7ky6PGVsjddTlusj2nMxz7gz6DY+YkxnvjWG2sQIGqxStlJMI63kF3aQPTyKd5wh4cmh9WXtQVB5mHeRh7wPeYgjD5OSx0eEY9YYishs4GPAqcaYkIjMRUPOt3BWCjOGpAg2GoIMQydQUquSdaGhwASU6fqiNEJvhEg7w8KOJHCfk0frj7LpbtjMaCmmmRVMsYGiNHpzxrJh1U5K3aix7b2CJ2Y3UrL2TabHUa/QhZPDOZcX3RfwbHw9VTvf5d4HcXixX5agGpERqGdzUrUxHTVKrDvwO0pVCaS4A6k25s4akedQ7sKFNSLnVhuzHm3N9DrnNx3k7gRO/j1fBg1re1ZzQjZa/zoFrZHd0MSQYmB7FLcNTOnBG/k6v66bwx1mFJvMZkYHKlkbWs94t3MPHffwRXMOL8oGKpMNlLOImaaSdekNlJaGSR+ChtOWm/iq4exYv9lUDKTw2q88ZIA8jDEdchDyMMbcKQPkISLnGpXHR4JjOUzOAjocY1IJyrjqhKVdKIehOO/r0e6T1ELclvM6lZNZAUzIoqs5getswWwXzKax1LZuZrQZxUbZwkjQcKb4Sh6vXcrUKM4s9WZGBN3Eyz/GKztQpfDgrI9SxC7zc251b6ai/dfmP+0D3fsgDjuG1+yfNSgTZy2cGpEi4Px3O1mNyKhqY1ZVG/NTtJa10tkVRg2HAbiFX3WhOpiNGo8mlKgjF9XPPjRvOAFt7xwLZGXQ3Qtij2CzbGa0K5MOWc94QcPj1GJk1ne4026gPIj22Z+xg5KpYdLj6ODfDmwayeb1STyhveVxABal3fKQg5SHiIwyxqwy75THR4Jj2Ri+CLgdxpc70drBFDpQgyPO2iN1qDFMJYyjqCIuQ2eSuwTb9JBxmY0rYWE/L5gxdZRbAOn0WDsoDgIjR7NhXYhArIvsAGr0oh5iZy3jxOUNjPSha1zEnH3J57nAvZqq5M/59sB8yYHufRCHDxuAL9XsgzWo2pgVqH6sRwl0F+7zDHviazUiq2uUTSgOvACAManFwvwA2yjFT98uN7GhqAFrQVMlATQHnYambmY4/4cBtQnco15jNlH8FuD1EE8tBeBH++89XiJDzuOFaeikYWYhTSuv5uHXUA80E9g+hJ1NY9kQrzflB82iZN6nPERktewtj48Ix2yYbIyJsp/RyRhCzoyZFx15e1BlKUUNo0FnmTvREfUrLpIRC3upRTIvhlfATAiTlsigS5oo9IfIrADqruKR3j/w7y401O5cxCnDcugMTmNxnXN5LzoI9fyFz7qz6fT/mNs69mII2e+9D+KwIlF9ANagamOu39eHqo0pH/B6Seoz1cbcfIBrhYEAxoS3yRsSIdjsITLmRJZbK5gURaOOLjS1Uo7q6xjgj8CvgJcSuMc3UbjMxhJA/MRs1HD60VKcUsF0ANtQImO7go2++/hcJfobaACSFdQmmyncV/3ru7Eo7VMeZoA8zAB5mAPL47DjWPYM3w2daNiRWgCnAR1hM+hnvx6DNsFnZNDzfyHSSt0k1gDDBEMnuXlBetObKRoD7ChmR3QuswMtFGUB8aFsa5/AqlPbyFu4i5Js1ON0Ab2zmMs5vJR+L//et5oTBtvxBhEG/HNkjrzFqRSxqyWOP72W0TkBQumofuag3mEpGtJ2okZsJOq1jV/PeAkR8AH+JgpHofm4Eajn988b+NNbUQJNQCJAX946Kscm8O5ES8eSQOw0Fthvcepx15J3PBvDLpxQGXaHyqOcfd3ousnXoLNgoTIa1iaxJgIrBXOCQdYAE7rInGLjaQdC03g7vpWyCOr9tSxhaiVgl7O1HQ0DIs6feYLL0looTP5Mw+PjTvGOdFQbU19tzPtiDXpf0FA5ejqvBwBXE8VxF/HOMOljhWRTgN5WITERjebqUZ1JEYhssEiSxDXeQ7Sim+yThcS0hJK11gBzgS23MyeSUMIGF1BhYXKLaFpHf0lL7EoesRYww+yLRckcTnl8BDhujaExhFFDGHD+t6FGLAf4Auol3ge8KNgTr+GhBoOVBdQbmGSwtoB9hkFagK3F7IxPYHVsO8MzgcgZ/DORRdf0tzj5LbS7pRGn9e4VznJ5iHuv4u+9zr0MGsNBsIuicC5taaihcyfxxCzi3hAZwTDpAYukJ5OOXWjkkoZ6gxOAWoNUA+OSuEqB1UPY9TIaDu/CWda2m0x5mE8G0Tx4ZB6zatdRlVoyNAJab/g6Zxx39F1wHBtDZ+KkFyikXw4RtDygDrgXLYHp8hCP3MuNw9zEN8TwDhNMHKykkCz1EK8HZBxrrQe4LjUjGPkTN0zuILvnbF7tRUOZGOAqYFfiFN7KfJBPhTYwzuY4bHsaxL5xD/8R7SMtLZ0e0PRMrY2nzEW8GFidxBdN4Pbm0dKLdkXFUNbpJsGUGmSdkxNsieJzox5girTVfpBrhnWTPRJomMXchtuZkyq5CQFUs0Q8JI/bgfm4NYaw2ximlkuchRIzrAJeRfN7biDNT2RjE4VjBbNKMKNtrDohaRuktIechJ9ev4+YbGWED4hewd/dHuKnNlA+D+19bsOZqX6Fc9NqqYjdxL3HHZPwIA6MO7hdWiiIncTi4fSvidKbxNM1njURoDtExqR2cqdAYpqLeC2qo08LZpzBWmdjjQIkjD/onCMJuL1Ex/SSHkS9ya5Cmn0LmeFBy2EsQK7jAfNHPm+O1175o8oYishXRGSdU7H+Ac6DoLNyBi0n+AIays5BjVaGs98D2Fl0rUriGuvkCU8F2QTGC1Y6kFXCTt8qqvyoYgV+yA+mBAjXTufNVJ1WGJDv8UPXSLa4L+aZME5PqNGek3co36F61kEcXrzf7835/l0/49uBfFoy0NnjNrTV7oR1VE4HewRQbHCtA/f8HDrqwV6PDuh5QrLO4KrMot0TIsNL/0Jn49LoC5WxtRawS2lgCyODneSmUkXJWcylhJ1mB6X79QyPdZ08qowh2gt5tjHm2g94HgvNFY4HPo5W9D+EeoNNaP9RFOgqpMkqoqnWxjXBRaLNRkaBNU8wJ4C1HuITvMSCOyiLAYHP87+eMhoq6xkxHw3B2wBrBFvkY7wS/Buf6tner3AHGoEP1bMO4vDioL83ERlY2uYC8hZzUjCT7kAmnT3oLHAlMNbg2p5Dx6NCohctjO5qpTANzHoXkSqDrHbOMz6GNw01hMXOORq+yN2Na6kSAAvbH6S3B61hBLDOYC6PcqVzX6qT+9DN9/tsRwWOGmMoIvegJQQviMhtIvJnEXlbRJaJyMXOMc+lmsOd7T9wXv9QRL4w4HQ5KIPGbODPaKeJDzWA61FjaIBIIc3eAppbDbjieEcKbLSId9tYE4C1FvbkZoq9OJMjP+PbJ4ZIWz2VpXHU4HYDyZv5TVYJu0L/wb02Dn9c/7O9wys8lM86iMOEvb63r4vIUyKyUkTeHPBdzRGR+0VkIXC/bkNQnatI4Mmez+kl6fRORCsenke9w74O8sICMT99ATQKyQbXGrBOhOQyIZkPjI7jyUEnAP3A2qFs7wuTZqMhtfcSnoq2UpBaSc/MYi4x3PIYV5pUpHKonu1owlFjDI0xN6E9xGeg7UWvGmOmOe9/LiJBYD4wU0SyUOM0w/n4TOB1ABHK0fWGBfgduhxoKm+SottK0aP3+YkEN1KRaZBdLpIeG1kpmBEgzUCri+TkCP5OwJ7D9zKD9I14gsvfQhPgLYB1B9/3D6GRM3i1DwYWV++pdFPl7SJtETw0zzqIw4u9vrdyYJkx5gTgu+y5nvB44GPGmGscQ3g1cB7a9dQxnUULXuK85aguRtEZ4QLAZ+PemcSdm0ZPBC0NW2lwVYFricE6ERJtCXwno97jZiA5nUX2s3wcIL2E7dH/5j8TKS8RsL7E781TXPqOGeQc2jMKpTn4fp/tA4jyI8FRYwz3wjnAt52Fq+eiI+Bw1ECcjhqG54B0EUkDRhhjNjifLUT7OR9EE8ypfuQc5zwJdHatdAg7PSHSMpspqjDwVhLXWMHUApPAWi3EChO4R4RIDwHpV/DY9C2MWH4jf3Chyts9nAZ/Np3593F9zw5KAzijM+BxGGsCQMaJ1FTG8A7bx6j8QZ51EB8dTsPxjowxrwJ5IpLp7HvGGBPWfRh08F2Ms3riMia39RD0ncRbLnTQbkFnjy2gNY6vCEwSHbyNhd0EptPgPgloh6jJoLPNOd4dICQbGZsDRE/hTc7lBcs5r3s8a2Qt411rqbIAlwgBEYqAKhsmCnbg/T7b0Yaj1RgKcLkxZpLzN9wYsw5VqKn0e0fL0MmRmgGfXQy8QX/ZQRyt20r1JcfRfs/ifFp97eTm5dFW7ye6IoGrMp3eJUlcE4ANLpLFBnc24Pop3ygZxvb073DnYnRSZhcQ+ynf9EUIdL/M+VF0lI/RvwCPC+BqHvR5iFkd5K7knfggzzqIIxN9e71/BNWFJGBWMMm1k5LIHdzuRw1eKxohiPM6K0RmwkW0z0vUb5ANBpkIWGCtA19ZEbsI0m1XsjaxkBne1HUv5anky5yXaje1r+JR+0n1CjPQ0LoESMuhvfM0FixrMsWtH/DZjhocrcbwJeBmZ5F0RGQygDEmhhaaXokavPlo3eDusNEZidvRkMSNKlgYNVJBVCabgNGt5E/Ipa02jD88hMZ2g2VcJH3gslzEGpO4KsDaAEw6lxdOaiN/0TNcIqiX2fotfhKM4XF/i5/10M+EkyJwsIHEzdxldZCTvY6q7dvM8H2V27zvZx3ER4r5wLWwm5Kt1RjTvZ9jt6OzxykaL7uL7L48WgNl1LlQvWlEoxpB9Tc3iS9sSBYKpssgw4CVYKLA+E1UGoPL5NGa1k6OAaK38Cue4FKca1iA/x5uHLaCSSPRnHk72uLXNptX23rJ3J+H916e7ajB0WoMf4QalJUissZ5n8J8oNlx1eejo938vT7fgXpvNqoENlpzGHDetwGlubRHfsCPtsfw+BK4E2A29ZJeAawLEHYZXGXA2rN46cxsOu1TeHOjc96+i3jKnMm/AnfxtQj9ZTopRpwoYM3hdlcUf/YbnNLXR+b+lOmDPusgPhrMAaodRpY7gc/u70BjSKDGrhQnp/xt7oxOYK1rOFtdqBfXgnptBu0jLgGywV3mIvoCMB7sZeDKQWegJUTQ48Imh45EHi2xZgrd85npRY3q6BzaswS7B80t9qB6FprNa1EXdmKumb2/MpuDfrajCUfV9PdA9guUun9fx3wf+L7zeif7WOXLGGIiRNDQIIrDIoMSN/QAIyyS62J4A/OZ6Ynhszpx+dwk1iRwTxDitXHc5eDaAvHwybw9eyNjHmyhGDRXuONUFhU+zcWxpUw1aPiTGmXdgPUbbjZ5tKb9ilvc3WTv2Ecv6CF51kEcXuz1vV2yj/1z9vPRepS4tQ6ghSK7hinxi3k6MJ9Z3WjJVxWqq2HgXOAlH9GtwLYoZIFZj3ps7cDITDq25dEWep3Z/gIa017i7NxWCqPowL/lCS6zz2CuhQ7gKZ5P1+nMk3nMfgdrzQd4tqMCR6tneCjQjoYmqdA0xSk3HoiUsHNxG7mli5geyKU9GsUbSyO0EVyTMumuieKbCKw4h5eH76Sk4GxerUcNoXULv3Dl0GHfzc2dOCvgOdfwANZrzE5cy9/83+anwW6yB5cLHQTG7F7SITe1aSnV0Yms8GfT7kIn9brR5R+GoOHsLhfJcAxPFli1buJhdKGoWqDKS8z3HB8fBgz3kHQPZcf2s/jnNqBjFnMT3+XHHjRdFEedAK7nPjYz+kBe4TGL49kY9qBhcWrdWAsoct5v+wJ/aE7gHtJDup1GqMtDIqh9nzYZ9HaAuzSDzvXV1FQ9zwVpaJlD3nDqd17M094FzGwbcG4bJx90C7+Kn8DKtPN5wbeN4VHnPgYxCOhnXLcB9x3cHvKQ8F7Lg7trBNFav3qg1kckx8bqSeIqAlYk8IwCuw3VuakuTHpMy77qLufxthVMiS5jEiPYTBx3xiompKFGNQbIeNbIpTxhv81Jg0QNxxlSYUE2Gl4WO9t3AcEYXo9gNrWQPzSJO+4iGYjgFw/RNc0UjAXqPs1fh6+lKq+ZIamV9JJ/4gb3i5wXeYDPJNDEt6CKnLidOYkv8fvgN/mpeZuTA2hu8rgbgQexX2xHc4FuQDrItQS710ekAGWa2YoaugjQZGEX2VhdBikWEhvANR3YAnYM7OJhbG8CQpNYaiL4bcC0k2+1kp/RR9A1nG2d9Fc2yFU8QiMlZiNjD/uDHwk4no2hCzVGWWjYkYeudeLJo9V/Mm9FfcTqkrhHRnFLRNdusn1EG2KkTcijaV0la8e8xpkeNCw5aRav9jUw3LqT2yL0z1QLEK1gg3yZ3wW9xOJ/4gs5zrVTI/Bgrm8QoOFqE/3eId/kF1lLmTI2g656tFMKtNa0wyAjklilNu50F8ktwFCwFgLpkKzcRbENeItpNKuYmEQjoaxhbI8GCHc1U8DZvOQC7NFstK7gMW7i3iTHKZPS8WwMLTQPk4GGHnWoAWvOo833Oqd7TmR5XRTfiA5yC+K4w5l0uQWzAhjzDX6eeI0z/d3krATibmLjv8p/R+7iayHU28tAR/AYwBzmBGN4k8PZllrQOwy7W5+OS+UbRD+c71/QNbzLUF0ctZip7p/zjS3n8WKI/vKXcWjlgEngTgLrlFaOBqAZrPGCiTaTW5hPc/IkasybTM8GgoXs6r2bL0XfZLpppdCqZWziRJa5fsj3mcPtNuwuPzvu9PE9G0On//BWpwf2Yx/GTTnXuUhEvu28LhCRt5we3Jki8ryIZL+Hc10vIr/rf7/7i85GjVa78z4EdPmIBhdxakYH2cYgvQI5CfzNWXR7E3i259MYnMLSjOf4eC6wEuzsbDpGLmFa7ypO9KCKHMWZGJnPjLRsOpND2Wnop/Ta+14OdP9HvcyPNYjIbBF51nm9W24fAKmukBZUL6cAneDavIAZ8Rbyy4XkKLSoOX00GzcAOwzidhOLxpWyazXgBbvSwtoQx1tVRr11L/+e6Zy7eSg7zBzmgJPDbqDclU1Xsolia9tejDVH0gD9Icj7HXjfnqEx5gfGmH9+0BsQEdd+zv+MMeZO5+1ZwCpjzGRjzHxjzAXGmM4PcNlU8XMFSsyQasnzFbHLk0O7ieONuUm6baxtEfx5E1hhhfHFwqR7ruOB6FNc0hEj2Ak0ptE7NI576I/5nkV/QWsMkJ0M8efTJh/nhTA6QZMKj/dWtHdVvKNc5kc8RPGefxN7ye39woXqTTGqiymdLPstN2eOY10gSGgjqq+Wl5hHMCGD5HmIt6CF/suA8WC22Fg94D4hTCCrmeI+nM6QU3kjOY/Zu/UTcF/O467XmZloJxeDyByZk+rG+lDxEcv7HTioG3GYU2pFZAE6UYCI/EVErhCR80Tk0QHHDrTg54jIGyKyVEQeFZF0Z3u9iPxURJYCV4rypK11WDAedo65XkR+JyKTgJ8BF4vIchEJOJ/Pd467zmF0WS4i96Z+6CLyOeee36afxCCFNDTUaEQnUSKoMsUf5upoGQ2tHWS7NjMqO4f2TTE8paPZbFooiP+Q2wrHszb0v3zBC6zw0pcVIDKyi+xtwGj6F31Kzuc0Tw/proe52mEYIYITHqfENeCe9giVj0GZH1KISLnIbtoqHM95jojMFZG7nHtbLSLTnP0FIvKKiKwRkT+KSIOI5Dvn2SAif0U9q1IR+R8RWeIce8eAa5wnIusdGV42YPtuL9j5jn4jIotEZIuIXOFst0Tkbufzr4h62s6+3RyaY9EcdmpCzgXs3EJF/fm82Hs3/5GqetjZQFmZi0SXjTvDT7gLjUbWgn2Ci+RygxUAu2oc69vyaEkAiSt4RFooHChG6xGujE1nkf0kl7ORse4reYTxrJXb1XscqI/HjLz3h3c1hiJSjbJqTEJX2jppr0P+CZwsyqQC8EngYeeH8z2UwWIKsAT4zwGfazPGTDHGPIyyyEx2WDBuGnhyY8xy4AfA353e3N0tQiIyzrneDGPMJHRm7FoRGQLcgf4gT0NrB53PkIdSpbehfZ5xtKLfDxjBuD/JI71hAnmCbY9j3U7Bdi/nxOwYaclzeem0DYx9KUGgUkiuzqJrbB/pBlx1aOW/BZgf821PMY3e2/iv8B3M8aMGuB1252Ns9jSKUs7GrGNR5h8B0px7+yJK0QZwO8r+UwU8hpJdpFAB3G2MqTLGNAC3GWOmAicAs0TkBBHxA39Aqd+q6a8+2BeGoDL4BNqhAfpjLkfl8mngVNhtCM8ApqE5wWZ01ngDGsrGAG8GPd09pGek0RcHGm2sSpBOoD1EWjZag9gHJmmwmoBcC3vkJioSbRRwCotsG8v9KFelJu1cQHIkW6x/cGESpwTsMa7kkzwi/+BC14U8WX6evDD0WJL3gXAwnuFM4EljTMjpP3xm4E5jTAJd9PxCUULHjwNPA6c4N7JQlHHls2hiOIW/D3i9EvibiFzHeytAPgsV1GLnGmehkyEnA3ONMS1OD+/Aa01ACQ7i6Gjqd7anAflPcmnGX/hsIEQwPUyaZynVwQx6W7YxtPIX3DKqi4ys/+ZrNUDGULZZEfwjIqStRA3biUB6FasyusjMO58XeYyrgqgihJ1rpDv/g87rfB+R8iFsOyWGd6Lz4zjWZH648RCAMeZ1IFM013ka8LCz/UW0JTOFBmPMmwPeX+V4I8vQro/x6EBXZ4zZaIwxwAMHuP5TxhjbGLMWTY3gXP9RZ/su4LUBx1+EGrMadDY5gtYSluOs7b2FkaEMuoJ+IqTTE7GxcnNp63YTa4+SVoTWCw6xsDeD9IBdYWF7VjCpCLAy6XbFdWU8cELyOcxhEdOtO5iTylfi/E9by/iTfUS/F6Bv0oFFDRx98t4nDtVs8sPAVajHtcQY04MK9ZUBbCvjjTE3DPjMQHaLjwO/R5PGi+XgWXIF+L8B1xh7EC1BrwPLUUqkfPopuxoDhFz/wd3hbZRKgFBDgFA8j7Z4Np1rwCq5iKdPeI0zFybx93kIbfATzushy4uuYWsBo8exNvxFfh9bxpTWTVT0oqN9K+qJ9tBPCuEHCgtpyjiN15M+Ylt3UrbwPayUdzTJ/MNAgj311z/g9d4yfDeZ7paLiIxACS/Ocrzm5/Y698EgOuD1AXPBzvd9H7pG90CDtBMNmTOA6A3cFxvLJsqo84IdBOpbKMh1k2hBvaaIkCh0kVxs48oAM9TG2oSG2zKaTfyDi+18mlOySNzOHdaDXJOgv0c/B+2PPusU3jh/BoueftJc8VzqeI4BeR8IB2MMXwcucfJGGajbujfmoT+qL+CMBsCbwAwRGQ0gIkERGbP3B0UTqKXGmNeAb6EKkH6Q9/8v4AoRKXTOlSsiZcBbqLudJyIecPjM2a18K+jvDhntvO7OoCf/Dm7PSCOUHMuGtj7S0ypZFxfYeSH/GBfHE/kJ32/yEPaOom7bdkqHgmsVauAKIDH2Sh4NPs3FoZc5z0a9Px86KrrQHtBS58/6Er/b9T1+1FLLmL56RrUas7vu8JiS+YeEJqDQuZ4PDZFS+KRzb6cBXcaYLmAhOnggIuegP/x9IRP9sXaJSBFwvrN9PVAuIqm1td8reelC4HInl1WEsqynsBrNR739SgAAIABJREFUKZfQn0KxnWcchkM8/BLnJCewOt9PJOYhVh8mOCxIXydKvBB2k4hb2MuBCW6Sa22sXqBqOPV2M4U2YFopdJWwPTGH211X8XeXU3JTQv+6P6dVUJv9DX62tJytLw24x2NJ3vvEuxpDY8xSNORZAbyA8ujtfUwSeBZ9kGedbS3A9cBDouwWb6Cu795wAQ+IyCrUTf7Nwc5aOm7x94CXnWu8AgwxxjSizBpvoEJZt9dH69Ae0FQD/Eggs4im6CYqcmewMNxAucciGeklPVBKg/sSnsz+Jj/bAcRmMS8rjnt7hPRS1Mv0Q7Iyk57OP3JD/sucn0ANbD57rkURBFpvZ84Wg3SPZIu5lV9Y2ygP4dQjHsMyP6QwxsSBHwJvO/ewfsDuiIgsA+4BUp7xHcA5ziTAlWin0TtaIY0xK1CZrEcJgBc62yPAvwPPOSFd83u85cfRDpO1aMi3FJ28wxiS6GByEv3kKRZaczgUNYyeh/hUsoQdgdFsCvmI7QLK+0hLR6ncMnxEu0F2gV0CrALLDVTOZi7bGZaqcnAlsYKPcfnQf/CJHNTja0J19Vw/4a138x/N6fS9fYl5KnIsynt/EA3Hjz+IMBHN3S1DlWR4BRuGJnC1XMaTG+cz0xUiLdhIcfoDXDumiaJx1/PAzunM+6efcNVczuqw8fwQuAfsazLpmhjHWxcm+BDqPY1EFbsJNbrdgPku/8/1SR7x1lCd/Dz3+VBljBuzmzBiEB8AIjIXuNUYs2Sv7T4gaYxJiMipwP84Sf/DeW/pxpheEclDjcoMJ5+FCH7gK2jI3IUzEYdOSK1DB9XILfxqTAZdm37N1wJ9ZJxu414AFFrEM3Jpb26lKGAR/yZIvY17Mthpn+Wv5/4f1wdRL0xGszFcQW0kSCj0GFe60QmcHGDxfVxffApvhMaxYfnBpGyOVnnvC0cVhdchxiY0b7YGCI1mY2MBLf71VI56nguaA4S6LuGp3ie5tLSMhhOf5qJHIXn5ZTyZfiffTtp4IsA6SPjT6R2bwL0qTNCHKu8WVJlX0Z/PkFoqPOn0uj7NX+P/4mw/6g2mFogaxIeL4cAjTooghqYXDjeedSYXvMCPBv4wjSEiwnJ0cu+faMjah3aVjEVTIP52snoieIsChFvCBLfYGnXssLBH+4itAya6SayKEcgExggm/CDXjEEH445K1kYf5wqpYq3xEC0E+2ywtgH//Dx/KriQZ9w3cW/te8hd7w9HtLz3hePZGEbQMKQc2JRFV/CX3Lr5ah6KNlA2MY2+zf/krOYfcdvYNvI33MPNPcPZ3PF3ripq1Vn+GLA2m46TY/g7QmSE0NxbKTqyt6CGzprFXPub/NTnIW5fw0OReczOor/eMPU3iEMAY8zs/WzfCEw+vHfzjnuY/S6HLEbDzMWoIXSjE28TUOOYv5Exoe9zh6uQ1r67+HJTJ/knALVukqabTDxE0g3WVuASsDNc2Ovj+PLR2enINN72PMpVSeDEOL7JHqKvubBbs+nM+S035y3i1NrHuDKqC5O9u14e5fLeA8dzb7KgtVyjgKyLeDqyjWGcx4sNNlaHj2hOAc3jx1Bb/nV+sa6at/1lNOxczMkj0dnhIUPY1hvHMzFExlK0tCEDLQ3owyGN/SW3WN/jR4F28uIjqI/NY3aQfo8wRfx6xLQ9DeIjRTeazz4R1QsfqiuNaM1p/Bz+1TSSungpWz09ZLQC/iBdaWD39pA1zkXSa2PVA+WCWZLA1Y02GMRnMZedDPXPYc756MTh43F8TQbJeZtpmRH8nWfzr1b23SF1zON4NoYWasDCHmLFVaxhKDvjXWRZ6fTGSmis/zq/qnqGixqXc2LWDBakr2NcK7hOAWQEm+RUFi3vIzOJGtUU4UMMnZm1ruZv3iy6vAs4PfppHkiiOcok7JEfHPQKBzGwD3gZavgC9OtJHxpx7KynzB3BH3ITzwjS5wNicbwT43hbhERGgPCGJJ4Q0OIiuR0sH1q2Y+ooHz6PWVeg3ubjqC4WvMqZ0Txas77Mbzc7245LoobjOUy2nL+tHuKjc+jYcg83BmJ4I0Xsap/NqyPHsCHjOu5/eSg7z3iGTzS2MmQXsP0k3gwX0mye5cIYWGtQxRmLzpi1eYhO+Az/1+3Cjv4bf46iI22A/jVQUotDxY3BiOiP4RDkaQZxdMNCJ9y6UG9uvbNtFxBNpydrFRP7Ps99vpFsHtVN7mYXkW1uEnkxApt9hMf7iC4BJgiJlQYrDWVfz/QTPrmFgqo43hdRT9MFFJzP8y3V1EyqYcr6lzg3jsOkdCSRNBwufOieoYgknb7F1F+5iEwVkd/s5/jdPbAHOOfAPtlFzv9yEfnUwd3T7gXjM4D6NEL8iO8NO4nFEQsjgolOYsWF93LjrjzaGEPtinpGtAFDMujYFMY3ZhGndsZI86HlL0NQY7fRQzSUTm9ViLTe/+WmGJq8TUeVL+z8hwG5wn1RJh2JchvEweF9ylbQsNhCc4anONu7UT3dFiYwrYnC7I2MiV/J37fdyO97C2nZEsObC9hu4j27GOoFXB7ia5O4/WjrW6VFsjRA5GG03MSD6mzLQ1xTBjTP4I2udvJt06+Ge/fKH/P6cjjC5PCAboVJxph6Y8wSY8xXDsXJjTHTnZflwMF+SRZa8xcH7Apqt2xm1Mj/5msFbzN17Ke5v6qMhsi93LjsRu4NN1FcC55IBh2NJez0rmfcqA4K+tBK+VXARLDXptHT6SEe6CBn7ENcl+r3TMk4jCo7sHuN5gPhSJTbIA4C71O2HufPixqsJBoapzqltrpIppWxtT2LrlAFdb2VrA8OZUcyidWaRUehLiZPoYtYl2gHy1jAbZHc6SNa105eBB2MC4GOZgoCBsm5hofqcFoyBSOIpPRzt304HvTlI8kZyp4sK3ki8rI47BbsyZSxT3aUvc7V67y8E5jpHHuLiLwuyr6SOm6BiJzovPWihikZpDcrjZD0kDE0TCBYyfqGf+OPozvIfm4sGwrnMzO+nOpeH33ps5jb1U3m6wn8pWh+MAq0Q7Igl7ZaG0tCpOeBVYUqkx/1GFNEBwn2JGdwi2A5I/C7hiZHgNwGcRA4gGyrBnwvK0WkQo/fzVoTxFk9EQ2RT3VebwSafUR3Luak4W4S5m98KpRJd7qHhFgkdyVwlcTx7ASGZNHdFyUwGu1eWeYh3tRJdqoTqQjomcusaA4dY+dz2vonuSwVmageYuR25uwRrbzXZzoacTiMYWBAqPfkPvbfDixw2C2exGG3kP2woxzgOt8G5jte1K+BP6HdGIi2pPmNMStECKAzvtlAyVB2eK7jgaYcOhbEcef/kq+P8hAP/43rtgfptR/ncrJpy6xkXWIVJ8QaKe1Gk9zDgaIs2isz6FnTS0YoQjAPrfvyooroR8Oc3Yrm3GtqJT7Q7yD1Azhi5XaAzw9i/9hbtjcBdznfy1TUAwTtEhpOf09uBJiLpldy0N55MujZmsQ1LESa6xd8S/xEkpfwlBTT1AQURQi2QrKoi8zZqH7/y0W8MYHLbXBVoYYwDnRMZlmljbXrIp7tob/9b/dCUHcwx/o1X/HcIbenopn3+kxHHQ53mHzpPvafjsNIYYx5jn52i/2xoxwsHgU+Idon+3ngL872WSg9lh/o/AT/CLVQYFewcaeFPcLCPvNlzqkppKV4CdM6LOz8MupH9ZCxs4FR6ain1wTJjFLqbQPj+8jYEMOf59zfZrT05hT6V8VLd/6noR5pymP0Ods9GXQEfPQNzPkdaXIbxAfHG8B3ReRbQJkxJux4hf+Gsgt1oINnajGxTcBEIDCEnZ5StnYCWd1kZgPpS5mS7CWQGyRsx3ElgVNcJCfbWI1oLrvWQ8IkcRWiNX8uoG8ep5e2kp8ziWU7UP3zo7qY+guOZHNJmMDVUTzvFhW845kOnbgOL47k2eQUO8p33s+HjTEhEXkFuBhtGK92dr1M/wpkacuZlBGkt2MCa7ou4AVrFRNiPWR2RfAnu8hkNJvGt1LQtYPhjWiuRUawaV0H2aeG8K/sJvdt53yzUEMYd44rQcPjlBeY6L83fX2VPGJtYxgG4S1O9bAnq8z7xYclt0F8QBhjHhSRt9DOp+dF5EZjzKsi/Bb4GBpVNNO/1vZqdOCWSSxPjqE2VkP15jDBYcDybQyL38ovM1cxyd7IqCEWySo3iceipNlA2E+oPoHnZLBGOudt+infcE1nUdkGxi5fR1XcuVbKKfIB/mw6grfy88kl7Fh1sfnHHm12B/tMh1p2hwNHQp3h6zgJWRE5n352i/2xo+wPPWjeZSD+CPwGWGyM6QBwmGHmARNPZVHTddy/K4krtpXhZVOoqbiLryU2MDbtfq6LfJr7rVbyXDsY3oeGuL3D2ZJ7OY/HQmSE2ygOomUPbzn33QW7lwatRI2g19nmAuzxrLHnyBxBRB7lKvMm011vcaoFJN5jac1hldsg3hf2kK2IjAS2GGN+g/JPngBgDPVoN1QGGt56nf82mjs8OUxAAPxEdgBD8mmW5UyO/Ynrs19j9tUGq9UisdpHdBVQ6CXcmUlPcwJPyhC2ABXX85fKJK4dE1jTh+qlH9WdDEAChPx/5IYR1dTs+DVfr3m/z3Q04kgwhncAp4vIGpSddivsnx3lAOdZCSRFZIWI3OKcowYNO+4beKAxbAYiOyipHMmW+Md5vu/PfL64gOa3DVbrb7l54ig2VmxidLCJYSEAH2H5Av8Td5FM+w03+2L46nCS0WjIuxQNc3JRr3ACagCj2bS7S9hBGfWylipzB3NEMKk8YipJ/V4X7j7schvEe8besr0KWO2kLyYAfx1wbA3qGXahxqYQTbcsAqZuYWTOX7g+I4eOXqAnirdkE6PHPcGVZ1SyfsMwdjzrIWp1k2MDyWw6TRdZQbRucRMQOp25M4KEPDNY2Iga20zUBvSgk4E5z3BR8WSWhr7LT1bMNbP3NTi/l2c6qnBMs9aISAmaiK40xth77mNIEY2X/IabH5zF614v0U8tZPozd/OleA1TTq9i7eLX+Fg24PHT45vMsi2ZhFpe4rwiNAfYDlyK0haNRGf+ylDetDIAP6GZhTS3hkhztVIoaEgC/ZMpgipjcgCX4UeOA8ltEB8eRJiF6lUbOrNsAdtcJM73EN9VTOOqekZ6gRI30YsTuDfOYMGrN3FP+j3ctHMxUyfGCHa4iGVl0U07+SNQAzUyjb5hV/Boo5vkd//MDT30L2ObRAfw4ApOCJRTn30Zj6/6F2eHj7cmgCPBM/xQICKfQcPX2/b1gzaGxgKa225nzoRsOk+JEFjTyJC+NEKRIKHIPGa6gOglPLaynPquxUwd8hLnlaFelkFb+aJohb8bLX8YAnYY7A1gt0RIO2cr5RmtFKZKalILy6dquI44koZ3k9sgPlSsQHuGU1UIrUCRj+gmG5lqY0WAICSn2lhWFl0LNlER30WxuY4HvDGCTW7ixfm09vaQWYGyJ+0A238iy0sXc1Lun7lhO5rLDqP6WAx4VjFBxrEu5w5+sPpfnB093gwhHMPG0BjzV2NMqTHm0X0eIOK6iGfeLKB1RgzviEWcuiVIuGcoO9wNlK4zyOQxrI0U0cQ2ymsTpPUBvWgYU4QWxDYDk72Ee/2E6sAuBKsWrA6wOtERt9w5PsVgnJJ5qhn+iCJqeFe5DeLDRDfqFZajk2kG2OEl1mFjyTZKrwDOCdI3H8wzbuK5EQLxrZSGtzE8H+gyxIcmcAfieP3oJMyOCjaVJHHlrWdcauLQoJ5nEdDXQ3piGNvLHubqNb/i1ihgD7bjHV9w/xc/aP8yv0//A18wjRR31VEWfJBP9Y6krjGLnrwusrmXLyX7yIiiCpSHkjJs8hLuFWKNkDw5RsATIS0HrPWowUyRMlSgxJwZKNN1ShH3ris87hRvEPtFA463hk7A+UexudPG5TLIecAzk1ix3U+4vpf04V3k2MPY2TaZpbaXkN9NMq+DnAx0wiRSydqeStb711BVZ7BCqOeZiQ7UTdsZ6vITqXyCS9d8hvtT1Q/HJY5rYwgMPZeXWu/jev9yJmc8xLWh0WxOz6A3o4/giiZKyukndLCAeB5NpNEXthGfwbseXK3oCJyD5nsCaPtUMbrKVxINVzrQ5vt0+vOFu/8fjyPxIPrhfP8eNPWyHs31hb1Ei1cz4Tov0UVgLbOIj1nFhPypLO2zcSU9RIb+nG9mL2SGdzzr84NE3jKqj7tcxAsv54mJdZSv6SNjO5ojnI7q3I4NjHHn0DGhj+CaG7ivFxBjsPfVK3884Pg0htp76QFmZNHztp/wyse5dOI41qb1kZZcxtRgHP9y1Lvzu4gOcRNp9RBNtlHkDxGMJPAHUcXdiBq+BpRaPAvNIybRLpRM1Og1O8eU0L+cYUr+e/SBDuK4RQYaHq9DdeeMBO4ZafS9lkZ4NbBAMMPDBAKrmDgpjr/DQ3yoi2TPEqZyIU9HBbMWJA/YeRlPlOXR1rGGqpWocUtHQ/C2V/iYbyRbTtjB0E3ZdPXx3paLPSZxRPwAa0S+UiOyrkbkbx/wPOU1ugDNu8GF1gG6gc3jWbfEwoz4F2flrGKSC0ik0xUTorUQn5jE50ng74zj60VDZS/qBQbRsoWR6OzcVjT8WI8avg50Kc+hqJEMO8d7nM+kWErgnaw1XxGRdfIBZeKwjByMTAZxGOD0l0/fx64USUMYnUApAqYMZ+sTF/D8xgi+ALALzAgPMeMnusNNtDeCr6CJIcXbKO39LH+NfJm7Ixb0Xcdf/FWsLf8TN6wxuJpQXc0GKqazIHs4W6e1kbd9DBv3IHMdEKEcUdHKAeR2yHBEGEPgi8DZ1cYcqIf2UCKAlsIs2cjoSAEt8S6y3+ggf5qQyPQQaTWIZfA1gCe1fkQcnTwZ7rzvQUfaBvonSCw0JE6tkRxCFbDbueYItH6sxTlmBGo8Ld6pfF8EzjaHTyaDOABkr3WlRfF+fj+z0VB1b2SgA+oI4Aq0qL62l/TClzg3z0UyHqAvZmGWxHFPjOBrLKd+h43LC4z/FA+1bWVYbA53+H2Ed81kwSkrOHHtGiYMQfUtCKRbJMY8yWWjmilsLaapE41cvOzbIH5gHAa5HTJ85MawRiRVp/dCjUhXjcitA/atdry9csdz/EONyJoakZdrRALOMdU1IitqRFYAXxrw2aoakbdrRJbXiKys2ZNN42Q0HKlbwGmxv3B9xE1yAyTzDO4hcfzhPjITqII20F9O40ONWEpxA875dqDhL2iIE0VHdz+aN+xBO1W2OPtyUU8xNTM9HP0uHGXsl4mIdMkAmYjIasfbK3c8xz84zDUviyMTEal2imL3kMmxxDDyQSAin3Gef4WI3C8ifxGRKwbs73X+zxaR+SLyDLDWkfkGEfkr2ipXKiLniMgbIrJURB4VkXTns/UicoezfZWIVIpIOUpscIvzHczUY3GhOjUZHaTnoWuW1PURrDqXl9p6yOo5nfmtfiLrbVxj4riD7eT6wLXWSzj9j/zbkIe41u4mI3Afn/OtZmLxk1zWgnqacWAj2FumssT3ENdkzWTBemdfD+y+fqpffuBi9kes3A41PnJjWG3MTSj32hnArw9waAXw+2plaekELne23wfcXG3M3g3lNwF3Ve/NpiHiR8kLXge6/x/fs9solCgBC1xr0fA5Rb/lRRlqRqAjaAyt6E+jn7U6F+1JLkcN5CbUYyxw/lLrFrtRpexB17ndhipjCDWI03DanMx7lInZj0zMfmRyLDCMvF+ISBXaoXOmI5+vvstHpgBfNcakKLAqgLsdmfc55/qYMWYKsAT4zwGfbXW2/w+6nGY9WqT/a4eAY75z3AjgAlTHXkLzy13VLFkNJvEYVw4FREi6i9nVZJAtEQJn95DuyaV1QxrhnhaKzf18JutJLhpVSHPZg1zb6iVc75yzR7A3T+PtGEjs6/wyjf7ffgIIG0MXqoteVA/TAJeTXz9S5XZIcSQTNeyNumpjljuva4DyGl0GMLvamNed7feji6qDsmncViMyDHiiWlfrAm2ID6GGIPBlfuttYLhrCyPTtzCqq4PMRCHNk1zYoSpWtwxjR+Jhro7n0Dn6Ap7fkkeb/Xc+WXAbP+5+lguibzFtxJnMa3iQT82azsJV3WS1LuGkYUmk1cbyCzLm8/w5v5ol4Uo2eN5imvd8XvSMYotpIzfSRm7kF9wancvMyi6yrxMpufs9FLzWmb1kIo5MzAFkIo5MTL9MjiecCTxqjGkFMMa0O7/3/eFtY0zdgPcNxpg3ndenoJ7/QuccXlTGKTzh/K9BWybfASck/TrwLDpAp8JVXxdZLg/xtX34Tsqi8+XVTKCIZkswKw1yU4LAvCFsdG9mVCNQkknP5lbyL5vPzFVt5G90EctGc5FcwHORXtI9C5hpkrhPQDuM4jiTdyK7l6s1QHw0G4OTWDrsPJ7vfFEdgCNKbh8GjjRjmGBPb9U/4HV0wOsk/SHqPlFtzIM1A9g0akRurFY2jedQTy4gmDbnmn6ccgOgZSdllwJ/q+HkXud+lgFTa5i2ElWgpk/wvA+dRMlYxrReoPVxrgqgo95mNEfjAbiLr5U650g6z5FEQ5OU95kE1gKv7cMQHjKZHEsMI4cYu2Xs5LO8A/btzSQ08L0ArxhjrtnPeVPfT5L9/Nac9UZuR8uuos45EwAVbHR9lbsabuZ3ZTHcw6eyZP1qqtL8RGJ9pC8VEidcxLMLf8J3twAXfI//5zuNeW+sYHIrELSREmD7jdxTUMSunp/wHVcSdwFaa5giaog7z26jOml9iztdn+AfppEh9Veax6LvuOkjQG4fBj7yMHkv1KPuNTUiU9DwYb+oNqYT6KwROc3ZtHuyocZh06jem01Dm7HrgCEvcq4/g65UmU0QDTVb0cLpFDkr6AI6YXRWGPSLzUGVoQMNKRrQ3J+ghi1Cfyg9nn7mmgAaPgdRBYyhnmqLMftcTH63TOQgZGIcmcg+ZCLHEMPIB8CrwJUikgfK6oPKOEVVdhHOIHYQeBOYISKjnXMFRQlxD4R3sAQZQzOai65gQKdSOr3Wm5wswGoba9wGxgbrKS8Yxo5WcM31Epv8B27IBDImsoIy6gv7yFz8Y77b4yW02eAZ8Wn+kj6KTaV/4Asb4vhaUZ2cSD9JSIpc2DeeNfI0F7kv4Un381zQtZchPOLkdqhxpBnDx4HcGmVi+TJQexCf+Rzw+xplzRjot18FrK7ZF5uGMRGgcSSbywOEvG7imehkRhwtmH4BmIF+uSmjliqhcaFfTGoBnx7UsNWjkyhCv2eYjhrKE1DjmYMqehdqXFPteGFj9lg+9B0ykfchE9mPTI4FhpH3C2PMGuC/gHnOBNOvgD8As5z3p3KQvJLGmBaUFfwhUYagN+jPEe8P/wAu3cdEwHq0bCsXcI1mowxjW3I94yhhe7eQbNrK8BlFNDf3EUyczBvbgXArRVMK2OX+Cd+J/JqvRU8zC6LD2Bn5Br80M5m3Yyo1Y3/J12lkaKdzje3ogDwK1WU3kJjD7ckFnBaooDZxBY/2/YTb9hiYj2C5HTIc06w1B4SILGLauCe5LHkXt/h9hOt6yUpDDWIIOA8NgxegSuNCp/dT9FaZgMcYdogwGp0tvhYdQUGVJR/15JYD16DKkupWSbU+2ThLhn74Dz2IIxkiFKBe25vVLImdzjx5ifPIolPWMXZkN5knj6TuyU2MjY5mQ85Wyipj+CveZNriOB7PTBYWAq+vYGIyiatkG8On/Be3tSymOsvgjqMRygWo/v0QzVMm6ij3dJCT3kZuz9n8KwG4jSH2Ucnho8KR5hkeNgiGn/CdRh+REWNYn/AQd3sJu32EUzNqr6FrpQSd93G0L3k8Gtp2AdlOWUSvc1wDavxy0BF4B2okx9BfmhOk39u0Gaz8H0Q/OtHJispCmq0oXtZSZTYxumQoO1s9xNc0MPzEDDoKY3g9MfwLR7Mhp4Oc8v/f3pmHyVGV+/9zqnu6Z98nmcm+zmQh6xBCBJIASQjIoghBFhWvVxT0Kqj8QBAJj1xU4BF3RQWjIhriDSiEJQFNVEhIMtkn62SfzL6vvZ/fH++p6c5kggEmyWS6vs9TT3dVV1X3OfX2e867fc+9PLEPcfGMKGOMu4i9k5Jpf3s9MzMsIocQmW1HLBUPMKqACterXJmu0Inf4VvNRhFq+ljC9ZlC3CpDwHqFj6nVXOq/m6dSUmjL8uJvtAgnpdPoQwRnGzL9DyOK6wiSRJ2GCFcnMvsLIrmINUhpXhPRGWQL4jcsQoTQVoQWEOlLPIYOzjrslfAytjJlQDOZOpGOAV58HaM50JxH3eEgnokZtCiFKr+Bpa57+GH9vTyetY6PtAJHc6idMIUtU5vI2PcvLukA2kHbA+4uxMIp8OCf+Eu+mLqNydYIDre+2JWVBRzPrhQ3iFszWSkSkIBI8nd4IL2ZtMY3mV9/gNGtLWTpdJpVhIjVRtbtyOpzNUTL6FKQ5NEBSNBkC5IrWAd8CngdIXi9Hck/zETyBf9svt5Whh3273HM5PhGDFEDQHo6TdMCeHel0Ba6ildrV3Bljh9PcghPjkU4vZO01eUMmthKmhrPngXAkjt4uiOJ9q9Y6A0fYe27k9mSWERZDpClCA3UuCuBLyXSeXkSnc1F7LlsHbMaiUawLegK4rnstXriBXGn/WNgr/3QvoTbay9gQ+YVvBG4gA2uAo7pFjJ0Cv6ARWATMjt0IUJThzh704iyEmtkBpiImMZZiHmcbrZExGGdTDSdwYGDWNhkv2oM+1o0qjVIwriJlLb8g7mDNUq5iFTMZ+VuH0kZ3+TRwkQ60r/ET/cB2xLwX7yYhyflUrf+Kb7u+So/CgXxJOdQXQ9kW+iGRDpcA6ksT8RX3UxGyjpmjUWCNiFOTGGJO90Qdw2GrlE4CzFbrf0UNoVIOPwpnhu8i3HM4h0NRKrJJ4JnF4QLkDwwO49wJxI59iHK0V7bNgUpucvliJjJAAAgAElEQVQz55abLR2J3kK07M7ONYz9TQ7iFzZNHK2kZaTQVg6o7UyalEx7cyod9dPYYmlcgcEc25NA8NrVXFb2d+ZHMmh892pWXLKT8fyKO/4FJFYwOHsPhYFv82gI0Nk0BMdTmqexDjeRFYjg6kRKQhMQomKIMrFDHPoNz3llqD4YK4uFKCwf4s9ru4Wl9W2khP7MzdnvMlNNYEsykuaQDK6NEJ44k7V2SswBovl+Tcgs0Z7tVSIBE5uKya5Lnoz4GW1z2M477M12OThD+DDPR/XMwGLLQ4ofb7KbUCiRziMRyMqkORTGbVlEPCu4xr2S+d79jG66geWJQOggIwcXUHHkKl5NOaJHhJA0r/ynudMzi7WJEyitL2Rv4j7GqBoGHkUGbS8ipzWIjA5GLBjbAuoibuhtnIa+6xWc88rwAyIdCXx0IsosDEQ+y2/ryikYMo7dg5vISihmQwAxg0vApUqYPmYKWyyi61OMMPmB9ijrQ0zheqRvD5vXTkQJjuD4KLKH91CIDs48lFJn4nnMJYaBxczA3Bhi4FRaXc1kBX7IPWVugod3MnFaIu2JDWQFNzE5YzCVSa9y1QpgSjkFIxPxJS/n+uUBkqYqhRuT81rNgPpa8gYVs75hLbNcHaQFEdk9gsj/RKKr41UQ5eJ08wGCKGej73oT/UUZupVSfzQsLn9RSl2llHrJ/lApNV8p9WLM+aMRBVhpXlOAnF1MSmgm68CTfC0lgUhzGymRRfwJRDDeCeG9aCtT9Xh2upAE1kIjyM2IcPkwHIlIQOUoUTaQIGJqg/R7yHy3xckz999vuxy8B8yMZHe3Pk02TCnfV0ptQqospiql1hmGlheVUlnm+pOxAd2ulPppzP4rSqm55v1Cw8CyVSn11kkYWCxkgB7ixRcay97aC3i3bQ2XJM7hn/v9uCNBEvLn8ZYaxcHCbUze1Uhu5XUsT17D3Knf4aHSKgZVIfI8GWjLpjZSzKbh2TTU+0nyRXCnRHA3WQQTkMT9RmAq0RK6EOLSiSAuIHt1vr7ed72G/qIMixBGjPHIyDcRGKeUyjOffxZ4FrrokiYis7YEpEokjMwQG7/Ir2vryQv8gjsG7WFC+AI28A/mWohiawKm7GJCRKPqXYT8yGzP9v/5iAqVTfhq03alElWG9ggaNpsGPEqd8DxOuV0OThnd+/Quc7xeaz1da/1npDLnPq31ZGA78LA552RsQD3CPKdfA58w19x4EgaWDIxrJZ2W6gt5138Jb4evYUXAhY4UsWdnJQWjbmTZFD/e8kt4u/UAIwY8yGNNd/OU9V0etH3Q70J41rdZnHoJ/654kY9nbWdS/cd5USG5sBFQiYh81iPZEBlEl6CImOONSKpYVje/YV/su15Df1GGR7XWb5v3zyGldH8AbjMsLrOQEjuAixHfSBXyQBsRpdWVXvAA3z2ajD/vGW53fYMfhFZwpXUFr4NUo8wEXGitLUIHIDQVU1uMjO5tiFnsNd+3H0mkdQNTiOEtJJpiMxH4BrDMmDkfpF0OTg3d+9Su4V4KoJTKQFh/1pjjvwNmq57ZgP4TLgT+abO3aK0bup9gBsDFZvdIDQM7H+NbwYd5RG1jkmoghyLKOj/F71r+yeyi+aw6fJTB6cm0jxvJobdrya9GiBfQqCNDOep+jYUj91G4s5Hs7K/zpJrHm+Es6o4hKV6JFiF7IhBEFJyFrH+igcjDLG55hG9XzGBdwQWsze+rfdfb6GusNR8U3XP0NDISvYwoumVadyWe/hMxXS9GfCWxCAHu9VxofZZnUmeyodCN//CT3KfSaMGNnxDeROAzSrEPEi1ghEV4XgRXOzKaViMmsoXwJrqQ0bcNmGE2e+HuGYhy7UBYbX7dLbfr/bTLwamhpz6FU6yrPQnei1novX+MJqIUPwQ+CTSP4EBTG6n+ERyMtJKqQVlhIklf4Oe5d/KL+lwaZxxlcNZyPrHrSe7zIrO8Sy5kbcvXeNIVwr19AxfOQvyCHa1kjn+Axxo/wtrQCq4piOD2KUJmlkgqQii7AwjNUmvVLRzwVJLvOsLQSCNZVWUUxaaB9am+6230l5nhMKXULPP+FuDfWusKxCn8LUSBAF3JzSuATYjfbz1CLrnBbOuAjQcZu2oxj7yzh6KjwNrpbHrnezxQMpDK3yNBkm2IYv1bBFf7HFb/24NvA1CKKLY3kFnnW0gU2SZ3KEJqmG9FBPlB8/5hrWX6+UHa5eCUcUKfxn6otW4GGmP8UZ8C1rwXGxASsJiqlLKUUkMRol4QWZqtlBoJXUwv0I2BRWsOIBx+F+dT2X4xbzflUePLoMk3lc2tj3N/wSCqSufx1tvX8LcLtjNl55PcdxhoHsn+I5PYWgZ6+G7GVVQyZDXRYMxOIH04h6uf4b/bECulyXztJiA1gcDIRSyNrGR+6CU+1jaB0trlfKLqdT56bJ8uqtKa+r7cd70KrfU5vSE+u93ItH0XwvKSbD77JLCu5+u0BXoQ6BzQKaBdZkuy9xeywl1P5oQ6stNAu/OoSrqclZkWgTmgF4BW5pprQWcm05rnoXMU6FzQ+aC/BHoc6JdB14NuB/1H0HNBp5vzskBbvdUuZ3v/soL8IXNjzpuK/Bm3AS8BWeZ4MbAVqTh6HNhhjivgj+beLyLEqXPNZ1cig+NWhMMPxD+4zdznEvt7E/BPT6TtC1fz14wf86WET/Os92UWjHmVeZfdwJ8yjzHw4nv57gzQs0Cr61nmPcSwjF9yR6ZF8EbQGUa2LwB9o5Hxhcm0ZG9n4tAcaotAX5qAf1E6Dd9Jo/lIBo3rF/B6wXQ2JIJ2my3hXOu73tj6dTmeiVJt1lo/0/PneBDTthGZvdkElyDTd/0CN6ReyNohl/H38jKKAnNY3ZlJU9pfueYWcC3VmkalmIiYHNuAYQOoOlJD/hyEpbcQeeA55rueApaY77NHaa1PTuH1vtvloGeYaOQrWuvz/sOpZwdKWRnUX+QiMvYC1i9dxDL3fFYWP8dtW27khaIDjGq8greOAZPGUdq2koVHfSS2F7IvjPid07TmbSPXX0HkLAdIWsHCih2c53maO2e4CHsbyBzQRPadYdzpiMvIXi/FXjPZXj/Z/DQ1gr7cd72A/mImnwClVAmSZvBcz593JZbWEZ12JxBl/Q0BkUX8peN5btY/4GsDAf8a5qoQ7o4smja7CF1krjuEZPHnAZfXkP8s6DsgcgRxFv8ASXsIIkEPRZTO67hKlA/bLgfnNFQzOe9YRKrXMeO6+ayasItx5Vfz15HJdDZcwVt7M2hq+wpPHQzhLprOJm8h+0DM4t1AnlJd5XVbgOIrebViMOWjUmljLqs9deR27KOwsZHs/WHcHnOuvTCYHVV2FpHvT9BaF2utZ2utT0ZbbrP8BpAa4wFEE6FtQfAC3j/wmbJreFW9zYVpgLWCq62JlG724C9QirFIIuh1iA8vFfgDqP/2EnjeIuxFnNlJiL+jEAmY2MErzftQhqfQLgcngdb60Dkws7Ge57ZVs/lXxhN8Y2YmjalAaDqby+7nMfdWpmSMY09bGUVr68gbg8irh6hCnLqIpe5Ps2TLII6dP4c13jCu2gWsSsymKeAmUAsQkbzqMiQDYjQnLhlxnDI8R/ruQ6HfKsNTROwssAGh3wqaY1mIkuosZVKghryj49kz9PfcBhD+N7Mn+0icBJGlyGzvOeBniBlcBgT9JFoRXA0uQllmlpiAzELtLH87zSbSQ46hg3iC6lpdSV3A+qSf8eW99WQnLuH2C6/nbzvu5XveeaxK+ROf7LyLX3ZqTTmSUTAYaC9mY+AL/LJiCIcH1ZBbOJ83O+rI3X4/3y+qouCgn6QRudS1LWBVM1E53G7eT0Nk3U7+77++s/dAPP8Bu8/G/IjpapMstCFK0qU1eiA1nffxvcT/5cF7geeBz2lc77gI/Cqfij8gUeNapGa5ERG2CNAexp1loTdBpMMcKzLfaZc9vS9T2UG/hG2p4MU/IYFQ+1f50Tu/49PhJNrnFrPZfS9PNn+T7wOEjZtnVxb1Qz/HrwdsZEbSF3g6COpfq7l81Kd4jgDebUi+XjWQ9mV+an2a3/kQWQuAVQ2RRoRdO4NoXTI4ZnJ8wAiSi2iOk+0nAVGKdvlcGEhVikXAz37NHfd68SdcwWuPAfcAy8Ikvt5I9oVPcwcIE3YhUp5nLx51CMiN4DoIVoMiYlei2PWoLqLJ1w7iGxYwPEBCOmC9zpXbPs3vXzvCsAmX8s+Rmym2gPBiHnYv5qG0Mka7ZvLugRf5eNFUNndMZ7O/nGGHETdMAeKeqUEiubV/5LaRBVTqTBrqEPmuQmR8uNnPIsZ947DWxA9cHN/+VGSWVokop2uRyoDfIg7mJaBuXcJnn/gBX7ce4FGQiNshP4ltD/LopG/zSCOiTEcjAtlh9iOIULo0Vqcicn4KbbZCtgM5zswwvmHXJ48L4g7uZ8zeAioTdjK5upmc5xXBOaPZO3E3hYkfZUVSPTmh2/hD2+tcdaiB3KatTB0LXXm09oJfFpILOA1x3Qx/nlvVZLa1I37GdiUDtwtRnnadfhe34pntgrOLeFWGsbNBN5IvFUBGyK8CTyMLQm1GFnJ6DEnOtqaz2dVKSs1DPFoQk3rwrzoGXPB3LktKor3K3OcwIlgdSJJ0OpJkHdJYkwAyaIpdrhHHb3huoUSpNvM6qESpv3zgG4m/0A1MD+Gyqsjfu5/RwXt5onk6JepVrvBfxWtv1JF7+WzWZM9gY+eP9d0d6/iIrbB2I+QKGQBaU4fUGBciNfURpOKp7mWudn+FHzUilR6ZGqsUsVZGmV8TRiYGEKMMlWmrUmqQ+jBt7cOI1z+fB0OOgAjR9UhR+N2I3+9O4H+ANYDHKD1bMELX81JdkAQP0az4eqDmHWbNvI0/7vXgdyGRaJtGvRIJzmwy35vbTmpmM5muOawOc/watg7OMRRrXVGs9Q0f8jZTgEFtpG5dyRXN+xmtH+ObKUXstf7Kdb4VXHuwmeyXaij4GFJjDNFUmBBSbTLJEJGAJCpPQIIi7yC+w0O7mTh4EjuC2dRVIHmFtrKchbiHbPKQNHqYGWqtK/SHb2ufxFn/8xlqoF1KqV8rpUqVUiuVUklKqc8rpTYY+p7/U0olm/OXKKV+YWiCDhiyx2fNPZbE3HeBUmqtoQBappRKleMopHb4KuC7wJOIr+QJZM2S55HcwzASCPEoRRqi3CKAv4LBwWf5r4pqBgz5rro/CRHODRHco//Gta4CKve7CRYhJojNF2ezhdhrTowGvGuYmziB0lAOtXAc8/WZ7RcHHxwlSo0oMWSlJUrdXqLU8hKlXi9Ral+JUo/HnLegRKm1JUptKlFqWUm07wuQxOeNzWQ0+fCqFtJCX+eptjt52v80d9mulCNIhcYNSpEOUtts7lGDJPEXGQvDh1gn45ES0RxEpnP+ynV6Ju82IIq0HOiAyBSiWRQRjMXU3W+oYohZDf3WcqXU60qpfSqmreeinJ11ZWgwFviZ1noi8kA/ASzXWs/QQt+zC/hczPlZyEh2D/A3JJ1lIjDJ8KnlIrW787TW05Ha46+Za68EfoOUDb0A3AT8Agl+2H48m+U3TJTW32uqRJTWRO7mR20vcV2rQk8D2k2qw+5q8mfmUleWTHt+Ok0BxOTwIaZyK2KOexGfThiwdjKREK5gIh1ZZ7FfHPQepiJyNQm4qUSpoSUxfV8c2/diIt+HcAzu/Qy/bXuQ73X+kK+HfCTb62rHMk9vR1ZevIko7Zs2SnEnoljzkNnidiS7IQl4FyEFKf8xX8m5mT+1If//JrACSgbnTnNdCHHvZPGf64CPa6tSaui5Kmd9hbXmoNZ6i3lfgtRBnqeUehSZdaUixAc2XtZaa6XUdqBaa70dQClVaq4dgpgIb5v0LQ+w1lz7DlI210E0ydqmM7KZpzXH+xTbkTWSAXzmNe2r/Ni/lBvbfsyX85X6aTXiuL6thPN3uggcyqT5vAT8NUG8HkQRDkUieCMQpbXcfLduJlsTLc87G/3ioPfwVrGQFlCi1E7Eh5yJ6fuS2L7XWqPUTxB3zaiPsuJIEXtIplO3k+xOpl2dx87wQUYkfZ5nQoBnC1O3fZGfZwbxfvJz/Ob/Huf+wCE1nJ9wtfvPLNp9gDGTH+I779zJL0Nj2HfQh7f4Bv6yZQmfnT2Sg9srKSicz8odY9ndVsvA4X68ByMwuYCqIem0NM9hTcdo9ociWDUf56WIJEScvK3atFX10NZzSc76ijKMraYIIyPZEuBjWuutSqnbkSqP7udHul1rT+/DSGH3zd2/SGsMgwa2Xw9kthYy1wc4McLbgSizPKKpN34/iQ3X8Up9Gylj20mt+ybfa0MCLeeH8bzVSvqVhezbV8aYZD+JFchs0F5tb6K5rwV06J7XTz5j/eKgV9H9udkLLa0q7qnvtS5DqcMBEsZdy0sTXuG6PQ/yaIcC7SPZtt6Cj/FQ7Lokq4Crn+C+hU9wn812HtCadqVIv4tfjL6LX5Qis8NrfsQ9LmDTFqaNBHzf5PtWPtU1+xiXiUSaZx5m+GiN691tTI0AneIr/+EHbuu5Jmd9xUzuCWlApVIqgeMpf04F64CLlFJjAJRSKUqpwtgTtCaMPEg76z6EPES7HM+FPNigUVRBZIY4ylzXoTVhtPan0l51P98vGMV+hXDDFQCZAbxHDzEiN5PGJETxNSEBmlZzjifme04Vp7VfHJw2rAMuKjF9X6JUSkls32sd9BIoPcqw2kUsnbac6wf4SHblUKuHckQ9zGJbEcbmxL6G/IcXAuGY7Ia9SGQ5x5y7HTFn1yN17ZUvce2Aj/JKCyJ/R4GARs1EBk57kvCh2nquyVlfVoYPIX6Ot5HUgVOG1roWCYb8SSm1DZmij4s9J8YxbCdXJ5pXmz3GY/aV8cvY9OiHEV9KtGRJ6zof3shaZmUjinIjMBvY107qGI1qz6fCi/gN28w5LkSxvtcaKD3htPaLg9OD4pi+L3mPvp/PPyreYP6W4RwatZGpY89ng+sow4KPsJg5rNY3sMxmpLZLOV9E0mZm2/cwA/0Oogs+HUBcKskIp+GAJnLyp7GpOZMGH5INEQJmpNLaQXR97w+Ec1XO+jWF13vBKEMP0fQXTdR3aMqVumaIWUgQpM2cl2K2Gi1MxervXOq5lNVjNlJ8YAYbQ8BnEPLXAUBdNvWEcSU3kzkbuAah8P9/SKCjXWsJJzuIX5glHywgNJWSpOe4dVQGLSktZGyfyK4O+7QpbFH15Lg+xzPBxXqxVook4DZgs9ZsNPeykABcEuIjH44ox41IwGPdXP7uC5CQ+Q6XTEfSybIGUjW8mvwC4LA+nnW936MvzwxPN7qX5EWQaFoSUXMkCTFLm5D1UkBM2k5EOeYapaou4x+htVxYO5hjI25gWRj4O+IbPACMaCDHn0hnhwdfAJnRhZB0igA9pDA4iC/ElIgCuLZQHD6P3Xs01pEhlE9rJzkfk4u6lamqnKHBxXqxXqwWK43yZdGwFLhAKSbEyNJ+xKLJR6IgfqJL2SZt5PwBF/F2CzLIHwSSG8gcYK79sKbyOYd4V4axq4JpxFRuR2aKA5GZYxPimLYTr22zuhXpv0GYCPQsvbamgCrfMhYN0ELl3oH4bQBSqinQHoKVHvxViDKcQjTVpq8EsxycXdiBOw2EhlFe1UHKpiDuoXsYO+lJvnYc8eojLJbVknR2SzJtyyA8LwHfaKKpYZsRYhAP0TK99cC4NtKtFNo70yWJoQzwBEmcnESHj9NFrd+HEc/K0I6u2rNCm+jShQhCOybaqzXaRJ9tJ7Zd11yHKLNMwKUUrlImlAM5SDL0v5HM/91IwCShjbRai4jfItyOpLrYjDX2+rUO4hP24AzRdbUVoAuo8t/Iss35VHV+nt+cX092bAEBSmEphbuD1AZwLQ+TcFUSHQM0Cq1pQQIkE40rpomY9LG/cGP2aPZXIJZPELjMTaAVISiJK2slLpWhecgJRIvRXYhS8iKKsBUxHQJIACUR6SubWCEcs9WZY8mAPo9SvZHiqkoGDp9AaTVSipdHNErd6sejLCJVFmE30eh03I3EDo6DXS9vD7o6Zp9VXBHOoHVfJ0n702idVKnyh81htUX0PxxGkq/LI7he6yT5EwqdhVLqRl444CboVYqhSJneeGTxs4Iyxgw4nw0NRJfMvdBD0EeU9T1uEFeNjUGsiWxH5dLM+wZEsOh2jhsxT+xa4q61ZBCFmAikaE1oBhsb2kj1L+PGIUiaQTHis8kGtMZVFcJ1wEVYWYRnIrPQ7tUnDuILdn5ebLWJrRht5ejKp7r+RT6+JZmOvJUsmKhRXQwzdq6q1uwD/gUsUuiUZSzSIRJKLUJjCqgIuAmWm+/M9JEcbCCHdJpCSLZDYT15uPEHkSBh3CBelaEr5tWDPHQfUT+grfxsf6IPk5NoFtvRMTlddn1oHWJapACMZf+RCexKrWZAJ7APMZOzzT1rwDoWxB1xE7x0IFWtSBDFofGKQxhLxR6U7YHYNpXt2aHtU47cxAsdGbRu8hBsC+I+/whDM7sn7WvNVmQWuAgpJW2J4D5UyaDzQiRsdxEa6SK0DcjbwrS0IZTXEmVaynWJGyfjNDe9TyFelaHtn0tGzIEWxCTuSm0wGyDKzgibnW1v14TGIoJE6TIAL7K4e/kAaodNoHQdMAYpxctDZp8+sPxBEqZ/nl9FEghootRJDuIPHo6vQ7YDKbZPO2KnumiNvb7lgVry9g6lfDxKjYxZOsDGu4i/8HqTtnPI3C8vjHtfGHfYIpxXQX7GBEprkZmhCzjfT3Krm4CjDPszzCicifALhhFFaM8A7dXqYhNbIzHXoXVX6Z7HCJj9mTKf1QHZSuFB6xagrZTzMpFIXh6iLMOIkDZpXIOf4zYrh7o287mD+INNKdc96Tccc+wE5mmlsAZT0YDkDqYDU1HKq1RXoYAG3kTcMNeYy3YgA/NhICeCq6yT1PRKBpFOU7UiElRE5gGBELhtGY8HxJ0yRGp5/wsxWe11TmyfTBfpqx09NpFkW9lpOK6Uz6WU5GPF+Gvs1fZyjCBVABkrmb8HSddpR0yROmSkdh9i1IgwqjqVppFnoP0O+hCMbD0KXIHktdoDs232dvcHanOdzVaj0TqAJFbXB3EXV5Kfa6wZ253zChIcXKA17UgazTiE2isIDNxHYdpAqus1VpvGungOq4NefD7iyJcdj8pwOzIr+zLwU+AWJDs/HQmiZJgtHUhTqms/TSky7M18noSYtnlKkRnzmZ2wOlKh07/Ok80RrLEptO5C2GNGIIq4xpx3dS0FLj+Ju0534x30SbyMBNmWI6zq84laL+lAiuEvTFOKdCNjqTH76QqdptDNj/DtI0cYNmmF+ujU4epwhrkuBZkhjlSK+Uh6TRIS9EsGGurIHppKiw8i9cDoNczN8pNeB/FThRLv5XjjkCqQjyBmwxok+msnVkPMqBxzbXRUln17mcVQbAmTIYVNBmo1alAz6VYmzZciFSwdSEnePcAqrbvMGAdxCqO4LgcWIHRvbyIUbbtBZoXdZc9c15UdoTUaIfEoQpRdKVp3mvNSkLK99eae9sp5U4Gi8ezYf5gRN3eQOg8YoDWNZ6LdfQXxODOMxV7gGeAOROhmIuuf3IWQVVqxijDWF9MtmhxrNnti/Iut5njOPFZVtZCecRlvlgK5iJI8iESqp5/+pjo4B9AO/BWRvzvN/neAPwC3KEUeJ1GEtlkMgNZBtN6B5LhOR6mBcph2YCky+A9HSkVzMOWmxxiam0VjGaJYLz4D7e1TiBtlqJR6VSk1KPYQUad1GIm8PY4sCFWG0GP9XCluVYohPQpdDIx/xm/u5zVVARZiDltvMS+7nZSDbzG/I4W2RsQM6kBM5bzu0ekefq+DfoSTPN/YKpRjyEC9CFmSYiiiFJ9SigVKkWjLzEm4MEHrY0h6zXCUGrdazXUh6WMvIT5KhSjceqC1lZSsTJqrFZEQcN0p/N5+hXg2k23lExs4sRlsbEU5BKFGmo2MnmuAf5sSp5Pd165u8SLmdsDcKxcIaVTST/jy4K/wk7mIiXILYhYVak1ZLzfTwTkEk8Nq5xZ2ZTLYHyNydTGyfs95CCvSa8CWkypE4Gn1BfdVvDrOQyDtKENLZ7CxFRiGLIf7IlF2m0vyqfA1kfU/PpJqtWZ877ey7yKelaFdn9l1KGZfx0aRzbHzgDnA+chaE2uAEq0JxiTN2grWjgba+YwB85qXSaOvkeyCyWwt3s7koQjF193AfVrzZK831ME5ASNDbqI1yRCVxy6/dYxMZiGBliuRwN8bwGtac8ScZ8tjNE8RlYek1RxC62NKMQ7xUa5CykIneOkYmkLHwgZyh2kdXxUo8awM3USTXHXM63GKsIfgSRLiW5yD+F3WI6VPu+FEE9p8jxuZJWpgwN08Fb6Gl8dczStXdJIcQghb12vNJaevxQ76MmKUVyx5CBgZPCFIcvy1YxC26wWI22Ul8BbQeMKMUQhEJiC+6t0KPRkZ4LcBIyFyaT5VQ6vJX6CxMrSm7fS0uO8hLpVhzEzOVoJ21r+tCE8IksRcGzviZiER4Tlmfw2wpjtRq7nGZs7WQN5rLPT+js/M/jM3T0LyHtG6i+7LQZwhphRTx7yeiiKMtUosJEXnCiRSXIKY0evMyo72RRYyE8wFdhmFOBZx21yURlNhEM/HfCTfrDXLTkuD+yDiVRnagmM3vrtvpqeI3XEMId2jzMhSi3MQ5XgUUYxrtaYz5pwEc/9IIp15f+amQV/lx7ceZsQCJBUioScF7KD/o5ulEiGqCHscmHswg8PdZDYVKTC4Elku9E1EMe7uOk+W9CwEyt0EJ4RxDwUsF4GrUmhf2ELWq1rz8dPZ7r6EeFaG3cld4UTfzAl+wP+krIxQT0cUo70a3hpgu9aEY8xmNZN1+cVsvPxZPneTj6R5QJHW7O3Ntjo4N2CCJ5JQQFoAAARvSURBVMeV39FtYO7RD/gegZOYexcgZvRCxCf5OvCG1tSglBeY0EmiHsyxwkayCyAyNZWW69tJb41oa0gvN7XPIm5Sa7ohtsbTFjrskdgoLHsWF9KaoNbHj7wng9aEtGa91jyBVLnswSxUrxSfQpixA4B+lwvrA3hKJrKjFhHuu3qzkQ7ODXQL1EG3EjylcClFAlFS1qCRs/+oCAG0plJrfgt8EvgesgzA75TiRwp9WQEVu5PwNR9meOVEdvjBavKR5PfgG/Ded+5n6CLlO8sbUqLWCWwx+w8itZPbEJKDmeb4akTBbDHbX8zxxUhu1hakGP1ac/we4Ajw0+h3aTfoBNCW2ZQ55jGvVu+3Tw8GfTPoX4J+AvTVoHNTaM2+luV35lATAF12NvrD2c6uTBoZ9HSTR5eR0QTzXvWyPHpAXwr6cdBvgH5oGiWX7mfE3MtZ9YgLf2kSLRHQafEij2dd4LoJ3g7zfhayvKDX7OcCg2I6+/werl8MfMO8H48QIVhm//YYwVOgvTFCdtoE7iRCqECfB/pLoJeAfiCNpo8VUtrkodN3pvvD2fqETLqMcnKd7kH5JDKZBXoR6GctQi9NZ+P3P8I/30yhKQKhB+JFHvsqPU8BUKe19gNorevez8Va611KqRDykGq6fayQhOhOsx/S+swFLcx37QB2GMabma1kzAngbUyjdXim8rmadGa422Wnsz8cnBpO5zNIJJpf2MVbeKagpQb5BeAFpVyjNjNtYTIdF6TSihffPTDwsW6X9Et57KvKcCXwbaXUXiQKtlRrvSbm8z8qpWxltkprfW/sxUqpmYgPrqe1iDUytY85v9d+9weGn0QrgEcP44gbTlCGp7M/HJwaTucz8CHLeppze/V3v29oLNpJpYPkAD0TDvdLeeyTylBr3aaUKgYuAS4Fliql7tdaLzGn3Kq13tjDpfcopW5D6i9v0mYOfvy90ciUvw/Coqefdjr7w8Gp4TTLZJg+KZM9x1f7qzz2SWUIoLUOI/6H1Uqp7cBngCX/4bKntNb9sqTN6Y+zD+cZRNEf+6JPptYopYqUUmNjDk1F+AbjEk5/nH04zyCK/toXfXVmmAr8RCmViTiWyxDOQRuxPok6rfW8M/0DzzCc/jj7cJ5BFP2yL/qkMtRalyAElD19Nvckxxefxp90VuH0x9mH8wyi6K990ZfM5DCQoZTa0ps3VUrdA3wTTs5B2Efh9MfZh/MMouj3fRGXtckOHDhw0B19aWbowIEDB2cNjjJ04MCBAxxl6MCBAweAowwdOHDgAHCUoQMHDhwAjjJ04MCBA8BRhg4cOHAAOMrQgQMHDgBHGTpw4MAB4ChDBw4cOAAcZejAgQMHgKMMHThw4ABwlKEDBw4cAI4ydODAgQPAUYYOHDhwADjK0IEDBw4ARxk6cODAAeAoQwcOHDgAHGXowIEDB4CjDB04cOAAcJShAwcOHACOMnTgwIEDwFGGDhw4cAA4ytCBAwcOAEcZOnDgwAHgKEMHDhw4AOD/A2C+opIWliKDAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def plot_attn(title, examples, layer, head, color_words,\n", "              color_from=True, width=3, example_sep=3, \n", "              word_height=1, pad=0.1, hide_sep=False):\n", "  \"\"\"Plot BERT's attention for a particular head/example.\"\"\"\n", "  plt.figure(figsize=(4, 4))\n", "  for i, example in enumerate(examples):\n", "    yoffset = 0\n", "    if i == 0:\n", "      yoffset += (len(examples[0][\"words\"]) -\n", "                  len(examples[1][\"words\"])) * word_height / 2\n", "    xoffset = i * width * example_sep\n", "    attn = example[\"attns\"][layer][head]\n", "    if hide_sep:\n", "      attn = np.array(attn)\n", "      attn[:, 0] = 0\n", "      attn[:, -1] = 0\n", "      attn /= attn.sum(axis=-1, keepdims=True)\n", "\n", "    words = [\"[CLS]\"] + example[\"words\"] + [\"[SEP]\"]\n", "    n_words = len(words)\n", "    for position, word in enumerate(words):\n", "      for x, from_word in [(xoffset, True), (xoffset + width, False)]:\n", "        color = \"k\"\n", "        if from_word == color_from and word in color_words:\n", "          color = \"#cc0000\"\n", "        plt.text(x, yoffset - (position * word_height), word,\n", "                 ha=\"right\" if from_word else \"left\", va=\"center\",\n", "                 color=color)\n", "\n", "    for i in range(n_words):\n", "      for j in range(n_words):\n", "        color = \"b\"\n", "        if words[i if color_from else j] in color_words:\n", "          color = \"r\"\n", "        plt.plot([xoffset + pad, xoffset + width - pad],\n", "                 [yoffset - word_height * i, yoffset - word_height * j],\n", "                 color=color, linewidth=1, alpha=attn[i, j])\n", "  plt.axis(\"off\")\n", "  plt.title(title)\n", "  plt.show()\n", "\n", "# Examples from Figure 5 of the paper.\n", "plot_attn(\"Direct object\", [dev_data[42], dev_data[1286]], 7, 9, \n", "          [\"funds\", \"plans\", \"line\"], example_sep=4)\n", "plot_attn(\"Noun premodifiers\", [dev_data[13], dev_data[1671]], 7, 10, \n", "          [\"language\", \"law\", \"fight\", \"time\", \"executive\"], color_from=False)\n", "plot_attn(\"Posessive\", [dev_data[111], dev_data[244]], 6, 5,\n", "          [\"his\", \"'s\", \"its\"])\n", "plot_attn(\"Passive auxiliary\", [dev_data[192], dev_data[680]], 3, 9,\n", "         [\"been\", \"was\"])\n", "plot_attn(\"Object of prep\", [dev_data[i] for i in [975, 979]], 8, 5,\n", "          [\"in\", \"with\", \"to\", \"of\", \"at\"], hide_sep=True)\n", "#plot_attn([\"PRT\", dev_data[152], dev_data[1692]], 5, 6,\n", "#          [\"out\", \"around\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Probing classifiers (Section 5)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class WordEmbeddings(object):\n", "  \"\"\"Class for loading/using pretrained GloVe embeddings\"\"\"\n", "    \n", "  def __init__(self):\n", "    self.pretrained_embeddings = load_pickle(\"./data/glove/embeddings.pkl\")\n", "    self.vocab = load_pickle(\"./data/glove/vocab.pkl\")\n", "\n", "  def tokid(self, w):\n", "    return self.vocab.get(w.lower(), 0)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["N_DISTANCE_FEATURES = 8\n", "def make_distance_features(seq_len):\n", "  \"\"\"Constructs distance features for a sentence.\"\"\"\n", "  # how much ahead/behind the other word is\n", "  distances = np.zeros((seq_len, seq_len))\n", "  for i in range(seq_len):\n", "    for j in range(seq_len):\n", "      if i < j:\n", "        distances[i, j] = (j - i) / float(seq_len)\n", "  feature_matrices = [distances, distances.T]\n", "\n", "  # indicator features on if other word is up to 2 words ahead/behind\n", "  for k in range(3):\n", "    for direction in ([1] if k == 0 else [-1, 1]):\n", "      feature_matrices.append(np.eye(seq_len, k=k*direction))\n", "  features = np.stack(feature_matrices)\n", "\n", "  # additional indicator feature for ROOT\n", "  features = np.concatenate(\n", "      [np.zeros([N_DISTANCE_FEATURES - 1, seq_len, 1]), \n", "       features], -1)\n", "  root = np.zeros((1, seq_len, seq_len + 1))\n", "  root[:, :, 0] = 1\n", "\n", "  return np.concatenate([features, root], 0)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def attn_linear_combo():\n", "  return Probe()\n", "\n", "\n", "def attn_and_words():\n", "  return Probe(use_words=True)\n", "\n", "\n", "def words_and_distances():\n", "  return Probe(use_distance_features=True, use_attns=False,\n", "               use_words=True, hidden_layer=True)\n", "\n", "\n", "class Probe(object):\n", "  \"\"\"The probing classifier used in Section 5.\"\"\"\n", "\n", "  def __init__(self, use_distance_features=False, use_words=False,\n", "               use_attns=True, include_transpose=True, hidden_layer=False):\n", "    self._embeddings = WordEmbeddings()\n", "\n", "    # We use a simple model with batch size 1\n", "    self._attns = tf.placeholder(\n", "        shape=[12, 12, None, None], dtype=tf.float32)\n", "    self._labels = tf.placeholder(\n", "        shape=[None], dtype=tf.int32)\n", "    self._features = tf.placeholder(\n", "        shape=[N_DISTANCE_FEATURES, None, None], dtype=tf.float32)\n", "    self._words = tf.placeholder(shape=[None], dtype=tf.int32)\n", "\n", "    if use_attns:\n", "      seq_len = tf.shape(self._attns)[-1]\n", "      if include_transpose:\n", "        # Include both directions of attention\n", "        attn_maps = tf.concat(\n", "            [self._attns,\n", "             tf.transpose(self._attns, [0, 1, 3, 2])], 0)\n", "        attn_maps = tf.reshape(attn_maps, [288, seq_len, seq_len])\n", "      else:\n", "        attn_maps = tf.reshape(self._attns, [144, seq_len, seq_len])\n", "      # Use attention to start/end tokens to get score for ROOT\n", "      root_features = (\n", "          (tf.get_variable(\"ROOT_start\", shape=[]) * attn_maps[:, 1:-1, 0]) +\n", "          (tf.get_variable(\"ROOT_end\", shape=[]) * attn_maps[:, 1:-1, -1])\n", "      )\n", "      attn_maps = tf.concat([tf.expand_dims(root_features, -1),\n", "                             attn_maps[:, 1:-1, 1:-1]], -1)\n", "    else:\n", "      # Dummy attention map for models not using attention inputs\n", "      n_words = tf.shape(self._words)[0]\n", "      attn_maps = tf.zeros((1, n_words, n_words + 1))\n", "\n", "    if use_distance_features:\n", "      attn_maps = tf.concat([attn_maps, self._features], 0)\n", "\n", "    if use_words:\n", "      word_embedding_matrix = tf.get_variable(\n", "          \"word_embedding_matrix\",\n", "          initializer=self._embeddings.pretrained_embeddings,\n", "          trainable=False)\n", "      word_embeddings = tf.nn.embedding_lookup(word_embedding_matrix, self._words)\n", "      n_words = tf.shape(self._words)[0]\n", "      tiled_vertical = tf.tile(tf.expand_dims(word_embeddings, 0),\n", "                               [n_words, 1, 1])\n", "      tiled_horizontal = tf.tile(tf.expand_dims(word_embeddings, 1),\n", "                                 [1, n_words, 1])\n", "      word_reprs = tf.concat([tiled_horizontal, tiled_vertical], -1)\n", "      word_reprs = tf.concat([word_reprs, tf.zeros((n_words, 1, 200))], 1) # dummy for ROOT\n", "      if not use_attns:\n", "        attn_maps = tf.concat([\n", "            attn_maps, tf.transpose(word_reprs, [2, 0, 1])], 0)\n", "\n", "    attn_maps = tf.transpose(attn_maps, [1, 2, 0])\n", "    if use_words and use_attns:\n", "      # attention-and-words probe\n", "      weights = tf.layers.dense(word_reprs, attn_maps.shape[-1])\n", "      self._logits = tf.reduce_sum(weights * attn_maps, axis=-1)\n", "    else:\n", "      if hidden_layer:\n", "        # 1-hidden-layer MLP for words-and-distances baseline\n", "        attn_maps = tf.layers.dense(attn_maps, 256,\n", "                                    activation=tf.nn.tanh)\n", "        self._logits = tf.squeeze(tf.layers.dense(attn_maps, 1), -1)\n", "      else:\n", "        # linear combination of attention heads\n", "        attn_map_weights = tf.get_variable(\"attn_map_weights\",\n", "                                           shape=[attn_maps.shape[-1]])\n", "        self._logits = tf.reduce_sum(attn_map_weights * attn_maps, axis=-1)\n", "\n", "    loss = tf.reduce_sum(\n", "        tf.nn.sparse_softmax_cross_entropy_with_logits(\n", "            logits=self._logits, labels=self._labels))\n", "    opt = tf.train.AdamOptimizer(learning_rate=0.002)\n", "    self._train_op = opt.minimize(loss)\n", "\n", "  def _create_feed_dict(self, example):\n", "    return {\n", "        self._attns: example[\"attns\"],\n", "        self._labels: example[\"heads\"],\n", "        self._features: make_distance_features(len(example[\"words\"])),\n", "        self._words: [self._embeddings.tokid(w) for w in example[\"words\"]]\n", "    }\n", "\n", "  def train(self, sess, example):\n", "    return sess.run(self._train_op, feed_dict=self._create_feed_dict(example))\n", "\n", "  def test(self, sess, example):\n", "    return sess.run(self._logits, feed_dict=self._create_feed_dict(example))\n", "\n", "\n", "def run_training(probe, train_data):\n", "  \"\"\"Trains and evaluates the given attention probe.\"\"\"\n", "  with tf.Session() as sess:\n", "    sess.run(tf.global_variables_initializer())\n", "\n", "    for epoch in range(1):\n", "      print(40 * \"=\")\n", "      print(\"EPOCH\", (epoch + 1))\n", "      print(40 * \"=\")\n", "      print(\"Training...\")\n", "      for i, example in enumerate(train_data):\n", "        if i % 2000 == 0:\n", "          print(\"{:}/{:}\".format(i, len(train_data)))\n", "        probe.train(sess, example)\n", "\n", "      print(\"Evaluating...\")\n", "      correct, total = 0, 0\n", "      for i, example in enumerate(dev_data):\n", "        if i % 1000 == 0:\n", "          print(\"{:}/{:}\".format(i, len(dev_data)))\n", "        logits = probe.test(sess, example)\n", "        for i, (head, prediction, reln) in enumerate(\n", "            zip(example[\"heads\"], logits.argmax(-1), example[\"relns\"])):\n", "          # it is standard to ignore punct for Stanford Dependency evaluation\n", "          if reln != \"punct\":\n", "            if head == prediction:\n", "              correct += 1\n", "            total += 1\n", "      print(\"UAS: {:.1f}\".format(100 * correct / total))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================\n", "('EPOCH', 1)\n", "========================================\n", "Training...\n", "0/36160\n", "2000/36160\n", "4000/36160\n", "6000/36160\n", "8000/36160\n", "10000/36160\n", "12000/36160\n", "14000/36160\n", "16000/36160\n", "18000/36160\n", "20000/36160\n", "22000/36160\n", "24000/36160\n", "26000/36160\n", "28000/36160\n", "30000/36160\n", "32000/36160\n", "34000/36160\n", "36000/36160\n", "Evaluating...\n", "0/1696\n", "1000/1696\n", "UAS: 78.0\n"]}], "source": ["tf.reset_default_graph()\n", "train_data = load_pickle(\"./data/depparse/train_attn.pkl\")\n", "run_training(attn_and_words(), train_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.16"}}, "nbformat": 4, "nbformat_minor": 2}