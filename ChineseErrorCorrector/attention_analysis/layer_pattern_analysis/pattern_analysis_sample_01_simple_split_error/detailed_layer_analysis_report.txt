Detailed Layer-wise Attention Analysis Report
============================================================

Sample Information:
  Text: 乌兰巴托不到4百年白勺历史。
  Error Type: simple_split_error
  Error Description: Simple split character error: 白勺 -> 的
  Error Positions: [7, 8]

Layer-wise Pattern Analysis:
----------------------------------------

1. Early Layers (L0-L11) - Token Recognition Phase:
   Average Error Attention: 0.023040
   Average Concentration: 0.055450
   Average Specialization: 0.000000
   Interpretation: Basic token processing, low error awareness

2. Middle Layers (L12-L23) - Semantic Processing Phase:
   Average Error Attention: 0.015439
   Average Concentration: 0.061890
   Average Specialization: 0.000000
   Interpretation: Semantic relationship processing

3. Late Layers (L24-L35) - High-level Integration Phase:
   Average Error Attention: 0.010614
   Average Concentration: 0.070129
   Average Specialization: 0.000000
   Interpretation: High-level semantic integration

Key Findings:
--------------------
1. Peak Error Attention:
   Layer: L0
   Value: 0.065297
   Phase: Early

2. Error Attention Trend:
   Slope: -0.00055657
   Direction: Decreasing
   Interpretation: Model loses focus on errors

3. Peak Head Specialization:
   Layer: L0
   Value: 0.000000
   Interpretation: Maximum functional differentiation between attention heads

4. Overall Error Processing Assessment:
   Mean Error Attention: 0.016364
   Error Attention Std: 0.011075
   Consistency: Low
   Overall Capability: Strong

Recommendations for Model Improvement:
----------------------------------------
2. Error attention decreases in higher layers
   - Implement error-aware loss functions
   - Add skip connections for error information
   - Train higher layers specifically for error correction

3. General recommendations:
   - Implement multi-task learning with error detection
   - Add character composition awareness
   - Use curriculum learning from simple to complex errors
