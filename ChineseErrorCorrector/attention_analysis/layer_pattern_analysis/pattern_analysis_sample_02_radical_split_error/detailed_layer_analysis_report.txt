Detailed Layer-wise Attention Analysis Report
============================================================

Sample Information:
  Text: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。
  Error Type: radical_split_error
  Error Description: Radical split error: 牛寺 -> 特
  Error Positions: [9, 10]

Layer-wise Pattern Analysis:
----------------------------------------

1. Early Layers (L0-L11) - Token Recognition Phase:
   Average Error Attention: 0.022218
   Average Concentration: 0.045898
   Average Specialization: 0.000000
   Interpretation: Basic token processing, low error awareness

2. Middle Layers (L12-L23) - Semantic Processing Phase:
   Average Error Attention: 0.013129
   Average Concentration: 0.051697
   Average Specialization: 0.000000
   Interpretation: Semantic relationship processing

3. Late Layers (L24-L35) - High-level Integration Phase:
   Average Error Attention: 0.009635
   Average Concentration: 0.057922
   Average Specialization: 0.000000
   Interpretation: High-level semantic integration

Key Findings:
--------------------
1. Peak Error Attention:
   Layer: L0
   Value: 0.058256
   Phase: Early

2. Error Attention Trend:
   Slope: -0.00054230
   Direction: Decreasing
   Interpretation: Model loses focus on errors

3. Peak Head Specialization:
   Layer: L0
   Value: 0.000000
   Interpretation: Maximum functional differentiation between attention heads

4. Overall Error Processing Assessment:
   Mean Error Attention: 0.014994
   Error Attention Std: 0.009782
   Consistency: Medium
   Overall Capability: Strong

Recommendations for Model Improvement:
----------------------------------------
2. Error attention decreases in higher layers
   - Implement error-aware loss functions
   - Add skip connections for error information
   - Train higher layers specifically for error correction

3. General recommendations:
   - Implement multi-task learning with error detection
   - Add character composition awareness
   - Use curriculum learning from simple to complex errors
