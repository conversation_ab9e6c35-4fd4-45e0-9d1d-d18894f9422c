#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成完美的注意力热图，使用图像处理方式添加中文标签
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置基础字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class PerfectAttentionVisualizer:
    """完美的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_with_clean_tokens(self, text, max_length=256):
        """提取注意力权重和清洁的token信息"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs
        input_ids = inputs['input_ids'][0].cpu().numpy()
        
        # 解码每个token
        clean_tokens = []
        for token_id in input_ids:
            try:
                # 解码单个token
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                clean_tokens.append(token_text.strip())
            except:
                clean_tokens.append(f'[{token_id}]')
        
        # 验证重构
        reconstructed = self.tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔍 原文: {text}")
        print(f"🔍 重构: {reconstructed}")
        print(f"🔍 Token数量: {len(clean_tokens)}")
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': clean_tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text,
            'reconstructed': reconstructed
        }
    
    def create_base_heatmap(self, attention_data, layer_idx, head_idx, max_display=10):
        """创建基础热图（使用英文标签）"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 使用索引标签
        index_labels = [f'T{i}' for i in range(len(tokens))]
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制热图
        im = ax.imshow(attention, cmap='Blues', aspect='auto', interpolation='nearest')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(index_labels)))
        ax.set_yticks(range(len(index_labels)))
        ax.set_xticklabels(index_labels, rotation=45, ha='right', fontsize=11)
        ax.set_yticklabels(index_labels, fontsize=11)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 设置标题和标签
        title = f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}'
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Key Tokens', fontsize=12, fontweight='bold')
        ax.set_ylabel('Query Tokens', fontsize=12, fontweight='bold')
        
        # 添加网格
        ax.set_xticks(np.arange(len(index_labels)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(index_labels)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=1)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        plt.tight_layout()
        
        return fig, tokens
    
    def add_chinese_labels_to_image(self, image_path, tokens, output_path):
        """使用PIL在图像上添加中文标签"""
        try:
            # 打开图像
            img = Image.open(image_path)
            draw = ImageDraw.Draw(img)
            
            # 尝试加载中文字体
            font_paths = [
                '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
                '/usr/share/fonts/truetype/arphic/uming.ttc',
                '/System/Library/Fonts/PingFang.ttc'
            ]
            
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font = ImageFont.truetype(font_path, 20)
                        print(f"✅ 使用字体: {font_path}")
                        break
                    except:
                        continue
            
            if font is None:
                # 使用默认字体
                font = ImageFont.load_default()
                print("⚠️ 使用默认字体")
            
            # 获取图像尺寸
            width, height = img.size
            
            # 计算标签位置（这需要根据实际图像布局调整）
            # 假设热图区域在图像中央
            margin_left = 100
            margin_bottom = 100
            heatmap_width = width - 2 * margin_left
            heatmap_height = height - 2 * margin_bottom
            
            # 在底部添加token映射
            y_pos = height - 50
            x_start = margin_left
            
            # 创建token映射文本
            mapping_text = "Token映射: "
            for i, token in enumerate(tokens[:8]):  # 只显示前8个
                mapping_text += f"T{i}={token} "
            if len(tokens) > 8:
                mapping_text += "..."
            
            # 绘制背景矩形
            bbox = draw.textbbox((0, 0), mapping_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            draw.rectangle([x_start-5, y_pos-5, x_start+text_width+5, y_pos+text_height+5], 
                         fill='white', outline='black')
            
            # 绘制文本
            draw.text((x_start, y_pos), mapping_text, fill='black', font=font)
            
            # 保存图像
            img.save(output_path)
            print(f"💾 添加中文标签的图像已保存: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 添加中文标签失败: {e}")
            return False
    
    def plot_perfect_heatmap(self, attention_data, layer_idx, head_idx, 
                            max_display=10, save_path=None):
        """绘制完美的注意力热图"""
        # 创建基础热图
        fig, tokens = self.create_base_heatmap(attention_data, layer_idx, head_idx, max_display)
        
        # 保存基础热图
        temp_path = save_path.replace('.png', '_temp.png') if save_path else '/tmp/temp_heatmap.png'
        fig.savefig(temp_path, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close(fig)
        
        # 添加中文标签
        if save_path:
            success = self.add_chinese_labels_to_image(temp_path, tokens, save_path)
            
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            if success:
                print(f"💾 完美热图已保存: {save_path}")
            else:
                print(f"⚠️ 热图已保存但中文标签添加失败: {save_path}")
    
    def create_perfect_analysis(self, output_dir):
        """创建完美版本的分析"""
        print(f"🎨 开始完美版本分析...")
        
        # 定义拆分字错误样本
        samples = [
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'target': '乌兰巴托不到4百年的历史。',
                'error_analysis': '白勺→的',
                'error_type': '简单拆分字错误'
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
                'error_analysis': '牛寺→特',
                'error_type': '部首拆分错误'
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力
            attention_data = self.extract_attention_with_clean_tokens(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'perfect_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择关键层进行分析
            key_layers = [8, 16, 24]
            key_heads = [0, 8, 16]
            
            # 生成热图
            for layer_idx in key_layers:
                if layer_idx < len(attention_data['attentions']):
                    for head_idx in key_heads:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'perfect_attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                            )
                            
                            self.plot_perfect_heatmap(
                                attention_data,
                                layer_idx,
                                head_idx,
                                max_display=min(10, len(attention_data['tokens'])),
                                save_path=save_path
                            )
            
            # 保存详细分析报告
            report_path = os.path.join(sample_dir, 'perfect_analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"完美版本拆分字错误注意力分析报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"解决方案: ✅ 使用PIL在图像上添加中文标签\n\n")
                
                f.write(f"样本信息:\n")
                f.write(f"  原文: {sample['text']}\n")
                f.write(f"  目标: {sample['target']}\n")
                f.write(f"  错误类型: {sample['error_type']}\n")
                f.write(f"  错误分析: {sample['error_analysis']}\n")
                f.write(f"  重构验证: {attention_data['reconstructed']}\n\n")
                
                f.write(f"Token化详细分析:\n")
                f.write("-" * 60 + "\n")
                f.write(f"{'位置':<4} {'Token ID':<8} {'Token内容':<15} {'热图显示':<10} {'说明':<15}\n")
                f.write("-" * 60 + "\n")
                
                for j, (token_id, token) in enumerate(zip(attention_data['input_ids'], attention_data['tokens'])):
                    # 判断是否为错误token
                    is_error = False
                    error_note = ""
                    if any(char in token for char in ['白', '勺', '牛', '寺']):
                        is_error = True
                        error_note = "⚠️拆分字错误"
                    
                    f.write(f"{j:<4} {token_id:<8} {token:<15} T{j:<9} {error_note:<15}\n")
                
                f.write(f"\n热图特点:\n")
                f.write(f"  📊 横纵坐标显示T0-T{len(attention_data['tokens'])-1}索引\n")
                f.write(f"  🏷️ 图像底部有完整的中文token映射\n")
                f.write(f"  🎯 可以直观观察拆分字错误token之间的注意力关系\n")
                
                f.write(f"\n关键观察点:\n")
                error_tokens = []
                for j, token in enumerate(attention_data['tokens']):
                    if any(char in token for char in ['白', '勺', '牛', '寺']):
                        error_tokens.append((j, token))
                        f.write(f"  🎯 '{token}'的位置: T{j}\n")
                
                if len(error_tokens) >= 2:
                    f.write(f"  📈 重点观察这些位置之间的注意力权重\n")
                
                f.write(f"\n模型架构信息:\n")
                f.write(f"  总层数: {len(attention_data['attentions'])}\n")
                f.write(f"  每层头数: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  序列长度: {len(attention_data['tokens'])}\n")
            
            print(f"💾 样本 {i+1} 完美分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成完美的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/perfect_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成完美的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = PerfectAttentionVisualizer(args.model_path, device)
    
    # 创建完美分析
    visualizer.create_perfect_analysis(args.output_dir)
    
    print(f"\n🎉 完美版本热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"✅ 使用PIL在图像上添加中文标签")
    print(f"📊 热图底部显示完整的token映射")
    print(f"🎯 可以直观观察拆分字错误的注意力模式")


if __name__ == '__main__':
    main()
