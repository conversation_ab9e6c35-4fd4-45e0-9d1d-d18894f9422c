#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成干净的注意力热图，解决中文显示和数值显示问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置matplotlib参数以避免中文显示问题
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def decode_token_safely(token, index):
    """安全地解码token，避免中文显示问题"""
    try:
        # 移除特殊前缀
        if token.startswith('Ġ'):
            clean_token = token[1:]
        elif token.startswith('##'):
            clean_token = token[2:]
        else:
            clean_token = token
        
        # 如果包含特殊编码字符，尝试解码
        if any(char in clean_token for char in ['ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë']):
            try:
                decoded = clean_token.encode('latin1').decode('utf-8')
                # 只保留前2个字符以避免显示问题
                return decoded[:2] if len(decoded) > 2 else decoded
            except:
                pass
        
        # 如果是普通字符，直接返回（限制长度）
        if len(clean_token) <= 3:
            return clean_token
        else:
            return clean_token[:3]
            
    except Exception:
        # 如果所有方法都失败，使用简单的索引标记
        return f'T{index}'


class CleanAttentionVisualizer:
    """干净的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention(self, text, max_length=256):
        """提取注意力权重"""
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取tokens
        tokens = self.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'attentions': attention_matrices,
            'text': text
        }
    
    def plot_clean_heatmap(self, attention_data, layer_idx, head_idx, 
                          max_display=15, save_path=None):
        """绘制干净的注意力热图"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 处理token显示
        display_labels = []
        for i, token in enumerate(tokens):
            clean_label = decode_token_safely(token, i)
            display_labels.append(clean_label)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制热图，不显示数值
        im = ax.imshow(attention, cmap='Blues', aspect='auto')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(display_labels)))
        ax.set_yticks(range(len(display_labels)))
        ax.set_xticklabels(display_labels, rotation=45, ha='right', fontsize=10)
        ax.set_yticklabels(display_labels, fontsize=10)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 设置标题和标签
        ax.set_title(f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}\n'
                    f'Text: {attention_data["text"][:50]}...', 
                    fontsize=12, fontweight='bold', pad=20)
        ax.set_xlabel('Key Tokens', fontsize=11)
        ax.set_ylabel('Query Tokens', fontsize=11)
        
        # 添加网格
        ax.set_xticks(np.arange(len(display_labels)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(display_labels)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=0.5)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 干净热图已保存: {save_path}")
        
        plt.close()
    
    def create_attention_summary(self, attention_data, output_dir, 
                               layers_to_analyze=None, heads_to_analyze=None):
        """创建注意力分析摘要"""
        if layers_to_analyze is None:
            layers_to_analyze = [8, 16, 24, 32]
        if heads_to_analyze is None:
            heads_to_analyze = [0, 8, 16, 24]
        
        print(f"🎨 生成注意力热图摘要...")
        
        # 为每个指定的层和头生成热图
        for layer_idx in layers_to_analyze:
            if layer_idx < len(attention_data['attentions']):
                for head_idx in heads_to_analyze:
                    if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                        save_path = os.path.join(
                            output_dir, 
                            f'clean_attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                        )
                        
                        self.plot_clean_heatmap(
                            attention_data,
                            layer_idx,
                            head_idx,
                            max_display=15,
                            save_path=save_path
                        )
        
        # 保存token信息
        token_info_path = os.path.join(output_dir, 'token_info.txt')
        with open(token_info_path, 'w', encoding='utf-8') as f:
            f.write(f"原文: {attention_data['text']}\n\n")
            f.write("Token信息:\n")
            f.write("-" * 40 + "\n")
            for i, token in enumerate(attention_data['tokens'][:20]):
                clean_token = decode_token_safely(token, i)
                f.write(f"{i:2d}: {token:15s} -> {clean_token}\n")
        
        print(f"💾 Token信息已保存: {token_info_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成干净的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--text', type=str,
                       default='你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
                       help='要分析的文本')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/clean_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成干净的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"分析文本: {args.text}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = CleanAttentionVisualizer(args.model_path, device)
    
    # 提取注意力
    print(f"🔍 提取注意力权重...")
    attention_data = visualizer.extract_attention(args.text)
    
    print(f"📊 模型信息:")
    print(f"  层数: {len(attention_data['attentions'])}")
    print(f"  每层头数: {attention_data['attentions'][0].shape[0]}")
    print(f"  序列长度: {len(attention_data['tokens'])}")
    
    # 生成干净的热图
    visualizer.create_attention_summary(attention_data, args.output_dir)
    
    print(f"\n🎉 干净热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")


if __name__ == '__main__':
    main()
