终极版本拆分字错误注意力分析报告
============================================================

字体状态: ⚠️ 使用备用方案

样本信息:
  原文: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。
  目标: 我为什么喜欢阿拉木图，我觉得有几个特点。
  错误类型: 部首拆分错误
  错误分析: 牛寺→特
  重构验证: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。

Token化详细分析:
------------------------------------------------------------
位置   Token ID Token内容         热图显示       说明             
------------------------------------------------------------
0    35946    我               T0                        
1    100678   为什么             T1                        
2    99729    喜欢              T2                        
3    103525   阿拉              T3                        
4    75405    木               T4                        
5    28029    图               T5                        
6    3837     ，               T6                        
7    104288   我觉得             T7                        
8    112485   有几个             T8                        
9    100664   牛               T9         ⚠️拆分字错误        
10   101277   寺               T10        ⚠️拆分字错误        
11   27442    点               T11                       
12   1773     。               T12                       

热图解读说明:
  📋 横纵坐标显示T0-T12索引
  📊 热图底部有token映射说明
  🔍 请参考上方Token化分析表了解具体对应关系

关键观察点:

模型架构信息:
  总层数: 36
  每层头数: 32
  序列长度: 13
