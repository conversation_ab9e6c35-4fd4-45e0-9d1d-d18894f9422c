#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成修正中文显示的注意力热图
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 强制设置中文字体
import matplotlib.font_manager as fm

def setup_chinese_font():
    """设置中文字体"""
    try:
        # 清除matplotlib字体缓存
        fm._rebuild()
        
        # 尝试多种中文字体
        chinese_fonts = [
            'Noto Sans CJK SC',
            'Noto Sans CJK TC', 
            'AR PL UMing CN',
            'WenQuanYi Micro Hei',
            'SimHei',
            'Microsoft YaHei'
        ]
        
        # 获取系统可用字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        print(f"🔍 系统可用字体数量: {len(available_fonts)}")
        
        # 查找可用的中文字体
        found_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                found_font = font
                break
        
        if found_font:
            print(f"✅ 找到中文字体: {found_font}")
            plt.rcParams['font.sans-serif'] = [found_font, 'DejaVu Sans']
        else:
            # 直接指定字体文件路径
            font_paths = [
                '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
                '/usr/share/fonts/truetype/arphic/uming.ttc',
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/Hiragino Sans GB.ttc'
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    print(f"✅ 使用字体文件: {font_path}")
                    # 注册字体
                    fm.fontManager.addfont(font_path)
                    # 获取字体名称
                    prop = fm.FontProperties(fname=font_path)
                    font_name = prop.get_name()
                    plt.rcParams['font.sans-serif'] = [font_name, 'DejaVu Sans']
                    found_font = font_name
                    break
        
        if not found_font:
            print("⚠️ 未找到中文字体，将使用备用方案")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            return False
        
        plt.rcParams['axes.unicode_minus'] = False
        
        # 测试中文显示
        test_fig, test_ax = plt.subplots(figsize=(1, 1))
        test_ax.text(0.5, 0.5, '测试中文', fontsize=12, ha='center', va='center')
        test_fig.savefig('/tmp/test_chinese.png', dpi=100)
        plt.close(test_fig)
        
        if os.path.exists('/tmp/test_chinese.png'):
            print("✅ 中文字体测试成功")
            return True
        else:
            print("❌ 中文字体测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 字体设置失败: {e}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        return False


class FixedChineseAttentionVisualizer:
    """修正中文显示的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        self.chinese_font_available = setup_chinese_font()
        
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_with_clean_tokens(self, text, max_length=256):
        """提取注意力权重和清洁的token信息"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs
        input_ids = inputs['input_ids'][0].cpu().numpy()
        
        # 解码每个token
        clean_tokens = []
        for token_id in input_ids:
            try:
                # 解码单个token
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                clean_tokens.append(token_text.strip())
            except:
                clean_tokens.append(f'[{token_id}]')
        
        # 验证重构
        reconstructed = self.tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔍 原文: {text}")
        print(f"🔍 重构: {reconstructed}")
        print(f"🔍 Token数量: {len(clean_tokens)}")
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': clean_tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text,
            'reconstructed': reconstructed
        }
    
    def plot_fixed_chinese_heatmap(self, attention_data, layer_idx, head_idx, 
                                  max_display=10, save_path=None):
        """绘制修正中文显示的注意力热图"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 如果中文字体不可用，使用备用方案
        if not self.chinese_font_available:
            # 创建token映射
            token_labels = [f'T{i}' for i in range(len(tokens))]
            display_tokens = token_labels
        else:
            display_tokens = tokens
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(14, 12))
        
        # 绘制热图
        im = ax.imshow(attention, cmap='Blues', aspect='auto', interpolation='nearest')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(display_tokens)))
        ax.set_yticks(range(len(display_tokens)))
        
        # 设置标签，如果是中文则使用较大字体
        if self.chinese_font_available:
            ax.set_xticklabels(display_tokens, rotation=45, ha='right', fontsize=12)
            ax.set_yticklabels(display_tokens, fontsize=12)
        else:
            ax.set_xticklabels(display_tokens, rotation=45, ha='right', fontsize=10)
            ax.set_yticklabels(display_tokens, fontsize=10)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 设置标题和标签
        title = f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}'
        if self.chinese_font_available:
            title += f'\n文本: {attention_data["text"][:30]}...'
        else:
            title += f'\nText: {attention_data["text"][:30]}...'
        
        ax.set_title(title, fontsize=13, fontweight='bold', pad=20)
        ax.set_xlabel('Key Tokens', fontsize=12, fontweight='bold')
        ax.set_ylabel('Query Tokens', fontsize=12, fontweight='bold')
        
        # 添加网格
        ax.set_xticks(np.arange(len(display_tokens)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(display_tokens)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=1)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 修正热图已保存: {save_path}")
        
        plt.close()
    
    def create_fixed_analysis(self, output_dir):
        """创建修正版本的分析"""
        print(f"🎨 开始修正版本分析...")
        
        # 定义拆分字错误样本
        samples = [
            {
                'text': '你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
                'target': '你选择几个个性重点培养，最终形成自己独特的男人魅力。',
                'error_analysis': '先择→选择, 鬼未力→魅力',
                'error_type': '复合拆分字错误'
            },
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'target': '乌兰巴托不到4百年的历史。',
                'error_analysis': '白勺→的',
                'error_type': '简单拆分字错误'
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
                'error_analysis': '牛寺→特',
                'error_type': '部首拆分错误'
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力
            attention_data = self.extract_attention_with_clean_tokens(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'fixed_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择关键层进行分析
            key_layers = [8, 16, 24, 32]
            key_heads = [0, 8, 16, 24]
            
            # 生成热图
            for layer_idx in key_layers:
                if layer_idx < len(attention_data['attentions']):
                    for head_idx in key_heads:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'fixed_attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                            )
                            
                            self.plot_fixed_chinese_heatmap(
                                attention_data,
                                layer_idx,
                                head_idx,
                                max_display=min(12, len(attention_data['tokens'])),
                                save_path=save_path
                            )
            
            # 保存详细分析报告
            report_path = os.path.join(sample_dir, 'fixed_analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"修正版本拆分字错误注意力分析报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"字体状态: {'✅ 中文字体可用' if self.chinese_font_available else '⚠️ 使用备用方案'}\n\n")
                
                f.write(f"样本信息:\n")
                f.write(f"  原文: {sample['text']}\n")
                f.write(f"  目标: {sample['target']}\n")
                f.write(f"  错误类型: {sample['error_type']}\n")
                f.write(f"  错误分析: {sample['error_analysis']}\n")
                f.write(f"  重构验证: {attention_data['reconstructed']}\n\n")
                
                f.write(f"Token化分析:\n")
                f.write("-" * 50 + "\n")
                f.write(f"{'位置':<4} {'Token ID':<8} {'Token内容':<12} {'显示方式':<10} {'说明':<15}\n")
                f.write("-" * 50 + "\n")
                
                for j, (token_id, token) in enumerate(zip(attention_data['input_ids'], attention_data['tokens'])):
                    # 判断是否为错误token
                    is_error = False
                    error_note = ""
                    if any(char in token for char in ['先', '择', '鬼', '未', '力', '白', '勺', '牛', '寺']):
                        is_error = True
                        error_note = "拆分字错误"
                    
                    display_way = token if self.chinese_font_available else f'T{j}'
                    
                    f.write(f"{j:<4} {token_id:<8} {token:<12} {display_way:<10} {error_note:<15}\n")
                
                f.write(f"\n热图说明:\n")
                if self.chinese_font_available:
                    f.write(f"  ✅ 横纵坐标显示实际中文token内容\n")
                else:
                    f.write(f"  ⚠️ 横纵坐标显示T0-T{len(attention_data['tokens'])-1}索引\n")
                    f.write(f"  📋 请参考上方Token化分析表了解具体对应关系\n")
                
                f.write(f"\n模型架构信息:\n")
                f.write(f"  总层数: {len(attention_data['attentions'])}\n")
                f.write(f"  每层头数: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  序列长度: {len(attention_data['tokens'])}\n")
            
            print(f"💾 样本 {i+1} 修正分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成修正中文显示的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/fixed_chinese_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成修正中文显示的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = FixedChineseAttentionVisualizer(args.model_path, device)
    
    # 创建修正分析
    visualizer.create_fixed_analysis(args.output_dir)
    
    print(f"\n🎉 修正版本热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    if visualizer.chinese_font_available:
        print(f"✅ 中文字体显示正常")
    else:
        print(f"⚠️ 使用备用显示方案（数字索引）")
        print(f"📋 请查看分析报告了解token对应关系")


if __name__ == '__main__':
    main()
