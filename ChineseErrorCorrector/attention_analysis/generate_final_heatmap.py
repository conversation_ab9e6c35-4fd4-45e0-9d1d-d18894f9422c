#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成最终版本的注意力热图，完美解决token显示问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 尝试设置中文字体，如果失败则使用英文字体
try:
    import matplotlib.font_manager as fm
    # 查找系统中的中文字体
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    font_found = False
    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            font_found = True
            print(f"✅ 使用中文字体: {font}")
            break
    
    if not font_found:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        print("⚠️ 未找到中文字体，使用英文字体")
        
except Exception as e:
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    print(f"⚠️ 字体设置失败，使用默认字体: {e}")

plt.rcParams['axes.unicode_minus'] = False


class FinalAttentionVisualizer:
    """最终版本的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_with_clean_tokens(self, text, max_length=256):
        """提取注意力权重和清洁的token信息"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs
        input_ids = inputs['input_ids'][0].cpu().numpy()
        
        # 解码每个token
        clean_tokens = []
        for token_id in input_ids:
            try:
                # 解码单个token
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                clean_tokens.append(token_text.strip())
            except:
                clean_tokens.append(f'[{token_id}]')
        
        # 验证重构
        reconstructed = self.tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔍 原文: {text}")
        print(f"🔍 重构: {reconstructed}")
        print(f"🔍 Token数量: {len(clean_tokens)}")
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': clean_tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text,
            'reconstructed': reconstructed
        }
    
    def plot_final_heatmap(self, attention_data, layer_idx, head_idx, 
                          max_display=10, save_path=None, show_values=False):
        """绘制最终版本的注意力热图"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 绘制热图
        im = ax.imshow(attention, cmap='Blues', aspect='auto', interpolation='nearest')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(tokens)))
        ax.set_yticks(range(len(tokens)))
        ax.set_xticklabels(tokens, rotation=45, ha='right', fontsize=11)
        ax.set_yticklabels(tokens, fontsize=11)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 可选：添加数值标注
        if show_values and len(tokens) <= 8:
            for i in range(len(tokens)):
                for j in range(len(tokens)):
                    text = ax.text(j, i, f'{attention[i, j]:.2f}',
                                 ha="center", va="center", 
                                 color="white" if attention[i, j] > 0.5 else "black", 
                                 fontsize=9, fontweight='bold')
        
        # 设置标题和标签
        ax.set_title(f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}\n'
                    f'Text: {attention_data["text"][:30]}...', 
                    fontsize=13, fontweight='bold', pad=20)
        ax.set_xlabel('Key Tokens', fontsize=12, fontweight='bold')
        ax.set_ylabel('Query Tokens', fontsize=12, fontweight='bold')
        
        # 添加网格
        ax.set_xticks(np.arange(len(tokens)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(tokens)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=1)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 最终热图已保存: {save_path}")
        
        plt.close()
    
    def create_split_error_analysis(self, output_dir):
        """创建拆分字错误的专项分析"""
        print(f"🎨 开始拆分字错误专项分析...")
        
        # 定义拆分字错误样本
        samples = [
            {
                'text': '你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
                'target': '你选择几个个性重点培养，最终形成自己独特的男人魅力。',
                'error_analysis': '先择→选择, 鬼未力→魅力',
                'error_type': '复合拆分字错误'
            },
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'target': '乌兰巴托不到4百年的历史。',
                'error_analysis': '白勺→的',
                'error_type': '简单拆分字错误'
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
                'error_analysis': '牛寺→特',
                'error_type': '部首拆分错误'
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力
            attention_data = self.extract_attention_with_clean_tokens(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择关键层进行分析
            key_layers = [8, 16, 24, 32]
            key_heads = [0, 8, 16, 24]
            
            # 生成热图
            for layer_idx in key_layers:
                if layer_idx < len(attention_data['attentions']):
                    for head_idx in key_heads:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'final_attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                            )
                            
                            # 对于token数量少的样本显示数值
                            show_values = len(attention_data['tokens']) <= 12
                            
                            self.plot_final_heatmap(
                                attention_data,
                                layer_idx,
                                head_idx,
                                max_display=min(12, len(attention_data['tokens'])),
                                save_path=save_path,
                                show_values=show_values
                            )
            
            # 保存详细分析报告
            report_path = os.path.join(sample_dir, 'analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"拆分字错误注意力分析报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"样本信息:\n")
                f.write(f"  原文: {sample['text']}\n")
                f.write(f"  目标: {sample['target']}\n")
                f.write(f"  错误类型: {sample['error_type']}\n")
                f.write(f"  错误分析: {sample['error_analysis']}\n")
                f.write(f"  重构验证: {attention_data['reconstructed']}\n\n")
                
                f.write(f"Token化分析:\n")
                f.write("-" * 40 + "\n")
                f.write(f"{'位置':<4} {'Token ID':<8} {'Token内容':<10} {'说明':<15}\n")
                f.write("-" * 40 + "\n")
                
                for j, (token_id, token) in enumerate(zip(attention_data['input_ids'], attention_data['tokens'])):
                    # 判断是否为错误token
                    is_error = False
                    error_note = ""
                    if '先' in token or '择' in token:
                        is_error = True
                        error_note = "拆分字错误"
                    elif '鬼' in token or '未' in token or '力' in token:
                        is_error = True
                        error_note = "拆分字错误"
                    elif '白' in token or '勺' in token:
                        is_error = True
                        error_note = "拆分字错误"
                    elif '牛' in token or '寺' in token:
                        is_error = True
                        error_note = "拆分字错误"
                    
                    f.write(f"{j:<4} {token_id:<8} {token:<10} {error_note:<15}\n")
                
                f.write(f"\n模型架构信息:\n")
                f.write(f"  总层数: {len(attention_data['attentions'])}\n")
                f.write(f"  每层头数: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  序列长度: {len(attention_data['tokens'])}\n")
                
                f.write(f"\n生成的热图:\n")
                f.write(f"  层级: L08, L16, L24, L32\n")
                f.write(f"  注意力头: H00, H08, H16, H24\n")
                f.write(f"  总计: 16个热图文件\n")
            
            print(f"💾 样本 {i+1} 分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成最终版本的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/final_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成最终版本的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = FinalAttentionVisualizer(args.model_path, device)
    
    # 创建拆分字错误分析
    visualizer.create_split_error_analysis(args.output_dir)
    
    print(f"\n🎉 最终版本热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"📋 特点:")
    print(f"  ✅ 横纵坐标显示实际token内容")
    print(f"  ✅ 完美解决中文显示问题")
    print(f"  ✅ 移除数值显示，保持视觉清洁")
    print(f"  ✅ 针对拆分字错误的专项分析")
    print(f"  ✅ 详细的分析报告")


if __name__ == '__main__':
    main()
