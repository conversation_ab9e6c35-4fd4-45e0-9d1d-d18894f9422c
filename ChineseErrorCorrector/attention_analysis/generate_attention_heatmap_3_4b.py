#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为3-4B模型生成注意力头热图
基于attention-analysis代码修改，适配现代transformers库
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def clean_token_for_display(token, index=None):
    """清理token用于显示，处理中文字符编码问题"""
    try:
        # 移除特殊前缀
        if token.startswith('Ġ'):
            token = token[1:]
        elif token.startswith('##'):
            token = token[2:]

        # 尝试处理编码问题
        if token.startswith('ä') or token.startswith('å') or token.startswith('æ') or token.startswith('ç') or token.startswith('è') or token.startswith('é'):
            # 这些是UTF-8编码的中文字符，尝试解码
            try:
                # 尝试从bytes解码
                decoded = token.encode('latin1').decode('utf-8')
                return decoded[:2] if len(decoded) > 2 else decoded
            except:
                pass

        # 如果是纯ASCII或已经是正确的中文字符
        if len(token) <= 3:
            return token
        else:
            return token[:3] + '...'

    except Exception:
        # 如果所有方法都失败，使用索引
        return f'T{index}' if index is not None else 'UNK'


class AttentionExtractor:
    """3-4B模型注意力提取器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成，设备: {device}")
        
    def extract_attention(self, text, max_length=512):
        """提取单个文本的注意力权重"""
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取tokens
        tokens = self.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions  # tuple of (batch_size, num_heads, seq_len, seq_len)
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            # 移除batch维度: (num_heads, seq_len, seq_len)
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'attentions': attention_matrices,  # [num_layers, num_heads, seq_len, seq_len]
            'input_ids': inputs['input_ids'][0].cpu().numpy(),
            'text': text
        }


def plot_attention_heatmap(attention_data, layer_idx, head_idx, save_path=None,
                          max_tokens=50, title=None):
    """绘制单个注意力头的热图"""
    tokens = attention_data['tokens'][:max_tokens]
    attention = attention_data['attentions'][layer_idx][head_idx][:max_tokens, :max_tokens]

    # 处理token显示：将编码后的token转换为可读形式
    display_tokens = []
    for i, token in enumerate(tokens):
        cleaned_token = clean_token_for_display(token, i)
        display_tokens.append(cleaned_token)

    # 创建图形
    plt.figure(figsize=(14, 12))

    # 绘制热图，移除数值显示
    sns.heatmap(
        attention,
        xticklabels=display_tokens,
        yticklabels=display_tokens,
        cmap='Blues',
        cbar=True,
        square=True,
        linewidths=0.1,
        cbar_kws={'label': 'Attention Weight'},
        annot=False,  # 移除数值显示
        fmt='.3f'
    )

    # 设置标题和标签
    if title is None:
        title = f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}'
    plt.title(title, fontsize=14, fontweight='bold')
    plt.xlabel('Key Tokens', fontsize=12)
    plt.ylabel('Query Tokens', fontsize=12)

    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45, ha='right', fontsize=10)
    plt.yticks(rotation=0, fontsize=10)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"💾 热图已保存: {save_path}")

    plt.close()


def plot_attention_overview(attention_data, save_dir, max_tokens=30):
    """绘制注意力概览图：显示所有层的平均注意力"""
    tokens = attention_data['tokens'][:max_tokens]
    attentions = attention_data['attentions']
    
    # 计算每层的平均注意力
    layer_avg_attentions = []
    for layer_attn in attentions:
        # 对所有头求平均
        avg_attn = np.mean(layer_attn, axis=0)[:max_tokens, :max_tokens]
        layer_avg_attentions.append(avg_attn)
    
    # 创建子图
    num_layers = len(layer_avg_attentions)
    cols = 4
    rows = (num_layers + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(20, 5*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    
    for i, layer_attn in enumerate(layer_avg_attentions):
        row = i // cols
        col = i % cols
        ax = axes[row, col]
        
        sns.heatmap(
            layer_attn,
            ax=ax,
            cmap='Blues',
            cbar=True,
            square=True,
            xticklabels=False,
            yticklabels=False
        )
        ax.set_title(f'Layer {i}', fontsize=10)
    
    # 隐藏多余的子图
    for i in range(num_layers, rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].set_visible(False)
    
    plt.suptitle('Average Attention by Layer', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    overview_path = os.path.join(save_dir, 'attention_overview.png')
    plt.savefig(overview_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"💾 概览图已保存: {overview_path}")


def plot_head_comparison(attention_data, layer_idx, save_dir, max_tokens=20):
    """比较同一层不同注意力头的模式"""
    tokens = attention_data['tokens'][:max_tokens]
    layer_attentions = attention_data['attentions'][layer_idx][:, :max_tokens, :max_tokens]
    
    num_heads = layer_attentions.shape[0]
    cols = 4
    rows = (num_heads + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(16, 4*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    
    for head_idx in range(num_heads):
        row = head_idx // cols
        col = head_idx % cols
        ax = axes[row, col]
        
        sns.heatmap(
            layer_attentions[head_idx],
            ax=ax,
            cmap='Blues',
            cbar=True,
            square=True,
            xticklabels=False,
            yticklabels=False
        )
        ax.set_title(f'Head {head_idx}', fontsize=10)
    
    # 隐藏多余的子图
    for head_idx in range(num_heads, rows * cols):
        row = head_idx // cols
        col = head_idx % cols
        axes[row, col].set_visible(False)
    
    plt.suptitle(f'Attention Heads Comparison - Layer {layer_idx}', fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    comparison_path = os.path.join(save_dir, f'layer_{layer_idx}_heads_comparison.png')
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"💾 头比较图已保存: {comparison_path}")


def analyze_attention_patterns(attention_data, save_dir):
    """分析注意力模式并生成统计图"""
    attentions = attention_data['attentions']
    tokens = attention_data['tokens']
    
    # 计算每层每头的注意力分布统计
    stats = {
        'layer_entropy': [],
        'head_entropy': [],
        'max_attention': [],
        'attention_spread': []
    }
    
    for layer_idx, layer_attn in enumerate(attentions):
        layer_entropies = []
        layer_max_attns = []
        layer_spreads = []
        
        for head_idx, head_attn in enumerate(layer_attn):
            # 确保注意力权重为正数且归一化
            head_attn = np.abs(head_attn)
            head_attn = head_attn / (np.sum(head_attn, axis=-1, keepdims=True) + 1e-10)

            # 计算熵（注意力分布的均匀程度）
            entropy = -np.sum(head_attn * np.log(head_attn + 1e-10), axis=-1)
            entropy = np.mean(entropy[np.isfinite(entropy)])  # 只取有限值
            if np.isfinite(entropy):
                layer_entropies.append(entropy)

            # 最大注意力权重
            max_attn = np.max(head_attn)
            if np.isfinite(max_attn):
                layer_max_attns.append(max_attn)

            # 注意力分布的标准差（衡量集中程度）
            spread = np.std(head_attn)
            if np.isfinite(spread):
                layer_spreads.append(spread)
        
        stats['layer_entropy'].append(layer_entropies)
        stats['head_entropy'].extend(layer_entropies)
        stats['max_attention'].extend(layer_max_attns)
        stats['attention_spread'].extend(layer_spreads)
    
    # 绘制统计图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 每层的平均熵
    layer_avg_entropy = []
    for entropies in stats['layer_entropy']:
        if entropies:  # 确保列表不为空
            avg_entropy = np.mean([e for e in entropies if np.isfinite(e)])
            if np.isfinite(avg_entropy):
                layer_avg_entropy.append(avg_entropy)
            else:
                layer_avg_entropy.append(0)
        else:
            layer_avg_entropy.append(0)

    axes[0, 0].plot(layer_avg_entropy, 'o-')
    axes[0, 0].set_title('Average Attention Entropy by Layer')
    axes[0, 0].set_xlabel('Layer')
    axes[0, 0].set_ylabel('Entropy')

    # 所有头的熵分布
    valid_entropies = [e for e in stats['head_entropy'] if np.isfinite(e)]
    if valid_entropies:
        axes[0, 1].hist(valid_entropies, bins=20, alpha=0.7)
    axes[0, 1].set_title('Distribution of Attention Entropy')
    axes[0, 1].set_xlabel('Entropy')
    axes[0, 1].set_ylabel('Frequency')

    # 最大注意力权重分布
    valid_max_attns = [a for a in stats['max_attention'] if np.isfinite(a)]
    if valid_max_attns:
        axes[1, 0].hist(valid_max_attns, bins=20, alpha=0.7)
    axes[1, 0].set_title('Distribution of Max Attention Weights')
    axes[1, 0].set_xlabel('Max Attention Weight')
    axes[1, 0].set_ylabel('Frequency')

    # 注意力分布的标准差
    valid_spreads = [s for s in stats['attention_spread'] if np.isfinite(s)]
    if valid_spreads:
        axes[1, 1].hist(valid_spreads, bins=20, alpha=0.7)
    axes[1, 1].set_title('Distribution of Attention Spread')
    axes[1, 1].set_xlabel('Standard Deviation')
    axes[1, 1].set_ylabel('Frequency')
    
    plt.tight_layout()
    
    stats_path = os.path.join(save_dir, 'attention_statistics.png')
    plt.savefig(stats_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"💾 统计图已保存: {stats_path}")
    
    return stats


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成3-4B模型注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--text', type=str,
                       default='你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
                       help='要分析的文本')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/output',
                       help='输出目录')
    parser.add_argument('--layer', type=int, default=8,
                       help='要详细分析的层索引')
    parser.add_argument('--head', type=int, default=4,
                       help='要详细分析的头索引')
    parser.add_argument('--max_tokens', type=int, default=30,
                       help='最大token数量')
    
    args = parser.parse_args()
    
    print("🎯 3-4B模型注意力热图生成")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"分析文本: {args.text}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化注意力提取器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    extractor = AttentionExtractor(args.model_path, device)
    
    # 提取注意力
    print(f"🔍 提取注意力权重...")
    attention_data = extractor.extract_attention(args.text, max_length=512)
    
    print(f"📊 模型信息:")
    print(f"  层数: {len(attention_data['attentions'])}")
    print(f"  每层头数: {attention_data['attentions'][0].shape[0]}")
    print(f"  序列长度: {len(attention_data['tokens'])}")
    
    # 生成各种热图
    print(f"\n🎨 生成注意力热图...")
    
    # 1. 单个注意力头的详细热图
    detailed_path = os.path.join(args.output_dir, f'attention_layer_{args.layer}_head_{args.head}.png')
    plot_attention_heatmap(
        attention_data, 
        args.layer, 
        args.head, 
        detailed_path,
        max_tokens=args.max_tokens,
        title=f'Attention Heatmap - Layer {args.layer}, Head {args.head}\nText: {args.text[:50]}...'
    )
    
    # 2. 所有层的概览
    plot_attention_overview(attention_data, args.output_dir, max_tokens=20)
    
    # 3. 同一层不同头的比较
    plot_head_comparison(attention_data, args.layer, args.output_dir, max_tokens=15)
    
    # 4. 注意力模式统计分析
    stats = analyze_attention_patterns(attention_data, args.output_dir)
    
    # 保存注意力数据
    data_path = os.path.join(args.output_dir, 'attention_data.json')
    with open(data_path, 'w', encoding='utf-8') as f:
        # 转换numpy数组为列表以便JSON序列化
        serializable_data = {
            'tokens': attention_data['tokens'],
            'text': attention_data['text'],
            'model_info': {
                'num_layers': len(attention_data['attentions']),
                'num_heads': attention_data['attentions'][0].shape[0],
                'sequence_length': len(attention_data['tokens'])
            },
            'generation_time': datetime.now().isoformat()
        }
        json.dump(serializable_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 注意力分析完成!")
    print(f"📁 输出文件:")
    print(f"  详细热图: {detailed_path}")
    print(f"  概览图: {os.path.join(args.output_dir, 'attention_overview.png')}")
    print(f"  头比较图: {os.path.join(args.output_dir, f'layer_{args.layer}_heads_comparison.png')}")
    print(f"  统计图: {os.path.join(args.output_dir, 'attention_statistics.png')}")
    print(f"  数据文件: {data_path}")


if __name__ == '__main__':
    main()
