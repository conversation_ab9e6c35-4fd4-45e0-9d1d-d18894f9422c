层-头注意力分析报告
============================================================

样本信息:
  原文: 乌兰巴托不到4百年白勺历史。
  目标: 乌兰巴托不到4百年的历史。
  错误类型: 简单拆分字错误
  错误分析: 白勺→的
  目标tokens: ['白', '勺']
  重构验证: 乌兰巴托不到4百年白勺历史。

Token化分析:
--------------------------------------------------
位置   Token ID Token内容         是否错误      
--------------------------------------------------
0    100444   乌                         
1    99533    兰                         
2    99395    巴                         
3    99829    托                         
4    99828    不到                        
5    19       4                         
6    104881   百年                        
7    99243    白               ⚠️错误      
8    109148   勺               ⚠️错误      
9    100022   历史                        
10   1773     。                         

层-头热图说明:
  📊 横坐标: Attention Head Number (H0-H31)
  📊 纵坐标: Layer Number (L0-L35)
  🎯 颜色深浅: 对应指标的数值大小

生成的热图类型:
  1. Maximum Attention Weight: 每层每头的最大注意力权重
  2. Mean Attention Weight: 每层每头的平均注意力权重
  3. Attention Entropy: 每层每头的注意力熵（分布均匀程度）
  4. Target Token Attention: 对拆分字错误token的注意力

关键观察点:
  🔍 观察不同层级对拆分字错误的处理模式
  🔍 识别哪些注意力头更关注错误token
  🔍 分析注意力分布的层级变化趋势

模型架构信息:
  总层数: 36
  每层头数: 32
  序列长度: 11

统计摘要:
  Maximum Attention Weight:
    最大值: 1.0000
    最小值: 1.0000
    平均值: 1.0000
    标准差: 0.0000

  Mean Attention Weight:
    最大值: 0.0909
    最小值: 0.0909
    平均值: 0.0909
    标准差: 0.0000

  Attention Entropy:
    最大值: nan
    最小值: nan
    平均值: nan
    标准差: nan

  Target Token Attention:
    最大值: 1.4573
    最小值: 0.0000
    平均值: 0.1800
    标准差: 0.2258

