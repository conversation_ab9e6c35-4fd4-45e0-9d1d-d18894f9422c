层-头注意力分析报告
============================================================

样本信息:
  原文: 你先择几个个性重点培养，最终形成自己独特的男人鬼未力。
  目标: 你选择几个个性重点培养，最终形成自己独特的男人魅力。
  错误类型: 复合拆分字错误
  错误分析: 先择→选择, 鬼未力→魅力
  目标tokens: ['先', '择', '鬼', '未', '力']
  重构验证: 你先择几个个性重点培养，最终形成自己独特的男人鬼未力。

Token化分析:
--------------------------------------------------
位置   Token ID Token内容         是否错误      
--------------------------------------------------
0    56568    你                         
1    60726    先               ⚠️错误      
2    103123   择               ⚠️错误      
3    100204   几个                        
4    102647   个性                        
5    99887    重点                        
6    101112   培养                        
7    3837     ，                         
8    103941   最终                        
9    101894   形成                        
10   99283    自己                        
11   105071   独特的                       
12   102015   男人                        
13   102036   鬼               ⚠️错误      
14   38342    未               ⚠️错误      
15   47534    力               ⚠️错误      
16   1773     。                         

层-头热图说明:
  📊 横坐标: Attention Head Number (H0-H31)
  📊 纵坐标: Layer Number (L0-L35)
  🎯 颜色深浅: 对应指标的数值大小

生成的热图类型:
  1. Maximum Attention Weight: 每层每头的最大注意力权重
  2. Mean Attention Weight: 每层每头的平均注意力权重
  3. Attention Entropy: 每层每头的注意力熵（分布均匀程度）
  4. Target Token Attention: 对拆分字错误token的注意力

关键观察点:
  🔍 观察不同层级对拆分字错误的处理模式
  🔍 识别哪些注意力头更关注错误token
  🔍 分析注意力分布的层级变化趋势

模型架构信息:
  总层数: 36
  每层头数: 32
  序列长度: 17

统计摘要:
  Maximum Attention Weight:
    最大值: 1.0000
    最小值: 1.0000
    平均值: 1.0000
    标准差: 0.0000

  Mean Attention Weight:
    最大值: 0.0588
    最小值: 0.0588
    平均值: 0.0588
    标准差: 0.0000

  Attention Entropy:
    最大值: nan
    最小值: nan
    平均值: nan
    标准差: nan

  Target Token Attention:
    最大值: 1.2525
    最小值: 0.0003
    平均值: 0.2475
    标准差: 0.2481

