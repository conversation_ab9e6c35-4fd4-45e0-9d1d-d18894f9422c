层-头注意力分析报告
============================================================

样本信息:
  原文: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。
  目标: 我为什么喜欢阿拉木图，我觉得有几个特点。
  错误类型: 部首拆分错误
  错误分析: 牛寺→特
  目标tokens: ['牛', '寺']
  重构验证: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。

Token化分析:
--------------------------------------------------
位置   Token ID Token内容         是否错误      
--------------------------------------------------
0    35946    我                         
1    100678   为什么                       
2    99729    喜欢                        
3    103525   阿拉                        
4    75405    木                         
5    28029    图                         
6    3837     ，                         
7    104288   我觉得                       
8    112485   有几个                       
9    100664   牛               ⚠️错误      
10   101277   寺               ⚠️错误      
11   27442    点                         
12   1773     。                         

层-头热图说明:
  📊 横坐标: Attention Head Number (H0-H31)
  📊 纵坐标: Layer Number (L0-L35)
  🎯 颜色深浅: 对应指标的数值大小

生成的热图类型:
  1. Maximum Attention Weight: 每层每头的最大注意力权重
  2. Mean Attention Weight: 每层每头的平均注意力权重
  3. Attention Entropy: 每层每头的注意力熵（分布均匀程度）
  4. Target Token Attention: 对拆分字错误token的注意力

关键观察点:
  🔍 观察不同层级对拆分字错误的处理模式
  🔍 识别哪些注意力头更关注错误token
  🔍 分析注意力分布的层级变化趋势

模型架构信息:
  总层数: 36
  每层头数: 32
  序列长度: 13

统计摘要:
  Maximum Attention Weight:
    最大值: 1.0000
    最小值: 1.0000
    平均值: 1.0000
    标准差: 0.0000

  Mean Attention Weight:
    最大值: 0.0769
    最小值: 0.0769
    平均值: 0.0769
    标准差: 0.0000

  Attention Entropy:
    最大值: nan
    最小值: nan
    平均值: nan
    标准差: nan

  Target Token Attention:
    最大值: 1.5459
    最小值: 0.0000
    平均值: 0.1949
    标准差: 0.2699

