#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成增强版的层-头注意力热图，包含多种分析维度
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class EnhancedLayerHeadVisualizer:
    """增强版层-头注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_data(self, text, max_length=256):
        """提取注意力数据"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs和tokens
        input_ids = inputs['input_ids'][0].cpu().numpy()
        tokens = []
        for token_id in input_ids:
            try:
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                tokens.append(token_text.strip())
            except:
                tokens.append(f'[{token_id}]')
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text
        }
    
    def compute_enhanced_statistics(self, attention_data, error_token_indices=None):
        """计算增强的注意力统计信息"""
        attentions = attention_data['attentions']
        num_layers = len(attentions)
        num_heads = attentions[0].shape[0]
        seq_len = len(attention_data['tokens'])
        
        print(f"📊 计算增强统计: {num_layers}层 × {num_heads}头")
        
        # 初始化统计矩阵
        stats = {
            'max_attention': np.zeros((num_layers, num_heads)),
            'mean_attention': np.zeros((num_layers, num_heads)),
            'attention_variance': np.zeros((num_layers, num_heads)),
            'attention_sparsity': np.zeros((num_layers, num_heads)),
            'error_token_attention': np.zeros((num_layers, num_heads)) if error_token_indices else None,
            'self_attention': np.zeros((num_layers, num_heads)),
            'cross_attention': np.zeros((num_layers, num_heads))
        }
        
        for layer_idx in range(num_layers):
            for head_idx in range(num_heads):
                attention_matrix = attentions[layer_idx][head_idx]
                
                # 基本统计
                stats['max_attention'][layer_idx, head_idx] = np.max(attention_matrix)
                stats['mean_attention'][layer_idx, head_idx] = np.mean(attention_matrix)
                stats['attention_variance'][layer_idx, head_idx] = np.var(attention_matrix)
                
                # 注意力稀疏性（低于阈值的权重比例）
                threshold = 0.1
                sparse_ratio = np.sum(attention_matrix < threshold) / attention_matrix.size
                stats['attention_sparsity'][layer_idx, head_idx] = sparse_ratio
                
                # 自注意力（对角线元素的平均值）
                diagonal_attention = np.mean(np.diag(attention_matrix))
                stats['self_attention'][layer_idx, head_idx] = diagonal_attention
                
                # 交叉注意力（非对角线元素的平均值）
                mask = np.ones_like(attention_matrix, dtype=bool)
                np.fill_diagonal(mask, False)
                cross_attention = np.mean(attention_matrix[mask])
                stats['cross_attention'][layer_idx, head_idx] = cross_attention
                
                # 错误token注意力
                if error_token_indices and stats['error_token_attention'] is not None:
                    error_attention = 0
                    for error_idx in error_token_indices:
                        if error_idx < attention_matrix.shape[1]:
                            # 所有token对错误token的注意力
                            error_attention += np.sum(attention_matrix[:, error_idx])
                    stats['error_token_attention'][layer_idx, head_idx] = error_attention / len(error_token_indices)
        
        return stats
    
    def plot_enhanced_heatmap(self, data_matrix, title, save_path, cmap='viridis'):
        """绘制增强版热图"""
        num_layers, num_heads = data_matrix.shape

        # 创建图形
        fig, ax = plt.subplots(figsize=(18, 12))

        # 绘制热图
        im = ax.imshow(data_matrix, cmap=cmap, aspect='auto', interpolation='nearest')

        # 设置坐标轴标签
        # 横坐标：attention heads (每4个显示一个标签)
        head_ticks = list(range(0, num_heads, 4))
        head_labels = [f'H{i}' for i in head_ticks]
        ax.set_xticks(head_ticks)
        ax.set_xticklabels(head_labels, fontsize=10)

        # 纵坐标：layers (每4个显示一个标签)
        layer_ticks = list(range(0, num_layers, 4))
        layer_labels = [f'L{i}' for i in layer_ticks]
        ax.set_yticks(layer_ticks)
        ax.set_yticklabels(layer_labels, fontsize=10)

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Value', fontsize=12)

        # 设置标题和标签（移除中文，避免乱码）
        # 只使用英文标题
        clean_title = title.split(' - ')[0]  # 只保留英文部分
        ax.set_title(clean_title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Attention Head Number', fontsize=14, fontweight='bold')
        ax.set_ylabel('Layer Number', fontsize=14, fontweight='bold')
        
        # 添加细网格
        ax.set_xticks(np.arange(num_heads) - 0.5, minor=True)
        ax.set_yticks(np.arange(num_layers) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=0.3, alpha=0.5)
        
        # 添加粗网格（每8个头/层）
        major_head_ticks = np.arange(0, num_heads, 8) - 0.5
        major_layer_ticks = np.arange(0, num_layers, 8) - 0.5
        for tick in major_head_ticks:
            ax.axvline(tick, color='black', linewidth=1, alpha=0.7)
        for tick in major_layer_ticks:
            ax.axhline(tick, color='black', linewidth=1, alpha=0.7)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 增强热图已保存: {save_path}")
        
        plt.close()
    
    def create_comprehensive_analysis(self, output_dir):
        """创建综合分析"""
        print(f"🎨 开始增强版层-头分析...")
        
        # 定义测试样本
        samples = [
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'error_type': '简单拆分字错误',
                'error_tokens': ['白', '勺'],
                'error_indices': [7, 8]  # 根据tokenization结果
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'error_type': '部首拆分错误',
                'error_tokens': ['牛', '寺'],
                'error_indices': [13, 14]  # 需要根据实际tokenization调整
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力数据
            attention_data = self.extract_attention_data(sample['text'])
            
            # 找到实际的错误token索引
            actual_error_indices = []
            for j, token in enumerate(attention_data['tokens']):
                if any(error_token in token for error_token in sample['error_tokens']):
                    actual_error_indices.append(j)
            
            print(f"🎯 错误token位置: {actual_error_indices}")
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'enhanced_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 计算增强统计
            stats = self.compute_enhanced_statistics(attention_data, actual_error_indices)
            
            # 生成多种热图
            heatmap_configs = [
                ('max_attention', 'Maximum Attention Weight', 'viridis'),
                ('mean_attention', 'Mean Attention Weight', 'plasma'),
                ('attention_variance', 'Attention Variance', 'inferno'),
                ('attention_sparsity', 'Attention Sparsity', 'cividis'),
                ('self_attention', 'Self-Attention Strength', 'Blues'),
                ('cross_attention', 'Cross-Attention Strength', 'Reds'),
            ]
            
            if stats['error_token_attention'] is not None:
                heatmap_configs.append(('error_token_attention', 'Error Token Attention', 'YlOrRd'))
            
            for metric_key, title, cmap in heatmap_configs:
                save_path = os.path.join(sample_dir, f'enhanced_{metric_key}.png')
                # 使用纯英文标题，避免中文乱码
                clean_title = title  # 只使用英文标题
                self.plot_enhanced_heatmap(stats[metric_key], clean_title, save_path, cmap)
            
            # 保存详细报告
            report_path = os.path.join(sample_dir, 'enhanced_analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"增强版层-头注意力分析报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"样本信息:\n")
                f.write(f"  原文: {sample['text']}\n")
                f.write(f"  错误类型: {sample['error_type']}\n")
                f.write(f"  错误tokens: {sample['error_tokens']}\n")
                f.write(f"  实际错误位置: {actual_error_indices}\n\n")
                
                f.write(f"Token化结果:\n")
                f.write("-" * 40 + "\n")
                for j, token in enumerate(attention_data['tokens']):
                    error_mark = "⚠️" if j in actual_error_indices else "  "
                    f.write(f"{j:2d}: {token:10s} {error_mark}\n")
                
                f.write(f"\n热图说明:\n")
                f.write(f"  📊 横坐标: Attention Head (H0-H31)\n")
                f.write(f"  📊 纵坐标: Layer Number (L0-L35)\n")
                f.write(f"  🎨 颜色: 各指标的数值大小\n\n")
                
                f.write(f"生成的热图类型:\n")
                for metric_key, title, cmap in heatmap_configs:
                    f.write(f"  - {title} (colormap: {cmap})\n")
                
                f.write(f"\n指标解释:\n")
                f.write(f"  • Maximum Attention: 每层每头的最大注意力权重\n")
                f.write(f"  • Mean Attention: 每层每头的平均注意力权重\n")
                f.write(f"  • Attention Variance: 注意力权重的方差（分布离散程度）\n")
                f.write(f"  • Attention Sparsity: 低权重注意力的比例（稀疏性）\n")
                f.write(f"  • Self-Attention: 对角线注意力（token对自身的注意力）\n")
                f.write(f"  • Cross-Attention: 非对角线注意力（token间的注意力）\n")
                if stats['error_token_attention'] is not None:
                    f.write(f"  • Error Token Attention: 对错误token的注意力强度\n")
                
                f.write(f"\n统计摘要:\n")
                for metric_key, title, _ in heatmap_configs:
                    if metric_key in stats and stats[metric_key] is not None:
                        matrix = stats[metric_key]
                        f.write(f"  {title}:\n")
                        f.write(f"    最大值: {np.max(matrix):.4f}\n")
                        f.write(f"    最小值: {np.min(matrix):.4f}\n")
                        f.write(f"    平均值: {np.mean(matrix):.4f}\n")
                        f.write(f"    标准差: {np.std(matrix):.4f}\n\n")
                
                f.write(f"模型架构:\n")
                f.write(f"  总层数: {len(attention_data['attentions'])}\n")
                f.write(f"  每层头数: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  序列长度: {len(attention_data['tokens'])}\n")
            
            print(f"💾 样本 {i+1} 增强分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成增强版层-头注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/enhanced_layer_head_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成增强版层-头注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = EnhancedLayerHeadVisualizer(args.model_path, device)
    
    # 创建增强分析
    visualizer.create_comprehensive_analysis(args.output_dir)
    
    print(f"\n🎉 增强版层-头热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"📊 热图类型:")
    print(f"  🔥 Maximum Attention Weight (最大注意力)")
    print(f"  📊 Mean Attention Weight (平均注意力)")
    print(f"  📈 Attention Variance (注意力方差)")
    print(f"  🕳️ Attention Sparsity (注意力稀疏性)")
    print(f"  🔵 Self-Attention Strength (自注意力强度)")
    print(f"  🔴 Cross-Attention Strength (交叉注意力强度)")
    print(f"  ⚠️ Error Token Attention (错误token注意力)")
    print(f"🎯 横坐标: Attention Head Number (H0-H31)")
    print(f"🎯 纵坐标: Layer Number (L0-L35)")


if __name__ == '__main__':
    main()
