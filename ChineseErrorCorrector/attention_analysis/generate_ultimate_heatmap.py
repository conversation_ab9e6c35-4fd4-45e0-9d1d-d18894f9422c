#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成终极版本的注意力热图，彻底解决中文显示问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 彻底解决中文字体问题
import matplotlib.font_manager as fm
import urllib.request
import shutil

def download_and_setup_chinese_font():
    """下载并设置中文字体"""
    try:
        # 创建字体目录
        font_dir = os.path.expanduser('~/.fonts')
        os.makedirs(font_dir, exist_ok=True)
        
        # 检查是否已有字体文件
        font_file = os.path.join(font_dir, 'NotoSansCJK-Regular.ttc')
        
        if not os.path.exists(font_file):
            print("📥 下载中文字体文件...")
            # 使用系统已有的字体文件
            system_font_paths = [
                '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
                '/usr/share/fonts/truetype/arphic/uming.ttc',
                '/System/Library/Fonts/PingFang.ttc'
            ]
            
            for system_font in system_font_paths:
                if os.path.exists(system_font):
                    shutil.copy2(system_font, font_file)
                    print(f"✅ 复制字体文件: {system_font}")
                    break
        
        if os.path.exists(font_file):
            # 注册字体
            fm.fontManager.addfont(font_file)
            
            # 设置字体
            plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 清除缓存
            fm._rebuild()
            
            print("✅ 中文字体设置成功")
            return True
        else:
            print("⚠️ 未找到中文字体文件")
            return False
            
    except Exception as e:
        print(f"❌ 字体设置失败: {e}")
        return False


def setup_fallback_font():
    """设置备用字体方案"""
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    print("⚠️ 使用备用字体方案")


class UltimateAttentionVisualizer:
    """终极版本的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        
        # 尝试设置中文字体
        self.chinese_font_available = download_and_setup_chinese_font()
        if not self.chinese_font_available:
            setup_fallback_font()
        
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_with_clean_tokens(self, text, max_length=256):
        """提取注意力权重和清洁的token信息"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs
        input_ids = inputs['input_ids'][0].cpu().numpy()
        
        # 解码每个token
        clean_tokens = []
        for token_id in input_ids:
            try:
                # 解码单个token
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                clean_tokens.append(token_text.strip())
            except:
                clean_tokens.append(f'[{token_id}]')
        
        # 验证重构
        reconstructed = self.tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔍 原文: {text}")
        print(f"🔍 重构: {reconstructed}")
        print(f"🔍 Token数量: {len(clean_tokens)}")
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': clean_tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text,
            'reconstructed': reconstructed
        }
    
    def plot_ultimate_heatmap(self, attention_data, layer_idx, head_idx, 
                             max_display=10, save_path=None):
        """绘制终极版本的注意力热图"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 创建双重显示方案
        if self.chinese_font_available:
            # 方案A: 尝试显示中文
            display_tokens = tokens
            use_chinese = True
        else:
            # 方案B: 使用索引+英文标注
            display_tokens = [f'T{i}' for i in range(len(tokens))]
            use_chinese = False
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 14))
        
        # 绘制热图
        im = ax.imshow(attention, cmap='Blues', aspect='auto', interpolation='nearest')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(display_tokens)))
        ax.set_yticks(range(len(display_tokens)))
        
        try:
            # 尝试设置标签
            ax.set_xticklabels(display_tokens, rotation=45, ha='right', fontsize=11)
            ax.set_yticklabels(display_tokens, fontsize=11)
        except Exception as e:
            print(f"⚠️ 标签设置失败，使用备用方案: {e}")
            # 备用方案：使用索引
            backup_labels = [f'T{i}' for i in range(len(tokens))]
            ax.set_xticklabels(backup_labels, rotation=45, ha='right', fontsize=11)
            ax.set_yticklabels(backup_labels, fontsize=11)
            use_chinese = False
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 设置标题和标签
        title = f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}'
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Key Tokens', fontsize=12, fontweight='bold')
        ax.set_ylabel('Query Tokens', fontsize=12, fontweight='bold')
        
        # 添加网格
        ax.set_xticks(np.arange(len(display_tokens)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(display_tokens)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=1)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        # 添加文本说明
        if not use_chinese:
            # 在图的底部添加token映射说明
            mapping_text = "Token Mapping: "
            for i, token in enumerate(tokens[:8]):  # 只显示前8个
                mapping_text += f"T{i}={token} "
            if len(tokens) > 8:
                mapping_text += "..."
            
            fig.text(0.1, 0.02, mapping_text, fontsize=10, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        
        plt.tight_layout()
        
        # 为底部文本留出空间
        if not use_chinese:
            plt.subplots_adjust(bottom=0.15)
        
        if save_path:
            try:
                plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                           facecolor='white', edgecolor='none')
                print(f"💾 终极热图已保存: {save_path}")
            except Exception as e:
                print(f"❌ 保存失败: {e}")
        
        plt.close()
    
    def create_ultimate_analysis(self, output_dir):
        """创建终极版本的分析"""
        print(f"🎨 开始终极版本分析...")
        
        # 定义拆分字错误样本
        samples = [
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'target': '乌兰巴托不到4百年的历史。',
                'error_analysis': '白勺→的',
                'error_type': '简单拆分字错误'
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
                'error_analysis': '牛寺→特',
                'error_type': '部首拆分错误'
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力
            attention_data = self.extract_attention_with_clean_tokens(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'ultimate_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择关键层进行分析
            key_layers = [8, 16, 24]  # 减少层数以提高成功率
            key_heads = [0, 8, 16]    # 减少头数以提高成功率
            
            # 生成热图
            for layer_idx in key_layers:
                if layer_idx < len(attention_data['attentions']):
                    for head_idx in key_heads:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'ultimate_attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                            )
                            
                            self.plot_ultimate_heatmap(
                                attention_data,
                                layer_idx,
                                head_idx,
                                max_display=min(10, len(attention_data['tokens'])),
                                save_path=save_path
                            )
            
            # 保存详细分析报告
            report_path = os.path.join(sample_dir, 'ultimate_analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"终极版本拆分字错误注意力分析报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"字体状态: {'✅ 中文字体可用' if self.chinese_font_available else '⚠️ 使用备用方案'}\n\n")
                
                f.write(f"样本信息:\n")
                f.write(f"  原文: {sample['text']}\n")
                f.write(f"  目标: {sample['target']}\n")
                f.write(f"  错误类型: {sample['error_type']}\n")
                f.write(f"  错误分析: {sample['error_analysis']}\n")
                f.write(f"  重构验证: {attention_data['reconstructed']}\n\n")
                
                f.write(f"Token化详细分析:\n")
                f.write("-" * 60 + "\n")
                f.write(f"{'位置':<4} {'Token ID':<8} {'Token内容':<15} {'热图显示':<10} {'说明':<15}\n")
                f.write("-" * 60 + "\n")
                
                for j, (token_id, token) in enumerate(zip(attention_data['input_ids'], attention_data['tokens'])):
                    # 判断是否为错误token
                    is_error = False
                    error_note = ""
                    if any(char in token for char in ['白', '勺', '牛', '寺']):
                        is_error = True
                        error_note = "⚠️拆分字错误"
                    
                    display_way = token if self.chinese_font_available else f'T{j}'
                    
                    f.write(f"{j:<4} {token_id:<8} {token:<15} {display_way:<10} {error_note:<15}\n")
                
                f.write(f"\n热图解读说明:\n")
                if self.chinese_font_available:
                    f.write(f"  ✅ 横纵坐标直接显示中文token内容\n")
                    f.write(f"  📊 可以直观观察'白'和'勺'之间的注意力关系\n")
                else:
                    f.write(f"  📋 横纵坐标显示T0-T{len(attention_data['tokens'])-1}索引\n")
                    f.write(f"  📊 热图底部有token映射说明\n")
                    f.write(f"  🔍 请参考上方Token化分析表了解具体对应关系\n")
                
                f.write(f"\n关键观察点:\n")
                if '白' in attention_data['tokens'] and '勺' in attention_data['tokens']:
                    white_idx = attention_data['tokens'].index('白')
                    spoon_idx = attention_data['tokens'].index('勺')
                    f.write(f"  🎯 '白'的位置: T{white_idx}\n")
                    f.write(f"  🎯 '勺'的位置: T{spoon_idx}\n")
                    f.write(f"  📈 重点观察这两个位置之间的注意力权重\n")
                
                f.write(f"\n模型架构信息:\n")
                f.write(f"  总层数: {len(attention_data['attentions'])}\n")
                f.write(f"  每层头数: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  序列长度: {len(attention_data['tokens'])}\n")
            
            print(f"💾 样本 {i+1} 终极分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成终极版本的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/ultimate_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成终极版本的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = UltimateAttentionVisualizer(args.model_path, device)
    
    # 创建终极分析
    visualizer.create_ultimate_analysis(args.output_dir)
    
    print(f"\n🎉 终极版本热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    if visualizer.chinese_font_available:
        print(f"✅ 中文字体显示正常")
    else:
        print(f"⚠️ 使用备用显示方案")
        print(f"📋 热图底部有token映射说明")
        print(f"📋 详细对应关系请查看分析报告")


if __name__ == '__main__':
    main()
