Clean Layer-Head Attention Analysis Report
============================================================

Sample Information:
  Text: 乌兰巴托不到4百年白勺历史。
  Error Type: simple_split_error
  Description: Simple split character error: 白勺 -> 的
  Error Tokens: ['白', '勺']
  Error Positions: [7, 8]

Tokenization Result:
----------------------------------------
 0: 乌               
 1: 兰               
 2: 巴               
 3: 托               
 4: 不到              
 5: 4               
 6: 百年              
 7: 白               ERROR
 8: 勺               ERROR
 9: 历史              
10: 。               

Heatmap Description:
  X-axis: Attention Head Number (H0-H31)
  Y-axis: Layer Number (L0-L35)
  Color: Metric values

Generated Heatmaps:
  - Maximum Attention Weight (colormap: viridis)
  - Mean Attention Weight (colormap: plasma)
  - Attention Variance (colormap: inferno)
  - <PERSON><PERSON><PERSON>ken Attention (colormap: YlOrRd)

Statistics Summary:
  Maximum Attention Weight:
    Max: 1.0000
    Min: 1.0000
    Mean: 1.0000
    Std: 0.0000

  Mean Attention Weight:
    Max: 0.0909
    Min: 0.0909
    Mean: 0.0909
    Std: 0.0000

  Attention Variance:
    Max: 0.0826
    Min: 0.0196
    Mean: 0.0625
    Std: 0.0142

  Error Token Attention:
    Max: 1.4573
    Min: 0.0000
    Mean: 0.1800
    Std: 0.2258

Model Architecture:
  Total Layers: 36
  Heads per Layer: 32
  Sequence Length: 11
