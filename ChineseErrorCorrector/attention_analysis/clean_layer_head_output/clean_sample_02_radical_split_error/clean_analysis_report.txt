Clean Layer-Head Attention Analysis Report
============================================================

Sample Information:
  Text: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。
  Error Type: radical_split_error
  Description: Radical split error: 牛寺 -> 特
  Error Tokens: ['牛', '寺']
  Error Positions: [9, 10]

Tokenization Result:
----------------------------------------
 0: 我               
 1: 为什么             
 2: 喜欢              
 3: 阿拉              
 4: 木               
 5: 图               
 6: ，               
 7: 我觉得             
 8: 有几个             
 9: 牛               ERROR
10: 寺               ERROR
11: 点               
12: 。               

Heatmap Description:
  X-axis: Attention Head Number (H0-H31)
  Y-axis: Layer Number (L0-L35)
  Color: Metric values

Generated Heatmaps:
  - Maximum Attention Weight (colormap: viridis)
  - Mean Attention Weight (colormap: plasma)
  - Attention Variance (colormap: inferno)
  - Error Token Attention (colormap: YlOrRd)

Statistics Summary:
  Maximum Attention Weight:
    Max: 1.0000
    Min: 1.0000
    Mean: 1.0000
    Std: 0.0000

  Mean Attention Weight:
    Max: 0.0769
    Min: 0.0769
    Mean: 0.0769
    Std: 0.0000

  Attention Variance:
    Max: 0.0709
    Min: 0.0167
    Mean: 0.0518
    Std: 0.0121

  Error Token Attention:
    Max: 1.5459
    Min: 0.0000
    Mean: 0.1949
    Std: 0.2699

Model Architecture:
  Total Layers: 36
  Heads per Layer: 32
  Sequence Length: 13
