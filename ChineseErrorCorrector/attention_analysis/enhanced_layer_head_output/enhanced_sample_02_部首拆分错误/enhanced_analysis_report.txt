增强版层-头注意力分析报告
============================================================

样本信息:
  原文: 我为什么喜欢阿拉木图，我觉得有几个牛寺点。
  错误类型: 部首拆分错误
  错误tokens: ['牛', '寺']
  实际错误位置: [9, 10]

Token化结果:
----------------------------------------
 0: 我            
 1: 为什么          
 2: 喜欢           
 3: 阿拉           
 4: 木            
 5: 图            
 6: ，            
 7: 我觉得          
 8: 有几个          
 9: 牛          ⚠️
10: 寺          ⚠️
11: 点            
12: 。            

热图说明:
  📊 横坐标: Attention Head (H0-H31)
  📊 纵坐标: Layer Number (L0-L35)
  🎨 颜色: 各指标的数值大小

生成的热图类型:
  - Maximum Attention Weight (colormap: viridis)
  - Mean Attention Weight (colormap: plasma)
  - Attention Variance (colormap: inferno)
  - Attention Sparsity (colormap: cividis)
  - Self-Attention Strength (colormap: Blues)
  - Cross-Attention Strength (colormap: Reds)
  - Error Token Attention (colormap: YlOrRd)

指标解释:
  • Maximum Attention: 每层每头的最大注意力权重
  • Mean Attention: 每层每头的平均注意力权重
  • Attention Variance: 注意力权重的方差（分布离散程度）
  • Attention Sparsity: 低权重注意力的比例（稀疏性）
  • Self-Attention: 对角线注意力（token对自身的注意力）
  • Cross-Attention: 非对角线注意力（token间的注意力）
  • Error Token Attention: 对错误token的注意力强度

统计摘要:
  Maximum Attention Weight:
    最大值: 1.0000
    最小值: 1.0000
    平均值: 1.0000
    标准差: 0.0000

  Mean Attention Weight:
    最大值: 0.0769
    最小值: 0.0769
    平均值: 0.0769
    标准差: 0.0000

  Attention Variance:
    最大值: 0.0709
    最小值: 0.0167
    平均值: 0.0518
    标准差: 0.0121

  Attention Sparsity:
    最大值: 0.9231
    最小值: 0.7219
    平均值: 0.8834
    标准差: 0.0373

  Self-Attention Strength:
    最大值: 0.9976
    最小值: 0.0771
    平均值: 0.1521
    标准差: 0.1210

  Cross-Attention Strength:
    最大值: 0.0769
    最小值: 0.0002
    平均值: 0.0707
    标准差: 0.0101

  Error Token Attention:
    最大值: 1.5459
    最小值: 0.0000
    平均值: 0.1949
    标准差: 0.2699

模型架构:
  总层数: 36
  每层头数: 32
  序列长度: 13
