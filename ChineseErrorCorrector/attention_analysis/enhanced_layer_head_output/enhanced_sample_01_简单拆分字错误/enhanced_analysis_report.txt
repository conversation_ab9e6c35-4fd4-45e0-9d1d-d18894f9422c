增强版层-头注意力分析报告
============================================================

样本信息:
  原文: 乌兰巴托不到4百年白勺历史。
  错误类型: 简单拆分字错误
  错误tokens: ['白', '勺']
  实际错误位置: [7, 8]

Token化结果:
----------------------------------------
 0: 乌            
 1: 兰            
 2: 巴            
 3: 托            
 4: 不到           
 5: 4            
 6: 百年           
 7: 白          ⚠️
 8: 勺          ⚠️
 9: 历史           
10: 。            

热图说明:
  📊 横坐标: Attention Head (H0-H31)
  📊 纵坐标: Layer Number (L0-L35)
  🎨 颜色: 各指标的数值大小

生成的热图类型:
  - Maximum Attention Weight (colormap: viridis)
  - Mean Attention Weight (colormap: plasma)
  - Attention Variance (colormap: inferno)
  - Attention Sparsity (colormap: cividis)
  - Self-Attention Strength (colormap: Blues)
  - Cross-Attention Strength (colormap: Reds)
  - Error Token Attention (colormap: YlOrRd)

指标解释:
  • Maximum Attention: 每层每头的最大注意力权重
  • Mean Attention: 每层每头的平均注意力权重
  • Attention Variance: 注意力权重的方差（分布离散程度）
  • Attention Sparsity: 低权重注意力的比例（稀疏性）
  • Self-Attention: 对角线注意力（token对自身的注意力）
  • Cross-Attention: 非对角线注意力（token间的注意力）
  • Error Token Attention: 对错误token的注意力强度

统计摘要:
  Maximum Attention Weight:
    最大值: 1.0000
    最小值: 1.0000
    平均值: 1.0000
    标准差: 0.0000

  Mean Attention Weight:
    最大值: 0.0909
    最小值: 0.0909
    平均值: 0.0909
    标准差: 0.0000

  Attention Variance:
    最大值: 0.0826
    最小值: 0.0196
    平均值: 0.0625
    标准差: 0.0142

  Attention Sparsity:
    最大值: 0.9091
    最小值: 0.6612
    平均值: 0.8673
    标准差: 0.0446

  Self-Attention Strength:
    最大值: 0.9966
    最小值: 0.0909
    平均值: 0.1670
    标准差: 0.1238

  Cross-Attention Strength:
    最大值: 0.0909
    最小值: 0.0004
    平均值: 0.0833
    标准差: 0.0124

  Error Token Attention:
    最大值: 1.4573
    最小值: 0.0000
    平均值: 0.1800
    标准差: 0.2258

模型架构:
  总层数: 36
  每层头数: 32
  序列长度: 11
