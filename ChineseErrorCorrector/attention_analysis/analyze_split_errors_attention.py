#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析拆分字错误的注意力模式
专门针对Split数据集中的错误类型
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def clean_token_for_display(token, index=None):
    """清理token用于显示，处理中文字符编码问题"""
    try:
        # 移除特殊前缀
        if token.startswith('Ġ'):
            token = token[1:]
        elif token.startswith('##'):
            token = token[2:]

        # 尝试处理编码问题
        if token.startswith('ä') or token.startswith('å') or token.startswith('æ') or token.startswith('ç') or token.startswith('è') or token.startswith('é'):
            # 这些是UTF-8编码的中文字符，尝试解码
            try:
                # 尝试从bytes解码
                decoded = token.encode('latin1').decode('utf-8')
                return decoded[:2] if len(decoded) > 2 else decoded
            except:
                pass

        # 如果是纯ASCII或已经是正确的中文字符
        if len(token) <= 3:
            return token
        else:
            return token[:3] + '...'

    except Exception:
        # 如果所有方法都失败，使用索引
        return f'T{index}' if index is not None else 'UNK'


class SplitErrorAttentionAnalyzer:
    """拆分字错误注意力分析器"""

    def __init__(self, model_path, device='cuda'):
        self.device = device
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_for_text(self, text, max_length=256):
        """提取文本的注意力权重"""
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取tokens
        tokens = self.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'attentions': attention_matrices,
            'text': text
        }
    
    def plot_focused_attention(self, attention_data, layer_idx, head_idx,
                              focus_tokens=None, save_path=None):
        """绘制聚焦于特定token的注意力热图"""
        tokens = attention_data['tokens']
        attention = attention_data['attentions'][layer_idx][head_idx]

        # 限制显示的token数量
        max_display = min(20, len(tokens))
        raw_tokens = tokens[:max_display]
        display_attention = attention[:max_display, :max_display]

        # 处理token显示：将编码后的token转换为可读形式
        display_tokens = []
        for i, token in enumerate(raw_tokens):
            cleaned_token = clean_token_for_display(token, i)
            display_tokens.append(cleaned_token)

        # 创建图形
        plt.figure(figsize=(16, 14))

        # 绘制热图，移除数值显示
        sns.heatmap(
            display_attention,
            xticklabels=display_tokens,
            yticklabels=display_tokens,
            cmap='Blues',
            cbar=True,
            square=True,
            linewidths=0.1,
            cbar_kws={'label': 'Attention Weight'},
            annot=False,  # 移除数值显示
            fmt='.3f'
        )

        # 高亮特定的tokens（基于原始token匹配）
        if focus_tokens:
            for i, token in enumerate(raw_tokens):
                if any(focus_token in token for focus_token in focus_tokens):
                    # 高亮行
                    plt.axhspan(i, i+1, alpha=0.3, color='red', linewidth=2)
                    # 高亮列
                    plt.axvspan(i, i+1, alpha=0.3, color='red', linewidth=2)

        plt.title(f'Attention Pattern - Layer {layer_idx}, Head {head_idx}\n'
                 f'Text: {attention_data["text"]}',
                 fontsize=12, fontweight='bold')
        plt.xlabel('Key Tokens', fontsize=11)
        plt.ylabel('Query Tokens', fontsize=11)

        # 旋转标签
        plt.xticks(rotation=45, ha='right', fontsize=9)
        plt.yticks(rotation=0, fontsize=9)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"💾 注意力热图已保存: {save_path}")

        plt.close()
    
    def analyze_split_error_patterns(self, examples, output_dir):
        """分析多个拆分字错误的注意力模式"""
        print(f"🔍 分析 {len(examples)} 个拆分字错误样本...")
        
        for i, example in enumerate(examples):
            print(f"\n📝 分析样本 {i+1}: {example['text']}")
            
            # 提取注意力
            attention_data = self.extract_attention_for_text(example['text'])
            
            # 为每个样本创建子目录
            sample_dir = os.path.join(output_dir, f'sample_{i+1}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 生成多个层和头的热图
            interesting_layers = [8, 16, 24, 32]  # 选择一些有代表性的层
            
            for layer_idx in interesting_layers:
                if layer_idx < len(attention_data['attentions']):
                    # 选择几个有代表性的头
                    for head_idx in [0, 8, 16, 24]:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'attention_L{layer_idx}_H{head_idx}.png'
                            )
                            
                            self.plot_focused_attention(
                                attention_data,
                                layer_idx,
                                head_idx,
                                focus_tokens=example.get('error_tokens', []),
                                save_path=save_path
                            )
            
            # 保存token信息
            token_info_path = os.path.join(sample_dir, 'tokens.txt')
            with open(token_info_path, 'w', encoding='utf-8') as f:
                f.write(f"原文: {example['text']}\n")
                f.write(f"目标: {example.get('target', 'N/A')}\n")
                f.write(f"错误类型: {example.get('error_type', 'N/A')}\n\n")
                f.write("Tokens:\n")
                for j, token in enumerate(attention_data['tokens']):
                    f.write(f"{j:2d}: {token}\n")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析拆分字错误的注意力模式")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/split_error_analysis',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 拆分字错误注意力模式分析")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化分析器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    analyzer = SplitErrorAttentionAnalyzer(args.model_path, device)
    
    # 定义拆分字错误样本
    split_error_examples = [
        {
            'text': '你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
            'target': '你选择几个个性重点培养，最终形成自己独特的男人魅力。',
            'error_type': '拆分字错误',
            'error_tokens': ['先择', '鬼未力']
        },
        {
            'text': '秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。',
            'target': '秋天时，一片片叶子从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。',
            'error_type': '拆分字错误',
            'error_tokens': ['叶了', '京尤', '木羊']
        },
        {
            'text': '乌兰巴托不到4百年白勺历史。',
            'target': '乌兰巴托不到4百年的历史。',
            'error_type': '拆分字错误',
            'error_tokens': ['白勺']
        },
        {
            'text': '在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想。',
            'target': '在老师的帮助和鼓励下，我终于试着用汉语来表达自己的思想。',
            'error_type': '拆分字错误',
            'error_tokens': ['厉力力']
        },
        {
            'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
            'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
            'error_type': '拆分字错误',
            'error_tokens': ['牛寺']
        }
    ]
    
    print(f"📊 将分析 {len(split_error_examples)} 个拆分字错误样本")
    
    # 分析注意力模式
    analyzer.analyze_split_error_patterns(split_error_examples, args.output_dir)
    
    print(f"\n🎉 拆分字错误注意力分析完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"每个样本的分析结果保存在对应的子目录中")


if __name__ == '__main__':
    main()
