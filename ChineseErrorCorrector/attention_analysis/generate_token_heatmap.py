#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成带有实际token内容的注意力热图，解决token乱码问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False


def decode_token_to_text(tokenizer, token_ids, tokens):
    """将token解码为实际的文本内容"""
    try:
        # 方法1: 使用tokenizer直接解码单个token
        decoded_tokens = []
        for i, token_id in enumerate(token_ids):
            try:
                # 解码单个token
                decoded = tokenizer.decode([token_id], skip_special_tokens=False)
                # 清理特殊字符
                if decoded.strip():
                    decoded_tokens.append(decoded.strip())
                else:
                    decoded_tokens.append(f'[{i}]')
            except:
                decoded_tokens.append(f'[{i}]')
        
        return decoded_tokens
    except Exception as e:
        print(f"解码失败: {e}")
        # 备用方案：使用原始文本重新分割
        return [f'T{i}' for i in range(len(tokens))]


def clean_token_display(token, max_length=3):
    """清理token用于显示"""
    if not token:
        return '[?]'
    
    # 移除特殊标记
    if token.startswith('<') and token.endswith('>'):
        return token
    
    # 处理空格token
    if token.strip() == '':
        return '[SP]'
    
    # 截断过长的token
    if len(token) > max_length:
        return token[:max_length] + '..'
    
    return token


def reconstruct_text_from_tokens(tokenizer, input_ids):
    """从token IDs重构原始文本，用于验证"""
    try:
        reconstructed = tokenizer.decode(input_ids, skip_special_tokens=True)
        return reconstructed
    except:
        return "重构失败"


class TokenAttentionVisualizer:
    """带有实际token内容的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_with_tokens(self, text, max_length=256):
        """提取注意力权重和正确的token信息"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length,
            return_offsets_mapping=False
        ).to(self.device)
        
        # 获取token IDs
        input_ids = inputs['input_ids'][0].cpu().numpy()
        
        # 解码每个token
        decoded_tokens = []
        for token_id in input_ids:
            try:
                # 解码单个token
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                decoded_tokens.append(token_text)
            except:
                decoded_tokens.append(f'[UNK_{token_id}]')
        
        # 验证重构
        reconstructed = self.tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔍 原文: {text}")
        print(f"🔍 重构: {reconstructed}")
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': decoded_tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text,
            'reconstructed': reconstructed
        }
    
    def plot_token_heatmap(self, attention_data, layer_idx, head_idx, 
                          max_display=15, save_path=None):
        """绘制带有实际token的注意力热图"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 清理token用于显示
        display_tokens = []
        for i, token in enumerate(tokens):
            cleaned = clean_token_display(token, max_length=4)
            display_tokens.append(cleaned)
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(14, 12))
        
        # 绘制热图
        im = ax.imshow(attention, cmap='Blues', aspect='auto', interpolation='nearest')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(display_tokens)))
        ax.set_yticks(range(len(display_tokens)))
        ax.set_xticklabels(display_tokens, rotation=45, ha='right', fontsize=9)
        ax.set_yticklabels(display_tokens, fontsize=9)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 设置标题和标签
        ax.set_title(f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}\n'
                    f'Text: {attention_data["text"][:40]}...', 
                    fontsize=12, fontweight='bold', pad=20)
        ax.set_xlabel('Key Tokens', fontsize=11)
        ax.set_ylabel('Query Tokens', fontsize=11)
        
        # 添加网格
        ax.set_xticks(np.arange(len(display_tokens)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(display_tokens)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=0.5)
        
        # 添加注意力权重的文本标注（可选，只在token数量较少时显示）
        if len(display_tokens) <= 10:
            for i in range(len(display_tokens)):
                for j in range(len(display_tokens)):
                    text = ax.text(j, i, f'{attention[i, j]:.2f}',
                                 ha="center", va="center", color="black", fontsize=8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 Token热图已保存: {save_path}")
        
        plt.close()
    
    def create_comprehensive_token_analysis(self, text_samples, output_dir):
        """为多个文本样本创建综合token分析"""
        print(f"🎨 开始综合token注意力分析...")
        
        for i, sample in enumerate(text_samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:30]}...")
            
            # 提取注意力
            attention_data = self.extract_attention_with_tokens(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'sample_{i+1:02d}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择代表性的层和头
            layers_to_analyze = [8, 16, 24, 32]
            heads_to_analyze = [0, 8, 16, 24]
            
            # 生成热图
            for layer_idx in layers_to_analyze:
                if layer_idx < len(attention_data['attentions']):
                    for head_idx in heads_to_analyze:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'token_attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                            )
                            
                            self.plot_token_heatmap(
                                attention_data,
                                layer_idx,
                                head_idx,
                                max_display=12,  # 减少显示数量以保证清晰度
                                save_path=save_path
                            )
            
            # 保存详细的token信息
            token_info_path = os.path.join(sample_dir, 'token_analysis.txt')
            with open(token_info_path, 'w', encoding='utf-8') as f:
                f.write(f"样本 {i+1} Token分析\n")
                f.write("=" * 60 + "\n")
                f.write(f"原文: {sample['text']}\n")
                f.write(f"重构: {attention_data['reconstructed']}\n")
                if 'target' in sample:
                    f.write(f"目标: {sample['target']}\n")
                if 'error_type' in sample:
                    f.write(f"错误类型: {sample['error_type']}\n")
                f.write("\n")
                
                f.write("Token详细信息:\n")
                f.write("-" * 40 + "\n")
                f.write(f"{'ID':<4} {'Token ID':<8} {'Token Text':<15} {'Display':<10}\n")
                f.write("-" * 40 + "\n")
                
                for j, (token_id, token_text) in enumerate(zip(attention_data['input_ids'], attention_data['tokens'])):
                    display_token = clean_token_display(token_text, max_length=4)
                    f.write(f"{j:<4} {token_id:<8} {token_text:<15} {display_token:<10}\n")
                
                if 'error_tokens' in sample:
                    f.write(f"\n错误tokens: {sample['error_tokens']}\n")
                
                # 添加token统计
                f.write(f"\n统计信息:\n")
                f.write(f"Token总数: {len(attention_data['tokens'])}\n")
                f.write(f"注意力层数: {len(attention_data['attentions'])}\n")
                f.write(f"每层头数: {attention_data['attentions'][0].shape[0]}\n")
            
            print(f"💾 样本 {i+1} 分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成带有实际token的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/token_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成带有实际token的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = TokenAttentionVisualizer(args.model_path, device)
    
    # 定义拆分字错误样本
    text_samples = [
        {
            'text': '你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
            'target': '你选择几个个性重点培养，最终形成自己独特的男人魅力。',
            'error_type': '拆分字错误',
            'error_tokens': ['先择', '鬼未力']
        },
        {
            'text': '乌兰巴托不到4百年白勺历史。',
            'target': '乌兰巴托不到4百年的历史。',
            'error_type': '拆分字错误',
            'error_tokens': ['白勺']
        },
        {
            'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
            'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
            'error_type': '拆分字错误',
            'error_tokens': ['牛寺']
        }
    ]
    
    # 创建综合分析
    visualizer.create_comprehensive_token_analysis(text_samples, args.output_dir)
    
    print(f"\n🎉 Token热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"📋 每个样本包含:")
    print(f"  - 4个层 × 4个头 = 16个token注意力热图")
    print(f"  - token_analysis.txt (详细的token分析信息)")
    print(f"  - 横纵坐标显示实际的token内容")


if __name__ == '__main__':
    main()
