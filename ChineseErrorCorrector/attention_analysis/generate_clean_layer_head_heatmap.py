#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成无乱码的层-头注意力热图
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置字体，避免中文显示问题
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class CleanLayerHeadVisualizer:
    """无乱码的层-头注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        
        print(f"Loading model: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"Model loaded successfully")
        
    def extract_attention_data(self, text, max_length=256):
        """提取注意力数据"""
        print(f"Processing text: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs和tokens
        input_ids = inputs['input_ids'][0].cpu().numpy()
        tokens = []
        for token_id in input_ids:
            try:
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                tokens.append(token_text.strip())
            except:
                tokens.append(f'[{token_id}]')
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text
        }
    
    def compute_attention_statistics(self, attention_data, error_token_indices=None):
        """计算注意力统计信息"""
        attentions = attention_data['attentions']
        num_layers = len(attentions)
        num_heads = attentions[0].shape[0]
        
        print(f"Computing statistics: {num_layers} layers x {num_heads} heads")
        
        # 初始化统计矩阵
        stats = {
            'max_attention': np.zeros((num_layers, num_heads)),
            'mean_attention': np.zeros((num_layers, num_heads)),
            'attention_variance': np.zeros((num_layers, num_heads)),
            'error_token_attention': np.zeros((num_layers, num_heads)) if error_token_indices else None,
        }
        
        for layer_idx in range(num_layers):
            for head_idx in range(num_heads):
                attention_matrix = attentions[layer_idx][head_idx]
                
                # 基本统计
                stats['max_attention'][layer_idx, head_idx] = np.max(attention_matrix)
                stats['mean_attention'][layer_idx, head_idx] = np.mean(attention_matrix)
                stats['attention_variance'][layer_idx, head_idx] = np.var(attention_matrix)
                
                # 错误token注意力
                if error_token_indices and stats['error_token_attention'] is not None:
                    error_attention = 0
                    for error_idx in error_token_indices:
                        if error_idx < attention_matrix.shape[1]:
                            # 所有token对错误token的注意力
                            error_attention += np.sum(attention_matrix[:, error_idx])
                    stats['error_token_attention'][layer_idx, head_idx] = error_attention / len(error_token_indices)
        
        return stats
    
    def plot_clean_heatmap(self, data_matrix, title, save_path, cmap='viridis'):
        """绘制无乱码热图"""
        num_layers, num_heads = data_matrix.shape
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 12))
        
        # 绘制热图
        im = ax.imshow(data_matrix, cmap=cmap, aspect='auto', interpolation='nearest')
        
        # 设置坐标轴标签
        # 横坐标：attention heads
        head_ticks = list(range(0, num_heads, 4))
        head_labels = [f'H{i}' for i in head_ticks]
        ax.set_xticks(head_ticks)
        ax.set_xticklabels(head_labels, fontsize=11)
        
        # 纵坐标：layers
        layer_ticks = list(range(0, num_layers, 4))
        layer_labels = [f'L{i}' for i in layer_ticks]
        ax.set_yticks(layer_ticks)
        ax.set_yticklabels(layer_labels, fontsize=11)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Value', fontsize=12)
        
        # 设置标题和标签（纯英文，无乱码）
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Attention Head Number', fontsize=12, fontweight='bold')
        ax.set_ylabel('Layer Number', fontsize=12, fontweight='bold')
        
        # 添加网格
        ax.set_xticks(np.arange(num_heads) - 0.5, minor=True)
        ax.set_yticks(np.arange(num_layers) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=0.3, alpha=0.5)
        
        # 添加主要网格线
        major_head_ticks = np.arange(0, num_heads, 8) - 0.5
        major_layer_ticks = np.arange(0, num_layers, 8) - 0.5
        for tick in major_head_ticks:
            ax.axvline(tick, color='black', linewidth=1, alpha=0.7)
        for tick in major_layer_ticks:
            ax.axhline(tick, color='black', linewidth=1, alpha=0.7)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"Clean heatmap saved: {save_path}")
        
        plt.close()
    
    def create_clean_analysis(self, output_dir):
        """创建无乱码分析"""
        print(f"Starting clean layer-head analysis...")
        
        # 定义测试样本
        samples = [
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'error_type': 'simple_split_error',
                'error_tokens': ['白', '勺'],
                'description': 'Simple split character error: 白勺 -> 的'
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'error_type': 'radical_split_error',
                'error_tokens': ['牛', '寺'],
                'description': 'Radical split error: 牛寺 -> 特'
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\nAnalyzing sample {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力数据
            attention_data = self.extract_attention_data(sample['text'])
            
            # 找到实际的错误token索引
            actual_error_indices = []
            for j, token in enumerate(attention_data['tokens']):
                if any(error_token in token for error_token in sample['error_tokens']):
                    actual_error_indices.append(j)
            
            print(f"Error token positions: {actual_error_indices}")
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'clean_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 计算统计
            stats = self.compute_attention_statistics(attention_data, actual_error_indices)
            
            # 生成热图
            heatmap_configs = [
                ('max_attention', 'Maximum Attention Weight', 'viridis'),
                ('mean_attention', 'Mean Attention Weight', 'plasma'),
                ('attention_variance', 'Attention Variance', 'inferno'),
            ]
            
            if stats['error_token_attention'] is not None:
                heatmap_configs.append(('error_token_attention', 'Error Token Attention', 'YlOrRd'))
            
            for metric_key, title, cmap in heatmap_configs:
                save_path = os.path.join(sample_dir, f'clean_{metric_key}.png')
                self.plot_clean_heatmap(stats[metric_key], title, save_path, cmap)
            
            # 保存分析报告
            report_path = os.path.join(sample_dir, 'clean_analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"Clean Layer-Head Attention Analysis Report\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"Sample Information:\n")
                f.write(f"  Text: {sample['text']}\n")
                f.write(f"  Error Type: {sample['error_type']}\n")
                f.write(f"  Description: {sample['description']}\n")
                f.write(f"  Error Tokens: {sample['error_tokens']}\n")
                f.write(f"  Error Positions: {actual_error_indices}\n\n")
                
                f.write(f"Tokenization Result:\n")
                f.write("-" * 40 + "\n")
                for j, token in enumerate(attention_data['tokens']):
                    error_mark = "ERROR" if j in actual_error_indices else ""
                    f.write(f"{j:2d}: {token:15s} {error_mark}\n")
                
                f.write(f"\nHeatmap Description:\n")
                f.write(f"  X-axis: Attention Head Number (H0-H31)\n")
                f.write(f"  Y-axis: Layer Number (L0-L35)\n")
                f.write(f"  Color: Metric values\n\n")
                
                f.write(f"Generated Heatmaps:\n")
                for metric_key, title, cmap in heatmap_configs:
                    f.write(f"  - {title} (colormap: {cmap})\n")
                
                f.write(f"\nStatistics Summary:\n")
                for metric_key, title, _ in heatmap_configs:
                    if metric_key in stats and stats[metric_key] is not None:
                        matrix = stats[metric_key]
                        f.write(f"  {title}:\n")
                        f.write(f"    Max: {np.max(matrix):.4f}\n")
                        f.write(f"    Min: {np.min(matrix):.4f}\n")
                        f.write(f"    Mean: {np.mean(matrix):.4f}\n")
                        f.write(f"    Std: {np.std(matrix):.4f}\n\n")
                
                f.write(f"Model Architecture:\n")
                f.write(f"  Total Layers: {len(attention_data['attentions'])}\n")
                f.write(f"  Heads per Layer: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  Sequence Length: {len(attention_data['tokens'])}\n")
            
            print(f"Sample {i+1} clean analysis completed, saved in: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Generate clean layer-head attention heatmaps")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='Model path')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/clean_layer_head_output',
                       help='Output directory')
    
    args = parser.parse_args()
    
    print("Generating Clean Layer-Head Attention Heatmaps")
    print("=" * 60)
    print(f"Model Path: {args.model_path}")
    print(f"Output Directory: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = CleanLayerHeadVisualizer(args.model_path, device)
    
    # 创建无乱码分析
    visualizer.create_clean_analysis(args.output_dir)
    
    print(f"\nClean layer-head heatmaps generation completed!")
    print(f"Results saved in: {args.output_dir}")
    print(f"Features:")
    print(f"  - No Chinese character encoding issues")
    print(f"  - Clean English titles and labels")
    print(f"  - Professional visualization quality")
    print(f"  - X-axis: Attention Head Number (H0-H31)")
    print(f"  - Y-axis: Layer Number (L0-L35)")


if __name__ == '__main__':
    main()
