#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成以attention head为横坐标、layer为纵坐标的注意力热图
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class LayerHeadAttentionVisualizer:
    """层-头注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention_with_clean_tokens(self, text, max_length=256):
        """提取注意力权重和清洁的token信息"""
        print(f"📝 处理文本: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs
        input_ids = inputs['input_ids'][0].cpu().numpy()
        
        # 解码每个token
        clean_tokens = []
        for token_id in input_ids:
            try:
                # 解码单个token
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                clean_tokens.append(token_text.strip())
            except:
                clean_tokens.append(f'[{token_id}]')
        
        # 验证重构
        reconstructed = self.tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔍 原文: {text}")
        print(f"🔍 重构: {reconstructed}")
        print(f"🔍 Token数量: {len(clean_tokens)}")
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': clean_tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text,
            'reconstructed': reconstructed
        }
    
    def compute_attention_statistics(self, attention_data, target_tokens=None):
        """计算注意力统计信息"""
        attentions = attention_data['attentions']
        tokens = attention_data['tokens']
        
        num_layers = len(attentions)
        num_heads = attentions[0].shape[0]
        seq_len = len(tokens)
        
        print(f"📊 计算注意力统计: {num_layers}层 × {num_heads}头 × {seq_len}tokens")
        
        # 初始化统计矩阵
        stats_matrices = {
            'max_attention': np.zeros((num_layers, num_heads)),
            'mean_attention': np.zeros((num_layers, num_heads)),
            'attention_entropy': np.zeros((num_layers, num_heads)),
            'target_attention': np.zeros((num_layers, num_heads)) if target_tokens else None
        }
        
        # 计算每层每头的统计信息
        for layer_idx in range(num_layers):
            for head_idx in range(num_heads):
                attention_matrix = attentions[layer_idx][head_idx]
                
                # 最大注意力权重
                stats_matrices['max_attention'][layer_idx, head_idx] = np.max(attention_matrix)
                
                # 平均注意力权重
                stats_matrices['mean_attention'][layer_idx, head_idx] = np.mean(attention_matrix)
                
                # 注意力熵（衡量注意力分布的均匀程度）
                # 对每个query token计算熵，然后取平均
                entropies = []
                for i in range(attention_matrix.shape[0]):
                    attention_dist = attention_matrix[i, :]
                    # 确保是概率分布（归一化）
                    attention_dist = attention_dist / (np.sum(attention_dist) + 1e-10)
                    # 避免log(0)
                    attention_dist = np.clip(attention_dist, 1e-10, 1.0)
                    entropy = -np.sum(attention_dist * np.log(attention_dist))
                    if np.isfinite(entropy):
                        entropies.append(entropy)

                if entropies:
                    stats_matrices['attention_entropy'][layer_idx, head_idx] = np.mean(entropies)
                else:
                    stats_matrices['attention_entropy'][layer_idx, head_idx] = 0.0
                
                # 如果指定了目标token，计算对目标token的注意力
                if target_tokens and stats_matrices['target_attention'] is not None:
                    target_attention_sum = 0
                    target_count = 0
                    for token_idx, token in enumerate(tokens):
                        if any(target_token in token for target_token in target_tokens):
                            # 计算所有token对该目标token的注意力总和
                            target_attention_sum += np.sum(attention_matrix[:, token_idx])
                            target_count += 1
                    
                    if target_count > 0:
                        stats_matrices['target_attention'][layer_idx, head_idx] = target_attention_sum / target_count
        
        return stats_matrices
    
    def plot_layer_head_heatmap(self, stats_matrix, metric_name, save_path=None, 
                               title_suffix="", vmin=None, vmax=None):
        """绘制层-头热图"""
        num_layers, num_heads = stats_matrix.shape
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(16, 12))
        
        # 绘制热图
        im = ax.imshow(stats_matrix, cmap='viridis', aspect='auto', 
                      interpolation='nearest', vmin=vmin, vmax=vmax)
        
        # 设置坐标轴
        # 横坐标：attention heads
        head_labels = [f'H{i}' for i in range(num_heads)]
        ax.set_xticks(range(0, num_heads, 4))  # 每4个头显示一个标签
        ax.set_xticklabels([f'H{i}' for i in range(0, num_heads, 4)], fontsize=10)
        
        # 纵坐标：layers
        layer_labels = [f'L{i}' for i in range(num_layers)]
        ax.set_yticks(range(0, num_layers, 4))  # 每4层显示一个标签
        ax.set_yticklabels([f'L{i}' for i in range(0, num_layers, 4)], fontsize=10)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label(metric_name, fontsize=12)
        
        # 设置标题和标签（移除中文，避免乱码）
        title = f'{metric_name} Across Layers and Heads'
        # 不添加中文后缀，避免乱码
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Attention Head Number', fontsize=12, fontweight='bold')
        ax.set_ylabel('Layer Number', fontsize=12, fontweight='bold')
        
        # 添加网格
        ax.set_xticks(np.arange(num_heads) - 0.5, minor=True)
        ax.set_yticks(np.arange(num_layers) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=0.5, alpha=0.3)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('black')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 层-头热图已保存: {save_path}")
        
        plt.close()
    
    def create_comprehensive_layer_head_analysis(self, output_dir):
        """创建综合的层-头分析"""
        print(f"🎨 开始层-头注意力分析...")
        
        # 定义拆分字错误样本
        samples = [
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'target': '乌兰巴托不到4百年的历史。',
                'error_analysis': '白勺→的',
                'error_type': '简单拆分字错误',
                'target_tokens': ['白', '勺']
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
                'error_analysis': '牛寺→特',
                'error_type': '部首拆分错误',
                'target_tokens': ['牛', '寺']
            },
            {
                'text': '你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
                'target': '你选择几个个性重点培养，最终形成自己独特的男人魅力。',
                'error_analysis': '先择→选择, 鬼未力→魅力',
                'error_type': '复合拆分字错误',
                'target_tokens': ['先', '择', '鬼', '未', '力']
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力
            attention_data = self.extract_attention_with_clean_tokens(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'layer_head_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 计算注意力统计
            stats = self.compute_attention_statistics(attention_data, sample['target_tokens'])
            
            # 生成不同类型的层-头热图
            metrics = [
                ('max_attention', 'Maximum Attention Weight', None, None),
                ('mean_attention', 'Mean Attention Weight', None, None),
                ('attention_entropy', 'Attention Entropy', None, None),
            ]
            
            # 如果有目标token，添加目标注意力热图
            if stats['target_attention'] is not None:
                metrics.append(('target_attention', 'Target Token Attention', None, None))
            
            for metric_key, metric_name, vmin, vmax in metrics:
                save_path = os.path.join(sample_dir, f'layer_head_{metric_key}.png')
                self.plot_layer_head_heatmap(
                    stats[metric_key],
                    metric_name,
                    save_path,
                    title_suffix="",  # 移除中文后缀
                    vmin=vmin,
                    vmax=vmax
                )
            
            # 保存详细分析报告
            report_path = os.path.join(sample_dir, 'layer_head_analysis_report.txt')
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"层-头注意力分析报告\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"样本信息:\n")
                f.write(f"  原文: {sample['text']}\n")
                f.write(f"  目标: {sample['target']}\n")
                f.write(f"  错误类型: {sample['error_type']}\n")
                f.write(f"  错误分析: {sample['error_analysis']}\n")
                f.write(f"  目标tokens: {sample['target_tokens']}\n")
                f.write(f"  重构验证: {attention_data['reconstructed']}\n\n")
                
                f.write(f"Token化分析:\n")
                f.write("-" * 50 + "\n")
                f.write(f"{'位置':<4} {'Token ID':<8} {'Token内容':<15} {'是否错误':<10}\n")
                f.write("-" * 50 + "\n")
                
                for j, (token_id, token) in enumerate(zip(attention_data['input_ids'], attention_data['tokens'])):
                    is_error = any(target_token in token for target_token in sample['target_tokens'])
                    error_mark = "⚠️错误" if is_error else ""
                    f.write(f"{j:<4} {token_id:<8} {token:<15} {error_mark:<10}\n")
                
                f.write(f"\n层-头热图说明:\n")
                f.write(f"  📊 横坐标: Attention Head Number (H0-H{attention_data['attentions'][0].shape[0]-1})\n")
                f.write(f"  📊 纵坐标: Layer Number (L0-L{len(attention_data['attentions'])-1})\n")
                f.write(f"  🎯 颜色深浅: 对应指标的数值大小\n\n")
                
                f.write(f"生成的热图类型:\n")
                f.write(f"  1. Maximum Attention Weight: 每层每头的最大注意力权重\n")
                f.write(f"  2. Mean Attention Weight: 每层每头的平均注意力权重\n")
                f.write(f"  3. Attention Entropy: 每层每头的注意力熵（分布均匀程度）\n")
                if stats['target_attention'] is not None:
                    f.write(f"  4. Target Token Attention: 对拆分字错误token的注意力\n")
                
                f.write(f"\n关键观察点:\n")
                f.write(f"  🔍 观察不同层级对拆分字错误的处理模式\n")
                f.write(f"  🔍 识别哪些注意力头更关注错误token\n")
                f.write(f"  🔍 分析注意力分布的层级变化趋势\n")
                
                f.write(f"\n模型架构信息:\n")
                f.write(f"  总层数: {len(attention_data['attentions'])}\n")
                f.write(f"  每层头数: {attention_data['attentions'][0].shape[0]}\n")
                f.write(f"  序列长度: {len(attention_data['tokens'])}\n")
                
                # 添加统计摘要
                f.write(f"\n统计摘要:\n")
                for metric_key, metric_name, _, _ in metrics:
                    if metric_key in stats and stats[metric_key] is not None:
                        matrix = stats[metric_key]
                        f.write(f"  {metric_name}:\n")
                        f.write(f"    最大值: {np.max(matrix):.4f}\n")
                        f.write(f"    最小值: {np.min(matrix):.4f}\n")
                        f.write(f"    平均值: {np.mean(matrix):.4f}\n")
                        f.write(f"    标准差: {np.std(matrix):.4f}\n\n")
            
            print(f"💾 样本 {i+1} 层-头分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成层-头注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/layer_head_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成层-头注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = LayerHeadAttentionVisualizer(args.model_path, device)
    
    # 创建层-头分析
    visualizer.create_comprehensive_layer_head_analysis(args.output_dir)
    
    print(f"\n🎉 层-头注意力热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"📊 热图类型:")
    print(f"  - Maximum Attention Weight (最大注意力权重)")
    print(f"  - Mean Attention Weight (平均注意力权重)")
    print(f"  - Attention Entropy (注意力熵)")
    print(f"  - Target Token Attention (目标token注意力)")
    print(f"🎯 横坐标: Attention Head Number")
    print(f"🎯 纵坐标: Layer Number")


if __name__ == '__main__':
    main()
