#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成简洁的注意力热图，使用数字索引避免中文显示问题
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置matplotlib参数
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class SimpleAttentionVisualizer:
    """简洁的注意力可视化器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        print(f"🔧 加载模型: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"✅ 模型加载完成")
        
    def extract_attention(self, text, max_length=256):
        """提取注意力权重"""
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取tokens
        tokens = self.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'attentions': attention_matrices,
            'text': text
        }
    
    def plot_simple_heatmap(self, attention_data, layer_idx, head_idx, 
                           max_display=15, save_path=None):
        """绘制简洁的注意力热图，使用数字索引"""
        tokens = attention_data['tokens'][:max_display]
        attention = attention_data['attentions'][layer_idx][head_idx][:max_display, :max_display]
        
        # 使用数字索引作为标签
        token_indices = [f'T{i}' for i in range(len(tokens))]
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 绘制热图
        im = ax.imshow(attention, cmap='Blues', aspect='auto')
        
        # 设置刻度和标签
        ax.set_xticks(range(len(token_indices)))
        ax.set_yticks(range(len(token_indices)))
        ax.set_xticklabels(token_indices, fontsize=10)
        ax.set_yticklabels(token_indices, fontsize=10)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Attention Weight', fontsize=12)
        
        # 设置标题和标签
        ax.set_title(f'Attention Heatmap - Layer {layer_idx}, Head {head_idx}', 
                    fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('Key Token Index', fontsize=12)
        ax.set_ylabel('Query Token Index', fontsize=12)
        
        # 添加网格
        ax.set_xticks(np.arange(len(token_indices)) - 0.5, minor=True)
        ax.set_yticks(np.arange(len(token_indices)) - 0.5, minor=True)
        ax.grid(which='minor', color='white', linestyle='-', linewidth=0.5)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            print(f"💾 简洁热图已保存: {save_path}")
        
        plt.close()
    
    def create_comprehensive_analysis(self, text_samples, output_dir):
        """为多个文本样本创建综合分析"""
        print(f"🎨 开始综合注意力分析...")
        
        for i, sample in enumerate(text_samples):
            print(f"\n📝 分析样本 {i+1}: {sample['text'][:30]}...")
            
            # 提取注意力
            attention_data = self.extract_attention(sample['text'])
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'sample_{i+1:02d}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 选择代表性的层和头
            layers_to_analyze = [8, 16, 24, 32]
            heads_to_analyze = [0, 8, 16, 24]
            
            # 生成热图
            for layer_idx in layers_to_analyze:
                if layer_idx < len(attention_data['attentions']):
                    for head_idx in heads_to_analyze:
                        if head_idx < attention_data['attentions'][layer_idx].shape[0]:
                            save_path = os.path.join(
                                sample_dir, 
                                f'attention_L{layer_idx:02d}_H{head_idx:02d}.png'
                            )
                            
                            self.plot_simple_heatmap(
                                attention_data,
                                layer_idx,
                                head_idx,
                                max_display=15,
                                save_path=save_path
                            )
            
            # 保存详细的token映射信息
            token_mapping_path = os.path.join(sample_dir, 'token_mapping.txt')
            with open(token_mapping_path, 'w', encoding='utf-8') as f:
                f.write(f"样本 {i+1} Token映射\n")
                f.write("=" * 50 + "\n")
                f.write(f"原文: {sample['text']}\n")
                if 'target' in sample:
                    f.write(f"目标: {sample['target']}\n")
                if 'error_type' in sample:
                    f.write(f"错误类型: {sample['error_type']}\n")
                f.write("\n")
                
                f.write("Token索引映射:\n")
                f.write("-" * 30 + "\n")
                for j, token in enumerate(attention_data['tokens'][:20]):
                    f.write(f"T{j:2d}: {token}\n")
                
                if 'error_tokens' in sample:
                    f.write(f"\n错误tokens: {sample['error_tokens']}\n")
            
            print(f"💾 样本 {i+1} 分析完成，保存在: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成简洁的注意力热图")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='模型路径')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/simple_output',
                       help='输出目录')
    
    args = parser.parse_args()
    
    print("🎯 生成简洁的注意力热图")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {args.output_dir}")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化可视化器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    visualizer = SimpleAttentionVisualizer(args.model_path, device)
    
    # 定义拆分字错误样本
    text_samples = [
        {
            'text': '你先择几个个性重点培养，最终形成自己独特的男人鬼未力。',
            'target': '你选择几个个性重点培养，最终形成自己独特的男人魅力。',
            'error_type': '拆分字错误',
            'error_tokens': ['先择', '鬼未力']
        },
        {
            'text': '秋天时，一片片叶了从树落下来，京尤像芭蕾舞演员在跳舞一木羊是那么的优美。',
            'target': '秋天时，一片片叶子从树上落下来，就像芭蕾舞演员在跳舞一样是那么的优美。',
            'error_type': '拆分字错误',
            'error_tokens': ['叶了', '京尤', '木羊']
        },
        {
            'text': '乌兰巴托不到4百年白勺历史。',
            'target': '乌兰巴托不到4百年的历史。',
            'error_type': '拆分字错误',
            'error_tokens': ['白勺']
        },
        {
            'text': '在老师的帮助和鼓厉力力下，我终于试着用汉语来表达自己的思想。',
            'target': '在老师的帮助和鼓励下，我终于试着用汉语来表达自己的思想。',
            'error_type': '拆分字错误',
            'error_tokens': ['厉力力']
        },
        {
            'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
            'target': '我为什么喜欢阿拉木图，我觉得有几个特点。',
            'error_type': '拆分字错误',
            'error_tokens': ['牛寺']
        }
    ]
    
    # 创建综合分析
    visualizer.create_comprehensive_analysis(text_samples, args.output_dir)
    
    print(f"\n🎉 简洁热图生成完成!")
    print(f"📁 结果保存在: {args.output_dir}")
    print(f"📋 每个样本包含:")
    print(f"  - 4个层 × 4个头 = 16个注意力热图")
    print(f"  - token_mapping.txt (详细的token映射信息)")


if __name__ == '__main__':
    main()
