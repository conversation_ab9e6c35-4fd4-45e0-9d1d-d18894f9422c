{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# General BERT Attention Analysis\n", "\n", "This notebook contains code for analyzing general patterns BERT's attention (see Sections 3 and 6 of [What Does BERT Look At? An Analysis of BERT's Attention](https://arxiv.org/abs/1906.04341))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import collections\n", "import pickle\n", "\n", "import matplotlib\n", "import numpy as np\n", "import seaborn as sns\n", "import sklearn\n", "\n", "from matplotlib import pyplot as plt\n", "from matplotlib import cm\n", "from sklearn import manifold\n", "\n", "sns.set_style(\"darkgrid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading the Data\n", "You can download the data used for the notebook [here](https://drive.google.com/open?id=1DEIBQIl0Q0az5ZuLoy4_lYabIfLSKBg-). You can create your own data and extract your own attention maps using `preprocess_unlabeled.py` and `extract_attention.py` (see the README for details)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def load_pickle(fname):\n", "    with open(fname, \"rb\") as f:\n", "        return pickle.load(f)  # add, encoding=\"latin1\") if using python3 and downloaded data\n", "\n", "# BERT-base Attention Maps extracted from Wikipedia\n", "# Data is a list of dicts of the followig form:\n", "# {\n", "#    \"tokens\": list of strings\n", "#    \"attns\": [n_layers, n_heads, n_tokens, n_tokens] \n", "#             tensor of attention weights\n", "# }\n", "data = load_pickle(\"./data/unlabeled_attn.pkl\")\n", "n_docs = len(data)\n", "\n", "# Average <PERSON><PERSON>-Shannon divergences between attention heads\n", "js_divergences = load_pickle(\"./data/head_distances.pkl\")\n", "\n", "\n", "def data_iterator():\n", "  for i, doc in enumerate(data):\n", "    if i % 100 == 0 or i == len(data) - 1:\n", "      print(\"{:.1f}% done\".format(100.0 * (i + 1) / len(data)))\n", "    yield doc[\"tokens\"], np.array(doc[\"attns\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Computing Average Attention to Particular Tokens/Positions (Sections 3.1 and 3.2 of the Paper)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing token stats\n", "0.1% done\n", "10.2% done\n", "20.3% done\n", "30.3% done\n", "40.4% done\n", "50.5% done\n", "60.6% done\n", "70.7% done\n", "80.7% done\n", "90.8% done\n", "100.0% done\n"]}], "source": ["avg_attns = {\n", "    k: np.zeros((12, 12)) for k in [\n", "      \"self\", \"right\", \"left\", \"sep\", \"sep_sep\", \"rest_sep\",\n", "      \"cls\", \"punct\"]\n", "}\n", "\n", "print(\"Computing token stats\")\n", "for tokens, attns in data_iterator():\n", "  n_tokens = attns.shape[-1]\n", "\n", "  # create masks indicating where particular tokens are\n", "  seps, clss, puncts = (np.zeros(n_tokens) for _ in range(3))\n", "  for position, token in enumerate(tokens):\n", "    if token == \"[SEP]\":\n", "      seps[position] = 1\n", "    if token == \"[CLS]\":\n", "      clss[position] = 1\n", "    if token == \".\" or token == \",\":\n", "      puncts[position] = 1\n", "\n", "  # create masks indicating which positions are relevant for each key\n", "  sep_seps = np.ones((n_tokens, n_tokens))\n", "  sep_seps *= seps[np.newaxis]\n", "  sep_seps *= seps[:, np.newaxis]\n", "\n", "  rest_seps = np.ones((n_tokens, n_tokens))\n", "  rest_seps *= (np.ones(n_tokens) - seps)[:, np.newaxis]\n", "  rest_seps *= seps[np.newaxis]\n", "\n", "  selectors = {\n", "      \"self\": np.eye(n_tokens, n_tokens),\n", "      \"right\": np.eye(n_tokens, n_tokens, 1),\n", "      \"left\": np.eye(n_tokens, n_tokens, -1),\n", "      \"sep\": np.tile(seps[np.newaxis], [n_tokens, 1]),\n", "      \"sep_sep\": sep_seps,\n", "      \"rest_sep\": rest_seps,\n", "      \"cls\": np.tile(clss[np.newaxis], [n_tokens, 1]),\n", "      \"punct\": np.tile(puncts[np.newaxis], [n_tokens, 1]),\n", "  }\n", "\n", "  # get the average attention for each token type\n", "  for key, selector in selectors.items():\n", "    if key == \"sep_sep\":\n", "      denom = 2\n", "    elif key == \"rest_sep\":\n", "      denom = n_tokens - 2\n", "    else:\n", "      denom = n_tokens\n", "    avg_attns[key] += (\n", "        (attns * selector[np.newaxis, np.newaxis]).sum(-1).sum(-1) /\n", "        (n_docs * denom))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Computing Attention Head Entropies (Section 3.3)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing entropy stats\n", "0.1% done\n", "10.2% done\n", "20.3% done\n", "30.3% done\n", "40.4% done\n", "50.5% done\n", "60.6% done\n", "70.7% done\n", "80.7% done\n", "90.8% done\n", "100.0% done\n"]}], "source": ["uniform_attn_entropy = 0  # entropy of uniform attention\n", "entropies = np.zeros((12, 12))  # entropy of attention heads\n", "entropies_cls = np.zeros((12, 12))  # entropy of attention from [CLS]\n", "\n", "print(\"Computing entropy stats\")\n", "for tokens, attns in data_iterator():\n", "  attns = 0.9999 * attns + (0.0001 / attns.shape[-1])  # smooth to avoid NaNs\n", "  uniform_attn_entropy -= np.log(1.0 / attns.shape[-1])\n", "  entropies -= (attns * np.log(attns)).sum(-1).mean(-1)\n", "  entropies_cls -= (attns * np.log(attns))[:, :, 0].sum(-1)\n", "\n", "uniform_attn_entropy /= n_docs\n", "entropies /= n_docs\n", "entropies_cls /= n_docs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plotting Utilities"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Pretty colors\n", "BLACK = \"k\"\n", "GREEN = \"#59d98e\"\n", "SEA = \"#159d82\"\n", "BLUE = \"#3498db\"\n", "PURPLE = \"#9b59b6\"\n", "GREY = \"#95a5a6\"\n", "RED = \"#e74c3c\"\n", "ORANGE = \"#f39c12\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_data_points(head_data):\n", "  xs, ys, avgs = [], [], []\n", "  for layer in range(12):\n", "    for head in range(12):\n", "      ys.append(head_data[layer, head])\n", "      xs.append(1 + layer)\n", "    avgs.append(head_data[layer].mean())\n", "  return xs, ys, avgs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Examples of Attention Head Behavior (Figure 1)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************************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\n", "text/plain": ["<Figure size 864x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["width = 3\n", "example_sep = 3\n", "word_height = 1\n", "pad = 0.1\n", "\n", "\n", "def plot_attn(example, heads):\n", "  \"\"\"Plots attention maps for the given example and attention heads.\"\"\"\n", "\n", "  for ei, (layer, head) in enumerate(heads):\n", "    yoffset = 1\n", "    xoffset = ei * width * example_sep\n", "\n", "    attn = example[\"attns\"][layer][head][-15:, -15:]\n", "    attn = np.array(attn)\n", "    attn /= attn.sum(axis=-1, keepdims=True)\n", "    words = example[\"tokens\"][-15:]\n", "    words[0] = \"...\"\n", "    n_words = len(words)\n", "\n", "    for position, word in enumerate(words):\n", "      plt.text(xoffset + 0, yoffset - position * word_height, word,\n", "               ha=\"right\", va=\"center\")\n", "      plt.text(xoffset + width, yoffset - position * word_height, word,\n", "               ha=\"left\", va=\"center\")\n", "    for i in range(1, n_words):\n", "      for j in range(1, n_words):\n", "        plt.plot([xoffset + pad, xoffset + width - pad],\n", "                 [yoffset - word_height * i, yoffset - word_height * j],\n", "                 color=\"blue\", linewidth=1, alpha=attn[i, j])\n", "\n", "plt.figure(figsize=(12, 4))\n", "plt.axis(\"off\")\n", "plot_attn(data[581], [(0, 0), (2, 0), (7, 6), (10, 5)])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Avg. Attention Plots (Figure 2, Sections 3.1 and 3.2)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x720 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["def add_line(key, ax, color, label, plot_avgs=True):\n", "  xs, ys, avgs = get_data_points(avg_attns[key])\n", "  ax.scatter(xs, ys, s=12, label=label, color=color)\n", "  if plot_avgs:\n", "    ax.plot(1 + np.arange(len(avgs)), avgs, color=color)\n", "  ax.legend(loc=\"best\")\n", "  ax.set_xlabel(\"Layer\")\n", "  ax.set_ylabel(\"Avg. Attention\")\n", "\n", "\n", "plt.figure(figsize=(5, 10))\n", "ax = plt.subplot(3, 1, 1)\n", "for key, color, label in [\n", "    (\"cls\", RED, \"[CLS]\"),\n", "    (\"sep\", BLUE, \"[SEP]\"),\n", "    (\"punct\", PURPLE, \". or ,\"),\n", "]:\n", "  add_line(key, ax, color, label)\n", "\n", "ax = plt.subplot(3, 1, 2)\n", "for key, color, label in [\n", "    (\"rest_sep\", BLUE, \"other -> [SEP]\"),\n", "    (\"sep_sep\", GREEN, \"[SEP] -> [SEP]\"),\n", "]:\n", "  add_line(key, ax, color, label)\n", "\n", "ax = plt.subplot(3, 1, 3)\n", "for key, color, label in [\n", "    (\"left\", RED, \"next token\"),\n", "    (\"right\", BLUE, \"prev token\"),\n", "    (\"self\", PURPLE, \"current token\"),\n", "]:\n", "  add_line(key, ax, color, label, plot_avgs=False)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Entropy Plots (Section 3.3, Figure 4)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAUIAAAE+CAYAAAD4TRkmAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi40LCBodHRwOi8vbWF0cGxvdGxpYi5vcmcv7US4rQAAIABJREFUeJzsnXd8FGX6wL+zu0k2vdGR0HmpooKiKIKelVNPTz04PRtWBNvBgXL4k1NU8FBPPeBQwcOKBURQLFhOsWADFSS8SpUO6X2zuzO/P2Y3kIQkk2RmswPz/egnOzsz7zzMzjzv877vUxRN03BwcHA4mnG1tAAODg4OLY2jCB0cHI56HEXo4OBw1OMoQgcHh6MeRxE6ODgc9TiK0MHB4ajHUYQODg5HPR6rGhZCrAGKQptbpZTXWXUtBwcHh+bQoCIUQqQDpwKZwH5glZSypIFzvIAipRxhhpAODg4OVqLUFVkihGgNzAB6AxLYA6QDxwPrgHullPvqOHcI8DywHV3ZTpFSrq5LCFVVtWAweiJc3G6FaJLHKI7ckcWRO7I0Re6YGHcO0Lqh4+qzCO8DZkopf6m5QwjRB/g/YFwd55YBs4BngZ7Au0IIIaUMNCRQdKAA9ntQHLkjjSN3ZGmS3NsNtWxFrLEQIg5wSSnLQ9vfAJdKKXcc7ni/P6gVFJSZLkdTSUtLIJrkMYojd2Rx5I4sTZG7devk74HBDR1nZI7wrNBxbuApYKqU8uUGThsDDABuFUJ0AFLQh9YODg4OUYcR95kHgV+B24ChwC0GzpkPpAkhPgdeBcbYZ1js4OBwtGHEfaYM2AcEpJR7hRANjqWllJXAFc0Vrj4uvnhkre8uuugSxoy5kbKyMq644rJa+0ePvpLRo68kNzeX66+/qtb+a6+9nosvvpQdO3Zw1VW1948dexvnnns+mzb9ysSJd9Taf9ddf2P48DNYt+4n7r337lr7p0y5j5NOGsI333zNQw/9o9b+Bx6YwYABx/Lpp5/w+OP/rLV/1qwn6NGjJ++//y5z5z5Va/8LL7xAcnImS5cu5r//nV9r//z5L5CZmcmiRS+xaNFLtfa//PIbJCQksGDBMyxb9mat/UuXrgBg9uwnWbnyvWr7vF4vixYtAeDRR2eyatWn1fanp2fw3HMvAjB9+jS+++6bqn0ej4vWrdsxd+6zAEydOpn169dVO7979x48+uiTAEyYcDubN2+qtr9//wFMnz4TgLFjb2DPnt3V9g8efBJTp04D4Lrr/kJ+fl61/cOGDWfChMkAjB79RyoqKqrtP/vs8xg37nbg4LPn8bgIBFTAvGdv166djBt3U639Zj57jzzyQJXcYZr77M2e/TQdOx5j+bNnFUYswmLgPeA1IcQ4dBcaBwcHhyOGBhdLhBDJQCcp5QYhRH8gR0q510whnMUSc3DkjiyO3JHFysWSOi1CIUQ7IUQvYCXgD30OAssaJYkFqJpGfkUQJ7u2g4ODGdQ3R3gycAcggKdD36nA+1YLVR+qpnH3qnyyc/30yYxhxrB0XIrSkiI5ODjYnDotQinlUinlGcCVUsozQv//Tkp5bwTlq0WhT2VDjp+gBhty/BT61IZPcogqcnNzmDVrBgCffvoJI0eex+uvL4rY9RcvfhUAn8/H8uVLG33+oeetWLGczz//tIEzHKIdI4sleUKIeUKIBUKI54QQLWoRpsQqxLn1z3FufdvBXmRmtmLiRH1l84svPmPSpMlcfvnoiF1/4cIFAOTl5TZJER563siRF3LaacNNlc8h8hhxn5kLPAJchh5jHGupRA1Q4FMpD+qfy4P6dma8k02spVmxYjnbt29j7Njb8Pl8XHnlZbzxxnLGj7+Jnj0FW7ZspqyshAcemImmadx33xSuvvo6Vq/+kl9/lUyYcDe7d+/itddeISYmhk6dspg06e988MG7vPPOMlRV5frrb+af/3yI/v2PZceO3xg06ERKS0vIzv6ZrKzO3HvvA9Vk+uSTD1my5HUCgQCKovDQQ7N4663FFBUVMmvWDILBANu2beW5557h8sv/zIwZ91NYWAjAnXf+je7dezB69CUMGDCQ337bTkZGBtOnP8Lzzy9g27atzJ07h7IyH5mZmVx88WU89dTj/PTTD4DubvOnP/2ZBx+cRkxMDHv37iE3N4cpU6YhRO+I/z4O9WNEg+RIKV8BiqSU04BjrBWpfhSUercdoo8+ffrxxBNzGDx4CCtXHhxQnHbacIYMOYUJEybQqVMW8+fP48kn5zJ37nySkpJ4663FACQnJzN37nwGDz6JvXv3cOONtzJnzrO88carXHLJ5Tz99EJ++ulHiouLq113x47f+Oc/n2Du3Pl06dKVb775imuuuZ6UlFQmTrybq68eQ5cuXbnuuht5/vkFDBp0Ek89NY9Jk/7OrFkPA7B79y5uuOEW5s17joKCfLKzN1SdN3bsrVXX+uKLVezZs5unn/4vc+fOZ+XK96p8Hdu1a89jj/2bSy8dxbJlS6y+3Q5NwIhFqAoh+gEJQggBZFgsU72ke130z4xhQ56fvhkxpHsdazD6qL6a36uXAKBt27bk5uYe9ozdu3fRtWs3EhISARg48AS+/XY1ffv2Jyurc9VxKSmptGvXDoD4+Hi6du0GQGJiEpWVPiC56tj09AymT7+PhIQEtm/fRv/+x9Yp8ZYtm1iz5js++ugDAIqL9VSaqalptG2rX69Nm7aha9Rm+/atDBx4HIqi4PF46NdvANu2bQGgZ09Rdf66dT/WKYNDy2FEEf4V6Ac8CbwMLLBUogZQFIXpp6WyMTdAv0wPirNiHBXExsaSm5sDgJQbq+0z8hu1b9+Rbdu2Ul5eTnx8PD/8sIZOnbJC5x/s7Iz+3iUlJcyfP4/Fi98G4K67xlW5W4X/KooLTdMX2zp37sI55/TlnHPOIz8/r2oO8HDXO/S8MJ07d2XFimWMGnUlgUCA9et/4vzzLwC+dJ5RG9CgIpRS/gz8HNocZK04DRNQVa54J5eygEaCR2HRBa3wuByrsKUZMmQoS5cuZuzY6xGiD4mJiY06Py0tjTFjbub2229GUVwcc0wnbrllfJWF1lgSExMZMGAgt9xyHW63h+TkZHJyDgDQpUtX7r//XiZPnorfH2DOnCe5+uoxzJjxAMuWLaGsrJQxY2qHuYVJT0/H7w/w2GOPEp5dOvXUYaxd+z0333wdfr+fM888y5kLtBFGIkumAJPQY44BkFJ2MFOIxkSWbCv0c+tHB+NE5/wugy6pMWaKc1R53kcDjtyR5WiS27Q0XMAooIOUMiruXFaymwSPUmURZiW7W1okBwcHm2NEEW4Fyq0WxCgul4tFF7RiZ3GQrGQ3LmdY7ODg0EyMKMJYYJ0QYh2h5UAppaUpthrCpSikxrmcSWgHBwdTMKIIZzalYSFEG+B74GxZcxmxGaiaxuTP8sjODdAn08PM0zNMjTUOqCqb8ypJR3WsTQeHo4Q6FaEQ4jZgrpSyViClEMID3CqlfLKOc2OAeVgwpM6vCPJzrp7s+ufcAPkVQTLjzSnPHFBVRr+d46xIOzgcZdSnQdYC7wkhfgZ+Qs9SnYaelaYvUDvF8kFmAf8B7jFJziqsjCzZWRykLKCvopcFNHYWB+mS6ihCB4cjnToVoZTyc+AsIcTZwAjgJPTs1G8Ct0kpD+t3I4S4FjggpXxfCGFIEbrdCmlpCYYETk3VOK5tMev2+xjQJo6u7ZJMmyvsnxzEpeShauBSoP8xSXjc9lmVdrtdhu9jNOHIHVkcuWtjejlPIcRn6IsqGnAc8AtwUX1ZrRuboVrVNAp9KmkmL5jkVwT5y4ocNPQKqi+ObEW61z6K8GjyD4sGHLkjS0v7ETYKKeXp4c9CiP8Bt5id2t+lKJYoqLQ4F/1axVQlfU2Ls8+wWNU08sqDKJrmrKY7ODQS0xVhJLDKIlQUhRnD0sHrRamosI1CcbJ2O0QDVr2XkcBIgfe3gWeB5VLKYGMal1KOaKJcdWL1S+9SFNLi3RT47PNDFvpUsnP1rN3ZuXrWbjsN6R3sj907YyNjv4nohd2/F0LMFEL0tFimejncS3+0kxbnok9mDG4F2w3pHY4M7P5eNvjGSCk3SiknAWcBnYD1QoiVQohTLJfuMFj90ofn2uxUIS88pH9rdBYzh6XbbljiYH/s3hkbGRqfD1wL9AFeAO4EYoAVwEArhTsc4ZfeirkIq817O8+hODjUh5XvJVi/GGhkseQvwJyaESZCiGmmS2OQ7/f5aJ/oNr3XsXKuzUola/f5GYcjA6u8OSLxfBvRJDcCJwgh5gohxofC55BSvmmqJAYp86tM+7KQm1bmcfvHuagmDmGtNO+tLENq9fxMQFXZVuhHVe017+NwZBCJ+UcjFuHLwEbgPeBU4Dl0K7FF8AU1FHRv7c2FQRb/UsplvRJNMZcVReGh09IoJJZ0Kk01wVNiFbweKAuA12NuGVK9xKmeozHOrZjathN/7WAUq6Z+rHy+wxhRhJlSyrtDn98SQqwyXYpGkBbnom+rGDbk+EmIUXju51K2FAa5/YRk4j3Ne0FVTWPKqgKy8/z0yYhhxunmmeAFPo0yPVcEZQF9OzPelKYpqtSoCMVIVwQ0iio10r3mtO3EXzsYwcrhq5XPdxgjT/TPQohTAYQQA4DtQogYIUSL1DcOT8q+MLIVr/y+Fdf0TWLVzgru/CSP7UWBZrWdX6GyPmSCr8/1k19hngmu1KjsVnO7OehDeg8uBfpmekwd0oczggNORnCHOrFy+Bo2ftwK9G1lzYq0EYtwGHCuEMKPvloMevywBnQzXSIDHDopO6p3Ir0zY3jkm0Lu/CSX245P4cyspplaViqrdK+bfpmeqjyKZk4qBzWNLQUBVA02FwQIahoek3pjJyN45LGjd0F4ft2K8NRIRHwZqWLXD6oSreZIKaNuxnxg61ieOjODGd8WMuu7In7O8XPzwGRi3Y27YalxLuLdUB6EeLe+bRaKojDz9AxLHvCdxUHKQzE/5UFMH756XC5nOBwh7OoBYLX7jNURXw0+3UKIEUKILcD7wJZQWq6oIyPezcOnpXN5rwTe3VbOhP/lsae0cUPlokoNX0ih+IL6th3ISnYTHzIw4904w1cbEwkPgM15lZZ4AIRHanaxYg/FSDc/HThNSnk8+qrxdGtFajpul8J1/ZO575Q09pUFuf3jPL7cXWH4fCvnIsI9/dXv5jB5Vb6pbj8oCt3S9DnC7mkesOGD6KBjpQtX2APgqqW7+NPbOQRMVoaqppFfYa+orDBG5giDUsrdAFLKXUII45qlhRjSPo4nz8zkoa8LmL66kEt7+rmmXxIeV/0Kwsq5CCudtQt9Khvz9DnC7LyAk3TBxlg5xLTSA8CuQ/owRu5CkRDiNiHEwFAdk7wGz4gC2iW6eXR4Br/vFs/iX8u4Z1U+OeUNJ89xKQoZ8eab91b29JGIv7ZrT29HrBpiHpPkqnrhXaFts7B70gWjIXZTgQeBDcAYSyUykRi3wrjjUuiXGcOTa4q57eNcJp2YyvFt4iIuS9hZO7z6apc8ipGIv7YqhtSOq69WUuynyg9CC22nmzRwsHLVOBIYUYRzW7qOcXMZ0SmebqkxPPR1AVM/L+DKPomM7p0YUdNd1TTuWZVvWRlSq1bVnPjrIwcrM7Bb2dFHAiOKME4IcSy676AKIKWsrO8EIYQbeAYQ6J3PLVLK9c2UtVlkpXj41xmZPLW2iBezS8nO9TPxxFRTXWTqw8oypFZiZU9v9bypXZPV2jEDu6ppTPm8wLYdj5GnuhfwFpANSPS444a4EEBKeSoHh9UtjtejMHFwCrcdn8yPOZXc9nEu2bnVdbpV+QitLENqJeGX5/nzW5me69DO86ZWYal3AdbNgR8Nc4RjpJTfhjeEECMaOkFKuTSU4h+gM1DQNPHMR1EUzu+aQM+0GB76upBJn+Vz/YBk/tA9Hg0sG06lxikkeJSq5AWpcfZQhGBdeiUrk1xY7eBrFXa1ZI/YOUIhxDD0Qu53CSEeC33tAsYD/RtqWEoZEEIsBC4BLqvv2MbUNTaLwWmwsEMS01fl8PRPxfxSFGTsoIxqDyFeL2nx5jyEeeVBfEG9d/cFNZT4eNPaDmO3erWqpjF+xV7W7a9gQBsv/x7ZzvThVIaprVXHivudqmkMaFNSdU+6tDWvbncYq56TORfEs70gQNc0jyWhmFY+3/VZhPlAOyAOaB/6TgUmGW1cSnmNEGIy8LUQoq+UsvRwxwWDWovVWb17UBK9Ulw893MJvxzw0SXVw7bCAH0yY1AqKkxbfFA0rVqPaWbbYexWrza/Isi6/RUENVi3v4Lt+0psYf2Esep+Tx+aQqEvibQ4F4WF5aa3b4XckfAuaMrcZuvWyYaOq1MRhhY31gshngk7VBtFCHEVcIyU8mGgDF2BRuWkgaIoXNorEZERw8xvCskpD3J2tySGtfOgAma9lnYdqlmJ3YdTDgex2rtg8qd5ZOcF6JPhYeZwcz0uwNgc4VlCiHvQLUMF0KSUDWWdWQI8J4T4DD1jzZ1SSvO7NhPp3yqWp87M5Nn1xXy6vZT3Nmukxbk4tWMcp3f0hkLvmnfzrZprsyt2rSNtJXb12zTaqamaRqlfo8SvUlKp/y31a5RUqhSH/pb4NUoP2V/oU9lXpttRP+cFyC0P0jrBXI8LI61NRl8F3mG00dAQ+E9NFaqlSPO6mDg4FW+Slw9lAZ/trODD7eW8s6WcDK+L0zrGMewYL30yYmzlGhDN2LGOtJXY1W+zxK9xQbd4+mbEEFDhiTVFIYUXVm66givza/Umt3MrkBSrkBTjIinGRXKMi6QYpUoRAhT5WkYRbpFSbjL1qlGO1+PitI5eTuvopTyg8s2eSlbtquDdreUs21xOq3h9/+nHeBHpHsM9qxX+YUU+lc2FfrYXBendDronaMQ0EFPdWJwIjchhF7/NgKoh8/2s3VfJ9/sq+TXfXzX3FeuCpFhdgSXGuMiId5GV4iE5pOASY5Sq/eG/yTEuEmMVvG6l1jN2oNTPNe8fjOxNiW2ZxKxlQoh3gR8IRehIKaeYLkmUEu9xMbyTl+GdvJT5Vb7e4+OznRW8vbmMpZvKaJPgYlhIKfZIq1spNrc31jSN3HKVzYUBNhX42VwQYHOBnwPlh0y9/lRMvEfhxHaxnNLey+B2sSTGNL98gROhETmsnEturpLdUxJgzf5K1uyr5McDlZQFNFxAr4wYRvVO4Os9PrYVBumVYe5z4q7RsdfcNgMjinCF6Ve1KQkxLs7IiueMrHhKKlVWh5Ti0k1lLP61jPaJboZ1jOP0Y7x0Ta2uFBvTG6uaxu6SIJsLA2wp8LMppPTC+REVoGOSm76ZsXRP89At1cMLG0r4NT+A1w0/Hajks50+PAoc2zqWUzrEMaR9HK2a4K5jV782sK8la6XfZmPmZEv9Kj8eqGTtvkrW7K9kT6metKRNgovhx3g5vm0sA1vHkhzrIr8iyGuyDBXzn5PUOBdeF1So4HWZmzA5TH1+hMdLKddKKRfW+P4PpkthQ5JiXZzVOZ6zOsdTXKny5W4fq3ZW8MavZbz2Sxkdk9ycfoyXYR29dEnV64j0yYhhQ6gwVLg39qsavxUF2FwQYEuhrvS2FgYoD6VL8ijQOcXDye3j6J4WQ/c0D11TPdUKVeVXBNlUEEBFTyb73/My2Vem8tVuH1/t8TH7h2Jm/1BMr3QPp7T3ckqHODoZjAetS+5ox7FkG09Q0/g1P8CafT7W7q8kO8+PqkG8R+HYVjFc3COBE9rG0iGx9rNj5ZC+wKcSLh9UoerbmfHmPof1WYSPAmcCCCFWSinDmanvQA+5cwiRHOvi3C7xnNslnkKfyhe7Kli1y8erG0t5ZWMpWcnuqvlGTYOciiBPrCliS2GAbUUBAqEfOd6j0DXVw1lZ3iqll5XiaXDOr+ZDmOF1kxnvoW9mLGP6J7GjOMhXe3x8tbuChRtKWLihhA6Jbk7pEMfJHeLonVH3irgGaGjo/+kT3XZQJ44le/h2a3YOB8pV1u7zsWZ/JT/sr6TEr5fL7ZHm4fJeiZzQNpbeGTENPoNWDulrRhlakQ2uPkV46L/EU8f3DjVIjXMxslsCI7slkFcR5MtdPj7bVcErG0urVsv2lqqU+n30SNN72e5pMXRP9dAhyd0kq6W+IY+iKGSl6Ap1lEgktzzI6j0+Vu/28VZoSJ8apzCkXRyndPByXJtY4tzVh/Qb83Rrc6ONkr5a7aNolRuKlZZsoU9lQ46+qPFzjp8bP8itGu5mel0M7RDH8W3jOK51bMSSkRijpuYzXxPWpwg1A58d6iHD6+aC7glc0D2BnLIA93xewJ6SID3TPTw6PL1FKsJlxrv5fbcEft8tgVK/ynf7Klm9u4LPd/n4YHsFcW4Y1DaOUzrEcVK7ONLiXPTO0Kvv9c4wt1SolVhpoaiaxuTP8ixJqWaFJVtcqS/yfbGrotrL2zHJzYXd4zmhjfGpkrpQNY1Jn+aSnRekT4abR4ZnmnZPXIqr3m0zqE8RuoQQMejxxdU+my5FFGFVT98qwcO8szMjNuQx8hAmxuiT3sOP8eJXNdYdqOSr3T5W7/Hx5W4fLgX6Z8aQV66GhsSKbYbGYN2ig5Up1cyyZPPK9emQL3b7+OlAJaoGreNdXNAtnn7tExiSoRAXY969yS0PsCFPty435AXJLQ/QOiGmgbOMEYmEJfX9ep3R026B/uzL0N8j1iK0eoLdqhfTDCsixqVwQts4Tmgbx9jjNDblB/hqj4/Pd1WwJ+TMuj7Xz87iAFkp5jzgYG2Gaquwcs6qOZbsntIAX+7SO7HsPD+gW32X9UxgaEcv3VPd3LOqgBVbc+mTEcOM0010qK6svd3apPwIenXJgwlLiio10r3mtB2mvljjruZeKvqx6wS72fNhLkWhV0YMvTJi+EufBC5bdoBwerm7/pfPxT0SuKRHAknNdGy168puTRnNltloh6lpGtuLgny5u4Ivd/vYUqhbqT3SPFzVN5FTO3irDXlzy4Osz9UV5PpcP/kVKpkmZUDqnOImwQNlAUjw6NtmEYmY9OhPkRxB7JoEwMqY3QKfWqUEAfq3iuGVjaUs21zGJT0SuLhHAglNdNq2a8eT7nXRPzOG7JBLUbrX/IWYuixCVdP4JT/AV7sr+GK3j90lQRSgb2YMNw5IYmgHL20TD38PlRqDuZrbzUJR6JLiJjsvSJcUt6klZSMRk+4owkNwkgDUpmYm7TuOT6HAp/JSdikvZpeydFMZl/ZM5MLu8Y1WiLbueE63sFjWZ/lsyPPTNzR81TTdgvtyl2755VaouBUY2DqWP/ZI4OQOcWQY6EDSvW76pLvIzlfpk+4ytdMp9KnI/CAaIPODpndqVsek21IRWhkxYMckAFYOMcPWT/jFTPe6yIh3c+8pafya7+elbN0v8c1NpVzaK5ELuyXg9Ri7tp07Hquek/wKtdrw9ZFvC/lhfyVFlVrViv7QDnGc2C6O5EZOTQQ1je3FuhW4vVgjqGl4TLrndvUuCNOgIhRCXA00Ng2XZdh1XslKrBxihq2fw3U8PdNjmDY0HZnn58XsEp5bX8Kbv5ZxWa8ERnY1rhAdDvJbkb/a9rd7Kzm5va78BrWNa9Y9tbLAe9irQP/PXt4FYFEaLiux67ySlVg9xGxo8l5kxPDAqelsyK3kxexSnl1XwuJfyviTSOT8rvHEug//SkQi954dYo01TeP7fZUs2VTGD/srq1wzspJcPPm7TGLd5vyeWcnuam4oWcnmDo3DIXnZefZ7L01PwxXyN1wAdEG3IqdLKZc1Tbza2HVeyUqsLILUGPpmxvLQabGsy6nkxQ0lzPupmDd+KWWUSOTcLvHE1FCIds29Zxb+oMYnOyp4c1MZ24sCZHpdXNsvka93+5AFAVLi3HhMzLTicrlYdEGrqufETIf+lFiFOLeuZOPcCimx0XWvG8KKNFx/AXKllFcJITJC55mmCJ2U97WJtpqyA1rFMmNYOj8e0IfMc34s5vVfShndO5GzOsdXxa2GEzpkW5DQ4dBwsg050WWhFFeqrNhSzrLNZeT7VLqmepgwKIXTO3kpqVR5YUOpZZaVx+Wie1osBQUB09oE3devIjTsrghY4+tnJVak4XodeCP0WQHMveM4Ke9rEo3TBYqicFybWAa2Tmft/kpe2FDKU2uLeU2W8ufeSZyZ5cWlhBI5WJDQISVWwRsaBno90WGh7CkNsPTXMj7YXo4vCIPaxHJJzwSObxNb1aHbNUY6Lc5F31b2HakZUYQvATejl/b8BZhb38FSyhIAIUQyukKc2tAFWqKcZ31YVTZQ1TQKKlTSveZaspEoAdkczkxP5IxeaazeWc4za/P515oiXv+1jD/1S2FDbgANyM4NmF4+9VALxezyqQFVZWuBn84p3gaHmOv3V/DK+kI+3V6GS4FzuiUxun8qPTJiD3v8fy5MsOQ5OVg+db8l5VOtkjuMleU8Fa2B+CAhxHz0Au2rgOFAppTy6gbO6QS8CcyRUi5oSAi/P6hFUxnKo6ncodG2zZqK0DSNb/ZW8sKGErYUBqoWBtwKnNnJa5ofrgZ8/JteKjTWBbN/l0GHJONlFeojoKqMfjunatFh0QWt8NRQhkFN4+vdPpZsKmNDrp+kGIXzu8ZzUfcE06I5Gkt+RZCr380hqOn3+/nzW7X4yKExNOW9bN06+XtgcEPHGbEIe0opTw99XiqE+LK+g4UQbYEPgPFSyo8MtN9o7LIaeChWD1+t8mszW4ErisKQ9nGc2C6WD7eX8681xQAENfh+fyUNrQ0YjetVQ20CVKpw48o8EkL5Hrum6lm9u6XG0DnVUy3tmBHqc0OpCGh8uL2cpZvK2F0apF2Cm1sGJnN2Z2+1ZLotgb6goYfBxbmJiumCaMGIIvQKIRKklGVCiHgaLvU7BUgH7hVC3Bv67nyzynnaYTXwcNh1tdsqBe5SFM7uHM+Hv1VU3ZOZw9JN69iCqsqotw9UvfQ3DEhiW1GQrYUBPvqtgrdDiswFdEx2h5RjTJWSzKhneHdMkgsXurJ1hbbzK4Is31zOiq1lFFVq9Er3cE+/VIZ2iLOkxkZTKPCplIVm7MsC1mR6titGFOETwI9CiPXo84TT6jtYSnkNN2k2AAAgAElEQVQHehZrS4jGhQEj2HW120oFrigK009NZafPQ+e4gKn3JK/i4EvvC8JJ7eL4fTf9cVc1jX2lQbYU6mURthQG2Jjn57OdvqrzU2IVuqXF0O0QC7JTsgePS6HYfzAFkwo89n0xX+z2EVDh5PZx/LFnAn0zY6LuN64ZLllz+2imQUUopXwp5D7TDdgqpcy1Xqy6satlBfZc7bZSgQdUlSveya13rq2pFPmCtbbDtXBdikL7JA/tkzyc2vHgMSWVKluLAmwpCLC10M+WwgDLN5fhDyWd8LggK9lDlxQ3HhdV33+528fZneO5uEcCxyRHb9Sq1cki7Ex9xZumSimnCyFe4ZAchEIIpJRXRES6w2BXy8rOWKXArQz5Sotz17t9OJJiXQxoFcuAVgdXc4Oqxs6SIFsK/br1WBDg+32VVUpQAZ44I4POqeblaAyoKjuLg2Qlu011erYyWYTdqa/7Wh76+59ICNIY7GhZ2RmrFqesDPnKiHfTL9NDdl6AvhkeMpq4Uut2KXRO8dA5xcMZnfTvNE1jwqd5/JIXoG+rGLJSzLMCjaxINwc7JhWJBPX9guuFELHo832j0Ds/N/AOoep2Dkc+h0sLZVotCgtDvhRF4eFh6VWWlZkKXFEUZg3PsMSystJKdqib+u7wGPT0/OeH/kpgHfBbBOSqF1XTyK8I0pAPpEPzCaeFUrWDWY3NxONy0T0j1vRCVuGww9s+zuPuzwt0P0sTcSkKGfHmKljQreSw8RrvxlQr2aFu6kvV/wzwjBBijBGn6EhhV/cZu2JpVmMLsat3AYpCtzR9SN89zWNqpmeHujEyubFSCDEJqAqhllLeb51I9WPbB9ympHtDc22h0pV2udd29S7Ir1CrKuStzw2YWlfEoW6MKMLXgQ+JknyEdn3A7YqiKMw8PcOyVXqrkgDY1btA09TDbDuK0GqMKMJiKWWDiRMihV0fcKuxY1lMu5ZPtZJIFDN3qI0RRbheCDEaWMvBfIS/WCpVA9jxAbcSKxWKlW3beZrDqo7ncDViHKzHiCI8LvR/GA3HfSaqsFKhWNm2lYlZwTr/x7BLUVhuM12K6qsR42AdRkLszhBCpKKn3t8czjfoED1YOW9qZdsa1iVmtdKSrVlpzuwFDWfEE3mMVLG7FD25qgd4TQihSSmnWy6Zg2GsLItp5ZxsoU9lY14AFdiYF7CNJWtXlyKHujHSvf8VOBnIAaYDl1gqkUOTsMrBN9x2utf8tsMFfwDTC/6ELVm3gumWbLrXTZ80vb0+aeYWSndoGYzMEQallL6QJagJIUotl6oFsePqq12xsuBPuLKfFSF2flVFFuhuLrJAxa+qxLodZWhnjHSTnwshXgaOEUL8B/jWYplajPC80h8W/cbkVfmmh2U5VCdc8MetQN9W5lptVobYbczVh/Og5yPcmGt6fTKHCGNksWSKEOI8dPeZjVLK5Q2dAyCEGALMlFKOaJ6IkcPO7hx2xMp6zFb+lv0yPdUyVPfLjN4chA7GqC8foRvdpX0RevaZjwG3EOJjKWW97jOhkLyrAFsNo52olchiZT1mK39Lt9vNkj+0qsqs7XaGxbanvq5sDHr9kXbomWdA7wQ/N9DuZuCPwAvNki7CWGmhONTG0pVdiyOQYt1uTugQb3q1Q4eWwUj2mZuklE+HvxdCpDXUqJRysRCii1EhoqWu8cG6rxWW1H21GivrvlpBJOoxZ5jaWnXsdr/DOHLXpr6hcTsgBRgjhPgE3dfVBTwPnGSmEMGgFhU9a35FkHX79Vq46/ZXsH1fia3mCK2ox2w104emgLcNSkUFhYWmFDqMGHa833B0yd26dbKh4+obGp+Mnp1aAPPQFaEKvN8oSWxEWpyL3hl6yqneGR5bzRHa1e3HSR3vEA3UNzReil7QfaSUckX4eyHEyRGRrAXQQ7wUFEX/a2bIl5U4yWodHJqHEfeZFUKIOOAKYDwQB/Q3cN42dKvSNhT6VLLzQpP3efZxn3Hcfhwcmke9ijC04DGOg8WbRkkpv4yAXC2CXd1n7Cq3g0O0UN9iyTIgFd0Fpj/w6pGsBMHa5AVWYle5HRyihYaGxgEgHn21+KiIN7Pr5L1d5T4SCQYD5OcfIBCobGlRDsu+fYotK0DWJ7fHE0t6emvc7qZF+dS3WHKREKITumP110BSKNTuAymluTUdHRyOIPLzD+D1JpCY2C4qrXO320UwaL9XuC65NU2jtLSI/PwDtGrVvklt1zuZJKXcIaX8B9AbuB64AdjWpCs5OBwlBAKVJCamRKUSPBJRFIXExJRmWeCG7EgppQa8B7wnhGjT5Ks5OBwlOEowsjT3fjd6QC2l3N+sKzo4OFjKmjXf8X//dw9dunQFoLKykokT76ZXr948+OA0fvllI8nJKVXHn3feSC644GJGjDiZ/v2PBfR5zmBQZdq0B9m1awcLFy4AYP36n6qOGT/+Lnr37lPVzkUXncuyZQfjLVav/pKPPvqAv/99WpP/LXPnPkXnzl0YOfLCJrdhBCd/kIPDEcigQYP5xz8eBuCbb1bz7LP/4ZFH/gXAuHF3cNJJp9Q6JyUllX//uyqtAEuXLmbRohf5618nc+KJukvwRRedW+2YI4VGK0IhxDFSyp1WCOPgcLRiVcU9gOLiItLS0ht93r59e6tZjs3h448/5NVXX8LlcnHssccxduxt7N+/j1mzZlBZ6SM3N4cbb7yV008fwf/+9xELF84nLS0dv99P585dyM/PZ9q0ewgGVSorK/nb3+6hZ09himzQNIvwQeAa0yRwcDjKsSJE8vvvv2P8+Jvw+/1s2vQLDz/8aNW+2bOf4Pnnn6vavuuuSXTv3oOiokLGj7+JsrJSioqKGD78DK6//hbD1wyfH6a4uIhevXpTVFTIggXzePbZF/B6vTzwwL18++1qQGH06Cs54YTBrFv3I/Pnz2Po0NN46qnHWbDgRVJSUvnb3+4AIDv7Z1JS0pg6dRpbt26lvNzcBB1NmSN0lKCDg4lYESJ56ND4t9+2cfPNY1i6VE8Z0NDQOBgM8tBD0/B4YkhIMJ72qubQOjxHuHPnDgoK8pk48XYAysrK2LVrJ8ceezwLF87nnXfeAhQCgQAFBfmkpKSQmqpn+wvPR5588lB27drB3XdPwOPxcM011zfpvtSFkXKeC2p85Qd2ALOllPmmSuPgcBRidYhkenpmo453u91MmvR3rr32CgYOPJ6hQ09r1vXbt+9ImzZt+de/5uDxeFixYjk9e/bi2Wf/w4UXXswpp5zKO+8s49133yYtLZ2SkhLy8/NJT09n48YNtGnTlrVrv6dVq1Y8/vhs1q//iXnzZvPUU/OaJdehGLEI49EzTq9CT6JwIrAfWAhcZJokDg5HKVZk0w4Pjd1uN2Vlpdx2213ExeklAmsOjY8/fhDXX39ztfPj4rzcffe9TJ8+jeOPH0R8fHyTZUlPT2fUqCsZP/4mgsEg7dt34Mwzz+aMM37H7NlP8OKL/6V16zYUFBTg8Xi4665JTJgwnuTkVDweXUX16NGTadP+zuLFrxMMBrnuuhubLM/hUBoKtRFCfCilPOuQ7Q+klOcIIT6TUp5uhhB+f1CLpkSRR1PiymjgSJN7797ttGvXuQUkMsaRFlkS5nD3vXXr5O+BwQ21bcQGTxFC9AYI/U0WQmQCSQbOdXBwcIh6jAyNxwEvCSE6AL9xMC3Xg3WdIIRwAXOAgYAPuEFKuan54jo4ODiYjxFF2AE4sUaihe8aOOdiwCulPCWU0fpR4A9NlPGIIaCq7CwOkpXsxuVycgY6OEQLRt7Gs4AfhRAPCiG6Gmz3NPTYZKSUqzEwRj/SCagqo9/O4daP8vjT2zkEVPvN0TgYx45pruxMc++3kVT9twkhYtEtutlCiNhDF0/qIAUoPGQ7KITwSCkDhzs4Wsp5hrGibODmvErKAvqPVRbQKCSW7mmxpl7DKdMYWeqSu7AwnvLyYpKSUqM2+YLbbc8RyeHk1jSNkpIi4uPjm/wcGXWoPgk4F2gLvG7g+CLg0Dp6rrqUIERPOc8wVqxipqOS4FEoC2gkeBTSqaSgoM5b0iSOtNXXaKcuuZOSMsjPP0BRUXS62SqKPROz1id3ODFrzd/DjHKeAAghNgA/As8Ad6PnJGyIL4ALgddCc4TrDElzBONyuVh0QStnjvAowO32NDlBaCQ40joeMzBiEQ4DuqNXsDsbWGzgnDeBs4UQX6IXfbquyRIeQXhcLrqkOgrQwSHaqK94UyzwZ3R3GR/6vF83KWWD0c6hFWbj0doODg4OLUh95sk24FjgSinlMGC3ESXo4ODgYDfqDLETQkwCrgQ2Ac8Cd0gpz7NIjgPAdovadnBwOHrpDLRu6CAjscbD0RdIRqIrxBeklOvNkNDBwcEhGmhQEYYRQqQBVwFjpJTHWyqVg4ODQwQxrAgdHBwcjlQcXw4HB4ejHkcROjg4HPU4itDBweGox6lrHEIIEQMsALoAccB0KeWyFhWqEQgh2gDfA2dLKTe2tDxGEELcg17uIRaYI6Wc38IiNUjoOVmI/pwEgRvtcL+FEEOAmVLKEUKIHsB/AQ1YD4yrkWYvaqgh93HAU+j33QdcLaXcZ8Z1HIvwIH8BckPO4+cB/25heQwTejnnAbZxeBdCjACGAqcCw4FOLSqQcUYCHinlUOB+6klQHC2EfIKfBbyhrx4DpoaedYUozRV6GLmfAG6TUo4AlgCTzbqWowgP8jpwb+izApibGsZaZgH/AXa3tCCN4Fz0ZBxvAsuBt1tWHMP8AnhCWdhT0Ks6RjubgT8esj0I+DT0+V30nKPRSE25R0spfwh99gAVZl3IUYQhpJQlUspiIUQy8AYwtaVlMoIQ4lrggJTy/ZaWpZG0Qk/Yezl6XPpLQojoTN5XnRL0YfFG9IxMT7aoNAaQUi6musJWpJRhv7liIDXyUjVMTbmllHsAhBBD0ZPAPG7WtRxFeAhCiE7AJ+jRMy+3tDwGGYOe6ed/wHHA80KIdi0rkiFygfellJVSSoneuzcYChUF3IUudy/0mjwLhRDeBs6JNg6dD0wGClpKkMYihBiFPvr5vZTygFntOoslIYQQbYEPgPFSyo9aWh6jHFpSNaQMb5FS7m05iQzzOXCHEOIxoD2QiK4co518DlopeUAM4G45cZrEWiHECCnl/4Dz0Tv/qEcI8RfgZmCElDLPzLYdRXiQKUA6cK8QIjxXeL6TcccapJRvCyFOB75BH5mMk1IGW1gsIzwOLBBCrEJf7Z4ipSxtYZkaywTgmVCqvWz0qaCoRgjhRp+G+A1YIoQA+FRKeZ8Z7Tshdg4ODkc9zhyhg4PDUY+jCB0cHI56HEXo4OBw1OMoQgcHh6MeRxE6ODgc9TiK0KHFEUKMEEIsamk5HI5eHEXo4OBw1GOZQ7UQYg1QFNrcKqV0irw7GEYIcRl6Te0Y9HRRlwB/BXZJKWcLIdKBD6WUg4QQDwPD0CM8HpNSvh6KstkPZADn2sRZ26GFaFARhh64U4FM9AdrlZSypIFzvOiB3SPMENLhqKQXejxpmRBiHnq2mmeBV4DZwBXoiRrOB7pKKU8LPXerhRArQ228IqV8syWEd7AX9dU1bg3MAHoDEtiDHoJ2PHr6pHvrSooYSqb4PHqtYg96GNLquoRQVVULBqMnwsXtVogmeYziyB1ZHLkjS1Pkjolx52AgmUd9FuF96Jlhf6m5QwjRB/g/9KHL4ShDz5H3LNATeFcIIaSUNsnxp6CPxuyGI3dkceSOLE2Se7uhlq2INRZCxAGucMICIcQ3wKVSyh2HO97vD2oFBWWmy9FU0tISiCZ5jOLIHVkcuSNLU+Ru3Tr5e/S8l/ViZI7wrNBxbvR6AVMN5OobAwwAbhVCdEDP5LunoWs5ODg4tARG3GceBH4FbkOvMXGLgXPmA2lCiM+BV4Ex9hkWOzg4HG0YcZ8pA/YBASnlXiFEg2NpKWUl+qqeZVx88cha31100SWMGXMjZWVlXHHFZbX2jx59JaNHX0lubi7XX39Vrf3XXns9F198KTt27OCqq2rvHzv2Ns4993w2bfqViRPvqLX/rrv+xvDhZ7Bu3U/ce+/dtfZPmXIfJ500hG+++ZqHHvpHrf0PPDCDAQOO5dNPP+Hxx/9Za/+sWU/Qo0dP3n//XebOfarW/hdeeIHk5EyWLl3Mf/9buyDc/PkvkJmZyaJFL7Fo0Uu19r/88hskJCSwYMEzLFtWe7F16dIVAMye/SQrV75XbZ/X62XRoiUAPProTFat+rTa/vT0DJ577kUApk+fxnfffVO1z+Nx0bp1O+bOfRaAqVMns379umrnd+/eg0cf1bPiT5hwO5s3b6q2v3//AUyfPhOAsWNvYM+e6uVbBg8+ialTpwFw3XV/IT+/el7PYcOGM2GCXgto9Og/UlFRvRzG2Wefx7hxtwMHnz2Px0UgoCd7NuvZ27VrJ+PG3VRrv5nP3iOPPFAld5jmPnuzZz9Nx47HWP7sWYURi7AYeA94TQgxDt2FxsHBweGIocHFklAxo05Syg1CiP5Ajtmp4J3FEnNw5I4sjtyRxcrFkjotQiFEOyFEL2Al4A99DgItXvRcU1W0wnysWPHWVBW1IM+ytq2S28HBoenUN0d4MnAHIICnQ9+pQIuWjdRUFfWRu2FTNvTog2vSDBSXOSHTmqqizpxMfrjtyTPNbdsiuR0cHJpHnYpQSrkUWCqEGCmltHamsjEUF+rKRA3qf4sLITXdlKbV/Fz49Wd949efUfNzcWeaVGHSQrkdHByahxGTJE8IMU8IsUAI8ZwQomULiaekQY8+4HLrf1PSzGu7rKT+7eZgpdxYO6Q3m9zcHGbNmgHAp59+wsiR5/H665HLwrV48asA+Hw+li9f2ujzDz1vxYrlfP75pw2c4RDtGHGfmQs8AlyGHmMca6lEDaAoCq5JM3SLKiUNRVHMa7tjZzRvAlSUgTcBpWNn89q2UO7wsDt/czZ0j/5hd2ZmKyZO1F08vvjiMyZNmsxxxw2J2PUXLlzApZeOIi8vl+XLl3LhhRc36vzweVdddQUjR15okZQOkcSIIsyRUr4ihDhHSjlNCNHi3Z/iclkyrHS5XKhPLiKlNIei5Da4TFYmVskdDcPuFSuWs337NsaOvQ2fz8eVV17GG28sZ/z4m+jZU7Bly2bKykp44IGZaJrGffdN4eqrr2P16i/59VfJhAl3s3v3Ll577RViYmLo1CmLSZP+zgcfvMs77yxDVVWuv/5m/vnPh+jf/1h27PiNQYNOpLS0hOzsn8nK6sy99z5QTaZPPvmQJUteJxAIoCgKDz00i7feWkxRUSGzZs0gGAywbdtWnnvuGS6//M/MmHE/hYWFANx559/o3r0Ho0dfwoABA/ntt+1kZGQwffojPP/8ArZt28rcuXMoK/ORmZnJxRdfxlNPPc5PP/0A6H6Hf/rTn3nwwWnExMSwd+8ecnNzmDJlGkL0juhv49AwRt50VQjRD0gQelXlDItlalFcHg+ezt1MV4KWEh52u60ZdjeXPn368cQTcxg8eAgrVx6cWTnttOEMGXIKEyZMoFOnLObPn8eTT85l7tz5JCUl8dZbiwFITk5m7tz5DB58Env37uHGG29lzpxneeONV7nkkst5+umF/PTTjxQXF1e77o4dv/HPfz7B3Lnz6dKlK9988xXXXHM9KSmpTJx4N1dfPYYuXbpy3XU38vzzCxg06CSeemoekyb9nVmzHgZg9+5d3HDDLcyb9xwFBflkZ2+oOm/s2FurrvXFF6vYs2c3Tz/9X+bOnc/Kle9VOX23a9eexx77N5deOoply5ZYfbsdmoARi/CvQD/0KvMvAwsslcih0YSH3amuSgq1OFOH3U2j+jxlr14CgLZt25Kbm3vYM3bv3kXXrt1ISEgEYODAE/j229X07dufrKyDUxQpKam0a9cOgPj4eLp27QZAYmISlZU+ILnq2PT0DKZPv4+EhAS2b99G//7H1inxli2bWLPmOz766AMAiov1nMKpqWm0batfr02btqFr1Gb79q0MHHgciqLg8Xjo128A27ZtAaBnT1F1/rp1P9Ypg0PL0aAilFL+DISWUhlkrTgtz8FFh2hQKMZRXC5caRkoLeQoGxsbS25uDgBSbqy2z8h9bN++I9u2baW8vJz4+Hh++GENnTplhc4/aJ0b/U1KSkqYP38eixe/DcBdd42rWkgK/1UUF5qmh5p17tyFc87pyznnnEd+fl7VYsjhrnfoeWE6d+7KihXLGDXqSgKBAOvX/8T5518AfGmr56g5aKpqyRx4JDCSfWYKMAk95hgAKWUHK4VqiGBlJWzZCD374Xa7TWu3yo8wvOhgoh/hkc6QIUNZunQxY8dejxB9SExMbNT5aWlpjBlzM7fffjOK4uKYYzpxyy3jqyy0xpKYmMiAAQO55ZbrcLs9JCcnk5NzAIAuXbpy//33MnnyVPz+AHPmPMnVV49hxowHWLZsCWVlpYwZUzveN0x6ejp+f4DHHnuU8OzSqacOY+3a77n55uvw+/2ceeZZR9VcoNV+slYbKEZC7H4ETpFSWmZqNCbELlhZCbf+EVQVXC6YswR3rDkL2Wp+LtqEgwHxyqMv4ErPNKXtSGBV6JTVPf3RFPIVDVght1aYjzrhan3BzuXG9ejzKCYt2FUp2SZ4RTQ7xO4QtgLlhq4aCbZs1JUg6H+3bKz/+EZQs1Owg0+e1YQfQnXC1agzJ+tK0cG2WOZvaqWfbNgrIniIV4TJGFksiQXWCSHWEZoFl1JammKrXnr20y3BsEXYs59pTSsuV7VpfjsNiy0bOkSBa46DOVjpb6ooCsrEh2DvTuiQZe4zGFayIbmt8Iowoghnmn7VZuB2uwnOWWLJHKGSmo7Wq3/VPIdZpr3VWOpQHX4IQ/ck2lxzHBqBhZ2apqpos6ZUPSeayUqWO+8ncdt6SnscZ8n0TJ2KUAhxGzBXSlnLgVoI4QFulVI+abpEBnDHxkLvul0hmkr0uaEYxMIH3MqIGIcIY6VlZeEzGKyshPGXURoaBQZNXBcIU59FuBZ4TwjxM/ATepbqNPSsNH2B2imWD0EI0Qb4Hjhb1vSniGJa2g2lSVg8dLAsIsYholja0Vs5cjjcuoDJhlB92Wc+B84SQpwNjABOQs9O/SZwm5SyztlWIUQMMI9oWmQxiB39CG1ryTpEHKs6ekvnCLuK+rdNwIhD9Ur05KyNYRbwH+AeIwe73QppaQmNvIT5aKpK0f/dRb5cj0f0J+X+x221YOJ2u0gP2mtVV1NVlOICUlNTbafA3W5XVDy3jcUKucPvTsCCdyewfS+HrhOnVuTjadvNlLbDGFksaRRCiGuBA1LK94UQhhRhMKhFhT+WVpiPunE9qEECG9dTsGOPbRZMwH5+bc3xD4sG7Ha/Qb/nVowcrHx3gomtQHGBpoLiojCxFW6D97116+SGD8KYH2FjGQOcLYT4H3Ac8LwQop2ZF7As5X2UJy844iguhF836P5hv26wxD/M4SBV3gU3Xm6+T2hKGnTvrSus7r3NfXdy9ulKEEBTUVrCj1AI8TbwLLBcShls6Hgp5emHnPs/4BYziz1ZGcpj57k2O85takkp4PVCeRl4vWhJKdhDcptipU+opoGigIL+N7zdnCb370H7eDl8ViMXtAXPtxENMhG9sPv3QoiZQoiepkvRGA73Y5pI1WSyTZQJWNzTW4hSUgS+UDYXn0/fdrAOK0c8Ve+l2qz3UtM0tA0/EHzyftR7bkD7aDkceyLEhNxl4ryQnGqe3CGMLJZsBCYJIR5BT8W1XgjxGfB/UsqvGjh3hClSHoKWlAJxcboVERfnWBFg3+iPCEQMOBwkmt1nNF8F2lefoH20DHZth+RUlN+PQjljJBoK2jef6Qf6KqCoAEzOAWBkaHw+cC3QB3gBuBOIAVYAA02VxgBKSRFaRYW+UVGhWxF2eOmtxKYKxc5TEVZiZZKLaHOf0XL2oX38Ntqq96G0BLK6oYy5C2XIcJSQFagV5NW8mKmyg7FV478Ac2pGmAghppkujRFS0qBnX8tCvuw412ZnhWKlA7sd8+PZtexrY0LsNE2DX9ajfvgWrFmtzyueMBTXWX+Ann1r/VaRCH01oghvBG4WQoxGT9A6T0rpl1K+abo0RtA0OPF06NRVd6zM/hEtJVWfN0hKRWlG7LHdiiAdii0jYizErgrFttMcYQ8ATT3oAVBDbs1fibb6f2gfLoMdWyAxGeX8S1HO+D1KZps6m45ER29EEb4MbATeA04FnkO3ElsEtaIcXpqLnghnObWWBZJSdKWYnKpbAod+Tkmrto+EpOovh10fQofa2PW3DLuhbMo23w3FQurzANDyctA+eQft03ehpAg6dka59naUISNQ4ryG2re6ozeiCDOllHeHPr8lhFhliSQGUfIOoB2aLGvMXbi88WhFBfrDXlSAFvrLru3659BqZC2vQ5cLklIhJRWS0yA5RX9ZCvMcP0K7Y9esORa4oUSEogJdCQKUl+l+vnn70T5chvbd5/q/47iTcZ11EfQ+NuqmKowowp+FEKdKKb8QQgwAtodiiRUpZaXF8tWmQxbEJ+g3PT4BZejvUFyueleOtWAQSgpDirIwpDQLDm4XF0BRIWzbBEX5xPQ/nsDt/4i6H6ulsONcm22z5hzODcVES9ayOfCabf3rPtSdWyE+EeXsi1HOvACltalxFaZiRBEOA84VQvjRV4sBfkE3sMwN+DOAy+VCfWJR1eqUkbKbitsNqRn6/1Cv0lTfX4L/1WdxrV0Ng4aaJLV9se1cm12x0JK1PG9lq3aQE4qdCPpRrhqHcsqZKN74Zjdv9SKmET/CflCVVitHStni3roujweO6WJJ28pZf8D19ScEX/4Prr7HocTbL6jeVAxMgkcjdlXgllqyFs2bauVlaAse05Vgr/66/1//E0yTPRKLmA22JoQYIYTYArwPbAml5WpRLIs1Bn9iyLwAACAASURBVFAUEq64AfJz0N56yfz2bUbVJDhUTYLbAosjkKxEcblQUtPNt3wsiCzRdm5Dvf92WLsaZdQNuCbPxDVgkDUKvIVrlkwHTpNS7hZCdASW0Pi0XKZhZU8fbrt4czakpqN9+Bba0N+hZJkzA2DlXJtVQwelpAitZhicDSxC2y6WWIjZbijq6k/Q/vskxCfo72Gv/iZJWoMoqVkSlFLuBpBS7hJCVJguRWOw0i3i0LaLCiExCfWFf+O6Z1azlW0kFLhTs+Qgtl0sIfojS7SAH+3V+Xo4XK9+uG65ByUtw0QpqxMtfoRFofolnwGnA3kNHG8tVr6YNXoeZdg5aPMfQ/vsfZQR5zev7UgpcKdmSRVVCiXZPklfo31uU8vLQZ37MGzORjnnYpTLxqB4dDViR++CMEZD7KYCDwIb0PMNthhWvpg1ex4A7fOVaIufQzvhFN0hu6lEUIE7NUtADQTQ7hhd5WalPrFIX2SLdix2BG/OFIqW/SPqf2ZAZSWusfegnDisert2HPGEMPJkzG3ROsaHwcoXs+bQwXXVONT7xqO9Nh/lhglNbzeCCtxuvbEl7N1ZzcGXvTtN9TSwzJ0jCt1nNE1De/cNtMULoV1HXOP+jtIhq/pBNh3xhDGiCOOEEMei+w6qAC3iSN1CKB2yUM67FO2dV9FOOxulGdWzIqnA7YJlCqWG4z01X9xmUKVQLEwOHC3uM1pZKer8x2DtVygnDkO59o7DupRZmh4vShZLegFvHbLdIo7ULYlywSi0rz9FfWE2rn/8G8UT0/BJDg1i5ZDH5XIRfPxlvfRjz36GHO+NohXmwy/r9Y1f1qMV5qOYmB/Pqg6zscpK27kVdfaDkLMPZfRNKGf/oU7FbGV6vGhZLBkjpfw2vCGEGNHQCUIIN/AMINAV5y1SyvVNFTKSHM5CUeK8uP4yFvVf96G9twTlglEtLOURgoVDHk1V4bGphtJCNZqaL6JNpiIao6zUrz5BWxhyjfnbDJRe/epv3OL0eC2WdEEIMQy9kPtdQojHQl+7gPFAQw5DFwJIKU8NKc4HgT80W9oQVq1O1WehKMeeCINORVv+CtpJp6O0aW/adY9arBzyWLmSnpqO1rPfQe8CuywkGbjfWsCPtugZtI/fhl79cY29GyW1YdcYO3sXQP0WYT7QDogDwm+9CkxqqFEp5dJQ0SeAzkBBc4Q8FEvdCxp4eVx/vhl1/RrUl+biutNJytBcojl1fH0oioJr8kz7LU5VZbJRDpvZRneNeQg2b0Q5948ol15b5RpjBDt6F4Sp818ZGsquF0I8E3aobgxSyoAQYiFwCXBZfcc2psC7WpCnW2xqEDZnk+qqxGWSM6eWGk9R7/4Hi1Rndaj+kKdlUX7FGMqem03Cxu+IO2W4Kdc1CzsXHLeiML320JNoRQXWhKthndxWUd+741+3huJH74dKH0kTpxE3dETLCnsYrHy+lYbidYUQVwP3oFuGCqBJKQ0vloRqGn8N9JVSlh7uGL8/qBktlK1pGurMyQctwskzTR8e19fTa8Eg6gN3QHEhrunzoiopgx0LjoMjd6SoenfCUz+TZ0LYNWbJ87przPipKO07Nal9NRBoVFaoxtKU+926dfL3wOCGjjNi905Gn/PbYfTiQoirgGOklA8DZehDalO6TqvnIhqalFXcblxXjUd9aALaWy+ijL7J1OtHI3aOGHA4SK2AgfJS1Gcfgx9Wo5x0uu4a08SUWbZ1YA9hRNItUspNjWx3CfBcqOxnDHCnlLK80dJFKUr33ijDz0dbuQztlN+hdO7e0iJZRrSHfDk0jnBHz7qfddeY3H0of74J5ay6XWMMYbEDu9UYUYRlQoh3gR8IZbuXUk6p74TQEPhPzRevNtHyYiqXXoO25ks9KcOUR49c5WDX2h8WY8dqh2F8//sA9T+PQnyi/v70bMA1xggWOrBHAiOKcIXlUjSGKHkxlcRklFE3oD0zC+2z91BGjIy4DBGhavV1g62yz1iJXasdaiXFaK/Pp2TVByAG4LplsiHXGCM0JXN8NFGfH+HxUsq1UsqFNb43zR+wSURRWijl5DPQVn2A9sZ/0Y4/pcX8ybSCPLRlL1HWKhOtz2Do0tM8K0XTQv8f8tlmFpDpRElnbBRNVdG++BDt9QVQVoL3kj9TOfKKZpW+PRxWZo63mvoswkeBMwHE/7d35uFRFVkffm8ngbAkIRA2BRMQKFRkEUHEDVBkccRP3FAWFRVBUQcRRAFRcGVUnA8UUGAEURTcUT9cUIZREUYFBTHFgCwGRzCYBQJku/X9Ud1JgCzdnb7pvkm9z8OT7s5dTl9uzq2qc87vCPGplNKnTH0Px5bcVSmRlLhpWZYWZXjoTtTyBVi3TahyG9Sm9diLZsHRIxyxC8FeComNsDr3wDrrXBBnVq4k8GAW7EjVUv07UiP+j74kjgV5qqD2NVSoPb9gL33eO3A4HWvoaOoktyBPuWvE5jTlOcKSd050GZ+HhUhK3LSat8QacDXqg9dR51+KdVqnKjmvystFLV+oKwBOaY3n9vtJaNGMzLVrURvX6RHAFx/qLmKdumF1ORc6dA083SeCRuCB4ORashvUftThHNS7S1GrV0L9OKyR46BHb9TTD7puSl8VlOcIlR+vDfhEGdZ4RRmex4pxVpRBpe3Cnv8U7N2tKwAG36j7UBTmYfXsg+e8i1G5R2HrJu0UN61HfbMGoqPhtM5YXc7F6nJOtSydUoWFsCMVe8PaEsIIP4V8JBupaj9KKdT6Nag3FkB2JlavgViDR2DVi9N9flw0pa9KynOEHm//Ys/xr6vEMhdh1aqNZ9gd2LMeQq16E+vy60vdrrJTNaUU6vMP9E1etz6ee2dgdeha+uJ97Vjo0gOrSw+U98ZX36/T/zbPRr0yB1q3xzqrh3aMzVqU/f0iaAReGirnIGrL9/DDBtTmbyHnoH4weDy6PzAK+/t1eC7qH1qx0AiLGqu9e7BffQFSf4SUtnjunobVql3xBg5P6d2cb1pmZYkQYifFoz/L+zrgyhJ/CKSypCoItmLAfuFx1A8b8MyYe4IoQ2WnaupgFvY/noNN66FjNzwjxxUpZqusDOzxI/ST3hOF55klZQZulFKwd5d2iBu/gd3eFNHmLbG6aKdIq3ZVNmWqqJKn1H2Ugt/3on7YgPpxgx752TbUj8fq2A2rU3dUy9aoKaP1NfFx6ml4RozFatmq0jbbMycVV2iEeYqpjh7RYiCfvAOxdXSN8IX9sDwnBkOCud5+2VAFaW1hqSyRUlbubqmBWNePQm3+DnvpC3jGTT/2RqtEpFH9tBF7wTOQk411/e1Ylww69tgBPOkty4IWrbBatIJBN6AO7NdT5+/XoVa9hfpoBSQ01E7xrHOhfUfwRFW52s8J2xbkw7aftPP7YQPs95a/t2iFNeAarE7doXW74j9+pVC+tc1T2+v+M8sXYj9yF1bf/8G6YmjwjccjJGqslILvvsJe9iJkpGOd31f3EIlPKHMfx6b0EXJNgsU9NTAuwEpMwho8HLXsRfj2SyjR0yGYoIMqyEe9vQS16i1o3lI711Jai1qWhXXf48TnpJMd1yQgZ2U1aoJ18eVw8eU6z2zzv7VTXPc5as1HEFsHYmrBoWxoejLW8DuxGjbWkelatf0+T6lU0DxeZWeiNn+rHd+W7+DoEYiOgdM7Y/W7Uo/+GjUp/XuVsrapOvdAvfkP1MdvozasxXPDaDjr3MCdewREjdW+vdivztPXpUUrnRMYisToYHFpUM1HhaILVUF1mRqDT5Thr5CdieexY0UZAllDUb+nYc+fCbu36wXv627V636lbevAtETl5cLWTdjr/wnr15S+Ub04SEyChklYDRpBwyRITNJqzYlJkNhIR63L+K52YSHq7uuKqxH+/jqe3/YUT3l/kTpvsUEjrE7d9ajvtE5lXocTvkMZ11tt34q9ZA6k7YJO3fEMHYOV1DSw6+PQFLPC8+bloj5cjvq/FRAdg3XlcKw+l/udE+ik3U6vETo5NTaOsBQqqyqifpHYj92LdfEgPDfcXvS5P+ocSindOe+1eRAdg+fme7DO6lnu+eyMA6jxw4veW8+8gidE0vFFiiX/+QmS22BddSNk/gl/pkPmAVRGetFrskuRnawdW+QULZ9zTEzCSkxCxdRCzXpIjwixoEGiPjbodcpO3bE6nQOntA6841oFDwdVUID67H3Ue0tBKaxBN+j2lAHkXFa1+ozatB77tfmQ/jtWj15Y194aUD/hSFvbDJSwqs9UVobLCSI9OmW1Fli9BqJWr0SddzFWchu/1DnU4UOoJXNQG9ZC+454br0Pq2FShec7/mEW0oebr5oEC2JqYZ3Wuex1vPx8yNJOUmWkQ8YByEiHjHRUxgFU6o/694WFJ+ZgWRa0FlidzsHqeHblS78qWLOyoqOx+g9Gdb8A+7X5esr89Wo8w+/EEmdWeHgno8bH398qfR/2svmw8Ru9RDLhieDyVV2+juckjshwOUmkiC5UhHXVjajvvsJeMgfP5GcqVOdQ//kJ+8WZkPmnjvoNuKrUqF+VE0BliRUTA0lNIalpmVn3yi6E7CztHP/8A7V8EaT/Dq0Fnjsmh+7/0s81K6thY6LGTtGjrVfnYj91vw46XDMSK670oIOTtcbF9/dWnd7UoSvqo+Xa1mtu1oGeYCuFImBtM1JxSobLOVzyVLPq1se67jbUS39D/XMV6oJ+xXltHg/Km7enCgt1Vcr7yyCpCZ4HnsZqLQI7l8dzzAgrpA+GEC+CW54oaNBQ/2vYGHVgvx5x7twW2r4iASaCW53PwXNaJ52G8vHbqI3faGd4ft8Tr6eTTadKdsjbvhW1fSt0PQ/P9aN0kKoSuKEiJlw4IsPlKC6KTlk9eqG+1KIMVpvTUUXjJAtPzkFUfh72i3+D7Vuxel6MNXRMUIrXVkIiql2HomsSSvEHRytLHB6hBJoIbtWOxbr6ZtS5fbBfmYN6+e+oLz/RuYctSmSThdhulZcLv+1Bpe1C7Ug99pejJhLVo1eljl+SSK2ICTf+SPXfePxnxyvSVJZAgyWRGJ0qC/XfNOxpd8DZ5+uggs9Z9b4MtWQOKBtrxFg8PXpX7jxhimJWlkiNYiqldL328oVw+BDWpVfqgIo39zCoRHDbhvR9kLZTO720XTpyve83b8AInapUkK9HybVjseaswBNilRi3tRjwEW6p/leB29GtPbcBcwOyxAEiveSrJFbzFlgDrkGtXIY1/lFo3By18nXU/Kfg1PZ4bpsQktagbn3SO2V3ZdeSLcvCOr8vqvM5OpCy6i2dezh0tK6+qej8h7KhhLNTaTth727IPeo7ATRuBienaJn8Filwcgoqtg5qwk2gCiE/P6SN0kFnLhTs/gU7ronrNAOdxB9HOB/djvNT4CJgATCirI29NcmLgBR0pPlRKeX7lbbUxViXXasL4ZfM0euE+/+L9ZcheoThor4OriJE63hW/Xism+5BnXcJ9pI52LNnQOdzICuDjD07oJXAumE0/Lb7GMdH5oHig9SPhxYpWBdcqn+enAInJ5de2VKyIibESz++zIUsl/YVcRJ/rkJbKeWF3tfvCiG+rmD7YcABKeVwIURD9NpizXaEPlGGZ6dCYpIenfiRomGoBKEO8rQ9A8+02ajP3kO9uxTycvUvtm9FTb9bv46OgZNOwTq9s3Z4LVrpzIAA2ok6uibr8r4iTuKPI4wVQtSVUh4WQtQBKlqwWAG86X1tAQWVMbC6YHXoiueBv+k/lHpxIT9+JKqhhBPLsuDeR+GXVGh7Rkiuic49vAr77PNRj47TCeQNG2Nde4ue2jY9OSSqz44t/bi8r4iT+BMsGQo8DGxBrxM+LKVcVtGBhRBx6JHgS1LK18rb1rZtVVgY/goXH1FRHgpd1Lhb2TbZD40rbkw/fVZE5laWhrJtPDnZ2PUTQurA7YICMm66Ag7nQN16JL78XkingXZBAdbvaSgH+nMo23asMb2TdjtNMH+XMTFRoQmWSClf9abPtAZ2SikPVLSPEKIl8A7wQkVOEKCwUEVUFMupqJpT0W6VlYGdugXsQgpSt5D563/D1j8lEJRtn9BwPFQO3E7bpZ0gwOEcMlO34QnRNNDJUrUqkbNqkUJWBP29+UuQUWO/tivzCgshpnh/LgPmAPcCs4UQ5To2IURT4BPgfinlIn8Nru74bnB7/Ajsp+7XTjFU+NbDoqIiPreyJCorQ9cw2zb85yf9PlT4poEQ+mmgLxBTWCIQE+pj2w4c21Am5Y0IV3p/zgvwmA8CicBUIcRU72cDqlOD96BwsBrBtRUDx9sZQrsdbS/pZCK4iwoGqhPlOcItQoha6K5116EDH1HAh3i725WGlPIe7z6GksQ3gFPbFwmFOlFF4bY8QishEdX2DEcqYsC59pJOPnjc1iOmulDeY3IkIIEB3p8S2AzsqQK7qh++fsAW+mcEyJ+FHd81cOE1KXrwOOCoLI/HkUAJlMwucM+1rgrKk+p/CXhJCDHSrPWFgKKpse2IWERVykKF7Li+NUKAbVtQWRla1NUFuDFdyUnVHLfjTz7Bp0KIiUCRLLCUcrpzJlVTHFz7qTJZqDanh/aPx8E1Qidx2qE4VkvvEuWmcOCPI1wBfEaE6BG6FUfXfqpKFirEo7aiNUKvQ3FDyg/g7PX2pRT50mdCmFJk9AjLxh9HeFBKOcVxSwzB4+QN7uCozbIsPPc/5b5ot4PX+5jlAm9KUcgePG7NLqgC/HGEW4QQQ4CNFOsRbnPUqmqIk4myjkYxExJRbU53LLLrymi3kw7F4eUCN17vqsAfR9jZ+8+Hopz0GUMZOLw+49gNXlq024wkHLvexSK7ek3WNcsFLsefErveQogEtKzWDinlIcetCiOORQMdTpR1ym4np2puxqnrbfIIw4M/XeyuAqZ4t10uhFBSykcdtywMOBkNdPIGdzSK6dLIrpOYNJTqhz//e/cCPYB04FHgSkctCidO1pDiYKKsk3bHJRxbs1tGZ7cahYPXu7gmfXjoa9IxCdVl4Y8jLJRS5qL7GSsgx2GbwofD4gXKtlFZGaG/CR202zqUDbleEdLcXP2+puPg9S5KV7LtonSlkB3bN5K97RpHnKyb8SdY8qVXcaaFEGIe8G+HbQobTkYD3Ro1dnpt0409NFwbNTYJ1WXiT7DkQSFEf3T6TKqUcmVF+7gZx6KvLo0aO7m26eYeGo7dJ3EJEFsHjh7RP0O5FGESqsukzLtOCBGFVpt5Ha0+8zkQJYT4XEpp0mcCxcXySo5Jx7u4h4ZTI1nrUDYqL0+/ycsLaRc7k1BdNuU9fkeitQWboZVnAGzgS6eNqo6YtIhScGkPDUdHsg4/ME1Cden4oz4zSkr5ou9zIYR7hjIRhpv6MVcFPvHU+Jx0sl20RujkSNY8MMNDeVPjZkA8MFII8QW6tsADLAG6V415huqOJzqa6OTWeNzUI6ZZC92f2rbB49HvQ4h5YFY95Y3ne6CVpgW6ybuFnhp/7M+BhRDnAE9JKXtV0kaDIWCcjNJ7cg5i+zKglH5vHJe7KW9q/C66oftAKeVHvs+FED0qOqhXv3A41Tnn0BDZOBmlj28AbU830ddqhD/pMx8JIWoDNwBjgdpAhwp22wEMBl6ptIUGQzA4GHQw0dfqR7kN3oUQKcCdFDdvuk5K+bU/B/bu+7qUssIRpGnwHhqM3cfiZKN0MNe7qglLg3chxPtAAnpU1wF4w18nGCg1pcG70xi7SyMWspzpJGuud9XiZIP3iqbGBUAddLQ4coZshmpHYWEBGRl/UFCQF25T/GbfPsuV4gXV0e7o6FokJjYmKiq4fM7ygiWDhBAt0YnV64H63lK7T6SU7htX+4kbu5NVBzIy/iA2ti716jVzzXWvSVPMSKAsu5VS5ORkk5HxB0lJzYM6drn5BFLKX6WUjwDtgVuAW4Fd/hxYSrnLn/XBSMKoc4SPgoI86tWLd40TNEQOlmVRr158pWYTfo0jvfJbq4BVQogmQZ8t0jHqHGHFOEFDsFT23gl4Qi2l3F+pM0YyRp2jxvL999/y0EMPkJLSCoC8vDzuu28S7dq157HHHmbbtlTi4uKLtu/ffyBXXDGYXr160KFDR0CvcxYW2jz88GPs3fsrixcvAmDLlh+Lthk7dhzt259WdJxBg/rx/vvFNQrffPM1q1d/wuTJDwf9XebOnU1ycgoDB15e7nYFBQWMG3cn+fn5zJz5HPHx8eVuHwgffbSSBQvmce211zNkyDDWrl3DihXLUEqRm5vLDTcMp3fvS/joo5Xs3r2LMWPuOmb/rVu38NJLc7FtxeHDOfTp05dhw0bw5JMzWLNmNfPnv0xyckrI7HWH5lEVYfLDajZdu57NI488AcCGDd+wYME8Zs58DoAxY+6mR4+eJ+wTH5/AnDlFpfi8++5bvP76Uu699366ddMrQ4MG9Ttmm0ghPT2dnJwcFi1a6sjx+/btz5Ahw9i8+QeWL3+NmTOfo27dumRlZXL77TeTktK6zH1nzZrJlCnTSU5OoaCggNGjR9KtW3cmTZpKWlroW6wH7AiFEC2klGkhtyRCMOoc7sGpWmKAgwezadAg8GWRfft+P2bkWBk+//wz3njjVTweDx07dmbMmLvYv38fTz/9JHl5uRw4kM5tt93BhRf2Ys2a1SxevJAGDRLJz88nOTmFjIwMpk17ANu2ycvLY8KEB2jbVhQd/+mnHyct7VdmznyMRo2S2LLlR44cOcKkSVNZt+4rVq/+hKioKDp16sIdd9zNwoXz2bs3jczMTLKzsxg8+BrWrPmcX3/dzeTJj9Chw5mlfo+VK9/lmmuup25d3fIhIaEBL764mLi4OH7++adS90lMbMRbb73BwIGDaNu2HXPnLiQ2trZjQZ5gRoSPATeG2hCDIRCcqCX+7rtvGTt2FPn5+Wzfvo0nnnim6Hdz5/4vS5e+XPR+3LiJtGvXjuzsLMaOHcXhwzlkZ2dz0UW9ueWW0X6f07e/j4MHs2nXrj3Z2VksWjSfBQteITY2lhkzpvLvf38DWAwZMpSzzjqbzZt/YOHC+fTseT6zZ89i0aKlxMcnMGHCPQD8/PNPxMcnMHXqI+zcuZMjR47Npxw/fhLTpj3IxImTWbhwPsnJrfjrX+9jx47tfP75p8ybt4ioqCgmT57IV1/9C4DatWvz7LOzeeWVl1m37itmzpzFhx++z+rVn5TpCNPT/+Ckk04+5rOKpuHTps1gxYrXeeaZJ9i7dy99+/bj7rvvDTo9piKCWSM0TjBInBzB1DgcCGyVnBrv2bOL228fybvv6jL7iqbGhYWFPP74w0RHxxSNfPzh+Km1b40wLe1XMjMzuO++uwE4fPgwe/em0bFjFxYvXsiHH74HWBQUFJCZmUF8fDwJCXpN27ce2aNHT9LS9jBp0niio6O58cZbyrXllFOSAdi9exdnnHEm0V6NxU6dOrNz5w4A2rVrD0BcXP2i9dS4uHjy8nLLPG7Tps3Zv38fbdu2K/rsxx830bBh6W1hc3NzkTKVm266lZtuupXs7Cwef/wR3nvvbQYPvrbc7xAsFT5ChRCLjvs3XwgxRQhhwqkBUNydbIRJzQkFvsCWx5lGW4kB9m6Oiopi4sTJrF37BV9/XXnt4ubNT6ZJk6Y899wLzJnzIldffR1nnHEmCxbMo3//y5g6dQZnnaUrxxo0SOTQoUNkZOhGT6mpWwHYuPE7GjVKYtas57nxxluYP//5cs/p8eiHc3JyClu3bqGgoAClFJs2baRlS+0kg3l+X3bZ5Sxb9krRiDQj408ef3w6R48eLcMODzNmPMSePbsB/bBo1qw5MTExgZ/cT/wZEdZBiyj8Cy3N1Q3YDywGBjlmWXXDpOaEFCcETH1T46ioKA4fzuGuu8ZRu3YscOLUuEuXrowaNeaY/WvXjmXSpKk8+ujDdOnSlTp16gRtS2JiItddN5SxY0dRWFhI8+Yn0adPX3r3vpjnn/87S5e+TOPGTcjMzCQ6Oppx4yYyfvxY4uISikZybdq0Zdq0B3nnnTcpLCzk5ptv8+vcp57ahj59LmHMmFtQStGxYycuvLAX27dvC+q7dOjQkUGDrmTcuDuJjo4mN/coo0ffSZs2bdm2LZVVqz7g2283FG0/e/Z8pk9/gieemE5BQQGWZXHaaafzl79cEdT5/aFc0QUAIcRnUspLSrz/REp5qRBirZTywlAYkZ9fqCKp9tGJWkylFPZT9xevad3/VMinx26uIU1N/ZlmzZLDbUpAVLcKjVBSVlpMZfDZPXbsKCZMePCE9Jnff999wj3UuHFc5UQXShAvhGgvpUwVQrQH4oQQjYD6fn8Dg5FgN9Q4Pv10FYmJiQwZMixkx3zyyRlBj0zLw58RYTdgHnASsActy9Ud2CelfCsURtSEEWFV4Ga7zYiw6qiudjs9IjwJ6Hac0MK3fuxnOA4TNTYYIhN/Eq8uAX4QQjwmhGjltEHVFRM1rhg3SkMZIoPK3jsVOkIp5V1AV2AT8LwQ4rNKnbGmUlrU2FBEdHQtcnKyjTM0BIxPhis6ulbQx/A3obo70A9oCqwI+mw1GYcbd7udxMTGZGT8waFDmeE2xW8sy50Cp9XRbp8wa7BU6AiFEFuBH4CXgEloTUJDgJiocflERUUHLaoZLtwcnDJ2H4s/a4QXALOAm4DNQGi7WdcgLI/HsUZCBoMheMpr3lQLuB6dLpMLxAOtpZTOdMIxGAyGMFHeiHAX0BEYKqW8APjNOEGDwVAdKTOhWggxERgKbAcWAPdIKfs7ZMcfwG6Hjm0wGGouyUCFURR/KksuQgdIBqId4itSyi2hsNBgMBgigQodoQ8hRANgODBSStnFUasMBoOhCvHbERoMBkN1pXLa5gaDwVANMI7QYDDUeIwjNBgMNR7T19iLECIGWASkALWBR6WU74fVqAAQQjQBvgP6SilTw22PPwghHkC3e6gFvCCl7rhgRwAAA9NJREFUXBhmkyrEe58sRt8nhcBtbrjeQohzgKeklL2EEG2AlwEFbAHuPE5mL2I4zu7OwGz0dc8FRkgp94XiPGZEWMww4IA3ebw/MCfM9viN949zPuCahHchRC+gJ3AecBHQMqwG+c9AIFpK2ROYjm5vG9F4c4IXALHej54FpnjvdQtwrhlIJSjF7r8Dd0kpewFvA/eH6lzGERazApjqfW0BBWG0JVCeRquI/xZuQwKgH7p2/R1gJfBBeM3xm21AtBDCgy47zQ+zPf6wAxhc4n1X4J/e1/+H1hyNRI63e4iUcpP3dTRQehu8IDCO0IuU8pCU8qAQIg54E5gSbpv8QQhxE/CHlPLjcNsSIEloCfVrgNHAq0IIN6hRHEJPi1PRikz/G1Zr/MDbUqOkw7aklL68uYNAQtVbVTHH2y2l/C+AEKInMBYtBhMSjCMsgRCiJfAFunrmtXDb4ycjgb5CiDVAZ2CJEKJZeE3yiwPAx1LKPCmlRD/dgxeUqzrGoe1uB3QCFgshYivYJ9IouR4YB7hGBFIIcR169nOZlPKPUB3XBEu8CCGaAp8AY6WUq8Ntj7+UbKnqdYajpZS/h88iv/kSuEcI8SzQHKiHdo6RTgbFo5Q/gRggKnzmBMVGIUQvKeUaYAD64R/xCCGGAbcDvaSUf4by2MYRFvMgkAhMFUL41goHGMUdZ5BSfiCEuBDYgJ6Z3CmlLAyzWf4wC1gkhPgXOtr9oJQyJ8w2Bcp44CWv1N7P6KWgiEYIEYVehtgDvC2EAPinlHJaKI5vSuwMBkONx6wRGgyGGo9xhAaDocZjHKHBYKjxGEdoMBhqPMYRGgyGGo9xhIawI4ToJYR4Pdx2GGouxhEaDIYaj0moNkQkQoir0T21Y9ByUVcC9wJ7pZTPCyESgc+klF2FEE8AF6ArPJ6VUq7wVtnsBxoC/VySrG0IE2ZEaIhU2qHrSc8HtqLVahYAI7y/vwEt1DAAaOXdrjcw2dtoDGCZlPIS4wQNFWEcoSFS2Y8WNPgH0BGIkVL+AhwUQpyO7rm9BDgT6OodAa5CjyBTvMeQVW20wZ0YR2iIOIQQCcAjwBB0T+0jaI1I0NJXU4E0KWU6Wg7rC69YZx9gOVrHDo5VWTEYysSsERoihUuFEN96X1vAemAdWiA3AzjJ+7t30Orhw7zvVwK9vCII9YF3vLqSVWa4wf0Y0QWDqxBC1EWrK58TqX02DO7DTI0NrsGrTLwe3czHOEFDyDAjQoPBUOMxI0KDwVDjMY7QYDDUeIwjNBgMNR7jCA0GQ43HOEKDwVDjMY7QYDDUeP4f2ICEVNOzLJYAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 360x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["xs, es, avg_es = get_data_points(entropies)\n", "xs, es_cls, avg_es_cls = get_data_points(entropies_cls)\n", "\n", "plt.figure(figsize=(5, 5))\n", "\n", "\n", "def plot_entropies(ax, data, avgs, label, c):\n", "  ax.scatter(xs, data, c=c, s=5, label=label)\n", "  ax.plot(1 + np.arange(12), avgs, c=c)\n", "  ax.plot([1, 12], [uniform_attn_entropy, uniform_attn_entropy],\n", "          c=\"k\", linestyle=\"--\")\n", "  ax.text(7, uniform_attn_entropy - 0.45, \"uniform attention\",\n", "          ha=\"center\")\n", "  ax.legend(loc=\"lower right\")\n", "  ax.set_ylabel(\"Avg. Attention Entropy (nats)\")\n", "  ax.set_xlabel(\"Layer\")\n", "\n", "plot_entropies(plt.subplot(2, 1, 1), es, avg_es, \"BERT Heads\",\n", "               c=BLUE)\n", "plot_entropies(plt.subplot(2, 1, 2), es_cls, avg_es_cls,\n", "               \"BERT Heads from [CLS]\", c=RED)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Clustering Attention Heads (Section 6, Figure 6)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXsAAALnCAYAAABocueuAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADl0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uIDIuMi40LCBodHRwOi8vbWF0cGxvdGxpYi5vcmcv7US4rQAAIABJREFUeJzs3Xd4FNXXwPHvphIgtGCQJkVgaAoKCqIShEAivVqQJk16FUWaSJEIGDpYQIpYXxWQYn4I0lRQCUVpQ5MeCD0JpO++f8zumoT0bN/zeR4f2d3ZmdnZzZk75945V2cwGBBCCOHaPOy9A0IIIaxPgr0QQrgBCfZCCOEGJNgLIYQbkGAvhBBuQIK9EEK4AS9774ArUBTlHFApi5c7qaq6Po/r6wPMUFW1QsH2zLy+YOBnVVV1Wbw+A3hOVdVm+Vi3F5AMvKCq6s5MXj9H+mNzF9gGDFNV9apxmT7Ayiw2cVhV1fqKolQG/s3wWgpwHfgeGKuqapKiKDuBoGx2OdP9zI6iKFWBWqqqbjY+rg/4q6q6Jy/ryWLdRYGuqqquMj4+h/bdLy/oujNspw8W/E3lYnv5/k0J65CWveWMBcpm8t9P9twpB2E6NuWBZkBJ4PMMy0SR+fFrkWG5Z9K8Vg14BxgAjDe+3jnN62OBSxnW93s+9n+Fcbsm6wAlH+vJzFigf5rHTwFfWGjdQphJy95yYkwtVfGAtMfmiqIoE4G9iqIEqKp60/i8PpfH70aG5VYrivIc0AmYpqrqLdMLiqLEAKkW+F4yXhFleoVkiXWrqnrdgusWwkyCvY0YL8/fR2uF1gV2AwOBeUAooALdVVU9nuY9M4DhwD1gtqqq89O8NhCtNRsIHAJGq6r6l/G1YsDHQFvgClrLNO2+1AY+AZ4EfgNOZ3j9OSAceAw4C4Spqvp5mtenAMPQAtU7+Tgc9/LxnuwkoqV08kVRFH+076E9UAItXTRBVdXvFUVZhZYWCjIeF9DSUp8qivKcqqp9FEWpAyxCa/1fBpYB4aqqGhRFmQrUBG4APYEk42uzjKmVd437YFBVVZc2jaMoigday38QUA74Exihquph03uA3sCbQA0gEuilquqZbD7rZGAE4A18hpb+Mhhfy+43VRZYCAQDhYFjwEhVVXcbX8/yN6UoirfxvV2AosbXh6mqqmb/zQhLkjSObU0DJgDPAw2Bg0AE8DSQCsxIs2x5oD7QBC2gfmDMvaMoSjtgOjAaeAItVfSL8Q8S4CO0ABOE9oc9xrRSRVF8gc1oAe1JtJTEgDSvPwxsQUslPGbc50XGbZoCwiigL9DS+P9cM+ao3wJ+StOqzxdFUXSKojQFXgO+K8Cq5gG1gFZAHbQT8afGYzUS2AvMR0sRdUZLDY0FRiqK4of2He4DHkc7OY9COxmadEY7GTUAZgPvG4PjN8CHaEG8LA+aghbIR6N9V/8CEcaTk8m7xtcbAqXQGhRZKY/W0HgOeMO4n20gV7+pz9FOEE2Mr19E+53l+JsyHotWxm09DsQCq7LZT2EF0rK3nMWKoszP8NydDB1ia1RV/RnA2JFYWlXVT4yPvyB97jYJ6KOq6g3gqKIozdBaeNvQgmWYqqobjMvONJ4I+iuKshB4CQhWVfWAcd0z0FqeoLXMHgIGq6oaB5xQFKU5UNr4+lBgh6qqC4yPTyuKUhMtMGxE+yNeqKrqJuO6BwL/5PLY6AA/42drlWGZcoqixGXy3rdUVV2a5vFhY4sWwBeIBhYAc3PYh+z8CixQVfUfAEVR5qJ9F+VUVf1XUZQk4J4pRaQoSipaauquoij9gFuqqk4wruuUoiiT0AK16ZjfQWtBpwJzFEUZDzRUVfWY8TMnZ0w1KYqiQztxTFJV9UfjcwOAM0AvYIlx0fmqqm43vr4M7XvKSgowQFXVGEA17kc9YBPZ/KbQTgIbgR9UVb1o3NYStBOPjpx/U5WBeOCcqqrXFUUZDFTPZj+FFUiwt5z3gP/L8Fxqhsdn0/w7Hjif4bFvmsf/GgO9yQG0YA9aK/R9RVGmp3ndF63FWQPwBA6neW1/mn/XBs4Y/yjTvh6aZt0vZgi8XmijXkzvN7ceVVU9oihKAtlLe2z8gdbA/xRFaZVmRMs1tCuejDLmsNsBF4CKaAHvCPC+MZDm1xqgozGY1kRrgYN2HHNSC6iT4Xh5AL6KovgYH5/LsH+xaK3k7ASitdT/MD2hqmqyoij7jds0SZuyiclhvTeMgd7kLlAozefI6jcFWmrqFUVRmvDgMcrpN/Ux8DJaf82vwAayHn0lrESCveVcV1X1dA7LZMwr67NZNuNrHmgtYtC+t7HA1gzLxKEFQUjf8ZecYbmMHYxpX/cCvkJrzaWVNlhlfH9O+fKMx+agMf0yAjAF+9RcHD+AC8blThtTD4fRUiHDc/HerKwBnkVLVSxDGxm0N5fv9QJ28t+JOC3TcUnK5LWcOnnjs3jek/QnoYzrzm69mZ0QTctn+Zsy9h38jHby+Rqtle8D/JDNds2/KeMVTGXgRbRUzkRgoKIoDVRVzepzCguTnL3jqmLMb5s8DZg6b1Wgoqqqp03/of2hNjO+low2hM/kiTT/PgJUUxSlZBavq0D1DOsO4b8U05G061YUpRpap1te6ShgY8PYEfkuMFRRlMb5WYexM7s7Wuf4FFVV16EFNdM+AmSsA572sYp2NXUuzfGqD7ytqmp2J/PM1vXfSrUWeBTQKM2+eqO1qK3RsZndb6o20BRoparqTOP9BqZcvo4cflOKovQCOqqquk5V1f7G12qh5e+FjUjL3nKKGTs3M7qnqmpsPtbngzas8F20Vmc3tM4x0EbKfKYoygm0fHMPtI7Sj1VVjVEU5XNggXG0hx8wOc16t6Gljz4zDoFsDHRF62AEWAqMUBRlFtpojXponYpvGl9fDCxVFOUA2slnEdlfoUD6Y+MNdEAbP98jzTIeWRw/0FI8WVmA9tkXK4rydC4DbFoJaKODOiuKEoUWuBcbXzOl1eLQglmgqqrRxsc1FUUpBawFpgLLFUX5AKiAll5alcvtxwFlFUWpoqpqxpvGPgSmKopyGTgJvI32fX6Vx8+YG1n+ptD6HPTAy4qirEM72b9nfJ8vOf+migOTFEW5ZfwcPdE+90krfA6RBWnZW86HaC2xjP+9m8/1HUL7A9qHNoLndVVV9wOoqvoN2hC5d4GjaGPMO6qqesj43mFof7Bb0XKjpo5CVFVNRsuZF0MbqjcQLcCbXj+PNmQzGK3F9iHwrqqqy4yvf47W+bgALQWzGS0HnZ20x+a0cZuDVFVNG7TKkvnxiwKKZLViVVVT0NJBDUg/AiRXVFVNQgtsndBOXvOBmWhDKE2t04/ROpQjjI8Xo41mWW48kYeidUIeAFajBfqJudyF79EC6VFFUQIzvDYPbcTLx8Z1PwI0U1U1u5NfvmT3m1JV9RIwGK2lfwzt9zgC7QryiZx+U2gnv5XG/06gnezbqqp629KfQ2RNJzNVCSGE65OWvRBCuAEJ9kII4QYk2AshhBuQYC+EEG5Agr0QQrgBCfZCCOEGJNgLIYQbkGAvhBBuQIK9EEK4AamNI+zCWAXxDP/VwvcE7gNjVFX9LZv3TUWbB2BYVsvkYR8aAuNVVe1a0HUJ4egk2At7ildVtb7pgaIoL6HVlbHJxBbGWkMS6IVbkGAvHEkAWuEz0zR5k9Cqf94H3lRV1VRjvqaiKDvQiqddA15RVTVKUZS2aEW6fNAm/1itqupkRVG+BA6oqjrXuO5BwAtotesXq6paV1GU4mgFu+qjlR3+CW0e2hRFURLRJtyohzYFYju0QmFJwE20GcWirHlghCgoydkLe/JTFOWQ8b/zaJU0ZymKUh1tNqzWqqo+gVZF8QdFUUzVL6sCL6mqWhO4jTYdow6tKmNvVVUbopXZfUdRlNLAp2gTc5u8bnwurYVogfsxtPlc6/FfWWcfYKOqqgrayWUU8JRxO1tJU3NeCEclLXthTxnTOE0wtqjRWu3bFUUxvawHqhn//bOqqqbpCg8DgaqqGoxXA20VRemONjmGDq088k6gkDFHfx9tvtTtaBOym7wIPKuqqgFIVBTlI7SgHmZ83TSj1mXjNg8oivIT2sTp2wt8JISwMmnZC4ehqurvaDMmlQa2q6pa3/QfWkv9iHHRtNMoGgCdsdV/EHgSrfb7OONyOmMAX4E2UffrwArjc2ll/FvwIP18rnHGfdSjnST6oF0JzFMUZQFCODgJ9sJhKIpSA22mqPVAK0VRahqfbw38zX+TY2emOtrkGZNUVd2IFpB9+W++1lVAe7QZvzKb7Pp/aNMb6hRF8UVLHf2cyT7WQzvpHFdVdRbaBCP18vZJhbA9SeMIe/JTFOVQmscewEBVVQ8rijIQ+NqYi08B2quqei9NWiejv4FNwAlFUe6gzYh1DC31c0ZV1avGqRS9VFW9ksn7R6DN6PUPWo4+Am3GqnSM+/YtsF9RlDi0icFH5PmTC2FjMlOVEEK4AUnjCCGEG5BgL4QQbkCCvRBCuAEJ9kII4QbyNBrn/v370psrhBBWUrhwYZ211i0teyGEcAMS7IUQwg1IsBdCCDcgwV4IIdyAlEsQwsWkpKRw7do1EhOT0OrECceiw9fXhzJlyuDlZbsQLMFeCBdz7do1vL39KFmyDDqd1QZ3iHwyGAzExcVw7do1ypcvb7PtShpHCBeTmJhE0aLFJNA7KJ1OR9GixYxXXrYjwV4Il2OQQO/gtO/Htik2CfZCCOEGJNgLIWzizJnTHDx4AICDBw9w+vSpAq1v6dJFbN68Md1zy5d/zLp13xVovVlJTEykc+d2AAwdOpBz585ZZTvWIsFeCAEJ0XjveQkSoq22iR07tnPu3FkANm/+kRs3rufwDmFJMhpHCIHniYXobv6F54mFpNafUaB13bsXx6xZM4iNjeXGjet06fISzz/flC1bNuHt7U2NGjXZt28vqnqCypWrcuzYEb766gs8PT14/PH6DBkynOXLPyYq6gq3b9/m6tUoRowYQ+PGz7Bjx3ZWrVpBiRIlSUlJplKlyg9sf9eunWzfvo3ExARGj36T2rXr0qlTWypVqkyVKlV46aVXmTlzGqmpqeh0OkaPfpPq1Wvw3XffsHPnDhIS4ilevARhYXNJTk5m6tRJxMbGUqFChQe29cYbfXn77YlUrfooe/f+xq+/7mHcuPEFOn7WIsHeCmJ6tENXpix+w9/Cq3I1e++OENlLiMbzwv+hw4Dnhe9IrTkCCgXme3WXLl0iOLgVzZo15/r16wwdOpDOnbvSunVbAgICqFOnLo0bP0NwcCsKF/Zj+fKP+eyzzylUqBDvvTeZP//cB4C3tw/h4Qv58899fPXVFzRs+BQLF85j1aq1FCtWnDffHJnp9suVK8dbb03g7NkzTJs2hVWrviA6+hqrVq2lePESTJjwFi+99ApNmzbj5EmVWbOms3z5au7evcvChUvx8PBg1KhhHD9+lCNH/qFq1UcZNGgoR48eITJyf7pttWvXkZ9+2sTQoSPZtOlHevV6Pd/Hzdok2FtDchKGS+e5//ZQdBUqSdAXDs3zxEIw6LUHhtQCt+5LlSrFN998yc6dOyhSpAgpKSlZLnvp0kXu3LnN2LHaNL7379/n8uVLANSooc03HBj4MElJidy+fZtixYpRvHgJAOrWfTzTddav/yQAVas+yq1bNwEoXryE+X3nz58zL1OjhsK1a9fw8PDA29ubd9+diJ+fH9evR5OSksKFCxdo0uRZAOrUqfvATVAtWrTk9dd78OqrPYmOjkZRaub9gNmI5OytzHDpPPenjrP3bgiRJY9bB9AZkgHQGZLxuHWgQOv78su11K37OFOnTqd582BM81x7eHig12v/1ul06PV6ypYtT2BgGRYsWMqSJZ/QtevL1KnzmHGZ9OstWbIkcXFx3L59G4Djx49luv1jx44CWodwmTIPm7dtUqlSZQ4fPgjAyZMqAQEBnD59it27dzJ9+izGjHkLvV6PwQBVqlThyJF/AFDVEw+cuPz8/GjQoCHz588lNPTFfB8zW5CWvZXpKlTCb4Rj5vCEAEhuvsWi63vuuaaEh89m27atFC1aFE9PT5KSklCUWixZsoDKlatQp05dli1bzPTps3jlldcYMmQgen0qZcuWo0WLlpmu18vLizFj3mL06GEUK1Y8y1IDV65cZtiwQSQnJ/HWWxMeeH348FGEhc3gyy/XkpKSwjvvTKZChYoUKuTHG2/0BSAgoDQ3blynY8cuTJ/+LoMG9aNSpcr4+Hg/sL727TsxaFA/xo17pwBHzfp0prNubsjkJbljztmPGI9Xpar23h3hZs6ePUvZspXsvRtu49ixo3z33TdMmTItT++LijpP1arp44M1Jy+Rlr0VFFu7MeeFhBBO77vvvmHjxg3MmBFm713JkbTshXAx0rJ3DrZu2UsHrRBCuAEJ9kII4QYk2AshhBuQYC+EEG5Agr0QwiZsUfUyJuYuW7dGFGi9ebF+/Q/Z3iHsSCTYC+HmkqcMI2X1Ygx3bll1O7aoenn69Cn27Nll8fVmZc2alaSmptpsewUh4+yFcHcXzmK4fIGUX7ehey4Yzw7d0ZUole/V2bPq5erVn3Hq1CnWr/+BRo0aZ1rd0uTAgf2sXbsGLy8vrly5THBwK/r06ce1a1f54IP3SUxMwNe3EG+/PYE7d+4wbdoUli9fzfbtP7Nv3+80atSYW7duMmXKBD744MN8Hy9bkWAvhIDUFEgFw86fSL18Aa8Js/O9KntWvezduy/r1n1Px46dM61u+dlnn6db/urVKNas+Yrk5GTatw+lT59+LF68gG7dXuaZZ55l//4/WbZsMVOnzqBduw5Mn/4uUVFXWLz4Y4oUKcLKlSuYNu39fB8rW5JgL4QATy/w8ED3fEs8279aoFXZu+qlSWbVLTN69NFqeHl54eXlha9vIUDrW1izZiVr167GYMBcg6dTpy6sXLmcPn36UaRIkbwcEocgwV4Id/dIVXTVauHZ/tUCpW9MTFUvO3fuSmTkfn7//Vcg56qXXl5ebN68kerVa7B7985sq16WLFmS48ePERhYJt0yOp2Hucqmqbrl888Hmatb5kalSpXp3r0Hjz1Wj3PnznHoUCQAixcvoHv3nmzZsommTZtRvnwFPDz+256jk2AvhJvznrbYouuzZ9XLChUqcObMab755stMq1vmxrBhI5kzJ4ykpCQSExMYNepNdu/eyYULFxgz5i1j+ebJLFv2KfXq1Wfs2BEsXvwxuoxnJwcjtXGEcDFSG8c5SG0cIYQQFifBXggh3IAEeyGEcAMS7IUQwg1IsBdCCDcgwV4IIdyABHshhE24YtVLZyLBXggBwM34VBYdiGHIzzetsn5XrHrpTOQOWiHc3M34VL48fo+fz8WjB1L0BVufM1W9XL36M3Q6D27dukmHDp3o0uUlhg4dSMmSpYiJiWHu3PnMnRvGxYsXMRj0DBw4mGLFijN//lwWL/4YgDffHMWAAYNQlJoFO3BWJsFeCDc364+7HL2RjKVuj3emqpfXr19n1aovMBj09OjxCi+8EAxAy5YhBAW9wA8/fEfx4iWYMGEKd+/eYciQgXzxxbckJiYRFRWFt7c3d+/ecfhADxLshXB7ExoX58tj99h6Ph69oeAte2eqevnYY4/j4+MDQNWqj5q3/cgjWrmJM2dOc/jwQY4dOwJAamoKd+7coV27DkREbMbb25s2bdrl7sDYmQR7IdxcqUKeDHuyGN1rF+HLY/c4djO5QOtzpqqXp06dJDU1leTkZP799ywVKz5iXo9pHYGBgfTu3ZfExARWrfqMYsWKERzcihEjBqPTeTB/vmULyVmLBHshBPBf0C8oZ6p6mZKSwpgxI4iJuUufPv0oUaJEutc7duxMWNgMhgwZyL17cXTu3A0PDw8KFy5MtWo1SE1NMde2/+qrtVSoUJHnnw8q8DG0Bql6KYSLkaqXuXPgwH7Wrfue6dNn2WX7UvVSCCGExUkaR7i8uLeH4Fm9Fr5duuNRMnezFQnX9+STDXnyyYb23g2bkWAvXJ7+3Bn0l86TvHMr3s1aSdAXbknSOMI9pKRAchLJ2zYTv8A+OVoh7Ela9sI9eHmBh4exZf+avfdGCJuTYC9cnkflR/GsUQvfLq/hUaKUvXdHCLuQNI5weUU/WIpfv+ES6LORHJ/Cub1XUf93kesn71hlG45W9fLAgf20bt2SJUsWAHDo0EFGjhzC0KED6du3F99//3/m5SZPfueB91+6dJGxY0cycuRQBgzow5IlC9Hr9Xz88VLatg1h377fC/T5LE1a9kK4ufjbiURM+Svdc83G1aPkI/4W3c6OHdsJCAjgiSeeZPPmHwkObkW1atUtug1T1ctWrUJztXyDBg0ZOnQkly9fYt68Ocybt4hSpQJITExg2LBBlC9f3lxOIaOPPlpCt24v07hxEwwGA++8M449e3bxxhtDuH492pIfyyIk2Avh5g58qbWwn+qjULp6cc7ujqLoQ375Xp+zVL1MKyJiCy++2IZSpbRRWr6+hZg3bzF+fn4cPnww0/eUKlWKzZs3UrhwYWrXrsuMGWF4enrm+7hZmwR7IdyYwWAg+sQd/Er6UqHBQwDUbluwu2+dqeqlyY0b1x84ERQtWjTbzzls2CjWrfuOZcuWcPbsaZ555jnGjn0Lf3/LXhFZigR7IQT6FD1J95K5duI2Xj6elH0s//chOFPVS5OHHy5LdHT610+dOolen3UJ0AMH9vPyy915+eXu3L9/n8WL57Ny5XJGjBid7X7Zi3TQCuHGdDodFZ8KJDE2mc3j/2D/qpOc35d1UMwNU9XLqVOn07x5sLkKZU5VL5cs+YSuXV+mTp3HjMukX2/aqpcAx48fy+TzPFj1Esiy6qVJq1ahbNy4wbzu+/fvM3v2+9y8eSPL9yxZspCDByMBKFy4MBUrPpJlft8RSMteCDfXoEd1ytULIPbqfQqX8qXs4wW7u9iZql6alC1bjiFDRjBhwjg8PDy4f/8+7dp1pEmT5zhwYD9//vkHffv2NC8/deoMpk+fxbx5c1i0aD5eXt6UK1eecePGF+jYWZNUvRTCxUjVy9yxZtXLGTOmEhzcisaNm2S5jFS9FEIIG4mM3G8eZ28pH3+8lH379lp0nZYgLXsbkKqLwpakZe8cpGXvgvTnzpC8I4K44X2IX74I/e2b9t4lh7F7924qVapEaGgoL774Is2aNWPZsmUWWXfv3r1JSkqyyLry6urVq4waNQqAWrVqkZCQwNy5c9m/f79d9kcIadnbQMzLIf890OnwrFmXIlPn2m+HHMju3btZsWIFq1evBiAxMZH69euzd+/eB6aIc1a1atXi4MGDFCpUyCbbk5a9c5CWvavy8gIfH7xbtsFv1AR7743Dio2NxdPTEy8vL/bs2cOLL75IaGgozz33HKdOnSIhIYFu3boREhLC888/z7Zt2wB44403aNmyJc899xxffvkloAXZ2NhYHnvsMe7duwfA/PnzWbRoEZcuXaJjx46EhobSsWNHLl26lG4/YmJi6NmzJ+3ataNhw4Z8+umnAISGhqKqKgDLly9n5syZHDp0iIYNG3Lv3j3WrFlDz549OX/+PM2aNUu3zoEDB7J169Zs192zZ0/atGlD7969iYjQarycOHGCzp07W+eAC7chQy9tQKouZm/nzp2Ehoai0+nw9vZm7ty5FC1alOPHj/PZZ59RtmxZ5syZw7p162jbti03b95k/fr1XL9+nVOnThEbG8tvv/3Gjh070Ol0bN++3bxub29vOnTowPr163nttdf49ttv2bhxI6NHj2bw4MGEhISwY8cOJk+ezMqVK83vO3PmDF27dqVDhw5ERUUREhLCgAEDMt3/+vXr07t3bwYOHMi5c+eIiIjg1q1bWX7e7NbdrVs32rdvz65du1i+fDmhoaGsWbOG3r17W+hoC3clwd4Gin6w1N674NCaNWtmTuOkVa5cOd58802KFCnClStXeOaZZ6hduzZ9+/alT58+JCcnM2TIEPz9/Zk9ezbDhw8nJiaGV155Jd16+vTpw8iRI1EUherVqxMQEMDRo0eZO3cu8+bNw2AwPDBmu0yZMixZsoQNGzZQrFgxkpOTH9i/tCnQ/v37ExYWxttvv42/v3+2wT67dVevrhUGa9q0KWPHjuX69ets376d9957L3cHM5/u6RM4kHSWeEMSVbzKUN27rMW3cebMaWJiYnjiiSc5ePAA/v7+BSqEtnTpIipVqkybNu3Mz8XE3GXfvr25KoR24MB+Jk16hzZt2jJ48HAWLZrP2bOnSUpKxs/Pj7Fj36J8+QoMHTqQhISEdGm47t17UbVqVXr2fBVFUdDpdCQmJtGgQUN69XqdceNGcf78eTZt+l++P5+lSbAXdhEeHk6DBg3Q6XRER0cTHh7OmDFj0i0zdOhQjhw5gr+/PwMGDMBgMHDkyBHi4uL44YcfiIqKokWLFtSvX5+DBw/y9ddfk5CQgKIodO/e3byeatWqYTAYmD9/Pv379wegRo0ajBw5ksaNG6OqKr/++mu6bS9YsIBGjRoxYMAAdu3aZU6p+Pr6cvXqVRRF4dChQ5QrVw6AiRMnMmrUKNauXUu7du3w8Mg6Q5rVugHz+3Q6Ha+++ipvvvkmLVq0wNvbuwBHO3v39AmMv7023XOLAvpbfDuOXPXy999/5caN6yxYoDXMdu3ayYIF4cyeHQ7A5MnTqFy5crr3RkVdoUqVKixZ8gkAer2eN97oR3BwK5Ys+YS2bUNwJBLshV00aNCAXr160bJlS/bt28f48Q/eefjKK6/QsmVLihQpQmBgIFFRUVSrVo1Zs2bxww8/oNfrmTRpEg8//DDXrl2jefPmeHp6MmLEiAda6r1792bGjBkEBQUB8P777zNy5EgSExOJj49nzpw56ZZv3bo1Y8eO5bvvvqN4ce1uzcTERAYPHszo0aOpUKGCOdBv2rSJU6dO8eGHH/L000/Tt29fcx4+M1mtO6MePXqgKAp//PFHno9vXpxKjgJA8SpHC7/H8dEVLCw4Y9XLEiVKcuLEcbZt20rDhk/TtGkQTZo8m6fPnZSURHJyks064vNKRuMAsffusW3fH8TExVGsaFGCGzfCv0iLZm+HAAAgAElEQVQRe++Wy5s+fTphYWGMHz+eyZOzvpXdXV25coX+/fuzZcuWPL0vr6NxbqfGMeXO1+bHLxd+luf8auVpm2mp6gmioq6kq3r57bfrWL78YwICAujUqav5DtPateswaFD/dFUvX3yxDX//fZgbN24wfvxEc9XLOXPm0a1bx3RVL5s3b5kujZP2rtgJE94iNLS1ueplWNiMdFUvM95BGxm5nw0bfiAycj+BgYGMGDGaJ55okGkaZ8aMD0hIiE+XxvHw8CQ0tDWtW7cFoG3bkGzTOLYejSMte2Dbvj+4GxsLwN3YWLbt+4NOLZrbea9cm6kDcvz48SxfvpymTZuaW90CNmzYwIwZM1i4cKHVt3U59RZvF+/EmeSrfHd/LyeSLxco2Dtj1cvTp0/xyCOVmDbtfQwGA3/++QeTJr1jDtaZp3Hi06VxHJ0EeyAmLi7bx8LyIiMjWbNmDUFBQTRt2pTIyEgJ9ml06NCBDh062GRbH8duTfe4SSGlQOszVb3s3LkrkZH7+f13rT8kp6qXXl5ebN68kerVa7B7985sq16WLFmS48ePERhYJt0ymVW9fP75oByrXv711x+cO/cvb789EQ8PD6pWrYqfnx+6jDvhxCTYA8WKFjW37E2PhXWl7YwNCgqSQG9HbxfvhJp8GYDa3hUp61WyQOtzxqqX3bq9wuLFC+jduztFihRBp/NgypT/RkBNnz4lXRqnRYtWPPNM1kXOHJHk7JGcvXAtcgdt7liz6iVIzt4h+RcpIjl6IdyQqerl0KEPTnGYX/fv32fcuFEWW5+lSMteCBcjLXvnILVxhBBCWJwEeyGEcAMS7IUQwg1IsBdCCDcgwV4IYRNnzpzm4MEDABw8eIDTp08VaH1Lly5i8+aN6Z6LibnL1q0RWbzDus6dO8fQoQPtsu3ckKGXQri55yM+4UbiffPj0r6F2RNq+aDliFUv3YkEeyHcXNpAn9njvHLGqpdpHTwYyYoVn2Iw6ImPj2fq1Bl4e3szZcpEypQpw+XLl6hduw7jxr3DjRs3mDp1EmCgVKmsyzE4Agn2QgiLunTpEsHBrdJVvezcuSutW7clICCAOnXq0rjxMwQHt6JwYT+WL/84XdXLP//cB4C3tw/h4QvNVS8bNnyKhQvnpat6mVHv3n1Zt+57OnbszIQJb/HSS6+Yq17OmjU9XdXLrJw9e5Z3353OQw89xOrVn/HLL9sICXmRixcvMH/+YgoVKkTXrh24efMGq1evoGXLEDp06MS2bVtZt+47ix9PS5FgL4QVuHMJDmesepnWQw89xLx5cyhcuDDXr0fz2GP1AK3uThHjd1i6dGmSkpK4cOEC7dt3AuDxx+s5dLCXDlohrMBUNttgMJjLZjuq0r6Fs32cV6aql1OnTqd582BzFcqcql4uWfIJXbu+TJ06jxmXSb/etFUvAY4fP/bAtjOregnkWPUyrbCwmUyc+C6TJk2ldOmH0qz7wZtbq1SpypEjf2e5P45EWvZCWIEzlc22dGesM1W9HDlyKHPnzk837WNIyIsMGTKAQoUKUapUANevX8/ys/bp04+pUyexbdtWypYtn+kykye/w6hRYwkIKJ3TobMqqY0jhBWs2/5LurLZxf39bVZsT2rjOJaPPlpC79598fPzS/e81MYRwgUEN25EcX9/dDodxf39CW7cyN67JOykY8cuDwR6e5A0jhBWIGWzhcnDDz9s710ApGUvhAvSkZf0rLA97fux7ZSHEuyFcDG+vj7ExcVIwHdQBoOBuLgYfH19bLpdSeMI4eR2795Nz549qVWrFgaDAV9fX6ZMmWK8o/PBgJ+cnIxer8fX19ci24+JiaFQoUL4+Ng2eDkvHb6+PpQpUybnRS3IJsE+6X4KFyOvE383iYeqFaNMzYJNaCyESK9Zs2asXr0agLi4OEJCQli6dCn16tV7YNmZM2dSpkwZ+vfvb+vdFHZk9WB//1Yi69/al+65kElPElDZ39qbFsItFS1alH79+rF+/Xq+//57fv/9d1JTUxk+fDiNGzdm7dq1+Pj4UL9+feLj43nvvffw9PSkSpUqLFq0iK+//prPP/8cvV7PxIkTGTFiBI0aNeL06dM0a9aMmJgY9u/fT/Xq1VmxYgUDBw6ka9euXLt2ja1bt3L//n3+/fdfRo8eTc+ePTly5Ajjxo3DYDBQqlQpli1bxuHDh5k8eTI+Pj68/vrrdO/e3d6HzeVZPdjvW6UC8OzAWgQqJTj5y2X8A+0/DEkIS3OkEgmBgYEsXLiQevXqsW3bNhISEmjWrBkRERH06NGDMmXK0KBBA+rXr8/PP/9MYGAg06ZNY+3atXh5eVGiRAm+/fZbAM6fP8+WLVt4+OGHqVChAjt37uTDDz+kTp063LlzJ9127969y48//sjp06fp1q0bPXv2ZNiwYSxbtoxatWqxevVq5s2bR/PmzUlISGDXrl32ODxuyarB3mAwcPXYbQqX8qXS04EA1OtUxZqbFMJuTCUSAHOJBHsNv7xw4QIvv/wyX331FaGhWrnflJQUzp8/b17m+vXrXL16lV69egEQHx9P8+bNqVq1KtWr/1d6uFSpUlSsWBGAwoULU6tWLQCKFStGQkJCuu0+/rhWr6ZChQrm11RVZfTo0YDWX/Doo48CUKNGzhUoheXYJGevT9GTGJdM1LFbePt6Ub6eY5cCFSI/bF0iITw8nAYNGqDT6YiOjiY8PJwxY8YQExPDypUr6dOnD02bNmXx4sXo9XrCwsKoWrWqsUaNntKlS1O+fHm++eYbihcvzubNmylSpAgXL17Ew+O/gXqZ1YTJSmbLVq9enU8//ZSKFSuyd+9erl69CpBuG8L6rBrsdTodlZ8J5NzeaL4f9TsAFZ4IkGAvXFKxokXTlUgoVrSoVbfXoEEDevXqRcuWLdmzZw+3b99m27ZtpKamMmnSJNq3b8/48eNp2bIlcXFxtG/fHn9/f5544gkmTpyIoijMnj2bLl26oNfr8ff359NPP+XixYsW3c8FCxYwYMAAUlJS0Ol0LF26lKioKItuQ+TM6rVxDHoDlw7f5O7lexQpXYiKT5TGy9czr6sRwuHZI2c/ffp0wsLCGD9+PJMnP1joSzgXa9bGkUJoQjipXbt20atXL/r378/y5ctZs2YNQUFB9t4tUQDWDPZyU5UQTioyMtIc4Js2bUpkZKQEe5EladkLIYSDkBLHQgghCkSCvRBCuAHJ2bsAR7pzUwjhmCRn7wLsOQVeVvJ6ApITlhCSsxc5cMTJrU2lAwwGg7l0gCWXF1mLvXePddt/YfWGH1m3/Rdi792z9y4JByDB3gVkvFPT2ndu5kZeT0COeMJyVrk5ccoJwf1IsHcBjji5dV5PQI54wnJWuTlxypWU+5EO2kw4W/7YESe3Dm7c6IFjaMnlRXppf7M6Xfo5aDM7ccqVlPuRDtpMOGKHp8ias52ccyOvnynjb9bDwwODwZDle+U37pikg9bGpNXjXFwxJZHXz5TxN2owGOjdoT2dWjQ3B/rdu3dTqVIlQkNDWfpBGOHvvcfOrf+zWerv8OHDzJo1y+rbEZmTNE4mbF2qVhSMK56c8/qZcvubTTtXbWJiIvXr12f+++/b5EqoXr16mc6JK2xDWvaZcMQOT5E1V+zczetnys9vNjY2Fk9PT44ePUpwcDAhISG0b9+eixcvkpCQQLdu3QgJCeH5559n27ZtAPQb2J+nmjfhsWee5MM1SwHYs2eP+f2DBg0iOTmZU6dO0aJFC0JCQmjZsiWXLl1i9+7d9O7dm82bN/PGG2+Y96NJkyZER0fzww8/8MILLxAcHCzlmq1AWvaZcMQOT5E1V+zczetnyu1vdufOnYSGhqLT6fD29mbu3Lm8/fbbLFmyhHr16rFp0ybGjx/PxIkTuXnzJuvXr+f69eucOnWKq3evs2n3Vlp+OxId8NNvhxhjMDBs2LAH5rFNSkqiQYMGzJw5k99++427d++a9yE0NJRJkyZx7949Tpw4QZUqVfDy8mLmzJns2bOHwoUL069fP7Zv306LFi0KeiiFkQR74fRc8eRsqc9kmrowKCiIw4cP8/DDDxMREZFumaFDh5rTK88++yxTpkyhdu3a9O3blz59+pCcnMyQIUO47BPLkxM6cuLdjRSJ96TtSx2znMf27bffJjw8nA4dOlC8eHGmTp1q3p6npycdO3Zkw4YN/Pnnn/Tp04ezZ89y48YNOnfuDGhXHf/++2+BP7/4jwR7IVyYaerC/v37s3TpUurXr//AMmXLluWff/7hscce49dff6VatWocOXKEuLg4fvjhB6KiomjRogXfb93IraMXqb/oVVITkwl/YRYje7yR6Ty2mzZtokmTJkyYMIFvv/2W8PBwXnvtNfM2e/fuzfDhw7l16xbh4eHcvHmT8uXLs3HjRry9vfn888/Nk5cLy5BgL4QLCwoKon///oSFhfHqq6+SnJz8wDKLFy9m7NixGAwGvLy8WLp0KWXLlmXWrFn88MMP6PV6Jk2aRGIpT+rEPcTB11Zy0xDHcwPa4ePjk+k8tnFxcQwcOJAPPvjAPNl5bJoO5MqVKwPQtm1bPDw8eOihhxg+fDghISGkpqZSqVIlunTpYqvD5BZknL0QLsySUxf2v/RxusejAlpT16+iJXZTGMm0hEKIfLHk1IXvBnblWOIlAOoWqkh571KW3FVhZdKyF0LYRFxqAn/FnyFen8SjvmVQfMvZe5ccjrTshRBOLU6fwKio1emeW17hjSyWFtYgwV4IYXVq4hUAavuWJ8S/Hj46CT22JnfQCuECHKU+/blz5+jevTuhoaG0aNGCUaNGERsbSxXvQPaN/4ptP29j3o0tXE6+BcDVq1cZNWpUnraRkJDAqlWrcr38pEmTePrpp9m9e3eetuNqJNgL4QIcoRhcfHw83bp1Y/To0URERLB9+3YaNmxInz59uJh8k3qFKvFC0ToAHEu4DMDDDz/M/Pnz87Sda9eu5SnYr1u3ju3bt9O0adM8bcfVyLWUEC7AEYrBRURE8Pzzz/PUU0+Zn+vRowfLly9n1qEvOJJwnr8+OU5KfBIndMUJ+fhRvLy86N27Nzt37mTPnj289957eHp6UqVKFRYtWkRKSgqDBg3iwoULJCUlER4ezpo1azhx4gSzZs3inXfeMW/r0KFDvPnmm3h6euLr68uSJUtYu3YtUVFRdOnShQ0bNuDn5wfAzJkzOXnyJNHR0dy5c4cPP/yQJk2aULNmTWrUqEHNmjUZMWIEw4YNIyEhgUKFCrF48WJSU1Pp0aMHDz/8MJcvX6ZVq1bp7g52ZBLshbARa9bdd4RKrf/++y9VqlR54PlKlSrRLv5x4rwPUr7JI0wZN5F/tv/JpEmTCAsLA7SSzJnV2ImLi+ORRx5h9erVnD59moiICN566y2OHj2aLtADDBs27IEaP1988QVr1qzhxx9/pFChQumW9/Pz46effuLYsWO8/vrr/PHHH1y6dInffvuNgIAAevXqxeDBgwkJCWHHjh1MnjyZqVOncuHCBTZs2EDx4sUJDg7m4MGDPPHEE9Y7sBYiaRwhcqmgeXFrplrsWak1PDycXbt2Ua5cOc6fP8+uXbsIDw83v3727FkaVKlDOe+SvN68G+W9S9G4cWNOnToFQKpBz/pzv3Ip6jJde7xMaGgo27dv58KFC5w6dYpGjbTPUq1aNYYNG5blfkRFRaWr8XP8+PFs99t0v0Ht2rW5du0aAAEBAQQEBABw9OhR5s6dS2hoKGFhYURHRwNQt25dSpUqhaenJw0bNjR/DkcnwV5YjaN0GlpKQYO1NVMtpsJpGScsyUpcagI74o6yJeageaRMfpnq75QoUYJNmzbx6quv0qBBAwBWrVpFQECAucW/f/9+AH7//Xdq167NPX0i55Ovs8nzCD5l/Km8oIO59R4UFISiKERGRgLalUOfPn3w8PBAr9c/sB+mGj+AucZPdg4ePAhoQb1cOW3Mv4fHfyGxRo0aTJ8+nYiICBYuXGgu0qaqKvfv3yc1NZX9+/dTs2bNfB87W5I0jrAaU3AEzMHRmatTFjRYO0KqBSw/5j0oKIg1a9bQunVrABo1asS0adNISUmhbt266TpT//rrL1588UV0Oh0fffQRf8efB6COX0WazH6fz4Yuozk/mmvsNG7cmEGDBplr5syePZuHHnqIpKQkJk2axIwZM8zrzqzGT3b+/vtvWrduzf3791myZMkDr7///vuMHDmSxMRE4uPjmTNnDgA+Pj706NGD6OhoOnbsyOOPP87OnTvZu3fvA6klRyJ30AqrWb3hx3QTX+t0Onp3aG/HPSqYgs7b6ihz5UbGn2XZzZ/TjXmv7lu2QOvctWuXOdhv2bIl1yUZItW/6TLwNYK/0NIzPUo8RzPjiB1rmjlzJmXKlKF///55et/58+fNHcrWIHPQCqfkajNIFTQvntdUi7VU8Q4E4Fji5XRj3vPLVGxty5YtbNmyhV69erFr164c33f58mUG9h1A346v0b3Es9o+GYdkCsuTlr2wGkdpyYr0Dsefp5RnUU4lRfHlnd94slAVhpRule/1pZ0gBbTgHxkZyZgxY3J8r1TSTM+aLXsJ9kK4mewCrK2LlV1MuimVNNOQYO+EpFUrHFVWATZOn8CoK1KszJ6k6qUTcrWRKMJ1VPQJoKJPwAPPO0qxsoxXF+W9SklpZAuQYG8ljnD7uhB5kbbj9ljiZXqUeK7Ao3TyKrNhoRnJ1Ub+SLC3EkcZUy1Ebl1Mvsm7gV3NHbfHEi7bZBhkWhmvLv5NimZ9zH67X224AjlyVhLcuNEDOXshMnKkvp1FNyPSPW5apJbN9yHj1UWnYk+le2yPqw1XIR20QthRQW/UsqScRsbYYqROxmGhj3qXoUfJ5y02TNTRSQetEE4qp5a7I/XtZNVxC7abVjDj1cWZ5Gu8F/2d+XHTIrVkLtt8kmAvhBXlNCrLWfp2bDVS593ArumuLvQGQ7rHxT0Ly/DQfJJgb2eOlLMVuZOX7yynlruz9O3YaqRO2qsLUws+1aDXhmB6lyIy/ixg/+Ghzkhy9nbmSDlbkTt5+c5c5fu1dImFnGR1g9etlDjeuvqF+TlbFU6zFcnZuzBHytmK3MnLd+YsLfec2HqkTlZpI9Pw0CMJF/g+5k9+iTtKWe+SkrfPBbcL9o6WNnGWnK34T16+M1OlS2eXMZdu7Ro2WaWNMp50rqTcZs71jZK3zwW3S+M42mW1o518RM7kO8u7vI6gySptdDHpJlvj/mbv/ZNU9QmkQ7GGFqnH7yikEJoFudqEGkJYgyWHN+anwFp2lTkvJt1MNxzTlfL2krO3IEmbCEfgyFcHlh5Tn59hm1mljeL0CekCPWCXsg7OyO2Cvat0mAnn5shVUS09pj4/wzZzqsyZlj3KOjgjtwv2rtJhJpybI4/CsvSYeksWWDPtm0lb/yfcemarvHC7YA8Fv4R25EtwYR2W/s4dOZ1o6eqXlhy2mXHfriTfyfe63I3bddBCwUfkONqIHmF9lv7OHbnBYIl5YdN28Pp7FuK+Pgko+LBNV5+zVjpoLaygl9COfAkurMPS37kjpxMLOqY+Lx28eT3p2Xq8vytxy2Bf0EtoR74EF9bhCt95bodTZlf9Mjfy0sGbU0d1ZvtckH1zZ24Z7As6IkdG9LgfZ//ObVWiGPLWwZvdFZMt99kduGWwL+gltCNfggvrsMV3XpA8fk7vteVk4nnp4M3uislRJkB3FR723gEhhMaU0jAYDOaUhqXem7a1Pe/GFi4n37Lovqe16GYE70V/x5d3fgOyH30T3LgRxf390el0FPf3T3fFZMt9dgdyqhTCQRSkEzin99pyMvG8dKJmd8Vk7X0+duwYkyZNIj4+nri4OEJCQpg4cSI6Xf4GxPz111/07duXTp06MW3aNIvtp6VIsHcwjjwkT1hXQTqBc3qvLUsUZ+zgzW+dHWvu8507d+jduzdfffUV1apVIzU1lR49erBixQr69++fr3Vu27aNIUOGMHjwYIvtpyW55Th7RyZj+N2XNXP2OU0mbi35KYJmYs19Xrt2LYcOHWLu3Ln/7WtcHD4+PkyZMoW9e/cC8NJLLzF06FAGDhzIrVu3uHXrFt9//z3z5s3j999/JzU1leHDh/PII4/Qs2dPfHx8mD59OiVLluS9997D09OTKlWqsGjRIr7++ms+//xz9Ho9EydO5Pbt2yxatAhPT0+eeeYZpk+fTpEiRd4DqgCBQCVgtMFg+J9Op2sLvAvogAPAIOB5YCaQCpwB3jAYDMlZfWZp2duYM01AnRdyRVJwBekEzum9eRlOacmKlwXpZC3pWQQfnRfx+iTi9An53ofMREVFUaVKlXTPFS1alJ9++onz58+zc+dOUlJSCA4OJigoCICgoCCGDx/O//73P86fP8+2bdtISEigWbNmRERE0KNHD8qUKUO7du2oX78+P//8M4GBgUybNo21a9fi5eVFiRIl+Pbbb7l16xYtW7Zkz549FC5cmH79+rF9+3bTriQaDIYXdTpdS2CsTqfbDiwGnjYYDNE6ne4toCLwKfCc8bnpQB/jc5mSYG9jrjIBdUaOXNgrL9z9pGXp4Y75rbNjjWGX4eHhNGjQgKCgIB555BE2bdpEYmIiY8aMAeDcuXMcPHiQJk2aoNPp8Pb25qmnnuLEiRMAVK9eHYCjR49y8OBBQkNDAUhJSeH8+fPm7Vy/fp2rV6/Sq1cvAOLj42nevDlVq1Y1r+Ps2bPcuHGDzp07AxAbG8u///5rWsVB4/8vAoWA0sBtg8EQDWAwGGbrdLpAoCzwrbGPwQ/4ObvPL6NxbCw3E1BnNTrBkTnrFUlGBRkR4wrStsRHl27N2w8VbK4HUydr9xLPAlo5YnvsB0CDBg3o1asXu3btolixYmzYsIFy5bSrluTkZMaPH0+JEiXMKZzk5GT++OMPHn30UQA8PLRwqSgKTZs2JSIigi1bttC5c2eqVq1q3k7p0qUpX74833zzDREREbz11lvmqwPTOipVqkT58uXZuHEjERERDBo0iKeeesq0iozp8mighE6nKwWg0+kWApWBS0AHg8HQDC2d80t2n19a9jaWU8vdWcfwO9sVSVYteFc5aeWXpSte5reT1dL7AVoaZs2aNbRu3RqABQsWsHr1alatWkVsbCytW7dm8ODBXLx4kRdeeIGkpCQ6d+7ME088kW49rVu3Zvfu3bRs2ZK4uDjat2+Pv7+/+XUPDw9mz55Nly5d0Ov1+Pv78+mnn3Lx4kXzMg899BDDhw8nJCSE1NRUKlWqRJcuXTLdb4PBoNfpdEOAzTqdLhWt5f8XMNL4nAcQA/TK7vNLB62NuWqaIC+fK6/HwBrHLKuOcEt1kDvr95zVdID5ld9OVkvvh8muXbvMwX7Lli3mFrejkGkJhUvJa0C1xgilrKantFSQdtZRVY5SVdIa+7Fr1y569erFmjVrAMz/dqSAL1UvhUvJa6rEGqmVrNJOlkqjOWs6yFGqSlpjPyIjI9MF9zVr1hAZGelQwd6aJNgLm8trft8a/QHWLmzmbH0YJvmpeGmNlFVBK29mxjTqxiQoKMhtAj1IGidTzppvdRaOkLO39nbc6TfkrCkrRyQ5exuzxY/XnYKBs5CglT9Z9X+IvJOcvY3ZIt/qKjchuRJLfO+ueBLP6TM5a8rK3Uiwz4QtfrzO2oHnyizxvWd3ErfFicAa28ipYWKN/g9XPGnam9xBm0HsvXvo9XrzY/8iRaxyF2vGQCKtIfuzxN3L2Z3EbXF3rjW2kVPDxDSCqXeH9nRq0dwiQdnd72S2BmnZZ7Bt3x/E3rtnfuzh4WGVFoUzTHNniL+G4beh6J5dis4v0N67Y3WWGHaZ3dWBLa7mbDlM1ZrkytfyJNhnYKsfmTOURTAcWQDX/8RwZAG6p2aan5dL7KxldxK3RdB0xmGqmZF+AMuT0TgZyIgMjSH+GoaNz0JqIngWQtfuN3PrXo5R/jhrzt4eXOVz5JU1R+O4ZM4+9t491m3/hdUbfmTd9l/SpWVy4qxVJy3NcGQBmBoCBr322EgusfPHGrlte2zDGnbv3k2lSpUIDQ0lJCSEDu3aob9zm94d2lO1dACLFy4s0Po/++wzkpOznNcjnVq1apGQkL5+/tatW/nss8/ytM2LFy+yZcuWPL3HmlwyjVOQYY3OkF6xiRuRoE/S/q1Pghv7zS/JJbZzcvTWcrNmzVi9Wqthb5oTtlq1atSrV4969eoVaN1z5syhe/fueHt75+v9rVrlvQjbzp07OXnypLnwmr25ZLB39panI/xRerz4vyxfc4bOZfEgZ7q3o2jRovTr14/169dz9+5dVqxYwerVq6lZsyY1atSgZs2ajBgxgmHDhpGQkEChQoVYvHgxFSpU4IMPPmDjxo2kpKQwYMAAvLy8uHbtGr179+abb74xb+POnTv069eP2NhYUlJSmDJlCs2aNQNg+PDhXLhwgcDAQD755BO+++47Tp48yfTp01m2bBnffvstOp2Orl27MmTIEE6fPs3QoUNJSkrCz8+PlStXEh4ezv3792ncuDFt2rSx05H8j0sGe2dveTr6H6Vc/TjGCTmv7NUIyu+xCgwM5NChQ+meu3TpEr/99hsBAQH06tWLwYMHExISwo4dO5g8eTIjR45k69at7Nq1i9TUVKZMmcKsWbMICwszXzWYfPDBBzRv3pyhQ4dy5coVgoODOXr0KAADBgzg6aefZuLEiaxcudJcr/748eN8//33bNu2DYC2bdsSHBzMpEmTGDt2LK1atWLz5s38888/jBkzhpMnTzpEoAcXzdk7e97d2a9MbK0gfTT55YzjwO11b0d2xyo8PJxdu3YBEB0dTXh4uPm1CxcuUL58+XTrCggIICBAK5B29OhR5s6dS2hoKGFhYURHR3Pq1CkaNGiAp6cnPj4+hIWFYZy27wGqqvLss9oMWuXKlcPf35/o6Gh8fMC/Ao8AACAASURBVHx4+umnAWjcuDGnTp0yv+fYsWNcuHCBNm3a0KZNG27dusWZM2c4deoUjRppcaZNmzYEBwcX9LBZnEsGe2ftpDKRG67yJrtgYq0TgTOekO3VCMruWJmmClyzZg379u2jQYMG2jIxMaxcuZJOnTqle69pWj+AGjVqMH36dCIiIli4cCGdO3emRo0aHD58GL1eT3JyMm3btiUxMREPD490N0uCNr3g77//DsCVK1e4c+cOAQEBJCUlcfjwYQB+++03ateubX5P9erVqVWrFj/99JN5kvG6deuiKAqRkZEAfP311yxbtizTbdqTS6ZxsuMMl9+SE8+b3Ny1CpZNiTljqtBe6bfsjlVQUBD9+/cnLCyMwoULM2vWLD744ANSU1OZNGkSNWrU4OrVq5mu9/3332fkyJEkJiYSHx/PnDlzqFevHsHBwbRo0QK9Xk///v3x9fWlSZMmdO7cmZ9++snc0h83bhyDBw9m/fr1xMfHs2jRIry8vPD19eWjjz7izJkzVKxYkenTp/P1118D8Pjjj9OsWTOCg4NJTEykYcOGlCtXjpkzZzJ8+HBmz56Nn58fK1as4MKFC8yZM4f69evTrVs3Kx7h3HG7cfYyRtz1ZPedWqsiozM0GhxFdsfKNHtU//79Wb58ucPNHGWycuVKLl26xOTJk626Hal6aUHOePntDOwZ/Oxx16p0Uudedscq7exRTZs2dciZoyIiIliyZAkLCzjW396kZe+ALXtnrEnjqMdVWuDCmUjL3oKcIR+eVU0aR+aoV0zSAhdC43bB3tH/+A3x1+DfbwED/PsthrojnaJ174wdlkK4E5cceulsDPHX0G/riiE+OtuaNI7M2e9tEOnZ494FYV1ul7N3RPq/JsDptVCtp1aD5s6x/14sUTvb0gVCWIOj9sG4OsnZ24g9OkYzpm3SlhJ2VtIp6vwctQ9G5J/DpnHSpjZsts00HaM23aYTpm2y44ylBER6Wd3FLekd5+W4wd7GgfeBjlFbnWSyKSXsrKRV6Pyy6oORE7nzcsg0jq1GpKRN22TWwrbFsEdXzMfLyBznl9WoNTmROy/HDPY2Crzprh4s0MKWXLXGGe5lcAaO+HuSE7nzcsjROPqfQqw+IiW7OVbzS0YwCEtyxN+TI56AXInbjcaxRWrDGlcPconr+JwpWFni92Tpz+voNyWKrDlsB63V5SFtk9uRQVKH3vE5UwejJX5PzvR5hXW5bbD3ePF/eLx68b//srmayO3IILmL1PE509WXJX5PzvR5hXU5ZBrHkeRlZJBc4jq+zDoYrZnaKci6LfF7kg5VYeK2LfvccsWbntxZZq1la6Y67J1GkatNYSIte3Iok+AgNz05Y417R5RZa9maqQ57p1Fc8WrTmTrZHYm07Mk+J2/K7es67oeHGqFr9rkd9tA+pRzchTU71qXT3vLsfbXkrNw+2Oe2TII9g63dSjm4CWumOiSNYnn2vlpyVm6fxsnNeHt7Tyhir1IO7sKaqQ5XTKPYm3Q654/bt+xzk5O3eyetg/QbCOEI5GopfxyyXIIlWaJj0xblG4TIjHRGuhdrlktw+WCfdhYoD0l9CCfjiPVxhPVYM9i7dBpHOjaFs5POSGEprh3s7Z1rF6KAZOimsBSXDvbSsSmcnXRGCktx+Zy9EEI4C8nZCyGEKBAJ9kII4QYk2AshhBuQYC+EEG5Agr0QQljQzfuphP9xh36bHOu+HrcvhCaEEJZw834qq/+J5acz9zEYIFlv7z1KT4K9EEJYwHt7bvN3dBKOOj5d0jhCCGEB7zYtSfsahfH1BG8HjKxyU5UQQljQzfhUVv8dy9HrSaxom7dKu1L10gFIqVnnId+VcFZyB60DkHkvnYd8V0I8SIJ9LkmpWech35UQD5Jgn0tSatZ5yHclxIMk2OeSlJp1HvJdCfEg6aAVQggHIR20QgghCkSCvRBC5JGj1r/JjpRLEEKIXHL0+jfZkWDvIuRGIiGsz9Hr32RH0jguQm4kEsL6HL3+TXacbHdFVuRGIiGsL8DPkzGNSvBVpzK0rlaYaiWdJzniPHsqslWsaFHuxsameyzcl6T1rMsU9J2JtOxdhNxIJNKStJ7ISFr2LsK/SBE6tWhu790QDkLSeiIjadkL4YKkPpDISIK9EC5I0noiI6mNI4QQDsKatXEkZy/MZASHEK5L0jjCTEZwCOG6JNgLMxnBIYTrkmAvzGQEhxCuSzpohZnk7F2bfL+OTyYvEVYngcD1SZ+Me5NgLwAJBO5A+mTcmwR7AUggcAfSJ+PeJNgLQAKBO5C7at2bdNAKQHL2QjgCa3bQun2wlyAnhHAUMhrHiqRjUgjhDtw+2EvHpBDCHbh9ITRnn85P0lBCiNyQnL2TB8t1239Jd7Iq7u8vM1bZkLP/foRjkRLHVuTs0/lJGsq+TH0+gLnPx5l/T8J1uX2wd3YFTUNJy7Rg5GQrnIXbd9A6u4LeKCOjkQpGbkYTzkJa9k6uoGkoaZkWTHDjRg9cGQnhiCTYuzlnH41kb87e5yPch6Rx3JzUSxHCPbj90EshhHAUUi5BCCFEgUiwF0IINyDBXggh3IAEeyGEcAMS7IUQwg1IsBdCCDcgwV4IIdyABHshhHADUi7BRUk1SyFEWtKyd1FSzVIIkZa07J1MblvsUs1SCJGWtOydTG5b7FJnXfx/e3cfGFdZ5wv8O2d6Oi9tgeS0m6aFaROV8V4aux1w1XirA9VdV8CdNYqr3N7gDmWLrRJZxcXXdRHrVsSIIOVllFKXVdjs5lpAvFh22O5mQdjBkhY4og2EbdIYT6bSdpLJyZy5f5xJmvfMZM6Zc86c7+cfOO3kzDNJ85tnfs/v+T1EkzHYO0yxM3Z2sySiyZjGcZhi+8+zzzoRTcaZvcNwxk5Ei8F+9kRENsF+9kREVBbm7IlcgJvsiGkcWhADhfP9y4Enpizsn71iBRfwbYhpHLIUd+M6HzfZEdM4LnZR55343Uhm4nqlP4hnY9fOeBwDhfMVW7JL1YvB3sUmB/rZrsctFCiKfdMg67zn7W+bkYojd2GwpwUtFCiKfdMg63CTHTHY04LGA8VFnXfid4MZYP9zADiDJ3ISBnsXW+kPzki/zGe2GfxFnXeaMjaqbqzwqjwGexczYlb+u5FMyW8aROMVXgAmKryYZjIXgz2VjakcKhUrvCqPwZ6KNn0GPxdW59BCWApaeQz2LlJuEH42du2s95iO1Tm0EJaCVh6DvYsYEYQ5QycjsBS08hjsHcgulQxM1xA5B3vjOJBdetXM9Ulheicn0zo7EZVByeRw69MnEH/4t1YPpSI4s3egxVYyVKpEcnprVLZKJTtRMjns7T6Jn/4mg3weUDWrR1QZDPYOtNhKBqZYiICvHkzj+d+Oum4SwmDvQMVWMpidU+dmKnKir7yrBnufP4nHfpOBxpk92VmxlQxml0DO9cbBNwGqhPF0zJHBUSQu+4Oiv04KeHH9285B61tWYO/z+te7AYM9GY7pIjKTUTn38aDvFgz2VBSWWZJduDXnXi6WXlax6emTctIp3BVLdvGVd9XgA+cH4fMCIiNY0XjguAstZpa+/kffmvFnnN2TlZTh3ETOvZScvZ2ZeeA40zguZNQsnbN7spLbcu7l4ocgKgoraoicjcGeisJ0DZGzMdi70GIXbo1c8CWiyuICLRGRTZi5QMuZPRGRCzDYExG5AIM9EZELMNgTEbkAN1W5DHvcELkTZ/Yuwx43RO7EYE9E5AIM9kRELsBg7zLcBUvkTtxBS0RkE9xBS0REZWGwJyJyAQZ7IiIXYLAnInIBBnsiIhdgsCcicgEGeyIiF2CwJyJyAQZ7IiIXYLAnInIBBnsiIhcoqTcOERE5E2f2REQuwGBPROQCDPZERC7AYE9E5AIM9kRELsBgT0TkAgz2REQuwGBPROQCDPZERC7AYE9E5AIM9kRELsBgT0TkAgz2REQuwGBPROQCDPZERC7AYE9E5AIM9kRELsBgT0TkAgz2REQusMTqAZBzhMNhL4DrAHwM+r+dpQD2A/iyLMvZBb42D2CVLMu/M2FcKwEMyrLsMfp5w+HwKwA+JMvys2UNkshinNlTKe4E8A4AW2RZ/kMAbwUQBnCvpaMiogVxZk9FCYfDDQCuBFAvy/LrACDL8ulwOLwdQHPhMWcDuAPAHwLIA/gpgM/Lsjw27V5fAvBRAGMAfgVgpyzLx8PhcBLAEIA3Q39juR/AdwA0ARABHADwWVmWx8Lh8AcB3AwgA+CZBYZ/czgcfiv0yc0XZVl+OBwOPw7gIVmW7y6M6QsAVsqy/Okivx/LCmM8H0AtgJPQP/EMAzgC4FxZln8fDoc9AGQAHwbwyjyvJwvg/wLYCOBKfpIgo3FmT8WKADgyHujHybJ8XJblfy5c3gZAgR7MLoIeuD4z+fHhcPjjAP4UwFtlWX4LgMMA7pv0kLQsy/9TluXvAvg2gP+SZflCAJsArARwfTgcrgPwfQAthb97dYGxH5VlOQLgfwPYGw6HV0F/U7q6MCah8P97iv1mFF7DCVmW3y7L8vnQ33B2yrLcCz2IX1l43MUAFFmWD831egqPWwpgvyzLYQZ6MgODPRVLw8L/Xv4UwO2yLOcLOfw9hT+b/pgfyLJ8unD9HQBbwuHw0sL1wUmPvQzAX4XD4V8C+C8AfwT9jeR/AeiWZfmFwuPuWmBcewBAluXDAF6AnoraD2B1OBzeCOBPAPTIsiwvcJ8Jsiz/E4D7wuHwJ8Ph8HcARAEsL/z1HQC2Ff7/r6B/Apjv9Yyb/NqJDMU0DhXrFwD+RzgcXiHL8snxPwyHw2sB3A3gQ5j5ZiBAT1dM/7Pp10sAjC+unpr0d14AH5Zl+cXCc50DPT20ZdLjAT0dNJ/cpP/3AFBlWc6Fw+E9AP4SwBqUNqtHOBy+FsA1AG4H8AD09FND4a9/DiAYDoe3AHgXgNYFXs+4ya+dyFCc2VNRZFk+BuAfAHw/HA6fBQCF/34PeppiGMDPAOwIh8OecDjsgx4MH592q58B+Hgh5w0AnwLwb3NU8/wMwKcn3e8nAHZCnwFfUJiVA8BVCwz/qsJ4IwDeBODpwp/fC+DPAVwI4F8WuMd0fwLgPlmWE9Bz8pdDD+aQZTkP/ftyL4AHZFkeWeD1EJmOM3sqxScAfAlAVzgcHgPgA9AJ4CuFv/8UgO8C6Iaeg34M+iLqZAkA5wH4RSFX/mucyW9P9ynoaZ5u6J8Qfg5gtyzLajgc/hiAfwiHw6MAnlxg3I3hcPg56LPov5BleQgAZFn+bTgcfhbAi7Isq/N8/b+Fw2Ft0vUNAG4BcHdhDSIHPS0zOSWzF8C3MDXFNOvrWWDsRIbw5PP5hR9FVIUK9fnPAHiXLMuvGXzvvwDQKsvy9DULIktwZk+uFA6HtwH4OoCvmxDokwDqALQYeV+icnBmT0TkAlygJSJyAQZ7IiIXKClnn8lkmPMhIjJJMBic0czPKJzZExG5AIM9EZELMNgTEbkAgz0RkQtwUxUROdbY2BgGBgaQzY5iak85O/PA51uKuro6LFlSuRDMYE9EjjUwMABRDKCmpg4ej2mFLIbK5/M4dep1DAwMYO3atRV7XqZxiMixstlRLF9+lmMCPQB4PB4sX35W4dNI5TDYE5GD5R0V6MfpY65s2onBnojIBRjsiYjKdOTIYezYcY3Vw5gXF2iJyDWGhk5i+/bv4dChHmzc2IA9ez6B2toVZd3zhz/ci8ceexSBQMCgUZqDM3sico3t27+HgwePQFFO4uDBI9i+/Xtl33Pt2nOxa9c3DRiduRjsTfDrX48iFPoVli9/CaHQr/DrX1d21Z2IZnfoUA9UVT9/XlVzOHSop+x7XnzxlorWyy8Wg70JLrnkFSiKhnweUBQNl1zyitVDIiIAGzc2QBS9AABR9GLjxgaLR1Q5DPYmGBrS5r0mImvs2fMJbN58ASRpBTZvvgB79nzC6iFVjP0/ezhQba0ARdGmXBOR9WprV+DBBz9n9TAswShkgieeWA9JEuDxAJIk4Ikn1ls9JCIyUX39Gtxzz31WD2NenNmb4I1vXIre3vOtHgYR0QTO7ImIXIDBnojIBRjsiYhcgMGeiMgFGOyJiFyA1ThERIs0NjaGm2/+Ko4f78fo6CiuuiqOzZvfbfWwZsWZPRG5xtDQCK644qcIh/fhiit+iqGhkbLu99hjj+Lss8/BnXfei29/+7u49dbdBo3UeAz2ROQa27f/Kw4e7IOijODgwT5s3/6vZd3vkkveg23btgPQz5b1eu2bLLHvyIiIDHbo0O+gqnorE1XVcOjQ78q6XzAYBACcPn0aX/jC53DNNdeWPUazcGZPRK6xceNKiKIe9kRRwMaNK8u+58DAcXzyk9vxvve9H3/8x+8r+35mYbAnItfYs+dibN68BpLkx+bNa7Bnz8Vl3W9oSEFb20584hOfxGWX/ZlBozSHJ58v/oTzTCZT2ePQiYjmcfToUdTXr7Ps+b/97Vtw4MDjWLfuzBhuvfU2+Hz+Bb+2v/9VNDY2TvmzYDDoMXyQBQz2RORYVgf7clQ62DONQ0TkAgz2REQuwGBPROQCDPZERC7AYE9E5AIM9kRELsB2CUREi5TL5fCNb3wNvb2vwuPx4LOfvRFveMMbrR7WrDizJyLXGBrK4YorjiEc/g2uuOIYhoZyZd3v3//9IADgrru+j2uuuRZ33fU9I4ZpCs7sicg1tm8/joMHM1BV4ODBDLZvP44HH1y76Pu9+91RvPOd/wsAcPz4caxYscKooRqOwZ6IXOPQoRGoqv7/qqpfl2vJkiW46aav4Mknk7j55r8v+35mYRqHiFxj40Y/RFH/f1HUr43wpS99FT/+cQe+8Y2vYXh42JB7Go3BnohcY8+e1di8OQhJErB5cxB79qwu634//ekjuP/+HwAA/H4/BEGAIJjW3qYsbIRGRI5ldSO04eFh3HzzV6EoCsbGxrB1ayve9a5oUV9b6UZozNkTES1SIBDA1772DauHURSmcYiIXIAze6p6uRwwMODDyIgAv19DXV0WXq/VoyKqLM7sqeoNDPiQyQjQNA8yGQEDAz6rh0RUcQz2VPVGRgQA4+tensI1kbvwXz1VPb9fAzBeSJYvXBO5C4M9Vb26uiyCQQ2CkEcwqOfsidyGwZ6qntcLrFmTRWPjMNas4eIsGW9oaAix2KV45ZVXrB7KnBjsicg10mlg27YAmpuXYdu2ANLp8u85NjaG3bu/Dp/P3gv/DPZE5Bo33BDAU095kU4LeOopL264IVD2Pb/73XbEYi1YuXKlASM0D4M9EbnGkSMCxsb0yqyxMQ+OHCkvBD7yyH6cc845ePvb32HE8EzFYE9ErnHBBRqWLNErs5YsyeOCC8qrzHr44Z/gmWd+gR07rsHLL/8KN930ZSjK74wYquHYCI2IHKvURmjptJ7KOXJEwAUXaNi9exg1NcaMZceOa/DZz34e69evL+rxbIRGRGSSmhrgnnvs2W/ebAz2REQGuOOOu60ewryYsycicgEG+wpQlJOIxXYhFNqGWGwXFOWk1UMiIpdhsK+AePx2JJOHoSgnkUweRjx+u9VDIiKXYbCvgFTqKFQ1BwBQ1RxSqaMWj4iI3IbBvgIikUaIot6QRRS9iEQaF/gKIiJjMdhXQCKxE9HoBkjSCkSjG5BI7LR6SETkMiy9rABJWoHOzhutHgYRmeCqq67EsmXLAAD19WvxxS9+xeIRzY7BniyRTgNtbT50dwtoatLQ3p41bCcj0VyGh4H9+wM4flzA6tUaLr98GIEyeqFls1nk83nb19gDTOOQRdrafOjqEjA05EFXl4C2Nnu3h6XqsH9/AK++6sXwsIBXX/Vi//7yul7++tcvI5sdwXXX7cDOndtx+HC3QSM1Hmf2ABRlBPH4AaRSg4hEViGR2AJJ8ls9rKrW3S1AVfU2IKrqQXc35x1kvuPH9YPnAUDTPDh+vLx/d36/Hx/96FZ84AMxvPZaL66//lP40Y86sGSJ/UIrf8MAxOMHkEweg6KMIJk8hnj8gNVDqnpNTRpEUe+rJ4p5NDXxXFgy3+rV+vGUACAIeaxeXd6/u/POC+F97/tTeDwehELrcPbZZ9u26yWDPYBUahCqqv/QVVVDKjVo8YiqX3t7Fs3NGmpr82hu1nP2RGa7/PJhrFuXQyCgYd26HC6/vLymaA8//BPcdls7AGBwcBCnT5+GJNnzEBP7fdawQCSyCsnkMaiqBlEUEImssnpIVa+mBti7lwGeKisQAK64wriul5df/mf42tf+Ftu3x+HxePCFL3zZlikcgP3sATBnT+RUpfaztxP2s7eAJPnR2Xmp1cMgIjINc/ZERC7AYE9E5AIM9kRELsBgT0TkAgz2REQuwGocIqIy3H//D3Dw4L9hbEzFBz/4IVx+eczqIc2KwZ6IXCOXA9LpAFRVgChqqKkZhte7+PulUs+iu/t53HVXAiMjI3jggX3GDdZgDPZE5BrpdADZrBeAB9msB+l0ACtXLn5H7dNPP4U3vOGN+Ju/+QwymdPYseM64wZrMAZ7InINVRUAjG9S9RSuF+/EiRM4frwft9zSjr6+Y/jc567HP/5jBzwe0zbCLhqDPZEZcjn4BgYgjIxA8/uRratDWfkCMoQoashmPdADfh6iWF7Xy7PPPhvr1q2HKIpYt249li71IZ1Oo7a21pDxGonVOEQm8A0MQMhk4NE0CJkMfAMDVg+JANTUDMPny0EQNPh8OdTUlNcUbePGP8TTT3chn89jcHAQw8PDOPvssw0arbE4sycygTAyMilZoF+T9bxelJWjn+6d79yMX/4yhXi8Ffm8hr/+68/Ba9NPcAz2RCbQ/H59Zg8gX7im6mTnRdnJmMYhMkG2rg5aMIi8IEALBvWcPZGFOLMnMoPXi+yaNVaPgmgCZ/ZERC7AYE9E5AIM9kRELsBgT0TkAqYv0PIwbyKqZo88sh+PProfADA6OoqXX/4V9u//GVasWGHxyKby5PP5oh+cyWSKf3BBLPYIksljUFUNoiggGl3Lw72pKmUyQEeHD/39AurrNbS0ZBEMWj2q6nb06FHU168r+vGeXA7L04NYomYxJvpwqmYV8gZugrrllr/HG9/4JsRiH1zwsf39r6KxsXHKnwWDQdOa6piexkmlBqGqev8JVdWQSg2a/ZRElujo8KGnR0Am40FPj4CODp/VQ6JplqcHIWaHIWgaxOwwlqeNi0cvvvgCenp+U1Sgt4LpaZxIZNWUmX0kssrspySyRH+/AE3TJ2aa5kF/v8lzqUwGvo4OCP390OrrkW1pAT9KzG+Jmp3SxmKJmjXs3vff/wP85V9eY9j9jGb6zD6R2IJodC0kyY9odC0SiS1mPyWRJerrNQiCnukUhDzq68vrqLgQX0cHhJ4eeDIZCD098HV0mPp81WBM9GE8F50vXBvh5MmT6O19FRdeeJEh9zOD6TN7SfIzR0+u0NKSnZGzN5PQ3w+Ppr+heDQNQn+/qc9XDU7VrJqRszfCL3+ZwoUXvtWQe5mF7RKIDBIMAlu3mhvgJ9Pq6/WZvabpPXjq6yv23E6V93pxcuVqw+/b2/sq1q5da/h9jcRgT+RQ2ZaWmTl7ssSVV/4fq4ewIAZ7IqcKBpHdutXqUZBDcActEZELMNgTEbkAgz0RkQsw2FcBRckhFnsNodDLiMVeg6LkrB4S0mmgtdWHSCSA1lYf0mljH09EpWGwrwLxeB+SydNQlBySydOIx/usHhLa2nzo6hIwNORBV5eAtrb5N6+U+niaRy4HX18fAkePwtfXB+Ssf/Mn67EapwqkUiNQVf3/VVW/tlp3twBV1Temq6oH3d3zzytKfTzNzTcwMHHYuZDJwDcwMOOIRDZtM8bY2Bhuuukr6O/vh9cr4HOf+yLWr19v9bBmxd+oKhCJ+CGK+v+Lon5ttaYmDaKob0wXxTyamuZvHVDq42luwsjIlP4vwsjMN3+3Nm3zDGew4sEHUHPbt7DiwQfgGc6Udb+urn9HLpfD3Xd/Hx//+NW4++47DBqp8RjsZ6EoI4jFHkEodB9isUegKNbPlOeTSKxBNLoMkuRFNLoMiYT1B123t2fR3KyhtjaP5mYN7e3z7ywt9fE0TSYD3759COzeDTzxBPJZ/fuXB6D5Z775V7xpm00s398J8dUeCMMZiK/2YPn+zrLuFwqtQy6Xg6ZpOH36NLxe+yZL7DsyC8XjByY6dSaTxxCPH7B1fx9J8qKz8zyrhzFFTQ2wd2/xAbvUx09Rhd0fczlgYMCHkREBfr+Guros5mu7PtEUTdOQHxkB/vM/kb/kEmh+P7J1dTMeX1+voadHD/iVaNpmF0uOT+0ntOR4ef2EAoEA+vv78NGPfggnTpzALbd824hhmsIdb+clYg9+Z6nG7o8DAz5kMnowzmQEDAzMn2aZ3hQNQ0MYbmzUc/WzvEu0tGTR0KAhGMyjocH8pm12Mba6HnlBD3t5QcDY6vL6Cf3oRw/gbW97B37843/G/fc/gJtu+ltks/b8XnJmPwv24HeWauz+ODIiAJMy7/r13Eptilbppm12ceryGJbv78SS4/0YW12PU5fHyrrfWWedNZG6OeusszE2NgZNs+enJAb7WSQSW2acm0v2VY3dH/1+DZnMeMDPw++fP4CwKVpx8oEgTl7xMcPu95GPfAxf//rf4dprr4aqqti+fQcCgYBh9zeS6WfQEpmOOXvXjqnUM2jtpNJn0HJmT85Xhd0fvV5gzRoD0iy5nF53PzJyZrF2kRF6fB0BOLOOYMgYqSK4QEtUxSY2WGnaxAarxSp1HYHshT8toipWzAarYunrBmdOcF1oHYHshcGeqIppfv+UA7Zn22BVrLq6LIJB/VD1YFDP2ZNzMGdPVMWydXUzc/aLZNg6AlmCwZ6omnm9M5qgkTsx2BOR+dJp+NraIHR3Q2tqQra9Xe+R4XCjo6O4+eavoq/vGILBZfjM6eHy5AAAHi5JREFUZz6H884LWT2sWTFnT0Sm87W1QejqgmdoCEJXF3xtbZaMw5NOY8W2j6Om+SKs2PZxeMo8JecnP/kXBAJB3HPPfbj++s/iW9/abdBIjcdgT1QNJnW99O3bpzestxGhuxuewqELHlWF0N1tyTiW33A9xKe6IKTTEJ/qwvIbri/rfj09PXjHO5oBAOvWrcerr/YYMUxTMNgTVQG7N4PTmpqQLxy6kBdFaE1NloxjyZFueMbGAACesTEsOVLem86b3nQ+/uM/DiKfz+Pw4W4MDg4iZ9OTwRjsiaqA3ZvBZdvboTU3I19bC625Wc/ZW2Dsgibkl+hLlfklSzB2QXlvOpdd9gEsW7YM1157NZ588l8RDr8ZXqt7SMyBC7REFWJmbxnbN4OrqUF2716rR4FTu2/F8huux5Ij3Ri7oAmndt9a1v1efPEFXHTRH+G66/4aL774Ao6X2R/fTGyERlSsMitK+vp8UzpZBoOacXXrVjaDs7DSxupGaCdOnMCXv/x5DA8PY8WKFbjxxi9h1ariWqJXuhEagz2ZJp0G2tp86O4W0NSkHzXo5Go7X2urXlGiqnreubm5pNnq0aOBiaMAAUAQ8mhsHDZjqBVV7velHFYH+3Kw6yVVjbY2H7q6BKiqB11dAtrafIs/etAGyq0oKbVHvVmMTieV9X2pwvbUdsUFWjJNd7ce6AFAVT3o7nb2P7dyK0rs0lum1CMPF1LO98XuVUTVxNm/fWRrTU0aRFHP/IliHk1Nzu6SWG5FyXhvmcbGYaxZY93BH0a3Ki7n+2L3KqJqwjQOmaa9PTsjZ+9oNqkoKZfh6aQyvi+2ryKqIgz2ZJqaGjg6R1+t6uqyM3L2VuHZuZXDYE/kMnO2KraihLIKj5S0K+bsTaIoOcRiryEUehmx2GtQFHtuoSYaZ5dmZU505Mhh7NhxDQDgv//7NWzfHse1116Nb35zFzTNHmtVDPYmicf7kEyehqLkkEyeRjzeZ/WQiOZli2Zl0xu6KYqhDd60IQWnrvggfh9uwKkrPghtSCl7yD/84V7s2nUTRkdHAQC33XYrrrnmWtx5573I5/M4ePDJsp/DCAz2JkmlRlD4vYGq6tdEdmaHZmXTSzED995raGlmZvs2jB18EnlFwdjBJ5HZvq3sMa9dey527frmxPVLL72ETZsuBAC8/e3NeOaZX5T9HEZgsDdJJOJH4fcGoqhfE81go9bEdmhWNr0UE8PDhpZm5g49h8mzsNyh58q6HwBcfPEWLFkyefkzD49HL20NBpfh1KlTZT+HERjsTZJIrEE0ugyS5EU0ugyJBI+Go5lstamoUEI5nErppZQW9LbQ6uuRF/SwlBcEIBCYcl1uaaZ34yZMnoV5N24q636z8XjOhNVM5jRWrFhu+HMsBqtxTCJJXnR2nmf1MMhimQzQ0eFDf7+A+noNLS3ZKd0AnLKpyMyOnZPNKMV8//vhe/RRw0ozg3vuQWb7NuQOPQfvxk0I7rnHoJGfcf75YaRSzyISuQhPPdWFSOQiw59jMRjsiUzU0eFDT4/emqCnR0BHhw9bt54pe3TKpqLxFgvAmRYLhnXsnGyWUszJ17kcMNB35k2nhD6OAAChVsLyB//ZiJHO6ZOfbMM3vnEzVPUOrF+/HhdfvMXU5ysWu14SlUhTFKTjcaipFMRIBDWJBARJmvWxu3cHkMmcaWQYDOZxww2TOl06pBGYXTp2Tm8Tnc+/gHPPtecB3wth10uXUZQRxOMHkEoNIhJZhURiCySJi7l2lo7HMZpMAqqK0WQS6XgcUmfnrI+tr9cmZvaCkEd9/bSaa4dsKrJLx87pfX3yedNiY9XhAq3F4vEDSCaPQVFGkEweQzx+wOoh0QLUVGpKRYeaSs352JaWLBoaNASDeTQ06Dl7J6p4x845qpT0N5nxBEMeHg+TDcVy3cxeUU4iHr8dqdRRRCKNSCR2QpJWWDaeVGoQqqrPklRVQyo1aNlYqDhiJDIxs4coQoxE5nxsMIgpOXqnmrPFgkkmqpQ0baJKKbt1K+rqshg87sWq4WMIYhgvYUyP/ZzgL8h1M/t4/HYkk4ehKCeRTB5GPH67peOJRFZBFPUfgygKiESKO9KMrFOTSGBpNAqPJGFpNIqaRMLqIVWduaqUvF5gnec1LMcpeJEDtPzErl+an+uCfSp1FKqq96lR1RxSqaOWjieR2IJodC0kyY9odC0SCXus3NPcBEmC1NmJ1b29kDo751ycpYJ0Gr7WVgQiEfhaW/XzKhcwvd5+cpWSMDIyZSLvyduj94zduS7YRyKNEEW9QFgUvYhEGhf4CnNJkh+dnZeit/cqdHZeysVZspymKFBiMRwPhaDEYtCU8vrHLKbBWralBVpDA/LBILSGhon6+lwOyCCIyeE973FdGFsU15Ve2i1nT+600GYrKymx2JQ1iaXR6JzVRsUIRCLwDA1NXOdrazE8z6L2fPr6fMhmNJyH1/ScvZZD/bkNlufsjxw5jO997zbcccfdE3/2ne98C6HQOvz5n39o1q9h6aXJJGkFOjtvtHoY5HILbbayUinVRsXQmpr0mb2qlt1gbWREgAYvXoEeJDW8UFKgV4cUvLR9G04deg7LN27Cm/fcA7G2vDTcD3+4F4899igCgQAAIJ1O46abvoLe3ldx5ZX2Kat15ecfRTmJWGwXQqFtiMV2QVFOlvj17FXvOovIO8+nv1+Y2KSkaR7099vnV1GMRKb0j5mv2qgYRjZYK7f08qXt23Di4JNQFQUnDj6Jl0zoejk8nEE8fg3e9773l31vI9nnX1gFlVuRw1717mP0wR719XrNOoDZN1tZyPBqIwMbrE2v9x//Hhbr1KHnkC98asmrKk6Z0PVyzZq1uOCCDWXf12iuS+MA5VfksFe9+xh9sEdLS3ZGzt4uxquNFq2U4w1zOfgGBiCMjEDz+5Gtq8N8Hdam1/sfLbGYbvnGTThx8EnkVRUeUcRyE7pe2pUrZ/blVuSwV737GH2wx/hmqxtuGMbWrfZZnDVCKZ+CfAMDEDIZvZY+k4FvYMDUsb15zz04Z/O7IUoSztn8brzZhK6XduXKmX0isXNGRU5pX78G8XgfUqkRRCJ+9qp3gWx7+8zZqpOUOIMuRymfgibXzHsK1+PMqFgSayU0mdz10q5cV3pJZFdlBbcFUie+vj59Bg19eVMLBpFdY84kxdfaOrX6prlZz9XP9th5xrVvn29KE7mGBm1GxdLRo0dRX7/OlNdhtkqXXroyjUNkR+PlmJnMmXLMYi2UOplvBm20UqpvsnV10IJBfZdsMKh/4iiwc8WSE7kyjUNkR+UEt4VSJ5rfP3UG7TdxnalQfVMUr3fOTxgLtoemkvCt0mZYw+9e5ZRjLrSAPN8M2q6qpT20XTBnbzOx2GtIJk+P71RHNLqMZ9m6hJk5e8uYvDDMnH3xGOwrTFFyMyp5JOnMP/5Q6OUps3lJ8qK3901WDLUk6TTQ1uZDd7eApiYN7e1ZW8QaspbZC8MM9sVjGqfCFtp969Qa/rY2H7q6BAwNedDVJaCtrfjFRVvJ5eDr60Pg6FH4+vr0NosuYnTHy0UvDJvwc9A0QFEEHD8uQFEEaC5bAmCwr7CFdt8mEmsQjS6DJHkRjS5zTA1/d7cAVdV/rVXVg+5uZ/7TqvQmH7sZP183rygT5+uWQ/P7J3WyKX5h2IyfQzotIJvVg342q18b5ciRw9ix4xoAwK9+JePaa6/Gjh3XoK1tJ4aGynvDNAqrcSosEvFPyclPn7lLkteROfqmJg1dXXrAF8U8mprsPW2aKz9eyRJFOzK642W2rm5mzr4IZvwcVBXInlDwzI1xnDjyHGou2ITLfnAPAgZ3vWxv/xY+/enP4vzzw+js7MC+fXtx3XXXlz3+cjlz+uVgTp25L6S9PYvmZg21tXk0N+s5+7mk00Brqw+RSACtrb4FG0hmMvoGm927A9i3zzd+9nRZ5qppX+xMdAaHpoOM7ng5Xlo53Nio5+qLXJw17OcwiSgCz9wYx+DTT2L0hILBp5/E4yZ0vfy7v/s6zj8/DADI5XLw+eyR0mSwr7DxmXtv75vQ2XnelMVZJ6upAfbuzSKVGsbevfMvzpaa3y9ns9Fc5qppN6pE0anpILucr2tGqWhNjYbfv/Ac8mP6JxdtTMVvTeh6uXLlSgBAd/ch/NM/PYiPfORjZT+HEZjGoYorNb9vxk7KOTfszLPJpxROTQeV3fHSKAb9HCYTBKBu0yb898EnoakqBFHEH5jU9fLnP/9/2Lv3+7jllnbU2KQsjTN7qrimJg2iqH9ILya/b0bvd7M37JiRhqDyvXfPPTh387vhlyScu/ndeK8JXS8fe+xRdHQ8iDvuuAtr155r+P0Xi3X2s1ioFp7KU2pNfqXOa83lgIEBH0ZGBPj9Gurqsovf/1PBLpOWs/C12qXOvr+/D1/+8uexZ08C73//e7F69WosX74cALBp04W4+uq/muVruKnKctzF6k59fT5kMgJQ2AIUDGpTDsqg2VWyo+Z0dgn2i8FNVTZQiZOo2APHfkZGxgM9AHgK16XJ5fQ3jaNHA+jr8zmlCGdeC70mp65PuA2D/SwqsYuV59jaz/TDrPXr0gwM6J8ONM2DTEbAwMCZyiEzSkinM3oHLDD/awLMWZ8w43W4HYP9NIqSw+jo+OIh0NwcNKUWnufY2s/0w6zr6kpP4cz36cCMEtLpjN4BCyz8iceMMsniX4cHpaSi7UIfs2kZm1kx2E8Tj/ehqyszEYiXLvWYsjhr9x44pW58qgbjh1k3Ng5jzZrFLc7O9+mgEodxGL0DFijiE88iN07Np9jX4fMtxalTrzsq4OfzeZw69Tp8vqUVfV7W2U9TqRm33c+xHd/4pKpnNj7t3avPdNnhcm51ddkZFT3jKnEYhxiJYDSZxHh1Qdk7YDH/azJLsa+jrq4OAwMDOH789zjzhmR3Hvh8S1FX4TMFWI0zDStxdJFIAENDZz5m1tbmkUoNA9Bn/JP74DQ3axNvBDS3SpSQaoqCdDwONZWCGImgJpGAIJXX+8UK1fI6SsXSyxIpygji8QNIpQYRiaxCIrEFklRcmoQ19rr5Avp8bwREtHgsvSxRPH4AyeQxKMoIksljiMcPFP211dq7plTzNTYrdQcs2QMrXNytKmf2odB9UJQzuXZJ8qO39yrrBlRlmLN3JiUWm5IHXxqN2qMPDk3gzL5EkcgqiKL+0kRRQCSyyuIRlcbuG65K6XBZtdJp+FpbEYhE4GtthRPKlcyo1CHnqMpgn0hsQTS6FpLkRzS6FonEFquHVBJuuCqRBYHX19YGoasLnqEhCF1d8LW1mf6c5TK8V32x5vv5ZDLw7duHwO7d8O3bB1N2mhGAKk3jOJ1TDx23iq+1VQ+8qoq8KEJrbkZ2714A5lXABCIReIaGJq7ztbUYtvlM2aoKl/l+Pr59+yD09MCjafqmrIYGZLduNX1MdsU0joEUZQSx2CMIhe5DLPbIlNy+Xdh9w5XdCN3d8BTSEx5VhdDdPfF3Zu1a1ZqakC/8kPKiCK2pyZD7mmm8V/3q3l5InZ0VK2Wc7+cj9PfDUzj526NpEPr7KzImN3JdsC+nUqdSqvXoQrPMF3jN2rWabW+H1tyMfG2tPlNtbzfkvtVovp+PVl+PvKD/TPKCAK2+3pIxuoHr0jis1DGHpRU66bSeQ+/uhtbUpAfewpPv2+ebsmu1oUHD1q3cAFZR8/x8kMnA19EBob8fWn09si0tMOWwAofgpioDxWKPIJk8BlXVIIoCotG16Oy81OphTShnQ5iV7LqrtlIHnxAZgcHeQHYPpnZ/M5oLd9USlc/MYO+6RmiS5Ld18EylBqGq+oKVqmpIpQYtHlFxmpq0KTN77qolshfXLdDazfTqoA0bah25IWy+9grkQA7cNEbzc10ax26mp22am1dj6VKvbdNM5A7z1caTeZjGqQBFOYl4/HakUkcRiTQikdgJSVph+vNOT9scPjzk+Oog9s5xvvlq48mZbJnGUZSTiMV2IRTahlhsFxTlpOnPGY/fjmTyMBTlJJLJw4jHbzf9OQHn9/GZzfjBJ0NDZw4+IWdx4qYxmp8t0zix2C4kk4ehqjmIohfR6AZ0dt5o6nOGQtumvKlI0gr09t5j6nMC9q8OWgxW5lSBOWrjWcpqLtelcVKpo1BVvTeMquaQSh01/Dmmp202bAihq+uliTeYSKTR8Oecjd2rgxaDlTlVoKZm1hz9ePsJTTvTfoKb1JzBlmmcSKQRoqgfGmJW4J2etgGAaHQDJGkFotENSCR2lnxPJ/TdqQRW5hjDjoeNVOLQdDKHLdM4lVgsNSNt49QNUWRPdjxshO0nzOW6NI4krTA9Rx+JNE5ZFzDi04NTN0SRPdnxsJGWluyMnD05g2s/gyUSO4tK25RSGVSNlTXVxo6pkbkYcdiI0a83GAS2bs3ihhuGsXUrF2edxJZpHDsppTKoGitrqo0dUyNzMeKwESe9XmIjNEtZVZJJ5jgeCiE/aXbrkST8wXPPmXaCk1WnQ42b7fWu7u2t2PNTaXhSlYUqURlElTNbaiQdj2M0mUReUTCaTCIdjxv2fGbeuxiWnTtLtuP6YL9QTr7Y3L65Y2RJp1FqEgksjUbhkSQsjUZRk0iYuhBq9SLrbK/X6XI5oK/Ph6NHA+jr8yGXW/hriGmconLyVvXNOTNGlnSaycy8NnPmxuvr8yGTEQB4AOQRDGpYs6Y6qoKYxjFRMbt1reqbc2aMLOk0k5mz32qcWVttZGQ80AOAp3BNC7FlnX0lFVNvX4n2DfOPcdWUmT1LOo0lSJJps20z7+1Wfr82ZWbv97MdRzFc/5ZYTE7e6kXaRGILotG1kCQ/otG1SCS2VPT5ieykri6LYFCDIOgpnLq66kjhmK2qc/ZG5dqtztmTi2Uy8HV0QOjvh1Zfj2xLC7iTqXqxzn6RrGiVTGQk3759EHp64NE05AUBWkMDslu3Wj0sMgkXaBfJ6lw7UbmE/n54ND0n7dE0CP39Fo+InKqqg73VuXaicmn19cgL+q9pXhCg1ddbPCJyqqoO9nbYEEVUjmxLC7SGBuSDQT2F09Ji9ZDIoao6Z09E5CTM2RMRUVkY7ImIXIDBnojIBRjsiYhcwPW9cYiIDJHLwTcwAGFkBJrfj2xdHeD1Wj2qCZzZExEZwDcwACGT0Te/ZTLwDQxYPaQpGOyJiAwgjIxMarysX9sJgz0RkQE0vx/jG5HyhWs7YbAnIjJAtq4OWjCot7UIBvWcvY0w2BeJ58A6h6YoUGIxHA+FoMRi0BTF6iGRG3i9yK5Zg+HGRmTXrLHV4izAdglF4zmwzsFzX8mp2C7BBngOrHOoqZQe6AFAVfVrIpdjsC9SJLIKoqh/u3gOrL2JkQggioULUb8mcjkG+yLxHFjnqEkksDQahUeSsDQaRU0iYfWQiCzHnD0RkU0wZ09ERGVhbxwiomJlMvB1dEDo74dWX6+fHBYMWj2qonBmT0RUJF9HB4SeHngyGQg9PfB1dFg9pKIx2FeJUUXBs7EYngiF8GwshlFuJCIynNDfD4+ml2B7NA1Cf7/FIyoeg32VeD4ex1AyCVVRMJRM4vl43OohEVUdrb4eeUEPm3lBgFZfb/GIisdgXyVeT6WQL2wkyqsqXudGIiLDZVtaoDU0IB8MQmto0HP2DsEF2ipxViSCoWQSeVWFRxRxFjcSudqoouD5eByvp1I4KxLBWxIJLJUkq4flfMEgslu3Wj2KRWGdfZXgLzdN9mwsNuXNvzYaxUXsD2R7ZtbZM9gTVaEnQiGokxbpRUnCJb29Fo6IisFNVURUkrMiEXgK/YGY1iOAwZ6oKr0lkUBtNApRklAbjeIt7A/kekzjEBHZBNM4VBHDioKHYjHcHgrhoVgMw9yYRVQ1OLOnCQ/FYuhNJqGpKgRRRCgaxYdZwUFUMZzZU0UMpFLQChuzNFXFADdmEVUNBnuaUBeJQChUcAiiiNWs4CCqGgz2NOGyRAKhaBQBScK6aBSXsoKjqqTTQGurD5FIAK2tPqTTVo+IKok5ewKgL84+HI9jIJVCXSSCyxIJBLgDt6q0tvrQ1SVAVT0QxTyamzXs3Zu1elg0CXP2ZLqH43H0JpMYVhT0JpN4mF0zq053tx7oAUBVPeju5q+/m/CnTQC4OOsGTU0aRFH/cC6KeTQ1aRaPiCqJwZ4AcHHWDdrbs2hu1lBbq6dw2tuZwnET5uwJwNSc/epIBJcyZ09Ucex6aSIuTBKRXTDYm4i7RonILliNYyIuTBKRG7g+2Dt9YXJUUfBsLIYnQiE8G4thlM3LiGgWrg/2Tt81+nw8jqFkEqqiYCiZxPOsj68oRckhFnsNodDLiMVeg6LkrB4S0axcn7N3Oh4/Z61Y7DUkk6ehqoAoAtHoMnR2nmf1sMihmLOnOZV7/BzTQOVJpUZQWPKBqurXRHbEYO9w5R4/xzRQeSIRPwrvtRBF/ZrIjpjGcTmmgcqjKDnE431IpUYQifiRSKyBJHmtHhY5lJlpnCVm3Zic4axIBEPJJPKquqg0kNtJkpc5enIEpnFcrtw0EBE5A9M4REQ2wWocIiIqC4M9EZELMNgTEbkAgz0RkQsw2BMRuQCDPRGRCzDYExG5AIM9EZELMNhXKXazJKLJuIO2Sj0bi03peVMbjeIinq1LZGvcQUsTip2xv55KIV9otJ5XVbzOs3WJXI3B3mGK7T9f7qEmRFRdGOwdptgZO7tZEtFk7GfvMMX2n18qSczRE9EEzuwdhjN2IloMVuMQEdkEq3GIiKgsDPZELqAoJxGL7UIotA2x2C4oykmrh0QVxmBPCxpWFDwUi+H2UAgPxWIY5m5cx4nHb0cyeRiKchLJ5GHE47dbPSSqMAZ7WtDD8Th6k0kMKwp6k0k8PEdtP9lXKnUUqpoDAKhqDqnUUYtHRJXGYO9ixe7GHUiloBVq+zVVxQB34zpOJNIIUfQCAETRi0ik0eIRUaUx2LtYsbtx6yIRCIXduIIoYvW02n6meewvkdiJaHQDJGkFotENSCR2Wj0kqjCWXrrYE6EQ1EmBWZQkXNLbO+Nxw4qCh+NxDKRSWB2J4NJEAgFJmvj7h2Ix9CaT0FQVgigiFI3iw9zQRVQyll6SKYrtnxOQJHy4sxPx555DHkBi06YpM3imeYjsj8HexUrdjTt5ofaVxx/HvZs2YVhRFkzzEE3HUtDKYxqHinZ7KDQjH7/+ve/FZYnEvGkeoulisV1IJg9DVXMQRS+i0Q3o7LzR6mFZzsw0DhuhUdHqIhG88vjjU/5sIJWaSPMQFYuloJXHNA4V7bJEAv5JM3bPkiVM2dCisBS08hjsXaTcc2kDkoSrn3sO69/7XgQkCesvvhiXzpLnZykmLYSloJXHnL2LVOpcWpZiEi0OSy/JEJU6l5almET2w2DvQItNxxh9Lu1c6ZqVGzYAnsIExePBqg0bynoeIiOl00Brqw+RSACtrT6k01aPqDIY7B2o2DYH0xl9ytVcDdJM+xxKZIC2Nh+6ugQMDXnQ1SWgrc1n9ZAqgqWXDrTYdIzR59LOla4ZPHwYGF8Lyuf1ayKb6O4WoKr6lERVPejudsec1x2vssoUm44pt/pmIXPtnOWOWrKzpiYNoqhPRkQxj6YmzeIRVQaDvQMVm45ZbLqnWJclEghFowhIEtZFoxNlmHP9OZERFGUEsdgjCIXuQyz2CBRlpKSvb2/PorlZQ21tHs3NGtrbsyaN1F5YelnFiu1qSeQksdgjSCaPQVU1iKKAaHQtOjsvtXpYhmDpJS2KkdU33ChFdpFKDUJV9dSLqmpIpQYtHpEzMNhXMSOrb3g0IdlFJLIKoqiHLlEUEImssnhEzsA0jguNKgqej8fxeiqFsyIRvCWRwNIFulRO73gpiCI+8ZvfsLslVZyijCAeP4BUahCRyCokElsgSX6rh2UIM9M4DPYutJi2CQ/FYnjl5z8/U1Lp8WD9e97DNghEBmLOngy1mDr9yxIJCEsmbcvI59kGgchBGOxdaDELtwFJQigaZf08kUMx2LvQYhduWT9P5FzM2RMR2QRz9kREVBYGeyIiF2CwJyJyAQZ7IiIXYLB3GbPbHhORPbEax2Uqdeg4EZWO1ThkmEodOk5E9sJg7zJGHzpORM7AYO8yRh86TkTOwJw9EZFNMGdPRERlYbAnInIBBnsiIhdgsCcicgEGeyIiF2CwJyJyAQZ7IiIXYLAnInIBBnsiIhdgsCcicgEGeyIiFyipNw4RETkTZ/ZERC7AYE9E5AIM9kRELsBgT0TkAgz2REQuwGBPROQCDPZERC7AYE9E5AIM9kRELsBgT0TkAv8fZcW/IYTx3iQAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 345.6x691.2 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ENTROPY_THRESHOLD = 3.8  # When to say a head \"attends broadly\"\n", "POSITION_THRESHOLD = 0.5  # When to say a head \"attends to next/prev\"\n", "SPECIAL_TOKEN_THRESHOLD = 0.6  # When to say a heads attends to [CLS]/[SEP]\"\n", "# Heads that were found to have linguistic behaviors\n", "LINGUISTIC_HEADS = {\n", "    (4, 3): \"Coreference\",\n", "    (7, 10): \"Determiner\",\n", "    (7, 9): \"Direct object\",\n", "    (8, 5): \"Object of prep.\",\n", "    (3, 9): \"Passive auxiliary\",\n", "    (6, 5): \"Possesive\",\n", "}\n", "\n", "# Use multi-dimensional scaling to compute 2-dimensional embeddings that\n", "# reflect Jenson-Shannon divergences between attention heads.\n", "mds = sklearn.manifold.MDS(metric=True, n_init=5, n_jobs=4, eps=1e-10,\n", "                           max_iter=1000, dissimilarity=\"precomputed\")\n", "pts = mds.fit_transform(js_divergences)\n", "pts = pts.reshape((12, 12, 2))\n", "pts_flat = pts.reshape([144, 2])\n", "\n", "colormap = cm.seismic(np.linspace(0, 1.0, 12))\n", "plt.figure(figsize=(4.8, 9.6))\n", "plt.title(\"BERT Attention Heads\")\n", "\n", "for color_by_layer in [<PERSON>als<PERSON>, <PERSON>]:\n", "  ax = plt.subplot(2, 1, int(color_by_layer) + 1)\n", "  seen_labels = set()\n", "  for layer in range(12):\n", "    for head in range(12):\n", "      label = \"\"\n", "      color = GREY\n", "      marker = \"o\"\n", "      markersize = 4\n", "      x, y = pts[layer, head]\n", "\n", "      if avg_attns[\"right\"][layer, head] > POSITION_THRESHOLD:\n", "        color = RED\n", "        marker = \">\"\n", "        label = \"attend to next\"\n", "        \n", "      if avg_attns[\"left\"][layer, head] > POSITION_THRESHOLD:\n", "        color = BLUE\n", "        label = \"attend to prev.\"\n", "        marker = \"<\"\n", "\n", "      if entropies[layer, head] > ENTROPY_THRESHOLD:\n", "        color = ORANGE\n", "        label = \"attend broadly\"\n", "        marker = \"^\"\n", "\n", "      if avg_attns[\"cls\"][layer, head] > SPECIAL_TOKEN_THRESHOLD:\n", "        color = PURPLE\n", "        label = \"attend to [CLS]\"\n", "        marker = \"$C$\"\n", "        markersize = 5\n", "\n", "      if avg_attns[\"sep\"][layer, head] > SPECIAL_TOKEN_THRESHOLD:\n", "        color = GREEN\n", "        marker = \"$S$\"\n", "        markersize = 5\n", "        label = \"attend to [SEP]\"\n", "\n", "      if avg_attns[\"punct\"][layer, head] > SPECIAL_TOKEN_THRESHOLD:\n", "        color = SEA\n", "        marker = \"s\"\n", "        markersize = 3.2\n", "        label = \"attend to . and ,\"\n", "\n", "      if color_by_layer:\n", "        label = str(layer + 1)\n", "        color = colormap[layer]\n", "        marker = \"o\"\n", "        markersize = 3.8\n", "\n", "      if not color_by_layer:\n", "        if (layer, head) in LINGUISTIC_HEADS:\n", "          label = \"\"\n", "          color = BLACK\n", "          marker = \"x\"\n", "          ax.text(x, y, LINGUISTIC_HEADS[(layer, head)], color=color)\n", "\n", "      if label not in seen_labels:\n", "        seen_labels.add(label)\n", "      else:\n", "        label = \"\"\n", "\n", "      ax.plot([x], [y], marker=marker, markersize=markersize,\n", "              color=color, label=label, linestyle=\"\")\n", "\n", "  ax.set_xticks([])\n", "  ax.set_yticks([])\n", "  ax.spines[\"top\"].set_visible(False)\n", "  ax.spines[\"right\"].set_visible(False)\n", "  ax.spines[\"bottom\"].set_visible(False)\n", "  ax.spines[\"left\"].set_visible(False)\n", "  ax.set_facecolor((0.96, 0.96, 0.96))\n", "  plt.title((\"Colored by Layer\" if color_by_layer else \"Behaviors\"))\n", "  handles, labels = ax.get_legend_handles_labels()\n", "  ax.legend(handles, labels, loc=\"best\")\n", "\n", "plt.suptitle(\"Embedded BERT attention heads\", fontsize=14, y=1.05)\n", "plt.subplots_adjust(top=1, bottom=0, right=1, left=0,\n", "                    hspace=0.1, wspace=0)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.16"}}, "nbformat": 4, "nbformat_minor": 2}