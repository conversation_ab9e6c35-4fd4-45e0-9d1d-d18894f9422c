#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析拆分字错误的注意力权重层级变化模式
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import argparse
from datetime import datetime

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from transformers import AutoTokenizer, AutoModelForCausalLM
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class LayerAttentionPatternAnalyzer:
    """层级注意力模式分析器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        
        print(f"Loading model: {model_path}")
        
        # 加载tokenizer和模型
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            output_attentions=True,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32
        ).to(device)
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        self.model.eval()
        print(f"Model loaded successfully")
        
    def extract_attention_data(self, text, max_length=256):
        """提取注意力数据"""
        print(f"Processing text: {text}")
        
        # 编码文本
        inputs = self.tokenizer(
            text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=max_length
        ).to(self.device)
        
        # 获取token IDs和tokens
        input_ids = inputs['input_ids'][0].cpu().numpy()
        tokens = []
        for token_id in input_ids:
            try:
                token_text = self.tokenizer.decode([token_id], skip_special_tokens=False)
                tokens.append(token_text.strip())
            except:
                tokens.append(f'[{token_id}]')
        
        # 前向传播获取注意力
        with torch.no_grad():
            outputs = self.model(**inputs)
            attentions = outputs.attentions
        
        # 转换为numpy数组
        attention_matrices = []
        for layer_attention in attentions:
            layer_attn = layer_attention.squeeze(0).cpu().numpy()
            attention_matrices.append(layer_attn)
        
        return {
            'tokens': tokens,
            'input_ids': input_ids,
            'attentions': attention_matrices,
            'text': text
        }
    
    def analyze_layer_progression(self, attention_data, error_indices):
        """分析注意力权重的层级变化"""
        attentions = attention_data['attentions']
        num_layers = len(attentions)
        num_heads = attentions[0].shape[0]
        
        # 初始化分析结果
        layer_analysis = {
            'error_attention_by_layer': [],
            'error_self_attention_by_layer': [],
            'error_cross_attention_by_layer': [],
            'attention_concentration_by_layer': [],
            'layer_specialization_by_layer': []
        }
        
        print(f"Analyzing attention progression across {num_layers} layers...")
        
        for layer_idx in range(num_layers):
            layer_attention = attentions[layer_idx]  # [num_heads, seq_len, seq_len]
            
            # 1. 对错误token的总注意力
            error_attention_total = 0
            for error_idx in error_indices:
                if error_idx < layer_attention.shape[2]:
                    # 所有头、所有token对该错误token的注意力总和
                    error_attention_total += np.sum(layer_attention[:, :, error_idx])
            
            avg_error_attention = error_attention_total / (len(error_indices) * num_heads * layer_attention.shape[1])
            layer_analysis['error_attention_by_layer'].append(avg_error_attention)
            
            # 2. 错误token的自注意力
            error_self_attention = 0
            for error_idx in error_indices:
                if error_idx < layer_attention.shape[1]:
                    # 错误token对自己的注意力
                    error_self_attention += np.mean(layer_attention[:, error_idx, error_idx])
            
            avg_error_self_attention = error_self_attention / len(error_indices)
            layer_analysis['error_self_attention_by_layer'].append(avg_error_self_attention)
            
            # 3. 错误token之间的交叉注意力
            error_cross_attention = 0
            if len(error_indices) > 1:
                for i, idx1 in enumerate(error_indices):
                    for j, idx2 in enumerate(error_indices):
                        if i != j and idx1 < layer_attention.shape[1] and idx2 < layer_attention.shape[2]:
                            error_cross_attention += np.mean(layer_attention[:, idx1, idx2])
                
                avg_error_cross_attention = error_cross_attention / (len(error_indices) * (len(error_indices) - 1))
            else:
                avg_error_cross_attention = 0
            
            layer_analysis['error_cross_attention_by_layer'].append(avg_error_cross_attention)
            
            # 4. 注意力集中度（方差）
            attention_variance = np.var(layer_attention)
            layer_analysis['attention_concentration_by_layer'].append(attention_variance)
            
            # 5. 层级专业化程度（头之间的差异）
            head_means = np.mean(layer_attention, axis=(1, 2))  # 每个头的平均注意力
            head_specialization = np.std(head_means)
            layer_analysis['layer_specialization_by_layer'].append(head_specialization)
        
        return layer_analysis
    
    def plot_layer_progression_analysis(self, layer_analysis, sample_info, save_dir):
        """绘制层级变化分析图"""
        num_layers = len(layer_analysis['error_attention_by_layer'])
        layers = list(range(num_layers))
        
        # 创建多子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Layer-wise Attention Analysis - {sample_info["error_type"]}', 
                    fontsize=16, fontweight='bold')
        
        # 1. 对错误token的注意力变化
        axes[0, 0].plot(layers, layer_analysis['error_attention_by_layer'], 'b-o', linewidth=2, markersize=4)
        axes[0, 0].set_title('Error Token Attention by Layer')
        axes[0, 0].set_xlabel('Layer Number')
        axes[0, 0].set_ylabel('Average Attention to Error Tokens')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 错误token自注意力变化
        axes[0, 1].plot(layers, layer_analysis['error_self_attention_by_layer'], 'r-o', linewidth=2, markersize=4)
        axes[0, 1].set_title('Error Token Self-Attention by Layer')
        axes[0, 1].set_xlabel('Layer Number')
        axes[0, 1].set_ylabel('Self-Attention Strength')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 错误token交叉注意力变化
        axes[0, 2].plot(layers, layer_analysis['error_cross_attention_by_layer'], 'g-o', linewidth=2, markersize=4)
        axes[0, 2].set_title('Error Token Cross-Attention by Layer')
        axes[0, 2].set_xlabel('Layer Number')
        axes[0, 2].set_ylabel('Cross-Attention Between Error Tokens')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 注意力集中度变化
        axes[1, 0].plot(layers, layer_analysis['attention_concentration_by_layer'], 'm-o', linewidth=2, markersize=4)
        axes[1, 0].set_title('Attention Concentration by Layer')
        axes[1, 0].set_xlabel('Layer Number')
        axes[1, 0].set_ylabel('Attention Variance (Concentration)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. 层级专业化程度
        axes[1, 1].plot(layers, layer_analysis['layer_specialization_by_layer'], 'c-o', linewidth=2, markersize=4)
        axes[1, 1].set_title('Head Specialization by Layer')
        axes[1, 1].set_xlabel('Layer Number')
        axes[1, 1].set_ylabel('Head Specialization (Std of Head Means)')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. 综合对比
        # 归一化所有指标到0-1范围进行对比
        norm_error_attn = np.array(layer_analysis['error_attention_by_layer'])
        norm_error_attn = (norm_error_attn - norm_error_attn.min()) / (norm_error_attn.max() - norm_error_attn.min() + 1e-8)
        
        norm_concentration = np.array(layer_analysis['attention_concentration_by_layer'])
        norm_concentration = (norm_concentration - norm_concentration.min()) / (norm_concentration.max() - norm_concentration.min() + 1e-8)
        
        norm_specialization = np.array(layer_analysis['layer_specialization_by_layer'])
        norm_specialization = (norm_specialization - norm_specialization.min()) / (norm_specialization.max() - norm_specialization.min() + 1e-8)
        
        axes[1, 2].plot(layers, norm_error_attn, 'b-', label='Error Attention', linewidth=2)
        axes[1, 2].plot(layers, norm_concentration, 'r-', label='Concentration', linewidth=2)
        axes[1, 2].plot(layers, norm_specialization, 'g-', label='Specialization', linewidth=2)
        axes[1, 2].set_title('Normalized Metrics Comparison')
        axes[1, 2].set_xlabel('Layer Number')
        axes[1, 2].set_ylabel('Normalized Value (0-1)')
        axes[1, 2].legend()
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        save_path = os.path.join(save_dir, 'layer_progression_analysis.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"Layer progression analysis saved: {save_path}")
    
    def generate_detailed_analysis_report(self, layer_analysis, sample_info, attention_data, save_dir):
        """生成详细的分析报告"""
        report_path = os.path.join(save_dir, 'detailed_layer_analysis_report.txt')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("Detailed Layer-wise Attention Analysis Report\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Sample Information:\n")
            f.write(f"  Text: {sample_info['text']}\n")
            f.write(f"  Error Type: {sample_info['error_type']}\n")
            f.write(f"  Error Description: {sample_info['description']}\n")
            f.write(f"  Error Positions: {sample_info['error_indices']}\n\n")
            
            # 层级变化分析
            f.write("Layer-wise Pattern Analysis:\n")
            f.write("-" * 40 + "\n\n")
            
            # 1. 早期层分析 (L0-L11)
            early_layers = slice(0, 12)
            early_error_attn = np.mean(layer_analysis['error_attention_by_layer'][early_layers])
            early_concentration = np.mean(layer_analysis['attention_concentration_by_layer'][early_layers])
            early_specialization = np.mean(layer_analysis['layer_specialization_by_layer'][early_layers])
            
            f.write("1. Early Layers (L0-L11) - Token Recognition Phase:\n")
            f.write(f"   Average Error Attention: {early_error_attn:.6f}\n")
            f.write(f"   Average Concentration: {early_concentration:.6f}\n")
            f.write(f"   Average Specialization: {early_specialization:.6f}\n")
            f.write(f"   Interpretation: Basic token processing, low error awareness\n\n")
            
            # 2. 中期层分析 (L12-L23)
            mid_layers = slice(12, 24)
            mid_error_attn = np.mean(layer_analysis['error_attention_by_layer'][mid_layers])
            mid_concentration = np.mean(layer_analysis['attention_concentration_by_layer'][mid_layers])
            mid_specialization = np.mean(layer_analysis['layer_specialization_by_layer'][mid_layers])
            
            f.write("2. Middle Layers (L12-L23) - Semantic Processing Phase:\n")
            f.write(f"   Average Error Attention: {mid_error_attn:.6f}\n")
            f.write(f"   Average Concentration: {mid_concentration:.6f}\n")
            f.write(f"   Average Specialization: {mid_specialization:.6f}\n")
            f.write(f"   Interpretation: Semantic relationship processing\n\n")
            
            # 3. 后期层分析 (L24-L35)
            late_layers = slice(24, 36)
            late_error_attn = np.mean(layer_analysis['error_attention_by_layer'][late_layers])
            late_concentration = np.mean(layer_analysis['attention_concentration_by_layer'][late_layers])
            late_specialization = np.mean(layer_analysis['layer_specialization_by_layer'][late_layers])
            
            f.write("3. Late Layers (L24-L35) - High-level Integration Phase:\n")
            f.write(f"   Average Error Attention: {late_error_attn:.6f}\n")
            f.write(f"   Average Concentration: {late_concentration:.6f}\n")
            f.write(f"   Average Specialization: {late_specialization:.6f}\n")
            f.write(f"   Interpretation: High-level semantic integration\n\n")
            
            # 关键发现
            f.write("Key Findings:\n")
            f.write("-" * 20 + "\n")
            
            # 找到错误注意力的峰值层
            max_error_attn_layer = np.argmax(layer_analysis['error_attention_by_layer'])
            max_error_attn_value = np.max(layer_analysis['error_attention_by_layer'])
            
            f.write(f"1. Peak Error Attention:\n")
            f.write(f"   Layer: L{max_error_attn_layer}\n")
            f.write(f"   Value: {max_error_attn_value:.6f}\n")
            f.write(f"   Phase: {'Early' if max_error_attn_layer < 12 else 'Middle' if max_error_attn_layer < 24 else 'Late'}\n\n")
            
            # 分析趋势
            error_attn_trend = np.polyfit(range(len(layer_analysis['error_attention_by_layer'])), 
                                        layer_analysis['error_attention_by_layer'], 1)[0]
            
            f.write(f"2. Error Attention Trend:\n")
            f.write(f"   Slope: {error_attn_trend:.8f}\n")
            f.write(f"   Direction: {'Increasing' if error_attn_trend > 0 else 'Decreasing'}\n")
            f.write(f"   Interpretation: {'Model learns to focus on errors' if error_attn_trend > 0 else 'Model loses focus on errors'}\n\n")
            
            # 专业化分析
            max_specialization_layer = np.argmax(layer_analysis['layer_specialization_by_layer'])
            max_specialization_value = np.max(layer_analysis['layer_specialization_by_layer'])
            
            f.write(f"3. Peak Head Specialization:\n")
            f.write(f"   Layer: L{max_specialization_layer}\n")
            f.write(f"   Value: {max_specialization_value:.6f}\n")
            f.write(f"   Interpretation: Maximum functional differentiation between attention heads\n\n")
            
            # 错误处理能力评估
            overall_error_attention = np.mean(layer_analysis['error_attention_by_layer'])
            error_attention_std = np.std(layer_analysis['error_attention_by_layer'])
            
            f.write(f"4. Overall Error Processing Assessment:\n")
            f.write(f"   Mean Error Attention: {overall_error_attention:.6f}\n")
            f.write(f"   Error Attention Std: {error_attention_std:.6f}\n")
            f.write(f"   Consistency: {'High' if error_attention_std < 0.001 else 'Medium' if error_attention_std < 0.01 else 'Low'}\n")
            f.write(f"   Overall Capability: {'Strong' if overall_error_attention > 0.01 else 'Moderate' if overall_error_attention > 0.001 else 'Weak'}\n\n")
            
            # 建议
            f.write("Recommendations for Model Improvement:\n")
            f.write("-" * 40 + "\n")
            
            if overall_error_attention < 0.001:
                f.write("1. Error attention is very low across all layers\n")
                f.write("   - Add specialized error detection heads\n")
                f.write("   - Increase training data with split character errors\n")
                f.write("   - Apply attention supervision for error tokens\n\n")
            
            if error_attn_trend < 0:
                f.write("2. Error attention decreases in higher layers\n")
                f.write("   - Implement error-aware loss functions\n")
                f.write("   - Add skip connections for error information\n")
                f.write("   - Train higher layers specifically for error correction\n\n")
            
            f.write("3. General recommendations:\n")
            f.write("   - Implement multi-task learning with error detection\n")
            f.write("   - Add character composition awareness\n")
            f.write("   - Use curriculum learning from simple to complex errors\n")
        
        print(f"Detailed analysis report saved: {report_path}")
    
    def analyze_samples(self, output_dir):
        """分析样本"""
        print("Starting comprehensive layer-wise attention analysis...")
        
        samples = [
            {
                'text': '乌兰巴托不到4百年白勺历史。',
                'error_type': 'simple_split_error',
                'description': 'Simple split character error: 白勺 -> 的',
                'error_tokens': ['白', '勺']
            },
            {
                'text': '我为什么喜欢阿拉木图，我觉得有几个牛寺点。',
                'error_type': 'radical_split_error',
                'description': 'Radical split error: 牛寺 -> 特',
                'error_tokens': ['牛', '寺']
            }
        ]
        
        for i, sample in enumerate(samples):
            print(f"\nAnalyzing sample {i+1}: {sample['text'][:20]}...")
            
            # 提取注意力数据
            attention_data = self.extract_attention_data(sample['text'])
            
            # 找到错误token索引
            error_indices = []
            for j, token in enumerate(attention_data['tokens']):
                if any(error_token in token for error_token in sample['error_tokens']):
                    error_indices.append(j)
            
            sample['error_indices'] = error_indices
            print(f"Error token positions: {error_indices}")
            
            # 创建样本目录
            sample_dir = os.path.join(output_dir, f'pattern_analysis_sample_{i+1:02d}_{sample["error_type"]}')
            os.makedirs(sample_dir, exist_ok=True)
            
            # 分析层级变化
            layer_analysis = self.analyze_layer_progression(attention_data, error_indices)
            
            # 生成可视化
            self.plot_layer_progression_analysis(layer_analysis, sample, sample_dir)
            
            # 生成详细报告
            self.generate_detailed_analysis_report(layer_analysis, sample, attention_data, sample_dir)
            
            print(f"Sample {i+1} analysis completed, saved in: {sample_dir}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Analyze layer-wise attention patterns for split character errors")
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/wangjiahao/Model/models/ChineseErrorCorrector3-4B',
                       help='Model path')
    parser.add_argument('--output_dir', type=str,
                       default='ChineseErrorCorrector/attention_analysis/layer_pattern_analysis',
                       help='Output directory')
    
    args = parser.parse_args()
    
    print("Layer-wise Attention Pattern Analysis for Split Character Errors")
    print("=" * 70)
    print(f"Model Path: {args.model_path}")
    print(f"Output Directory: {args.output_dir}")
    print("=" * 70)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化分析器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    analyzer = LayerAttentionPatternAnalyzer(args.model_path, device)
    
    # 分析样本
    analyzer.analyze_samples(args.output_dir)
    
    print(f"\nLayer-wise attention pattern analysis completed!")
    print(f"Results saved in: {args.output_dir}")
    print(f"Generated files:")
    print(f"  - layer_progression_analysis.png (visualization)")
    print(f"  - detailed_layer_analysis_report.txt (comprehensive analysis)")


if __name__ == '__main__':
    main()
