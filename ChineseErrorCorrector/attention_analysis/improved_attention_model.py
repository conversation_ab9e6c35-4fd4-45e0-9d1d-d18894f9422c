#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的注意力模型实现
包含错误感知注意力头、跨层错误传递机制和多尺度注意力融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Tuple, List
import math


class ErrorAwareAttentionHead(nn.Module):
    """错误感知注意力头"""
    
    def __init__(self, hidden_size: int, num_attention_heads: int, dropout: float = 0.1):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        self.attention_head_size = hidden_size // num_attention_heads
        
        # 标准注意力组件
        self.query = nn.Linear(hidden_size, hidden_size)
        self.key = nn.Linear(hidden_size, hidden_size)
        self.value = nn.Linear(hidden_size, hidden_size)
        
        # 错误检测组件
        self.error_detector = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
        # 错误增强注意力
        self.error_query = nn.Linear(hidden_size, hidden_size)
        self.error_key = nn.Linear(hidden_size, hidden_size)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(hidden_size)
        
    def transpose_for_scores(self, x):
        new_x_shape = x.size()[:-1] + (self.num_attention_heads, self.attention_head_size)
        x = x.view(*new_x_shape)
        return x.permute(0, 2, 1, 3)
    
    def forward(self, hidden_states, error_positions=None):
        batch_size, seq_len, hidden_size = hidden_states.shape
        
        # 1. 错误检测
        error_scores = self.error_detector(hidden_states)  # [batch, seq_len, 1]
        error_mask = error_scores.squeeze(-1)  # [batch, seq_len]
        
        # 2. 标准注意力计算
        query_layer = self.transpose_for_scores(self.query(hidden_states))
        key_layer = self.transpose_for_scores(self.key(hidden_states))
        value_layer = self.transpose_for_scores(self.value(hidden_states))
        
        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))
        attention_scores = attention_scores / math.sqrt(self.attention_head_size)
        
        # 3. 错误增强注意力
        error_query_layer = self.transpose_for_scores(self.error_query(hidden_states))
        error_key_layer = self.transpose_for_scores(self.error_key(hidden_states))
        
        error_attention_scores = torch.matmul(error_query_layer, error_key_layer.transpose(-1, -2))
        error_attention_scores = error_attention_scores / math.sqrt(self.attention_head_size)
        
        # 4. 错误权重增强
        # 对检测到的错误位置增加注意力权重
        error_boost = error_mask.unsqueeze(1).unsqueeze(1)  # [batch, 1, 1, seq_len]
        error_boost = error_boost.expand(-1, self.num_attention_heads, seq_len, -1)
        
        # 融合标准注意力和错误增强注意力
        enhanced_attention_scores = attention_scores + 0.5 * error_attention_scores * error_boost
        
        # 5. 应用softmax
        attention_probs = F.softmax(enhanced_attention_scores, dim=-1)
        attention_probs = self.dropout(attention_probs)
        
        # 6. 计算输出
        context_layer = torch.matmul(attention_probs, value_layer)
        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.hidden_size,)
        context_layer = context_layer.view(*new_context_layer_shape)
        
        # 7. 残差连接和层归一化
        output = self.layer_norm(context_layer + hidden_states)
        
        return output, attention_probs, error_mask


class CrossLayerErrorPropagation(nn.Module):
    """跨层错误传递机制"""
    
    def __init__(self, hidden_size: int, num_layers: int):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # 错误信息编码器
        self.error_encoder = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, hidden_size // 4)
        )
        
        # 错误信息解码器
        self.error_decoder = nn.Sequential(
            nn.Linear(hidden_size // 4, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, hidden_size)
        )
        
        # 错误权重门控
        self.error_gate = nn.Sequential(
            nn.Linear(hidden_size + hidden_size // 4, hidden_size),
            nn.Sigmoid()
        )
        
        # 层级衰减补偿
        self.layer_compensation = nn.Parameter(torch.ones(num_layers))
        
    def forward(self, hidden_states, error_info, layer_idx):
        """
        Args:
            hidden_states: 当前层的隐藏状态 [batch, seq_len, hidden_size]
            error_info: 累积的错误信息 [batch, seq_len, hidden_size//4]
            layer_idx: 当前层索引
        """
        batch_size, seq_len, hidden_size = hidden_states.shape
        
        if error_info is None:
            # 初始化错误信息
            error_info = torch.zeros(batch_size, seq_len, self.hidden_size // 4, 
                                   device=hidden_states.device)
        
        # 1. 编码当前层的错误信息
        current_error = self.error_encoder(hidden_states)
        
        # 2. 融合历史错误信息
        # 应用层级补偿权重
        compensation_weight = self.layer_compensation[layer_idx]
        fused_error = error_info + compensation_weight * current_error
        
        # 3. 解码错误信息
        decoded_error = self.error_decoder(fused_error)
        
        # 4. 门控机制决定错误信息的影响程度
        gate_input = torch.cat([hidden_states, fused_error], dim=-1)
        error_gate = self.error_gate(gate_input)
        
        # 5. 应用错误增强
        enhanced_hidden_states = hidden_states + error_gate * decoded_error
        
        return enhanced_hidden_states, fused_error


class MultiScaleAttentionFusion(nn.Module):
    """多尺度注意力融合"""
    
    def __init__(self, hidden_size: int, num_attention_heads: int):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_attention_heads = num_attention_heads
        
        # 字符级注意力（局部）
        self.char_attention = nn.MultiheadAttention(
            hidden_size, num_attention_heads // 2, dropout=0.1, batch_first=True
        )
        
        # 语义级注意力（全局）
        self.semantic_attention = nn.MultiheadAttention(
            hidden_size, num_attention_heads // 2, dropout=0.1, batch_first=True
        )
        
        # 尺度融合权重
        self.scale_fusion = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, 2),
            nn.Softmax(dim=-1)
        )
        
        # 输出投影
        self.output_projection = nn.Linear(hidden_size, hidden_size)
        self.layer_norm = nn.LayerNorm(hidden_size)
        
    def create_local_mask(self, seq_len, window_size=3):
        """创建局部注意力掩码"""
        mask = torch.full((seq_len, seq_len), float('-inf'))
        for i in range(seq_len):
            start = max(0, i - window_size)
            end = min(seq_len, i + window_size + 1)
            mask[i, start:end] = 0
        return mask
    
    def forward(self, hidden_states):
        batch_size, seq_len, hidden_size = hidden_states.shape
        
        # 1. 字符级注意力（局部窗口）
        local_mask = self.create_local_mask(seq_len).to(hidden_states.device)
        char_output, char_attention = self.char_attention(
            hidden_states, hidden_states, hidden_states, attn_mask=local_mask
        )
        
        # 2. 语义级注意力（全局）
        semantic_output, semantic_attention = self.semantic_attention(
            hidden_states, hidden_states, hidden_states
        )
        
        # 3. 自适应融合权重
        fusion_input = torch.cat([char_output, semantic_output], dim=-1)
        fusion_weights = self.scale_fusion(fusion_input)  # [batch, seq_len, 2]
        
        # 4. 加权融合
        char_weight = fusion_weights[:, :, 0:1]  # [batch, seq_len, 1]
        semantic_weight = fusion_weights[:, :, 1:2]  # [batch, seq_len, 1]
        
        fused_output = char_weight * char_output + semantic_weight * semantic_output
        
        # 5. 输出投影和残差连接
        output = self.output_projection(fused_output)
        output = self.layer_norm(output + hidden_states)
        
        return output, char_attention, semantic_attention, fusion_weights


class ImprovedTransformerLayer(nn.Module):
    """改进的Transformer层"""
    
    def __init__(self, hidden_size: int, num_attention_heads: int, 
                 intermediate_size: int, layer_idx: int, num_layers: int):
        super().__init__()
        self.layer_idx = layer_idx
        
        # 错误感知注意力头
        self.error_aware_attention = ErrorAwareAttentionHead(
            hidden_size, num_attention_heads
        )
        
        # 跨层错误传递
        self.cross_layer_error = CrossLayerErrorPropagation(hidden_size, num_layers)
        
        # 多尺度注意力融合
        self.multi_scale_attention = MultiScaleAttentionFusion(
            hidden_size, num_attention_heads
        )
        
        # 前馈网络
        self.intermediate = nn.Linear(hidden_size, intermediate_size)
        self.output = nn.Linear(intermediate_size, hidden_size)
        self.layer_norm = nn.LayerNorm(hidden_size)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, hidden_states, error_info=None, error_positions=None):
        # 1. 跨层错误传递
        enhanced_hidden_states, updated_error_info = self.cross_layer_error(
            hidden_states, error_info, self.layer_idx
        )
        
        # 2. 错误感知注意力
        attention_output, attention_probs, error_mask = self.error_aware_attention(
            enhanced_hidden_states, error_positions
        )
        
        # 3. 多尺度注意力融合
        fused_output, char_attention, semantic_attention, fusion_weights = \
            self.multi_scale_attention(attention_output)
        
        # 4. 前馈网络
        intermediate_output = F.gelu(self.intermediate(fused_output))
        layer_output = self.output(intermediate_output)
        layer_output = self.layer_norm(layer_output + fused_output)
        layer_output = self.dropout(layer_output)
        
        return {
            'hidden_states': layer_output,
            'error_info': updated_error_info,
            'attention_probs': attention_probs,
            'error_mask': error_mask,
            'char_attention': char_attention,
            'semantic_attention': semantic_attention,
            'fusion_weights': fusion_weights
        }


class ImprovedAttentionModel(nn.Module):
    """改进的注意力模型"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.hidden_size = config.hidden_size
        self.num_layers = config.num_hidden_layers
        self.num_attention_heads = config.num_attention_heads
        
        # 改进的Transformer层
        self.layers = nn.ModuleList([
            ImprovedTransformerLayer(
                self.hidden_size,
                self.num_attention_heads,
                config.intermediate_size,
                layer_idx,
                self.num_layers
            ) for layer_idx in range(self.num_layers)
        ])
        
        # 错误位置预测头
        self.error_prediction_head = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, hidden_states, error_positions=None):
        batch_size, seq_len, hidden_size = hidden_states.shape
        
        # 初始化错误信息
        error_info = None
        
        # 存储每层的输出
        all_hidden_states = []
        all_attention_probs = []
        all_error_masks = []
        all_char_attentions = []
        all_semantic_attentions = []
        all_fusion_weights = []
        
        current_hidden_states = hidden_states
        
        # 逐层前向传播
        for layer in self.layers:
            layer_outputs = layer(
                current_hidden_states, 
                error_info, 
                error_positions
            )
            
            current_hidden_states = layer_outputs['hidden_states']
            error_info = layer_outputs['error_info']
            
            # 保存输出
            all_hidden_states.append(current_hidden_states)
            all_attention_probs.append(layer_outputs['attention_probs'])
            all_error_masks.append(layer_outputs['error_mask'])
            all_char_attentions.append(layer_outputs['char_attention'])
            all_semantic_attentions.append(layer_outputs['semantic_attention'])
            all_fusion_weights.append(layer_outputs['fusion_weights'])
        
        # 错误位置预测
        error_predictions = self.error_prediction_head(current_hidden_states)
        
        return {
            'last_hidden_state': current_hidden_states,
            'hidden_states': all_hidden_states,
            'attention_probs': all_attention_probs,
            'error_masks': all_error_masks,
            'char_attentions': all_char_attentions,
            'semantic_attentions': all_semantic_attentions,
            'fusion_weights': all_fusion_weights,
            'error_predictions': error_predictions
        }


def create_improved_model_config():
    """创建改进模型的配置"""
    class Config:
        def __init__(self):
            self.hidden_size = 768
            self.num_hidden_layers = 12  # 简化版本，使用12层
            self.num_attention_heads = 12
            self.intermediate_size = 3072
            self.vocab_size = 50000
            self.max_position_embeddings = 512
            
    return Config()


if __name__ == "__main__":
    # 测试改进的模型
    config = create_improved_model_config()
    model = ImprovedAttentionModel(config)
    
    # 创建测试输入
    batch_size, seq_len = 2, 10
    hidden_states = torch.randn(batch_size, seq_len, config.hidden_size)
    error_positions = torch.tensor([[0, 0, 1, 1, 0, 0, 0, 0, 0, 0],
                                   [0, 0, 0, 1, 1, 0, 0, 0, 0, 0]], dtype=torch.float)
    
    # 前向传播
    outputs = model(hidden_states, error_positions)
    
    print("改进模型测试成功!")
    print(f"输出形状: {outputs['last_hidden_state'].shape}")
    print(f"注意力层数: {len(outputs['attention_probs'])}")
    print(f"错误预测形状: {outputs['error_predictions'].shape}")
