#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
课程学习策略生成器
为大模型设计渐进式的拆分字错误学习课程
"""

import os
import sys
import json
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
import math

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)


@dataclass
class LearningStage:
    """学习阶段定义"""
    stage_id: int
    stage_name: str
    difficulty_level: str
    learning_objectives: List[str]
    error_patterns: List[str]
    sample_count: int
    prerequisites: List[int]
    estimated_duration: str


@dataclass
class CurriculumSample:
    """课程样本"""
    sample_id: str
    stage_id: int
    difficulty_score: float
    error_complexity: int
    semantic_depth: int
    content: Dict[str, Any]
    learning_targets: List[str]


class CurriculumLearningStrategy:
    """课程学习策略生成器"""
    
    def __init__(self):
        self.difficulty_factors = {
            'character_complexity': 0.3,    # 字符复杂度
            'context_length': 0.2,          # 上下文长度
            'semantic_abstractness': 0.25,   # 语义抽象度
            'error_frequency': 0.15,         # 错误频率
            'linguistic_features': 0.1       # 语言学特征复杂度
        }
        
        self.learning_stages = []
        self.curriculum_samples = []
        
    def design_curriculum_stages(self, knowledge_base: Dict[str, Any]) -> List[LearningStage]:
        """设计课程学习阶段"""
        print("📚 设计课程学习阶段...")
        
        stages = [
            # 阶段1：基础字符识别
            LearningStage(
                stage_id=1,
                stage_name="基础字符识别",
                difficulty_level="初级",
                learning_objectives=[
                    "识别简单的拆分字错误",
                    "理解字符的基本结构",
                    "掌握常见错误模式"
                ],
                error_patterns=["单字符拆分", "高频错误"],
                sample_count=200,
                prerequisites=[],
                estimated_duration="1-2天"
            ),
            
            # 阶段2：上下文理解
            LearningStage(
                stage_id=2,
                stage_name="上下文理解",
                difficulty_level="初级",
                learning_objectives=[
                    "结合上下文判断字符正确性",
                    "理解语境对字符选择的影响",
                    "掌握上下文线索的使用"
                ],
                error_patterns=["上下文相关错误", "语境敏感错误"],
                sample_count=300,
                prerequisites=[1],
                estimated_duration="2-3天"
            ),
            
            # 阶段3：语义场分析
            LearningStage(
                stage_id=3,
                stage_name="语义场分析",
                difficulty_level="中级",
                learning_objectives=[
                    "理解不同语义场的错误特征",
                    "掌握领域特定的纠错规律",
                    "分析语义场间的差异"
                ],
                error_patterns=["领域特定错误", "语义场相关错误"],
                sample_count=400,
                prerequisites=[1, 2],
                estimated_duration="3-4天"
            ),
            
            # 阶段4：复杂模式识别
            LearningStage(
                stage_id=4,
                stage_name="复杂模式识别",
                difficulty_level="中级",
                learning_objectives=[
                    "识别复杂的拆分字模式",
                    "处理多字符拆分错误",
                    "理解错误的层次结构"
                ],
                error_patterns=["多字符拆分", "复合错误", "嵌套错误"],
                sample_count=350,
                prerequisites=[2, 3],
                estimated_duration="3-4天"
            ),
            
            # 阶段5：语言学特征分析
            LearningStage(
                stage_id=5,
                stage_name="语言学特征分析",
                difficulty_level="高级",
                learning_objectives=[
                    "分析错误的语音、形态、句法特征",
                    "理解错误的语言学机制",
                    "掌握深层语言学分析"
                ],
                error_patterns=["语言学复杂错误", "理论分析错误"],
                sample_count=300,
                prerequisites=[3, 4],
                estimated_duration="4-5天"
            ),
            
            # 阶段6：跨领域迁移
            LearningStage(
                stage_id=6,
                stage_name="跨领域迁移",
                difficulty_level="高级",
                learning_objectives=[
                    "将学到的知识迁移到新领域",
                    "处理未见过的错误模式",
                    "进行创新性的错误分析"
                ],
                error_patterns=["新颖错误", "跨领域错误", "创新模式"],
                sample_count=250,
                prerequisites=[4, 5],
                estimated_duration="3-4天"
            ),
            
            # 阶段7：综合应用
            LearningStage(
                stage_id=7,
                stage_name="综合应用",
                difficulty_level="专家级",
                learning_objectives=[
                    "综合运用所有学到的知识",
                    "处理复杂的实际应用场景",
                    "进行高质量的错误分析和纠正"
                ],
                error_patterns=["综合性错误", "实际应用错误"],
                sample_count=200,
                prerequisites=[5, 6],
                estimated_duration="5-7天"
            )
        ]
        
        self.learning_stages = stages
        print(f"✅ 设计了 {len(stages)} 个学习阶段")
        return stages
    
    def calculate_sample_difficulty(self, error_char: str, correct_char: str, 
                                  context: str, metadata: Dict[str, Any]) -> float:
        """计算样本难度分数"""
        difficulty_score = 0.0
        
        # 字符复杂度
        char_complexity = (len(error_char) + len(correct_char)) / 2
        normalized_char_complexity = min(1.0, char_complexity / 5.0)
        difficulty_score += self.difficulty_factors['character_complexity'] * normalized_char_complexity
        
        # 上下文长度
        context_length = len(context)
        normalized_context_length = min(1.0, context_length / 100.0)
        difficulty_score += self.difficulty_factors['context_length'] * normalized_context_length
        
        # 语义抽象度
        abstractness = self._calculate_semantic_abstractness(error_char, correct_char, context)
        difficulty_score += self.difficulty_factors['semantic_abstractness'] * abstractness
        
        # 错误频率（频率越低，难度越高）
        frequency = metadata.get('frequency', 1)
        frequency_difficulty = 1.0 - min(1.0, frequency / 10.0)
        difficulty_score += self.difficulty_factors['error_frequency'] * frequency_difficulty
        
        # 语言学特征复杂度
        linguistic_complexity = self._calculate_linguistic_complexity(metadata)
        difficulty_score += self.difficulty_factors['linguistic_features'] * linguistic_complexity
        
        return min(1.0, difficulty_score)
    
    def _calculate_semantic_abstractness(self, error_char: str, correct_char: str, context: str) -> float:
        """计算语义抽象度"""
        abstract_indicators = ['思想', '概念', '理论', '原则', '精神', '意识', '观念', '哲学']
        concrete_indicators = ['手', '脚', '桌子', '椅子', '房子', '车', '书', '笔']
        
        text = error_char + correct_char + context
        
        abstract_count = sum(1 for indicator in abstract_indicators if indicator in text)
        concrete_count = sum(1 for indicator in concrete_indicators if indicator in text)
        
        if abstract_count + concrete_count == 0:
            return 0.5  # 中性
        
        return abstract_count / (abstract_count + concrete_count)
    
    def _calculate_linguistic_complexity(self, metadata: Dict[str, Any]) -> float:
        """计算语言学特征复杂度"""
        complexity_score = 0.0
        
        linguistic_features = metadata.get('linguistic_features', {})
        
        # 语音特征复杂度
        phonetic_features = linguistic_features.get('phonetic', {})
        if phonetic_features.get('is_split_error', False):
            complexity_score += 0.3
        
        # 形态学特征复杂度
        morphological_features = linguistic_features.get('morphological', {})
        morpheme_count = morphological_features.get('morpheme_count', 1)
        complexity_score += min(0.3, morpheme_count / 5.0)
        
        # 句法特征复杂度
        syntactic_features = linguistic_features.get('syntactic', {})
        sentence_length = syntactic_features.get('sentence_length', 0)
        complexity_score += min(0.2, sentence_length / 100.0)
        
        # 语义特征复杂度
        semantic_features = linguistic_features.get('semantic', {})
        conceptual_complexity = semantic_features.get('conceptual_complexity', 0)
        complexity_score += min(0.2, conceptual_complexity / 50.0)
        
        return min(1.0, complexity_score)
    
    def generate_curriculum_samples(self, knowledge_base: Dict[str, Any], 
                                  stages: List[LearningStage]) -> List[CurriculumSample]:
        """生成课程样本"""
        print("📝 生成课程样本...")
        
        all_samples = []
        character_mappings = knowledge_base['character_mappings']
        context_patterns = knowledge_base['context_patterns']
        
        # 为每个阶段生成样本
        for stage in stages:
            stage_samples = []
            
            # 根据阶段难度筛选合适的错误模式
            suitable_errors = self._filter_errors_by_stage(
                character_mappings, stage, knowledge_base
            )
            
            # 生成指定数量的样本
            for i in range(stage.sample_count):
                if not suitable_errors:
                    continue
                
                error_char = np.random.choice(list(suitable_errors.keys()))
                mapping = np.random.choice(suitable_errors[error_char])
                correct_char = mapping['correct_char']
                
                # 构造上下文
                context = self._generate_context_for_stage(
                    error_char, context_patterns, stage
                )
                
                # 计算难度分数
                difficulty_score = self.calculate_sample_difficulty(
                    error_char, correct_char, context, mapping
                )
                
                # 创建样本
                sample = CurriculumSample(
                    sample_id=f"stage_{stage.stage_id}_sample_{i+1}",
                    stage_id=stage.stage_id,
                    difficulty_score=difficulty_score,
                    error_complexity=len(error_char),
                    semantic_depth=self._calculate_semantic_depth(mapping),
                    content={
                        'error_char': error_char,
                        'correct_char': correct_char,
                        'context': context,
                        'stage_name': stage.stage_name,
                        'learning_objectives': stage.learning_objectives
                    },
                    learning_targets=stage.learning_objectives
                )
                
                stage_samples.append(sample)
            
            # 按难度排序
            stage_samples.sort(key=lambda x: x.difficulty_score)
            all_samples.extend(stage_samples)
            
            print(f"  阶段 {stage.stage_id} ({stage.stage_name}): {len(stage_samples)} 个样本")
        
        self.curriculum_samples = all_samples
        print(f"✅ 总共生成了 {len(all_samples)} 个课程样本")
        return all_samples
    
    def _filter_errors_by_stage(self, character_mappings: Dict, 
                              stage: LearningStage, 
                              knowledge_base: Dict[str, Any]) -> Dict:
        """根据阶段筛选合适的错误"""
        filtered_errors = {}
        
        for error_char, mappings in character_mappings.items():
            for mapping in mappings:
                # 根据阶段特点筛选
                if stage.stage_id == 1:  # 基础阶段
                    if len(error_char) <= 2 and mapping.get('frequency', 0) >= 3:
                        if error_char not in filtered_errors:
                            filtered_errors[error_char] = []
                        filtered_errors[error_char].append(mapping)
                
                elif stage.stage_id == 2:  # 上下文理解
                    if mapping.get('semantic_field') in ['学习教育', '生活日常']:
                        if error_char not in filtered_errors:
                            filtered_errors[error_char] = []
                        filtered_errors[error_char].append(mapping)
                
                elif stage.stage_id == 3:  # 语义场分析
                    if mapping.get('semantic_field') != '其他':
                        if error_char not in filtered_errors:
                            filtered_errors[error_char] = []
                        filtered_errors[error_char].append(mapping)
                
                elif stage.stage_id >= 4:  # 高级阶段
                    if error_char not in filtered_errors:
                        filtered_errors[error_char] = []
                    filtered_errors[error_char].append(mapping)
        
        return filtered_errors
    
    def _generate_context_for_stage(self, error_char: str, 
                                   context_patterns: Dict, 
                                   stage: LearningStage) -> str:
        """为特定阶段生成上下文"""
        # 尝试从上下文模式中找到合适的上下文
        for context_key, patterns in context_patterns.items():
            for pattern in patterns:
                if pattern['error_char'] == error_char:
                    context_before, context_after = context_key
                    return f"{context_before}{error_char}{context_after}"
        
        # 如果没找到，根据阶段生成简单上下文
        if stage.stage_id <= 2:
            return f"这个{error_char}很重要。"
        elif stage.stage_id <= 4:
            return f"在学习过程中，我们发现{error_char}的使用需要特别注意。"
        else:
            return f"从语言学角度分析，{error_char}的使用体现了复杂的语义特征。"
    
    def _calculate_semantic_depth(self, mapping: Dict[str, Any]) -> int:
        """计算语义深度"""
        semantic_field = mapping.get('semantic_field', '其他')
        
        depth_mapping = {
            '生活日常': 1,
            '学习教育': 2,
            '人际关系': 2,
            '工作职业': 3,
            '时间空间': 3,
            '情感表达': 4,
            '抽象概念': 5,
            '其他': 1
        }
        
        return depth_mapping.get(semantic_field, 1)
    
    def create_learning_schedule(self, stages: List[LearningStage]) -> Dict[str, Any]:
        """创建学习计划"""
        print("📅 创建学习计划...")
        
        schedule = {
            'total_stages': len(stages),
            'estimated_total_duration': '20-30天',
            'stage_schedule': [],
            'prerequisites_graph': {},
            'learning_path': []
        }
        
        # 构建先决条件图
        for stage in stages:
            schedule['prerequisites_graph'][stage.stage_id] = stage.prerequisites
        
        # 生成学习路径
        learning_path = self._generate_learning_path(stages)
        schedule['learning_path'] = learning_path
        
        # 详细阶段计划
        for stage in stages:
            stage_info = {
                'stage_id': stage.stage_id,
                'stage_name': stage.stage_name,
                'difficulty_level': stage.difficulty_level,
                'sample_count': stage.sample_count,
                'estimated_duration': stage.estimated_duration,
                'learning_objectives': stage.learning_objectives,
                'prerequisites': stage.prerequisites,
                'recommended_order': learning_path.index(stage.stage_id) + 1
            }
            schedule['stage_schedule'].append(stage_info)
        
        print(f"✅ 学习计划创建完成，包含 {len(stages)} 个阶段")
        return schedule
    
    def _generate_learning_path(self, stages: List[LearningStage]) -> List[int]:
        """生成学习路径（拓扑排序）"""
        # 简单的拓扑排序实现
        in_degree = {stage.stage_id: 0 for stage in stages}
        graph = {stage.stage_id: [] for stage in stages}
        
        # 构建图
        for stage in stages:
            for prereq in stage.prerequisites:
                graph[prereq].append(stage.stage_id)
                in_degree[stage.stage_id] += 1
        
        # 拓扑排序
        queue = [stage_id for stage_id, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        return result
    
    def save_curriculum_data(self, stages: List[LearningStage], 
                           samples: List[CurriculumSample],
                           schedule: Dict[str, Any],
                           output_dir: str) -> None:
        """保存课程数据"""
        print(f"💾 保存课程数据到: {output_dir}")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存阶段定义
        stages_data = []
        for stage in stages:
            stages_data.append({
                'stage_id': stage.stage_id,
                'stage_name': stage.stage_name,
                'difficulty_level': stage.difficulty_level,
                'learning_objectives': stage.learning_objectives,
                'error_patterns': stage.error_patterns,
                'sample_count': stage.sample_count,
                'prerequisites': stage.prerequisites,
                'estimated_duration': stage.estimated_duration
            })
        
        stages_path = os.path.join(output_dir, "learning_stages.json")
        with open(stages_path, 'w', encoding='utf-8') as f:
            json.dump(stages_data, f, ensure_ascii=False, indent=2)
        
        # 保存样本数据（按阶段分组）
        samples_by_stage = defaultdict(list)
        for sample in samples:
            sample_data = {
                'sample_id': sample.sample_id,
                'difficulty_score': sample.difficulty_score,
                'error_complexity': sample.error_complexity,
                'semantic_depth': sample.semantic_depth,
                'content': sample.content,
                'learning_targets': sample.learning_targets
            }
            samples_by_stage[sample.stage_id].append(sample_data)
        
        for stage_id, stage_samples in samples_by_stage.items():
            samples_path = os.path.join(output_dir, f"stage_{stage_id}_samples.json")
            with open(samples_path, 'w', encoding='utf-8') as f:
                json.dump(stage_samples, f, ensure_ascii=False, indent=2)
        
        # 保存学习计划
        schedule_path = os.path.join(output_dir, "learning_schedule.json")
        with open(schedule_path, 'w', encoding='utf-8') as f:
            json.dump(schedule, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 课程数据保存完成:")
        print(f"  - 阶段定义: {stages_path}")
        print(f"  - 样本数据: {len(samples_by_stage)} 个阶段文件")
        print(f"  - 学习计划: {schedule_path}")


def main():
    """主函数"""
    print("🚀 课程学习策略生成器启动")
    print("=" * 50)
    
    # 初始化策略生成器
    strategy = CurriculumLearningStrategy()
    
    # 导入必要的模块
    from ChineseErrorCorrector.semantic_learning.deep_semantic_learner import DeepSemanticLearner
    
    learner = DeepSemanticLearner()
    
    # 数据路径
    data_path = "ChineseErrorCorrector/data/split_data/split_errors_data.json"
    output_dir = "ChineseErrorCorrector/semantic_learning/curriculum_data"
    
    try:
        # 1. 加载数据并构建知识库
        print("📂 加载数据并构建知识库...")
        data = learner.load_split_error_data(data_path)
        patterns = learner.extract_semantic_patterns(data)
        knowledge_base = learner.build_semantic_knowledge_base(patterns)
        
        # 2. 设计课程阶段
        stages = strategy.design_curriculum_stages(knowledge_base)
        
        # 3. 生成课程样本
        samples = strategy.generate_curriculum_samples(knowledge_base, stages)
        
        # 4. 创建学习计划
        schedule = strategy.create_learning_schedule(stages)
        
        # 5. 保存课程数据
        strategy.save_curriculum_data(stages, samples, schedule, output_dir)
        
        # 6. 生成统计报告
        total_samples = len(samples)
        avg_difficulty = np.mean([s.difficulty_score for s in samples])
        
        print(f"\n🎉 课程学习策略生成完成!")
        print(f"📊 统计信息:")
        print(f"  - 学习阶段: {len(stages)} 个")
        print(f"  - 训练样本: {total_samples} 个")
        print(f"  - 平均难度: {avg_difficulty:.3f}")
        print(f"  - 预计学习时间: 20-30天")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
