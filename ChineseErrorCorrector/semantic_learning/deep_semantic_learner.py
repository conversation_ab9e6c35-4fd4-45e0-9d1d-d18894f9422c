#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度语义学习系统
利用拆分字错误数据让大模型学习更深层的语义信息
"""

import os
import sys
import json
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import defaultdict, Counter
import jieba
import jieba.posseg as pseg
from dataclasses import dataclass
import pickle

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)


@dataclass
class SemanticPattern:
    """语义模式数据结构"""
    error_char: str
    correct_char: str
    context_before: str
    context_after: str
    semantic_field: str
    frequency: int
    confidence: float
    linguistic_features: Dict[str, Any]


@dataclass
class ContextualEmbedding:
    """上下文嵌入表示"""
    char_embedding: np.ndarray
    context_embedding: np.ndarray
    semantic_embedding: np.ndarray
    position_encoding: np.ndarray


class DeepSemanticLearner:
    """深度语义学习器"""
    
    def __init__(self):
        self.semantic_patterns = []
        self.character_mappings = defaultdict(list)
        self.context_patterns = defaultdict(list)
        self.semantic_fields = {}
        self.linguistic_features = {}
        
        # 语义场分类
        self.semantic_field_keywords = {
            '情感表达': ['喜欢', '爱', '恨', '高兴', '难过', '激动', '失望', '满意'],
            '学习教育': ['学习', '教育', '老师', '学生', '课程', '考试', '知识', '技能'],
            '人际关系': ['朋友', '家人', '同事', '伴侣', '关系', '交流', '沟通', '理解'],
            '工作职业': ['工作', '职业', '公司', '同事', '老板', '项目', '任务', '责任'],
            '生活日常': ['生活', '日常', '家庭', '购物', '做饭', '休息', '娱乐', '运动'],
            '时间空间': ['时间', '地点', '位置', '方向', '距离', '速度', '大小', '形状'],
            '抽象概念': ['思想', '观念', '理论', '原则', '方法', '策略', '计划', '目标']
        }
        
        # 语言学特征分析器
        self.linguistic_analyzers = {
            'phonetic': self._analyze_phonetic_features,
            'morphological': self._analyze_morphological_features,
            'syntactic': self._analyze_syntactic_features,
            'semantic': self._analyze_semantic_features,
            'pragmatic': self._analyze_pragmatic_features
        }
    
    def load_split_error_data(self, data_path: str) -> List[Dict]:
        """加载拆分字错误数据"""
        print(f"📂 加载拆分字错误数据: {data_path}")
        
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 加载了 {len(data)} 条拆分字错误数据")
        return data
    
    def extract_semantic_patterns(self, data: List[Dict]) -> List[SemanticPattern]:
        """提取语义模式"""
        print("🔍 提取语义模式...")
        
        patterns = []
        
        for item in data:
            source = item['source']
            metadata = item.get('metadata', {})
            errors = metadata.get('errors', [])
            
            for error in errors:
                error_char = error['error_char']
                correct_char = error['correct_char']
                position = error['position']
                
                # 提取上下文
                context_window = 5
                start_pos = max(0, position - context_window)
                end_pos = min(len(source), position + len(error_char) + context_window)
                
                context_before = source[start_pos:position]
                context_after = source[position + len(error_char):end_pos]
                
                # 分析语义场
                semantic_field = self._classify_semantic_field(source, metadata)
                
                # 提取语言学特征
                linguistic_features = self._extract_linguistic_features(
                    error_char, correct_char, context_before, context_after, source
                )
                
                pattern = SemanticPattern(
                    error_char=error_char,
                    correct_char=correct_char,
                    context_before=context_before,
                    context_after=context_after,
                    semantic_field=semantic_field,
                    frequency=1,
                    confidence=0.8,
                    linguistic_features=linguistic_features
                )
                
                patterns.append(pattern)
        
        # 合并相同模式并计算频率
        pattern_dict = defaultdict(list)
        for pattern in patterns:
            key = (pattern.error_char, pattern.correct_char, pattern.semantic_field)
            pattern_dict[key].append(pattern)
        
        merged_patterns = []
        for key, pattern_list in pattern_dict.items():
            if len(pattern_list) > 1:
                # 合并模式
                merged_pattern = pattern_list[0]
                merged_pattern.frequency = len(pattern_list)
                merged_pattern.confidence = min(1.0, 0.5 + 0.1 * len(pattern_list))
                merged_patterns.append(merged_pattern)
            else:
                merged_patterns.extend(pattern_list)
        
        print(f"✅ 提取了 {len(merged_patterns)} 个语义模式")
        return merged_patterns
    
    def _classify_semantic_field(self, text: str, metadata: Dict) -> str:
        """分类语义场"""
        # 基于主题分类
        topic = metadata.get('topic', '')
        if '理想' in topic or '伴侣' in topic:
            return '人际关系'
        elif '学习' in topic or '汉语' in topic:
            return '学习教育'
        elif '工作' in topic or '职业' in topic:
            return '工作职业'
        
        # 基于关键词分类
        for field, keywords in self.semantic_field_keywords.items():
            if any(keyword in text for keyword in keywords):
                return field
        
        return '其他'
    
    def _extract_linguistic_features(self, error_char: str, correct_char: str, 
                                   context_before: str, context_after: str, 
                                   full_text: str) -> Dict[str, Any]:
        """提取语言学特征"""
        features = {}
        
        for feature_type, analyzer in self.linguistic_analyzers.items():
            try:
                features[feature_type] = analyzer(
                    error_char, correct_char, context_before, context_after, full_text
                )
            except Exception as e:
                features[feature_type] = {'error': str(e)}
        
        return features
    
    def _analyze_phonetic_features(self, error_char: str, correct_char: str, 
                                 context_before: str, context_after: str, 
                                 full_text: str) -> Dict[str, Any]:
        """分析语音特征"""
        return {
            'error_length': len(error_char),
            'correct_length': len(correct_char),
            'is_split_error': len(error_char) > len(correct_char),
            'character_similarity': self._calculate_character_similarity(error_char, correct_char)
        }
    
    def _analyze_morphological_features(self, error_char: str, correct_char: str,
                                      context_before: str, context_after: str,
                                      full_text: str) -> Dict[str, Any]:
        """分析形态学特征"""
        # 词性分析
        words_before = list(pseg.cut(context_before))
        words_after = list(pseg.cut(context_after))
        
        return {
            'pos_before': [word.flag for word, pos in words_before[-2:]] if words_before else [],
            'pos_after': [word.flag for word, pos in words_after[:2]] if words_after else [],
            'is_compound_error': ',' in error_char or '，' in error_char,
            'morpheme_count': len(error_char)
        }
    
    def _analyze_syntactic_features(self, error_char: str, correct_char: str,
                                  context_before: str, context_after: str,
                                  full_text: str) -> Dict[str, Any]:
        """分析句法特征"""
        # 句法位置分析
        sentence_parts = full_text.split('，')
        error_position = 'unknown'
        
        for i, part in enumerate(sentence_parts):
            if error_char in part:
                if i == 0:
                    error_position = 'beginning'
                elif i == len(sentence_parts) - 1:
                    error_position = 'end'
                else:
                    error_position = 'middle'
                break
        
        return {
            'sentence_position': error_position,
            'clause_count': len(sentence_parts),
            'has_conjunction': any(conj in full_text for conj in ['但是', '然而', '因此', '所以']),
            'sentence_length': len(full_text)
        }
    
    def _analyze_semantic_features(self, error_char: str, correct_char: str,
                                 context_before: str, context_after: str,
                                 full_text: str) -> Dict[str, Any]:
        """分析语义特征"""
        # 语义角色分析
        semantic_roles = {
            'agent': self._contains_agent_markers(context_before + error_char + context_after),
            'patient': self._contains_patient_markers(context_before + error_char + context_after),
            'instrument': self._contains_instrument_markers(context_before + error_char + context_after),
            'location': self._contains_location_markers(context_before + error_char + context_after)
        }
        
        return {
            'semantic_roles': semantic_roles,
            'abstractness_level': self._calculate_abstractness(error_char, correct_char),
            'emotional_valence': self._analyze_emotional_valence(full_text),
            'conceptual_complexity': len(correct_char) + len(context_before) + len(context_after)
        }
    
    def _analyze_pragmatic_features(self, error_char: str, correct_char: str,
                                  context_before: str, context_after: str,
                                  full_text: str) -> Dict[str, Any]:
        """分析语用特征"""
        return {
            'formality_level': self._assess_formality(full_text),
            'discourse_markers': self._identify_discourse_markers(full_text),
            'speech_act': self._classify_speech_act(full_text),
            'register': self._determine_register(full_text)
        }
    
    def _calculate_character_similarity(self, char1: str, char2: str) -> float:
        """计算字符相似度"""
        if not char1 or not char2:
            return 0.0
        
        # 简单的编辑距离相似度
        def edit_distance(s1, s2):
            m, n = len(s1), len(s2)
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(m + 1):
                dp[i][0] = i
            for j in range(n + 1):
                dp[0][j] = j
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if s1[i-1] == s2[j-1]:
                        dp[i][j] = dp[i-1][j-1]
                    else:
                        dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
            
            return dp[m][n]
        
        max_len = max(len(char1), len(char2))
        if max_len == 0:
            return 1.0
        
        distance = edit_distance(char1, char2)
        return 1.0 - (distance / max_len)
    
    def _contains_agent_markers(self, text: str) -> bool:
        """检测施事标记"""
        agent_markers = ['我', '你', '他', '她', '我们', '你们', '他们']
        return any(marker in text for marker in agent_markers)
    
    def _contains_patient_markers(self, text: str) -> bool:
        """检测受事标记"""
        patient_markers = ['被', '让', '使', '把']
        return any(marker in text for marker in patient_markers)
    
    def _contains_instrument_markers(self, text: str) -> bool:
        """检测工具标记"""
        instrument_markers = ['用', '通过', '借助', '依靠']
        return any(marker in text for marker in instrument_markers)
    
    def _contains_location_markers(self, text: str) -> bool:
        """检测位置标记"""
        location_markers = ['在', '到', '从', '向', '朝']
        return any(marker in text for marker in location_markers)
    
    def _calculate_abstractness(self, error_char: str, correct_char: str) -> float:
        """计算抽象程度"""
        abstract_chars = ['思', '想', '念', '理', '概', '意', '识', '神', '精', '魂']
        concrete_chars = ['手', '脚', '眼', '口', '头', '身', '物', '东', '西', '器']
        
        error_abstract = sum(1 for char in error_char if char in abstract_chars)
        error_concrete = sum(1 for char in error_char if char in concrete_chars)
        
        correct_abstract = sum(1 for char in correct_char if char in abstract_chars)
        correct_concrete = sum(1 for char in correct_char if char in concrete_chars)
        
        total_chars = len(error_char) + len(correct_char)
        if total_chars == 0:
            return 0.5
        
        abstractness = (error_abstract + correct_abstract) / total_chars
        return min(1.0, abstractness)
    
    def _analyze_emotional_valence(self, text: str) -> str:
        """分析情感倾向"""
        positive_words = ['好', '喜欢', '高兴', '满意', '成功', '优秀', '美好']
        negative_words = ['坏', '讨厌', '难过', '失望', '失败', '糟糕', '困难']
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _assess_formality(self, text: str) -> str:
        """评估正式程度"""
        formal_markers = ['因此', '然而', '此外', '综上所述', '根据', '基于']
        informal_markers = ['嗯', '哦', '呀', '吧', '呢', '啊']
        
        formal_count = sum(1 for marker in formal_markers if marker in text)
        informal_count = sum(1 for marker in informal_markers if marker in text)
        
        if formal_count > informal_count:
            return 'formal'
        elif informal_count > formal_count:
            return 'informal'
        else:
            return 'neutral'
    
    def _identify_discourse_markers(self, text: str) -> List[str]:
        """识别话语标记"""
        discourse_markers = ['但是', '然而', '因此', '所以', '首先', '其次', '最后', '总之']
        return [marker for marker in discourse_markers if marker in text]
    
    def _classify_speech_act(self, text: str) -> str:
        """分类言语行为"""
        if '？' in text or '吗' in text:
            return 'question'
        elif '！' in text or '请' in text:
            return 'request'
        elif '应该' in text or '必须' in text:
            return 'directive'
        else:
            return 'statement'
    
    def _determine_register(self, text: str) -> str:
        """确定语域"""
        academic_terms = ['研究', '分析', '理论', '方法', '结果', '结论']
        casual_terms = ['觉得', '感觉', '挺', '蛮', '特别', '非常']
        
        academic_count = sum(1 for term in academic_terms if term in text)
        casual_count = sum(1 for term in casual_terms if term in text)
        
        if academic_count > casual_count:
            return 'academic'
        elif casual_count > academic_count:
            return 'casual'
        else:
            return 'neutral'
    
    def build_semantic_knowledge_base(self, patterns: List[SemanticPattern]) -> Dict[str, Any]:
        """构建语义知识库"""
        print("🏗️ 构建语义知识库...")
        
        knowledge_base = {
            'character_mappings': defaultdict(list),
            'context_patterns': defaultdict(list),
            'semantic_field_distributions': defaultdict(Counter),
            'linguistic_feature_correlations': defaultdict(dict),
            'error_prediction_rules': [],
            'semantic_embeddings': {}
        }
        
        # 构建字符映射
        for pattern in patterns:
            key = pattern.error_char
            knowledge_base['character_mappings'][key].append({
                'correct_char': pattern.correct_char,
                'frequency': pattern.frequency,
                'confidence': pattern.confidence,
                'semantic_field': pattern.semantic_field
            })
        
        # 构建上下文模式
        for pattern in patterns:
            context_key = (pattern.context_before[-3:], pattern.context_after[:3])
            knowledge_base['context_patterns'][context_key].append({
                'error_char': pattern.error_char,
                'correct_char': pattern.correct_char,
                'frequency': pattern.frequency
            })
        
        # 语义场分布
        for pattern in patterns:
            knowledge_base['semantic_field_distributions'][pattern.semantic_field][pattern.error_char] += pattern.frequency
        
        # 语言学特征关联
        for pattern in patterns:
            for feature_type, features in pattern.linguistic_features.items():
                if feature_type not in knowledge_base['linguistic_feature_correlations']:
                    knowledge_base['linguistic_feature_correlations'][feature_type] = defaultdict(list)
                
                knowledge_base['linguistic_feature_correlations'][feature_type][pattern.error_char].append({
                    'correct_char': pattern.correct_char,
                    'features': features,
                    'frequency': pattern.frequency
                })
        
        print(f"✅ 知识库构建完成:")
        print(f"  - 字符映射: {len(knowledge_base['character_mappings'])} 个")
        print(f"  - 上下文模式: {len(knowledge_base['context_patterns'])} 个")
        print(f"  - 语义场: {len(knowledge_base['semantic_field_distributions'])} 个")
        
        return knowledge_base
    
    def generate_training_data_for_llm(self, knowledge_base: Dict[str, Any],
                                     output_path: str) -> None:
        """为大模型生成训练数据"""
        print("📚 为大模型生成训练数据...")

        training_examples = []

        # 生成基于语义模式的训练样本
        for error_char, mappings in knowledge_base['character_mappings'].items():
            for mapping in mappings:
                correct_char = mapping['correct_char']
                semantic_field = mapping['semantic_field']

                # 生成多种训练格式
                examples = self._generate_training_examples(
                    error_char, correct_char, semantic_field, knowledge_base
                )
                training_examples.extend(examples)

        # 生成高级语义理解样本
        advanced_examples = self._generate_advanced_semantic_examples(knowledge_base)
        training_examples.extend(advanced_examples)

        # 生成对比学习样本
        contrastive_examples = self._generate_contrastive_examples(knowledge_base)
        training_examples.extend(contrastive_examples)

        # 保存训练数据
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(training_examples, f, ensure_ascii=False, indent=2)

        print(f"✅ 生成了 {len(training_examples)} 个训练样本")
        print(f"💾 训练数据已保存到: {output_path}")
    
    def _generate_training_examples(self, error_char: str, correct_char: str, 
                                  semantic_field: str, knowledge_base: Dict[str, Any]) -> List[Dict]:
        """生成训练样本"""
        examples = []
        
        # 1. 直接纠错样本
        examples.append({
            'input': f"纠正拆分字错误: {error_char}",
            'output': correct_char,
            'task_type': 'direct_correction',
            'semantic_field': semantic_field,
            'metadata': {
                'error_type': 'split_character',
                'difficulty': 'basic'
            }
        })
        
        # 2. 上下文纠错样本
        context_patterns = knowledge_base['context_patterns']
        for context_key, patterns in context_patterns.items():
            for pattern in patterns:
                if pattern['error_char'] == error_char:
                    context_before, context_after = context_key
                    examples.append({
                        'input': f"在上下文'{context_before}[MASK]{context_after}'中，'{error_char}'应该纠正为什么？",
                        'output': f"应该纠正为'{correct_char}'，因为这是一个拆分字错误。",
                        'task_type': 'contextual_correction',
                        'semantic_field': semantic_field,
                        'metadata': {
                            'context_before': context_before,
                            'context_after': context_after,
                            'difficulty': 'intermediate'
                        }
                    })
        
        # 3. 语义解释样本
        examples.append({
            'input': f"解释为什么'{error_char}'是错误的，正确的应该是'{correct_char}'？",
            'output': f"'{error_char}'是一个拆分字错误，将完整的字符'{correct_char}'错误地拆分成了多个部分。在{semantic_field}语境中，正确的表达应该使用'{correct_char}'。",
            'task_type': 'semantic_explanation',
            'semantic_field': semantic_field,
            'metadata': {
                'explanation_type': 'error_analysis',
                'difficulty': 'advanced'
            }
        })
        
        # 4. 模式识别样本
        examples.append({
            'input': f"识别句子中的拆分字错误模式，并说明在{semantic_field}领域中的语义特点。",
            'output': f"拆分字错误'{error_char}'→'{correct_char}'在{semantic_field}领域中表现为字符结构的错误分解，影响语义的完整性和准确性。",
            'task_type': 'pattern_recognition',
            'semantic_field': semantic_field,
            'metadata': {
                'pattern_type': 'split_character',
                'difficulty': 'advanced'
            }
        })
        
        return examples

    def _generate_advanced_semantic_examples(self, knowledge_base: Dict[str, Any]) -> List[Dict]:
        """生成高级语义理解样本"""
        examples = []

        # 跨语义场的错误模式分析
        semantic_fields = list(knowledge_base['semantic_field_distributions'].keys())
        for i, field1 in enumerate(semantic_fields):
            for field2 in semantic_fields[i+1:]:
                examples.append({
                    'input': f"比较{field1}和{field2}领域中拆分字错误的语义特征差异。",
                    'output': f"在{field1}领域中，拆分字错误主要影响该领域特有的概念表达；而在{field2}领域中，错误模式可能呈现不同的语义特征。这种差异反映了不同语义场对字符完整性的不同要求。",
                    'task_type': 'cross_domain_analysis',
                    'semantic_fields': [field1, field2],
                    'metadata': {'difficulty': 'expert', 'analysis_type': 'comparative'}
                })

        # 语义层次分析
        for error_char, mappings in knowledge_base['character_mappings'].items():
            if len(mappings) > 1:  # 一对多映射
                correct_chars = [m['correct_char'] for m in mappings]
                examples.append({
                    'input': f"分析拆分字错误'{error_char}'可能对应的多个正确形式{correct_chars}在语义层次上的区别。",
                    'output': f"拆分字错误'{error_char}'的多种纠正形式反映了汉字语义的层次性：{', '.join(correct_chars)}在不同语境中承载着不同的语义功能，体现了汉字系统的复杂性和丰富性。",
                    'task_type': 'semantic_hierarchy_analysis',
                    'metadata': {'error_char': error_char, 'correct_chars': correct_chars, 'difficulty': 'expert'}
                })

        # 认知语言学分析
        examples.append({
            'input': "从认知语言学角度解释拆分字错误的产生机制和语义影响。",
            'output': "拆分字错误反映了学习者对汉字认知结构的不完整理解。从认知角度看，这类错误源于对字符整体性认知的缺失，影响了语义概念的准确表达和理解。",
            'task_type': 'cognitive_analysis',
            'metadata': {'theory': 'cognitive_linguistics', 'difficulty': 'expert'}
        })

        return examples

    def _generate_contrastive_examples(self, knowledge_base: Dict[str, Any]) -> List[Dict]:
        """生成对比学习样本"""
        examples = []

        # 正负样本对比
        for error_char, mappings in knowledge_base['character_mappings'].items():
            for mapping in mappings:
                correct_char = mapping['correct_char']

                # 正确示例
                examples.append({
                    'input': f"判断以下用法是否正确：使用'{correct_char}'表达相关概念。",
                    'output': f"正确。'{correct_char}'是标准的汉字形式，能够准确表达相关语义概念。",
                    'task_type': 'correctness_judgment',
                    'label': 'correct',
                    'metadata': {'char': correct_char, 'judgment_type': 'positive'}
                })

                # 错误示例
                examples.append({
                    'input': f"判断以下用法是否正确：使用'{error_char}'表达相关概念。",
                    'output': f"错误。'{error_char}'是拆分字错误，正确的形式应该是'{correct_char}'。这种错误会影响语义的准确传达。",
                    'task_type': 'correctness_judgment',
                    'label': 'incorrect',
                    'metadata': {'char': error_char, 'correct_char': correct_char, 'judgment_type': 'negative'}
                })

        # 细粒度对比分析
        context_patterns = knowledge_base['context_patterns']
        for context_key, patterns in list(context_patterns.items())[:5]:  # 限制数量
            if len(patterns) > 1:
                context_before, context_after = context_key
                examples.append({
                    'input': f"在上下文'{context_before}[MASK]{context_after}'中，比较不同字符选择的语义效果。",
                    'output': f"在此上下文中，不同字符的选择会产生显著的语义差异。正确的字符选择能够保持语义的连贯性和准确性，而错误的拆分字会破坏语义的完整性。",
                    'task_type': 'contextual_comparison',
                    'metadata': {'context': context_key, 'patterns': len(patterns)}
                })

        return examples

    def save_knowledge_base(self, knowledge_base: Dict[str, Any], save_path: str) -> None:
        """保存知识库"""
        with open(save_path, 'wb') as f:
            pickle.dump(knowledge_base, f)
        print(f"💾 知识库已保存到: {save_path}")
    
    def load_knowledge_base(self, load_path: str) -> Dict[str, Any]:
        """加载知识库"""
        with open(load_path, 'rb') as f:
            knowledge_base = pickle.load(f)
        print(f"📂 知识库已从 {load_path} 加载")
        return knowledge_base


def main():
    """主函数"""
    print("🚀 深度语义学习系统启动")
    print("=" * 50)
    
    # 初始化学习器
    learner = DeepSemanticLearner()
    
    # 数据路径
    data_path = "ChineseErrorCorrector/data/split_data/split_errors_data.json"
    output_dir = "ChineseErrorCorrector/semantic_learning/outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. 加载拆分字错误数据
        data = learner.load_split_error_data(data_path)
        
        # 2. 提取语义模式
        patterns = learner.extract_semantic_patterns(data)
        
        # 3. 构建语义知识库
        knowledge_base = learner.build_semantic_knowledge_base(patterns)
        
        # 4. 保存知识库
        kb_path = os.path.join(output_dir, "semantic_knowledge_base.pkl")
        learner.save_knowledge_base(knowledge_base, kb_path)
        
        # 5. 生成大模型训练数据
        training_data_path = os.path.join(output_dir, "llm_training_data.json")
        learner.generate_training_data_for_llm(knowledge_base, training_data_path)
        
        # 6. 生成分析报告
        report_path = os.path.join(output_dir, "semantic_analysis_report.json")
        generate_analysis_report(knowledge_base, patterns, report_path)
        
        print("\n🎉 深度语义学习完成!")
        print(f"📁 输出文件:")
        print(f"  - 知识库: {kb_path}")
        print(f"  - 训练数据: {training_data_path}")
        print(f"  - 分析报告: {report_path}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


def generate_analysis_report(knowledge_base: Dict[str, Any], 
                           patterns: List[SemanticPattern], 
                           report_path: str) -> None:
    """生成分析报告"""
    print("📊 生成分析报告...")
    
    report = {
        'summary': {
            'total_patterns': len(patterns),
            'unique_error_chars': len(knowledge_base['character_mappings']),
            'semantic_fields': len(knowledge_base['semantic_field_distributions']),
            'context_patterns': len(knowledge_base['context_patterns'])
        },
        'top_error_patterns': [],
        'semantic_field_analysis': {},
        'linguistic_feature_insights': {},
        'recommendations': []
    }
    
    # 分析高频错误模式
    error_freq = defaultdict(int)
    for pattern in patterns:
        error_freq[pattern.error_char] += pattern.frequency
    
    top_errors = sorted(error_freq.items(), key=lambda x: x[1], reverse=True)[:10]
    report['top_error_patterns'] = [
        {'error_char': char, 'frequency': freq} for char, freq in top_errors
    ]
    
    # 语义场分析
    for field, distribution in knowledge_base['semantic_field_distributions'].items():
        report['semantic_field_analysis'][field] = {
            'total_errors': sum(distribution.values()),
            'unique_errors': len(distribution),
            'top_errors': distribution.most_common(5)
        }
    
    # 生成建议
    report['recommendations'] = [
        "重点关注高频拆分字错误的语义特征学习",
        "加强不同语义场中错误模式的对比分析",
        "利用上下文信息提高错误检测的准确性",
        "结合语言学特征进行多维度的错误分析",
        "建立基于语义场的个性化纠错策略"
    ]
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 分析报告已保存到: {report_path}")


if __name__ == '__main__':
    main()
