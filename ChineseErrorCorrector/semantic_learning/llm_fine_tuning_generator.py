#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大模型微调数据生成器
专门为深度语义学习生成高质量的微调数据
"""

import os
import sys
import json
import random
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import numpy as np

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from ChineseErrorCorrector.semantic_learning.deep_semantic_learner import DeepSemanticLearner


class LLMFineTuningGenerator:
    """大模型微调数据生成器"""
    
    def __init__(self):
        self.instruction_templates = {
            'error_correction': [
                "请纠正以下句子中的拆分字错误：",
                "识别并修正句子中的字符拆分错误：",
                "找出句子中的拆分字错误并给出正确形式：",
                "检查并纠正以下文本的拆分字问题："
            ],
            'semantic_analysis': [
                "分析以下拆分字错误的语义特征：",
                "解释拆分字错误对语义表达的影响：",
                "从语义角度分析以下错误：",
                "说明拆分字错误的语义问题："
            ],
            'pattern_recognition': [
                "识别以下文本中的错误模式：",
                "分析拆分字错误的规律：",
                "总结以下错误的特征模式：",
                "归纳拆分字错误的类型："
            ],
            'contextual_understanding': [
                "根据上下文判断正确的字符形式：",
                "在给定语境中选择合适的字符：",
                "结合上下文分析字符使用的正确性：",
                "基于语境理解选择正确的表达："
            ]
        }
        
        self.response_templates = {
            'correction_with_explanation': "原句中的'{error}'是拆分字错误，正确的形式应该是'{correct}'。修正后的句子为：{corrected_sentence}",
            'semantic_analysis': "拆分字错误'{error}'→'{correct}'在语义上的问题是：{semantic_issue}。这种错误会导致{impact}。",
            'pattern_explanation': "这是一个{pattern_type}类型的拆分字错误，特征是{characteristics}。在{domain}领域中，这类错误{frequency_info}。",
            'contextual_reasoning': "在上下文'{context}'中，应该使用'{correct}'而不是'{error}'，因为{reasoning}。"
        }
    
    def generate_instruction_following_data(self, knowledge_base: Dict[str, Any], 
                                          num_samples: int = 1000) -> List[Dict]:
        """生成指令跟随格式的训练数据"""
        print(f"📝 生成 {num_samples} 个指令跟随训练样本...")
        
        samples = []
        
        # 获取错误映射
        character_mappings = knowledge_base['character_mappings']
        context_patterns = knowledge_base['context_patterns']
        semantic_fields = knowledge_base['semantic_field_distributions']
        
        for _ in range(num_samples):
            sample_type = random.choice(['correction', 'analysis', 'pattern', 'contextual'])
            
            if sample_type == 'correction':
                sample = self._generate_correction_sample(character_mappings, context_patterns)
            elif sample_type == 'analysis':
                sample = self._generate_analysis_sample(character_mappings, semantic_fields)
            elif sample_type == 'pattern':
                sample = self._generate_pattern_sample(character_mappings, semantic_fields)
            else:
                sample = self._generate_contextual_sample(character_mappings, context_patterns)
            
            if sample:
                samples.append(sample)
        
        print(f"✅ 生成了 {len(samples)} 个有效样本")
        return samples
    
    def _generate_correction_sample(self, character_mappings: Dict, 
                                  context_patterns: Dict) -> Dict:
        """生成纠错样本"""
        # 随机选择一个错误字符
        error_char = random.choice(list(character_mappings.keys()))
        mapping = random.choice(character_mappings[error_char])
        correct_char = mapping['correct_char']
        
        # 构造包含错误的句子
        error_sentence = self._construct_error_sentence(error_char, context_patterns)
        correct_sentence = error_sentence.replace(error_char, correct_char)
        
        instruction = random.choice(self.instruction_templates['error_correction'])
        
        return {
            'instruction': instruction,
            'input': error_sentence,
            'output': f"句子中的'{error_char}'是拆分字错误，应该纠正为'{correct_char}'。\n\n修正后的句子：{correct_sentence}",
            'task_type': 'error_correction',
            'metadata': {
                'error_char': error_char,
                'correct_char': correct_char,
                'semantic_field': mapping.get('semantic_field', 'unknown')
            }
        }
    
    def _generate_analysis_sample(self, character_mappings: Dict, 
                                semantic_fields: Dict) -> Dict:
        """生成语义分析样本"""
        error_char = random.choice(list(character_mappings.keys()))
        mapping = random.choice(character_mappings[error_char])
        correct_char = mapping['correct_char']
        semantic_field = mapping.get('semantic_field', '通用')
        
        instruction = random.choice(self.instruction_templates['semantic_analysis'])
        
        # 生成语义分析内容
        semantic_issues = [
            "字符结构被错误拆分，破坏了语义的完整性",
            "影响了词汇的语义表达准确性",
            "导致语义理解的歧义和困惑",
            "破坏了语言的规范性和标准性"
        ]
        
        impacts = [
            "读者理解困难",
            "语义传达不准确",
            "影响文本的专业性",
            "降低表达的清晰度"
        ]
        
        semantic_issue = random.choice(semantic_issues)
        impact = random.choice(impacts)
        
        return {
            'instruction': instruction,
            'input': f"错误：{error_char} → 正确：{correct_char}",
            'output': f"这个拆分字错误的语义问题在于：{semantic_issue}。在{semantic_field}领域中，使用'{error_char}'代替'{correct_char}'会{impact}，因为它违背了汉字的基本构造原则。",
            'task_type': 'semantic_analysis',
            'metadata': {
                'error_char': error_char,
                'correct_char': correct_char,
                'semantic_field': semantic_field
            }
        }
    
    def _generate_pattern_sample(self, character_mappings: Dict, 
                               semantic_fields: Dict) -> Dict:
        """生成模式识别样本"""
        # 选择多个相关错误
        error_chars = random.sample(list(character_mappings.keys()), 
                                   min(3, len(character_mappings)))
        
        instruction = random.choice(self.instruction_templates['pattern_recognition'])
        
        error_examples = []
        for error_char in error_chars:
            mapping = random.choice(character_mappings[error_char])
            correct_char = mapping['correct_char']
            error_examples.append(f"{error_char} → {correct_char}")
        
        pattern_types = [
            "字符部件拆分",
            "复合字符分解",
            "结构性拆分",
            "形近字混淆"
        ]
        
        characteristics = [
            "将完整字符错误分解为多个部分",
            "破坏了字符的整体结构",
            "影响语义的准确表达",
            "常见于复杂字符的书写"
        ]
        
        pattern_type = random.choice(pattern_types)
        characteristic = random.choice(characteristics)
        
        return {
            'instruction': instruction,
            'input': "错误模式：" + "；".join(error_examples),
            'output': f"这些错误属于{pattern_type}模式，主要特征是{characteristic}。这类错误在中文学习中较为常见，需要通过系统的字符结构学习来避免。",
            'task_type': 'pattern_recognition',
            'metadata': {
                'error_chars': error_chars,
                'pattern_type': pattern_type
            }
        }
    
    def _generate_contextual_sample(self, character_mappings: Dict, 
                                  context_patterns: Dict) -> Dict:
        """生成上下文理解样本"""
        # 选择一个上下文模式
        if not context_patterns:
            return None
        
        context_key = random.choice(list(context_patterns.keys()))
        patterns = context_patterns[context_key]
        pattern = random.choice(patterns)
        
        context_before, context_after = context_key
        error_char = pattern['error_char']
        correct_char = pattern['correct_char']
        
        instruction = random.choice(self.instruction_templates['contextual_understanding'])
        
        reasoning_options = [
            f"'{correct_char}'在此语境中语义更加准确",
            f"上下文要求使用完整的字符形式'{correct_char}'",
            f"'{error_char}'是拆分形式，不符合标准表达",
            f"语境的语义连贯性需要使用'{correct_char}'"
        ]
        
        reasoning = random.choice(reasoning_options)
        
        return {
            'instruction': instruction,
            'input': f"上下文：{context_before}[?]{context_after}\n选项：A) {error_char}  B) {correct_char}",
            'output': f"正确答案是B) {correct_char}。{reasoning}。使用'{error_char}'会破坏语义的完整性和表达的准确性。",
            'task_type': 'contextual_understanding',
            'metadata': {
                'context_before': context_before,
                'context_after': context_after,
                'error_char': error_char,
                'correct_char': correct_char
            }
        }
    
    def _construct_error_sentence(self, error_char: str, context_patterns: Dict) -> str:
        """构造包含错误的句子"""
        # 尝试从上下文模式中找到合适的句子
        for context_key, patterns in context_patterns.items():
            for pattern in patterns:
                if pattern['error_char'] == error_char:
                    context_before, context_after = context_key
                    return f"{context_before}{error_char}{context_after}"
        
        # 如果没找到，生成一个简单的句子
        simple_sentences = [
            f"这个{error_char}很重要。",
            f"我们需要{error_char}这个概念。",
            f"他的{error_char}很强。",
            f"这种{error_char}很常见。"
        ]
        return random.choice(simple_sentences)
    
    def generate_multi_turn_conversations(self, knowledge_base: Dict[str, Any], 
                                        num_conversations: int = 100) -> List[Dict]:
        """生成多轮对话数据"""
        print(f"💬 生成 {num_conversations} 个多轮对话...")
        
        conversations = []
        character_mappings = knowledge_base['character_mappings']
        
        for _ in range(num_conversations):
            conversation = self._create_multi_turn_conversation(character_mappings)
            if conversation:
                conversations.append(conversation)
        
        print(f"✅ 生成了 {len(conversations)} 个对话")
        return conversations
    
    def _create_multi_turn_conversation(self, character_mappings: Dict) -> Dict:
        """创建多轮对话"""
        error_char = random.choice(list(character_mappings.keys()))
        mapping = random.choice(character_mappings[error_char])
        correct_char = mapping['correct_char']
        
        conversation = {
            'conversation_id': f"conv_{random.randint(1000, 9999)}",
            'turns': [
                {
                    'role': 'user',
                    'content': f"请帮我检查这个句子：我觉得这个{error_char}很有意思。"
                },
                {
                    'role': 'assistant',
                    'content': f"我发现句子中有一个拆分字错误。'{error_char}'应该写成'{correct_char}'。正确的句子是：我觉得这个{correct_char}很有意思。"
                },
                {
                    'role': 'user',
                    'content': f"为什么'{error_char}'是错误的？"
                },
                {
                    'role': 'assistant',
                    'content': f"'{error_char}'是一个拆分字错误，它将完整的汉字'{correct_char}'错误地分解成了多个部分。这种错误会影响语义的准确表达和文本的规范性。在正式的中文写作中，应该使用标准的汉字形式。"
                },
                {
                    'role': 'user',
                    'content': "还有其他类似的错误需要注意吗？"
                },
                {
                    'role': 'assistant',
                    'content': "是的，拆分字错误在中文学习中比较常见。建议多练习汉字的整体结构，理解字符的完整形态，避免将复合字符错误拆分。同时，多阅读标准的中文文本也有助于培养正确的字符使用习惯。"
                }
            ],
            'metadata': {
                'error_char': error_char,
                'correct_char': correct_char,
                'topic': 'split_character_correction'
            }
        }
        
        return conversation
    
    def save_fine_tuning_data(self, samples: List[Dict], output_path: str, 
                            format_type: str = 'instruction') -> None:
        """保存微调数据"""
        print(f"💾 保存微调数据到: {output_path}")
        
        if format_type == 'instruction':
            # 指令跟随格式
            formatted_data = []
            for sample in samples:
                formatted_data.append({
                    'instruction': sample['instruction'],
                    'input': sample['input'],
                    'output': sample['output']
                })
        elif format_type == 'conversation':
            # 对话格式
            formatted_data = samples
        else:
            # 原始格式
            formatted_data = samples
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(formatted_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 已保存 {len(formatted_data)} 个样本")


def main():
    """主函数"""
    print("🚀 大模型微调数据生成器启动")
    print("=" * 50)
    
    # 初始化生成器
    generator = LLMFineTuningGenerator()
    learner = DeepSemanticLearner()
    
    # 数据路径
    data_path = "ChineseErrorCorrector/data/split_data/split_errors_data.json"
    output_dir = "ChineseErrorCorrector/semantic_learning/fine_tuning_data"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 1. 加载数据并构建知识库
        print("📂 加载数据并构建知识库...")
        data = learner.load_split_error_data(data_path)
        patterns = learner.extract_semantic_patterns(data)
        knowledge_base = learner.build_semantic_knowledge_base(patterns)
        
        # 2. 生成指令跟随数据
        instruction_samples = generator.generate_instruction_following_data(
            knowledge_base, num_samples=1000
        )
        
        instruction_path = os.path.join(output_dir, "instruction_following_data.json")
        generator.save_fine_tuning_data(instruction_samples, instruction_path, 'instruction')
        
        # 3. 生成多轮对话数据
        conversation_samples = generator.generate_multi_turn_conversations(
            knowledge_base, num_conversations=200
        )
        
        conversation_path = os.path.join(output_dir, "conversation_data.json")
        generator.save_fine_tuning_data(conversation_samples, conversation_path, 'conversation')
        
        # 4. 生成统计报告
        stats = {
            'total_instruction_samples': len(instruction_samples),
            'total_conversations': len(conversation_samples),
            'task_type_distribution': {},
            'semantic_field_coverage': len(knowledge_base['semantic_field_distributions']),
            'unique_error_patterns': len(knowledge_base['character_mappings'])
        }
        
        # 统计任务类型分布
        for sample in instruction_samples:
            task_type = sample.get('task_type', 'unknown')
            stats['task_type_distribution'][task_type] = stats['task_type_distribution'].get(task_type, 0) + 1
        
        stats_path = os.path.join(output_dir, "generation_statistics.json")
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print("\n🎉 微调数据生成完成!")
        print(f"📁 输出文件:")
        print(f"  - 指令跟随数据: {instruction_path}")
        print(f"  - 对话数据: {conversation_path}")
        print(f"  - 统计报告: {stats_path}")
        print(f"\n📊 生成统计:")
        print(f"  - 指令样本: {stats['total_instruction_samples']}")
        print(f"  - 对话数量: {stats['total_conversations']}")
        print(f"  - 语义场覆盖: {stats['semantic_field_coverage']}")
        print(f"  - 错误模式: {stats['unique_error_patterns']}")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
