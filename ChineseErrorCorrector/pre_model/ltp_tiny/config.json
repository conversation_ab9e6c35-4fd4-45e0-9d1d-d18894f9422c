{"model": {"_ltp_target_": "ltp_core.models.ltp_model.LTPModule", "backbone": {"_ltp_target_": "ltp_core.models.utils.load_transformers", "config": {"output_attentions": false, "output_hidden_states": false, "use_cache": true, "torchscript": false, "use_bfloat16": false, "pruned_heads": {}, "is_encoder_decoder": false, "is_decoder": false, "max_length": 20, "min_length": 0, "do_sample": false, "early_stopping": false, "num_beams": 1, "temperature": 1.0, "top_k": 50, "top_p": 1.0, "repetition_penalty": 1.0, "length_penalty": 1.0, "no_repeat_ngram_size": 0, "bad_words_ids": null, "num_return_sequences": 1, "architectures": ["ElectraModel"], "finetuning_task": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "prefix": null, "bos_token_id": null, "pad_token_id": 0, "eos_token_id": null, "decoder_start_token_id": null, "task_specific_params": null, "xla_device": null, "directionality": "bidi", "model_type": "electra", "vocab_size": 21128, "embedding_size": 128, "hidden_size": 256, "num_hidden_layers": 3, "num_attention_heads": 4, "intermediate_size": 1024, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "attention_probs_dropout_prob": 0.1, "max_position_embeddings": 512, "type_vocab_size": 2, "initializer_range": 0.02, "layer_norm_eps": 1e-12}}, "processor": {"cws": {"_ltp_target_": "ltp_core.models.processor.TokenOnly"}, "pos": {"_ltp_target_": "ltp_core.models.processor.WordsOnly"}, "ner": {"_ltp_target_": "ltp_core.models.processor.WordsOnly"}, "srl": {"_ltp_target_": "ltp_core.models.processor.WordsOnly"}, "dep": {"_ltp_target_": "ltp_core.models.processor.WordsWithHead"}, "sdp": {"_ltp_target_": "ltp_core.models.processor.WordsWithHead"}}, "heads": {"cws": {"_ltp_target_": "ltp_core.models.components.token.MLPTokenClassifier", "input_size": 256, "num_labels": 2, "dropout": 0.1}, "pos": {"_ltp_target_": "ltp_core.models.components.token.MLPTokenClassifier", "input_size": 256, "num_labels": 27, "dropout": 0.1}, "ner": {"_ltp_target_": "ltp_core.models.components.token.RelTransformerTokenClassifier", "input_size": 256, "num_labels": 13, "dropout": 0.1, "num_heads": 4, "num_layers": 2}, "srl": {"_ltp_target_": "ltp_core.models.components.token.BiaffineTokenClassifier", "input_size": 256, "hidden_size": 128, "num_labels": 97, "dropout": 0.1, "use_crf": true}, "dep": {"_ltp_target_": "ltp_core.models.components.graph.BiaffineClassifier", "input_size": 256, "num_labels": 14, "dropout": 0.1, "arc_hidden_size": 100, "rel_hidden_size": 128}, "sdp": {"_ltp_target_": "ltp_core.models.components.graph.BiaffineClassifier", "input_size": 256, "num_labels": 56, "arc_hidden_size": 100, "rel_hidden_size": 128}}}, "nerual": true, "vocabs": {"cws": ["B-W", "I-W"], "pos": ["n", "v", "wp", "u", "d", "a", "m", "p", "r", "ns", "c", "q", "nt", "nh", "nd", "j", "i", "b", "ni", "nz", "nl", "z", "k", "ws", "o", "h", "e"], "ner": ["O", "S-Ns", "S-Nh", "B-Ni", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "S-Ni", "B-Ns", "E-Ns", "I-Ns", "B-Nh", "E-Nh", "I-Nh"], "srl": ["O", "B-A0", "B-A0-ADV", "B-A0-CND", "B-A0-CRD", "B-A0-MNR", "B-A0-PRD", "B-A0-PSE", "B-A0-PSR", "B-A0-QTY", "B-A1", "B-A1-CRD", "B-A1-DIS", "B-A1-FRQ", "B-A1-PRD", "B-A1-PSE", "B-A1-PSR", "B-A1-QTY", "B-A1-TPC", "B-A2", "B-A2-CRD", "B-A2-PRD", "B-A2-PSE", "B-A2-PSR", "B-A2-QTY", "B-A3", "B-A3-TMP", "B-A4", "B-ARGM-ADV", "B-ARGM-BNF", "B-ARGM-CND", "B-ARGM-CRD", "B-ARGM-DGR", "B-ARGM-DIR", "B-ARGM-DIS", "B-ARGM-EXT", "B-ARGM-FRQ", "B-ARGM-LOC", "B-ARGM-MNR", "B-ARGM-PRD", "B-ARGM-PRP", "B-ARGM-QTY", "B-ARGM-T", "B-ARGM-TMP", "B-ARGM-TPC", "B-rel-ADV", "B-rel-DIS", "B-rel-EXT", "B-rel-MNR", "I-A0", "I-A0-ADV", "I-A0-CND", "I-A0-CRD", "I-A0-MNR", "I-A0-PRD", "I-A0-PSE", "I-A0-PSR", "I-A0-QTY", "I-A1", "I-A1-CRD", "I-A1-DIS", "I-A1-FRQ", "I-A1-PRD", "I-A1-PSE", "I-A1-PSR", "I-A1-QTY", "I-A1-TPC", "I-A2", "I-A2-CRD", "I-A2-PRD", "I-A2-PSE", "I-A2-PSR", "I-A2-QTY", "I-A3", "I-A3-TMP", "I-A4", "I-ARGM-ADV", "I-ARGM-BNF", "I-ARGM-CND", "I-ARGM-CRD", "I-ARGM-DGR", "I-ARGM-DIR", "I-ARGM-DIS", "I-ARGM-EXT", "I-ARGM-FRQ", "I-ARGM-LOC", "I-ARGM-MNR", "I-ARGM-PRD", "I-ARGM-PRP", "I-ARGM-QTY", "I-ARGM-T", "I-ARGM-TMP", "I-ARGM-TPC", "I-rel-ADV", "I-rel-DIS", "I-rel-EXT", "I-rel-MNR"], "dep": ["ATT", "WP", "ADV", "VOB", "SBV", "COO", "RAD", "HED", "POB", "CMP", "LAD", "FOB", "DBL", "IOB"], "sdp": ["mDEPD", "mPUNC", "FEAT", "mRELA", "Root", "AGT", "eSUCC", "EXP", "MEAS", "eCOO", "CONT", "LOC", "DATV", "LINK", "PAT", "TIME", "dCONT", "SCO", "MANN", "mNEG", "ePREC", "dFEAT", "rEXP", "dEXP", "dTIME", "rCONT", "rAGT", "dLINK", "STAT", "REAS", "rPAT", "TOOL", "dSTAT", "dMANN", "rTIME", "rLOC", "dDATV", "rFEAT", "MATL", "rDATV", "dREAS", "dLOC", "rLINK", "dPAT", "rMANN", "rREAS", "rTOOL", "rMEAS", "dSCO", "dMEAS", "rSCO", "dAGT", "rMATL", "rSTAT", "dTOOL", "dMATL"]}}