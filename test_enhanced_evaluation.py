#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的Split评估功能
"""

import json
import os
import subprocess

def create_realistic_test_results():
    """创建更真实的测试结果"""
    test_results = {
        "test_info": {
            "test_time": "2024-01-01T12:00:00",
            "data_path": "test_data.json",
            "model_path": "test_model",
            "use_vllm": False,
            "batch_size": 1,
            "max_samples": 10,
            "total_samples": 10,
            "total_time_seconds": 20.0,
            "average_time_per_sample": 2.0,
            "throughput_samples_per_second": 0.5
        },
        "metrics": {
            "exact_match_count": 2,
            "exact_match_rate": 0.2,
            "error_type_statistics": {
                "拆分字错误": {
                    "total": 8,
                    "correct": 2,
                    "accuracy": 0.25
                },
                "其他错误": {
                    "total": 2,
                    "correct": 0,
                    "accuracy": 0.0
                }
            }
        },
        "results": [
            # 完全正确的样本
            {
                "id": 0,
                "source": "你先择正确的答案",
                "target": "你选择正确的答案",
                "prediction": "你选择正确的答案",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 模型未修改的样本
            {
                "id": 1,
                "source": "这个方案很好",
                "target": "这个方案很好",
                "prediction": "这个方案很好",
                "error_type": "其他错误",
                "metadata": {}
            },
            # 模型修改但不完全正确
            {
                "id": 2,
                "source": "需要进行改正",
                "target": "需要进行改正",
                "prediction": "需要进行修正",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 模型完全正确
            {
                "id": 3,
                "source": "这是一个测试",
                "target": "这是一个测试",
                "prediction": "这是一个测试",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 模型修改错误
            {
                "id": 4,
                "source": "最后一个样本",
                "target": "最后一个样本",
                "prediction": "最后一个例子",
                "error_type": "其他错误",
                "metadata": {}
            },
            # 拆分字错误 - 模型未修改
            {
                "id": 5,
                "source": "京尤像芭蕾舞演员",
                "target": "就像芭蕾舞演员",
                "prediction": "京尤像芭蕾舞演员",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 拆分字错误 - 模型部分修改
            {
                "id": 6,
                "source": "鬼未力很强",
                "target": "魅力很强",
                "prediction": "魅力很强",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 拆分字错误 - 模型修改错误
            {
                "id": 7,
                "source": "厉力很大",
                "target": "励力很大",
                "prediction": "力量很大",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 拆分字错误 - 模型未修改
            {
                "id": 8,
                "source": "申请表格",
                "target": "申请表格",
                "prediction": "申请表格",
                "error_type": "拆分字错误",
                "metadata": {}
            },
            # 拆分字错误 - 模型修改但不完全
            {
                "id": 9,
                "source": "木羊的行为",
                "target": "样的行为",
                "prediction": "羊的行为",
                "error_type": "拆分字错误",
                "metadata": {}
            }
        ]
    }
    
    # 确保目录存在
    os.makedirs("ChineseErrorCorrector/model_test/Split_results", exist_ok=True)
    
    # 保存测试结果
    with open("ChineseErrorCorrector/model_test/Split_results/realistic_test_results.json", 'w', encoding='utf-8') as f:
        json.dump(test_results, f, ensure_ascii=False, indent=2)
    
    print("✅ 真实测试结果文件已创建")
    return "ChineseErrorCorrector/model_test/Split_results/realistic_test_results.json"

def test_enhanced_evaluation():
    """测试增强的评估功能"""
    print("\n🔍 测试增强的评估功能...")
    
    test_file = create_realistic_test_results()
    
    cmd = [
        "python", "ChineseErrorCorrector/model_test/Split_test/split_evaluate.py",
        "--results_file", test_file,
        "--output_file", "ChineseErrorCorrector/model_test/Split_results/enhanced_evaluation.json",
        "--skip_cherrant"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 增强评估成功")
            print("\n增强评估输出:")
            print(result.stdout)
        else:
            print("❌ 增强评估失败")
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"❌ 运行增强评估时出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 测试增强的Split评估功能")
    print("=" * 50)
    
    # 测试增强评估
    success = test_enhanced_evaluation()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  增强评估: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 增强评估系统工作正常！")
        print("现在可以获得更详细的评估指标，包括:")
        print("  • 模型修改率统计")
        print("  • 按错误类型的详细分析")
        print("  • 基于编辑距离的P/R/F1指标")
        print("  • ChERRANT指标（如果可用）")
    else:
        print("\n❌ 增强评估系统存在问题")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
