accelerate==1.5.2
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
airportsdata==20250224
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
astor==0.8.1
async-timeout==5.0.1
attrs==25.3.0
audioread==3.0.1
av==14.4.0
beautifulsoup4==4.13.3
blake3==1.0.4
blinker==1.9.0
cachetools==5.5.2
cbor==1.0.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
compressed-tensors==0.9.3
contourpy==1.3.2
cupy-cuda12x==12.1.0
cycler==0.12.1
datasets==3.2.0
decorator==5.2.1
deepspeed==0.16.4
Deprecated==1.2.18
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docstring_parser==0.16
einops==0.8.1
email_validator==2.2.0
exceptiongroup==1.2.2
fastapi==0.115.12
fastapi-cli==0.0.7
fastrlock==0.8.3
ffmpy==0.6.0
filelock==3.18.0
fire==0.7.0
FlagEmbedding==1.3.4
Flask==3.0.3
fonttools==4.58.2
frozenlist==1.5.0
fsspec==2024.9.0
gguf==0.17.1
googleapis-common-protos==1.70.0
gradio==5.31.0
gradio_client==1.10.1
groovy==0.1.2
grpcio==1.73.0
h11==0.14.0
hf-xet==1.1.3
hjson==3.1.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.33.0
idna==3.10
ijson==3.3.0
importlib_metadata==8.0.0
inscriptis==2.6.0
interegular==0.3.3
ir_datasets==0.5.10
itsdangerous==2.2.0
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.8
lark==1.2.2
lazy_loader==0.4
librosa==0.11.0
llguidance==0.7.30
llvmlite==0.44.0
lm-format-enforcer==0.10.11
loguru==0.7.3
ltp==4.1.3.post1
lxml==5.3.1
lz4==4.4.3
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.3
mdurl==0.1.2
mistral_common==1.5.4
modelscope==1.26.0
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.2.0
multiprocess==0.70.16
nest-asyncio==1.6.0
networkx==3.2.1
ninja==********
nltk==3.9.1
numba==0.61.2
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cufile-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py==12.575.51
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
omegaconf==2.3.0
openai==1.68.2
OpenCC==1.1.9
opencv-python-headless==*********
opentelemetry-api==1.26.0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-semantic-conventions==0.47b0
opentelemetry-semantic-conventions-ai==0.4.9
orjson==3.10.18
outlines==0.1.11
outlines_core==0.1.26
packaging==24.2
pandas==2.2.3
partial-json-parser==0.2.1.1.post5
peft==0.15.0
pillow==11.1.0
platformdirs==4.3.8
pooch==1.8.2
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.21.1
propcache==0.3.1
protobuf==4.25.8
psutil==7.0.0
py-cpuinfo==9.0.0
pyahocorasick==2.1.0
pyarrow==19.0.1
pycorrector==1.1.2
pycountry==24.6.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydub==0.25.1
Pygments==2.19.1
pygtrie==2.4.2
pynvml==11.5.0
pyparsing==3.2.3
pypinyin==0.53.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-json-logger==3.3.0
python-Levenshtein==0.12.1
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
pyzmq==26.3.0
ray==2.47.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rich-toolkit==0.14.0
rouge-chinese==1.0.3
rpds-py==0.24.0
ruff==0.11.13
safehttpx==0.1.6
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.13.1
semantic-version==2.10.0
sentence-transformers==4.0.1
sentencepiece==0.2.0
shellingham==1.5.4
shtab==1.7.2
six==1.17.0
sniffio==1.3.1
some-package==0.1
soundfile==0.13.1
soupsieve==2.6
soxr==0.5.0.post1
sse-starlette==2.3.6
starlette==0.46.1
sympy==1.13.1
tensorboardX==*******
termcolor==3.1.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
tomlkit==0.13.3
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0
tqdm==4.67.1
transformers==4.52.4
trec-car-tools==2.6
triton==3.2.0
trl==0.9.6
typer==0.15.2
typing_extensions==4.13.0
tyro==0.8.14
tzdata==2025.2
unlzw3==0.2.3
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
vllm==0.8.5
warc3-wet==0.2.5
warc3-wet-clueweb09==0.2.5
watchfiles==1.0.4
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
xformers==0.0.29.post2
xgrammar==0.1.18
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
zlib-state==0.1.9
