{"settings": {"language": "语言", "theme": "主题", "light": "浅色", "dark": "深色", "system": "系统"}, "header": {"documents": "文档", "knowledgeGraph": "知识图谱", "retrieval": "检索", "api": "API", "projectRepository": "项目仓库", "logout": "退出登录", "themeToggle": {"switchToLight": "切换到浅色主题", "switchToDark": "切换到深色主题"}}, "login": {"description": "请输入您的账号和密码登录系统", "username": "用户名", "usernamePlaceholder": "请输入用户名", "password": "密码", "passwordPlaceholder": "请输入密码", "loginButton": "登录", "loggingIn": "登录中...", "successMessage": "登录成功", "errorEmptyFields": "请输入您的用户名和密码", "errorInvalidCredentials": "登录失败，请检查用户名和密码", "authDisabled": "认证已禁用，使用无需登陆模式。", "guestMode": "无需登陆"}, "common": {"cancel": "取消", "save": "保存", "saving": "保存中...", "saveFailed": "保存失败"}, "documentPanel": {"clearDocuments": {"button": "清空", "tooltip": "清空文档", "title": "清空文档", "description": "此操作将从系统中移除所有文档", "warning": "警告：此操作将永久删除所有文档，无法恢复！", "confirm": "确定要清空所有文档吗？", "confirmPrompt": "请输入 yes 确认操作", "confirmPlaceholder": "输入 yes 确认", "clearCache": "清空LLM缓存", "confirmButton": "确定", "clearing": "正在清除...", "timeout": "清除操作超时，请重试", "success": "文档清空成功", "cacheCleared": "缓存清空成功", "cacheClearFailed": "清空缓存失败：\n{{error}}", "failed": "清空文档失败：\n{{message}}", "error": "清空文档失败：\n{{error}}"}, "deleteDocuments": {"button": "删除", "tooltip": "删除选中的文档", "title": "删除文档", "description": "此操作将永久删除选中的文档", "warning": "警告：此操作将永久删除选中的文档，无法恢复！", "confirm": "确定要删除 {{count}} 个选中的文档吗？", "confirmPrompt": "请输入 yes 确认操作", "confirmPlaceholder": "输入 yes 确认", "confirmButton": "确定", "deleteFileOption": "同时删除上传文件", "deleteFileTooltip": "选中此选项将同时删除服务器上对应的上传文件", "success": "文档删除流水线启动成功", "failed": "删除文档失败：\n{{message}}", "error": "删除文档失败：\n{{error}}", "busy": "流水线被占用，请稍后再试", "notAllowed": "没有操作权限"}, "selectDocuments": {"selectCurrentPage": "全选当前页 ({{count}})", "deselectAll": "取消全选 ({{count}})"}, "uploadDocuments": {"button": "上传", "tooltip": "上传文档", "title": "上传文档", "description": "拖拽文件到此处或点击浏览", "single": {"uploading": "正在上传 {{name}}：{{percent}}%", "success": "上传成功：\n{{name}} 上传完成", "failed": "上传失败：\n{{name}}\n{{message}}", "error": "上传失败：\n{{name}}\n{{error}}"}, "batch": {"uploading": "正在上传文件...", "success": "文件上传完成", "error": "部分文件上传失败"}, "generalError": "上传失败\n{{error}}", "fileTypes": "支持的文件类型：TXT, MD, DOCX, PDF, PPTX, XLSX, RTF, ODT, EPUB, HTML, HTM, TEX, JSON, XML, YAML, YML, CSV, LOG, CONF, INI, PROPERTIES, SQL, BAT, SH, C, CPP, PY, JAVA, JS, TS, SWIFT, GO, RB, PHP, CSS, SCSS, LESS", "fileUploader": {"singleFileLimit": "一次只能上传一个文件", "maxFilesLimit": "最多只能上传 {{count}} 个文件", "fileRejected": "文件 {{name}} 被拒绝", "unsupportedType": "不支持的文件类型", "fileTooLarge": "文件过大，最大允许 {{maxSize}}", "dropHere": "将文件拖放到此处", "dragAndDrop": "拖放文件到此处，或点击选择文件", "removeFile": "移除文件", "uploadDescription": "您可以上传{{isMultiple ? '多个' : count}}个文件（每个文件最大{{maxSize}}）", "duplicateFile": "文件名与服务器上的缓存重复"}}, "documentManager": {"title": "文档管理", "scanButton": "扫描", "scanTooltip": "扫描输入目录中的文档", "refreshTooltip": "复位文档清单", "pipelineStatusButton": "流水线状态", "pipelineStatusTooltip": "查看流水线状态", "uploadedTitle": "已上传文档", "uploadedDescription": "已上传文档列表及其状态", "emptyTitle": "无文档", "emptyDescription": "还没有上传任何文档", "columns": {"id": "ID", "fileName": "文件名", "summary": "摘要", "status": "状态", "length": "长度", "chunks": "分块", "created": "创建时间", "updated": "更新时间", "metadata": "元数据", "select": "选择"}, "status": {"all": "全部", "completed": "已完成", "processing": "处理中", "pending": "等待中", "failed": "失败"}, "errors": {"loadFailed": "加载文档失败\n{{error}}", "scanFailed": "扫描文档失败\n{{error}}", "scanProgressFailed": "获取扫描进度失败\n{{error}}"}, "fileNameLabel": "文件名", "showButton": "显示", "hideButton": "隐藏", "showFileNameTooltip": "显示文件名", "hideFileNameTooltip": "隐藏文件名"}, "pipelineStatus": {"title": "流水线状态", "busy": "流水线忙碌", "requestPending": "待处理请求", "jobName": "作业名称", "startTime": "开始时间", "progress": "进度", "unit": "批", "latestMessage": "最新消息", "historyMessages": "历史消息", "errors": {"fetchFailed": "获取流水线状态失败\n{{error}}"}}}, "graphPanel": {"dataIsTruncated": "图数据已截断至最大返回节点数", "statusDialog": {"title": "LightRAG 服务器设置", "description": "查看当前系统状态和连接信息"}, "legend": "图例", "nodeTypes": {"person": "人物角色", "category": "分类", "geo": "地理名称", "location": "位置", "organization": "组织机构", "event": "事件", "equipment": "装备", "weapon": "武器", "animal": "动物", "unknown": "未知", "object": "物品", "group": "群组", "technology": "技术", "product": "产品", "document": "文档", "other": "其他"}, "sideBar": {"settings": {"settings": "设置", "healthCheck": "健康检查", "showPropertyPanel": "显示属性面板", "showSearchBar": "显示搜索栏", "showNodeLabel": "显示节点标签", "nodeDraggable": "节点可拖动", "showEdgeLabel": "显示边标签", "hideUnselectedEdges": "隐藏未选中的边", "edgeEvents": "边事件", "maxQueryDepth": "最大查询深度", "maxNodes": "最大返回节点数", "maxLayoutIterations": "最大布局迭代次数", "resetToDefault": "重置为默认值", "edgeSizeRange": "边粗细范围", "depth": "深", "max": "Max", "degree": "邻边", "apiKey": "API密钥", "enterYourAPIkey": "输入您的API密钥", "save": "保存", "refreshLayout": "刷新布局"}, "zoomControl": {"zoomIn": "放大", "zoomOut": "缩小", "resetZoom": "重置缩放", "rotateCamera": "顺时针旋转图形", "rotateCameraCounterClockwise": "逆时针旋转图形"}, "layoutsControl": {"startAnimation": "继续布局动画", "stopAnimation": "停止布局动画", "layoutGraph": "图布局", "layouts": {"Circular": "环形", "Circlepack": "圆形打包", "Random": "随机", "Noverlaps": "无重叠", "Force Directed": "力导向", "Force Atlas": "力地图"}}, "fullScreenControl": {"fullScreen": "全屏", "windowed": "窗口"}, "legendControl": {"toggleLegend": "切换图例显示"}}, "statusIndicator": {"connected": "已连接", "disconnected": "未连接"}, "statusCard": {"unavailable": "状态信息不可用", "serverInfo": "服务器信息", "workingDirectory": "工作目录", "inputDirectory": "输入目录", "maxParallelInsert": "并行处理文档", "summarySettings": "摘要设置", "llmConfig": "LLM配置", "llmBinding": "LLM绑定", "llmBindingHost": "LLM端点", "llmModel": "LLM模型", "embeddingConfig": "嵌入配置", "embeddingBinding": "嵌入绑定", "embeddingBindingHost": "嵌入端点", "embeddingModel": "嵌入模型", "storageConfig": "存储配置", "kvStorage": "KV存储", "docStatusStorage": "文档状态存储", "graphStorage": "图存储", "vectorStorage": "向量存储", "workspace": "工作空间", "maxGraphNodes": "最大图节点数", "rerankerConfig": "重排序配置", "rerankerBindingHost": "重排序端点", "rerankerModel": "重排序模型", "lockStatus": "锁状态", "threshold": "阈值"}, "propertiesView": {"editProperty": "编辑{{property}}", "editPropertyDescription": "在下方文本区域编辑属性值。", "errors": {"duplicateName": "节点名称已存在", "updateFailed": "更新节点失败", "tryAgainLater": "请稍后重试"}, "success": {"entityUpdated": "节点更新成功", "relationUpdated": "关系更新成功"}, "node": {"title": "节点", "id": "ID", "labels": "标签", "degree": "度数", "properties": "属性", "relationships": "关系(子图内)", "expandNode": "扩展节点", "pruneNode": "修剪节点", "deleteAllNodesError": "拒绝删除图中的所有节点", "nodesRemoved": "已删除 {{count}} 个节点，包括孤立节点", "noNewNodes": "没有发现可以扩展的节点", "propertyNames": {"description": "描述", "entity_id": "名称", "entity_type": "类型", "source_id": "信源ID", "Neighbour": "邻接", "file_path": "信源", "keywords": "Keys", "weight": "权重"}}, "edge": {"title": "关系", "id": "ID", "type": "类型", "source": "源节点", "target": "目标节点", "properties": "属性"}}, "search": {"placeholder": "搜索节点...", "message": "还有 {count} 个"}, "graphLabels": {"selectTooltip": "选择查询标签", "noLabels": "未找到标签", "label": "标签", "placeholder": "搜索标签...", "andOthers": "还有 {count} 个", "refreshTooltip": "重载图形数据(添加文件后需重载)"}, "emptyGraph": "无数据(请重载图形数据)"}, "retrievePanel": {"chatMessage": {"copyTooltip": "复制到剪贴板", "copyError": "复制文本到剪贴板失败", "thinking": "正在思考...", "thinkingTime": "思考用时 {{time}} 秒", "thinkingInProgress": "思考进行中..."}, "retrieval": {"startPrompt": "输入查询开始检索", "clear": "清空", "send": "发送", "placeholder": "输入查询内容 (支持模式前缀: /<Query Mode>)", "error": "错误：获取响应失败", "queryModeError": "仅支持以下查询模式：{{modes}}", "queryModePrefixInvalid": "无效的查询模式前缀。请使用：/<模式> [空格] 查询内容"}, "querySettings": {"parametersTitle": "参数", "parametersDescription": "配置查询参数", "queryMode": "查询模式", "queryModeTooltip": "选择检索策略：\n• Naive：基础搜索，无高级技术\n• Local：上下文相关信息检索\n• Global：利用全局知识库\n• Hybrid：结合本地和全局检索\n• Mix：整合知识图谱和向量检索\n• Bypass：直接传递查询到LLM，不进行检索", "queryModeOptions": {"naive": "Naive", "local": "Local", "global": "Global", "hybrid": "Hybrid", "mix": "Mix", "bypass": "Bypass"}, "responseFormat": "响应格式", "responseFormatTooltip": "定义响应格式。例如：\n• 多段落\n• 单段落\n• 要点", "responseFormatOptions": {"multipleParagraphs": "多段落", "singleParagraph": "单段落", "bulletPoints": "要点"}, "topK": "KG Top K", "topKTooltip": "实体关系检索数量, 适用于非naive模式", "topKPlaceholder": "输入top_k值", "chunkTopK": "文本块 Top K", "chunkTopKTooltip": "文本块检索数量, 适用于所有模式", "chunkTopKPlaceholder": "输入文本块chunk_top_k值", "maxEntityTokens": "实体令牌数上限", "maxEntityTokensTooltip": "统一令牌控制系统中分配给实体上下文的最大令牌数", "maxRelationTokens": "关系令牌数上限", "maxRelationTokensTooltip": "统一令牌控制系统中分配给关系上下文的最大令牌数", "maxTotalTokens": "总令牌数上限", "maxTotalTokensTooltip": "整个查询上下文的最大总令牌预算（实体+关系+文档块+系统提示）", "historyTurns": "历史轮次", "historyTurnsTooltip": "响应上下文中考虑的完整对话轮次（用户-助手对）数量", "historyTurnsPlaceholder": "历史轮次数", "onlyNeedContext": "仅需上下文", "onlyNeedContextTooltip": "如果为True，仅返回检索到的上下文而不生成响应", "onlyNeedPrompt": "仅需提示", "onlyNeedPromptTooltip": "如果为True，仅返回生成的提示而不产生响应", "streamResponse": "流式响应", "streamResponseTooltip": "如果为True，启用实时流式输出响应", "userPrompt": "用户提示词", "userPromptTooltip": "向LLM提供额外的响应要求（与查询内容无关，仅用于处理输出）。", "userPromptPlaceholder": "输入自定义提示词（可选）", "enableRerank": "启用重排", "enableRerankTooltip": "为检索到的文本块启用重排。如果为True但未配置重排模型，将发出警告。默认为True。"}}, "apiSite": {"loading": "正在加载 API 文档..."}, "apiKeyAlert": {"title": "需要 API Key", "description": "请输入您的 API Key 以访问服务", "placeholder": "请输入 API Key", "save": "保存"}, "pagination": {"showing": "显示第 {{start}} 到 {{end}} 条，共 {{total}} 条记录", "page": "页", "pageSize": "每页显示", "firstPage": "首页", "prevPage": "上一页", "nextPage": "下一页", "lastPage": "末页"}}