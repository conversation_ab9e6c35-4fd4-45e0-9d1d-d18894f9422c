import{s as R,a as W,S as v}from"./chunk-OW32GOEJ-DFogk2Dj.js";import{_ as f,c as t,d as H,l as S,e as P,k as z,U,a0 as _,X as C,u as F}from"./mermaid-vendor-BNDdXxLk.js";import{G as O}from"./graph-Ay_vBBag.js";import{l as X}from"./layout-CrX5nS2o.js";import"./chunk-BFAMUDN2-U-jvyawD.js";import"./chunk-SKB7J2MH-Dh8Tfa_E.js";import"./feature-graph-BWr9U7tw.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";import"./_baseUniq-I-HKKcQv.js";import"./_basePickBy-BPAKxgo7.js";var J=f(e=>e.append("circle").attr("class","start-state").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit).attr("cy",t().state.padding+t().state.sizeUnit),"drawStartState"),D=f(e=>e.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",t().state.textHeight).attr("class","divider").attr("x2",t().state.textHeight*2).attr("y1",0).attr("y2",0),"drawDivider"),Y=f((e,i)=>{const d=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+2*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),c=d.node().getBBox();return e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",c.width+2*t().state.padding).attr("height",c.height+2*t().state.padding).attr("rx",t().state.radius),d},"drawSimpleState"),I=f((e,i)=>{const d=f(function(g,B,m){const k=g.append("tspan").attr("x",2*t().state.padding).text(B);m||k.attr("dy",t().state.textHeight)},"addTspan"),r=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+1.3*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.descriptions[0]).node().getBBox(),l=r.height,x=e.append("text").attr("x",t().state.padding).attr("y",l+t().state.padding*.4+t().state.dividerMargin+t().state.textHeight).attr("class","state-description");let a=!0,s=!0;i.descriptions.forEach(function(g){a||(d(x,g,s),s=!1),a=!1});const w=e.append("line").attr("x1",t().state.padding).attr("y1",t().state.padding+l+t().state.dividerMargin/2).attr("y2",t().state.padding+l+t().state.dividerMargin/2).attr("class","descr-divider"),p=x.node().getBBox(),o=Math.max(p.width,r.width);return w.attr("x2",o+3*t().state.padding),e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",o+2*t().state.padding).attr("height",p.height+l+2*t().state.padding).attr("rx",t().state.radius),e},"drawDescrState"),$=f((e,i,d)=>{const c=t().state.padding,r=2*t().state.padding,l=e.node().getBBox(),x=l.width,a=l.x,s=e.append("text").attr("x",0).attr("y",t().state.titleShift).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),p=s.node().getBBox().width+r;let o=Math.max(p,x);o===x&&(o=o+r);let g;const B=e.node().getBBox();i.doc,g=a-c,p>x&&(g=(x-o)/2+c),Math.abs(a-B.x)<c&&p>x&&(g=a-(p-x)/2);const m=1-t().state.textHeight;return e.insert("rect",":first-child").attr("x",g).attr("y",m).attr("class",d?"alt-composit":"composit").attr("width",o).attr("height",B.height+t().state.textHeight+t().state.titleShift+1).attr("rx","0"),s.attr("x",g+c),p<=x&&s.attr("x",a+(o-r)/2-p/2+c),e.insert("rect",":first-child").attr("x",g).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",o).attr("height",t().state.textHeight*3).attr("rx",t().state.radius),e.insert("rect",":first-child").attr("x",g).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",o).attr("height",B.height+3+2*t().state.textHeight).attr("rx",t().state.radius),e},"addTitleAndBox"),q=f(e=>(e.append("circle").attr("class","end-state-outer").attr("r",t().state.sizeUnit+t().state.miniPadding).attr("cx",t().state.padding+t().state.sizeUnit+t().state.miniPadding).attr("cy",t().state.padding+t().state.sizeUnit+t().state.miniPadding),e.append("circle").attr("class","end-state-inner").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit+2).attr("cy",t().state.padding+t().state.sizeUnit+2)),"drawEndState"),Z=f((e,i)=>{let d=t().state.forkWidth,c=t().state.forkHeight;if(i.parentId){let r=d;d=c,c=r}return e.append("rect").style("stroke","black").style("fill","black").attr("width",d).attr("height",c).attr("x",t().state.padding).attr("y",t().state.padding)},"drawForkJoinState"),j=f((e,i,d,c)=>{let r=0;const l=c.append("text");l.style("text-anchor","start"),l.attr("class","noteText");let x=e.replace(/\r\n/g,"<br/>");x=x.replace(/\n/g,"<br/>");const a=x.split(z.lineBreakRegex);let s=1.25*t().state.noteMargin;for(const w of a){const p=w.trim();if(p.length>0){const o=l.append("tspan");if(o.text(p),s===0){const g=o.node().getBBox();s+=g.height}r+=s,o.attr("x",i+t().state.noteMargin),o.attr("y",d+r+1.25*t().state.noteMargin)}}return{textWidth:l.node().getBBox().width,textHeight:r}},"_drawLongText"),K=f((e,i)=>{i.attr("class","state-note");const d=i.append("rect").attr("x",0).attr("y",t().state.padding),c=i.append("g"),{textWidth:r,textHeight:l}=j(e,0,0,c);return d.attr("height",l+2*t().state.noteMargin),d.attr("width",r+t().state.noteMargin*2),d},"drawNote"),L=f(function(e,i){const d=i.id,c={id:d,label:i.id,width:0,height:0},r=e.append("g").attr("id",d).attr("class","stateGroup");i.type==="start"&&J(r),i.type==="end"&&q(r),(i.type==="fork"||i.type==="join")&&Z(r,i),i.type==="note"&&K(i.note.text,r),i.type==="divider"&&D(r),i.type==="default"&&i.descriptions.length===0&&Y(r,i),i.type==="default"&&i.descriptions.length>0&&I(r,i);const l=r.node().getBBox();return c.width=l.width+2*t().state.padding,c.height=l.height+2*t().state.padding,c},"drawState"),A=0,Q=f(function(e,i,d){const c=f(function(s){switch(s){case v.relationType.AGGREGATION:return"aggregation";case v.relationType.EXTENSION:return"extension";case v.relationType.COMPOSITION:return"composition";case v.relationType.DEPENDENCY:return"dependency"}},"getRelationType");i.points=i.points.filter(s=>!Number.isNaN(s.y));const r=i.points,l=U().x(function(s){return s.x}).y(function(s){return s.y}).curve(_),x=e.append("path").attr("d",l(r)).attr("id","edge"+A).attr("class","transition");let a="";if(t().state.arrowMarkerAbsolute&&(a=C(!0)),x.attr("marker-end","url("+a+"#"+c(v.relationType.DEPENDENCY)+"End)"),d.title!==void 0){const s=e.append("g").attr("class","stateLabel"),{x:w,y:p}=F.calcLabelPosition(i.points),o=z.getRows(d.title);let g=0;const B=[];let m=0,k=0;for(let u=0;u<=o.length;u++){const h=s.append("text").attr("text-anchor","middle").text(o[u]).attr("x",w).attr("y",p+g),y=h.node().getBBox();m=Math.max(m,y.width),k=Math.min(k,y.x),S.info(y.x,w,p+g),g===0&&(g=h.node().getBBox().height,S.info("Title height",g,p)),B.push(h)}let N=g*o.length;if(o.length>1){const u=(o.length-1)*g*.5;B.forEach((h,y)=>h.attr("y",p+y*g-u)),N=g*o.length}const n=s.node().getBBox();s.insert("rect",":first-child").attr("class","box").attr("x",w-m/2-t().state.padding/2).attr("y",p-N/2-t().state.padding/2-3.5).attr("width",m+t().state.padding).attr("height",N+t().state.padding),S.info(n)}A++},"drawEdge"),b,T={},V=f(function(){},"setConf"),tt=f(function(e){e.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"insertMarkers"),et=f(function(e,i,d,c){b=t().state;const r=t().securityLevel;let l;r==="sandbox"&&(l=H("#i"+i));const x=r==="sandbox"?H(l.nodes()[0].contentDocument.body):H("body"),a=r==="sandbox"?l.nodes()[0].contentDocument:document;S.debug("Rendering diagram "+e);const s=x.select(`[id='${i}']`);tt(s);const w=c.db.getRootDoc();G(w,s,void 0,!1,x,a,c);const p=b.padding,o=s.node().getBBox(),g=o.width+p*2,B=o.height+p*2,m=g*1.75;P(s,B,m,b.useMaxWidth),s.attr("viewBox",`${o.x-b.padding}  ${o.y-b.padding} `+g+" "+B)},"draw"),at=f(e=>e?e.length*b.fontSizeFactor:1,"getLabelWidth"),G=f((e,i,d,c,r,l,x)=>{const a=new O({compound:!0,multigraph:!0});let s,w=!0;for(s=0;s<e.length;s++)if(e[s].stmt==="relation"){w=!1;break}d?a.setGraph({rankdir:"LR",multigraph:!0,compound:!0,ranker:"tight-tree",ranksep:w?1:b.edgeLengthFactor,nodeSep:w?1:50,isMultiGraph:!0}):a.setGraph({rankdir:"TB",multigraph:!0,compound:!0,ranksep:w?1:b.edgeLengthFactor,nodeSep:w?1:50,ranker:"tight-tree",isMultiGraph:!0}),a.setDefaultEdgeLabel(function(){return{}});const p=x.db.getStates(),o=x.db.getRelations(),g=Object.keys(p);for(const n of g){const u=p[n];d&&(u.parentId=d);let h;if(u.doc){let y=i.append("g").attr("id",u.id).attr("class","stateGroup");h=G(u.doc,y,u.id,!c,r,l,x);{y=$(y,u,c);let E=y.node().getBBox();h.width=E.width,h.height=E.height+b.padding/2,T[u.id]={y:b.compositTitleSize}}}else h=L(i,u,a);if(u.note){const y={descriptions:[],id:u.id+"-note",note:u.note,type:"note"},E=L(i,y,a);u.note.position==="left of"?(a.setNode(h.id+"-note",E),a.setNode(h.id,h)):(a.setNode(h.id,h),a.setNode(h.id+"-note",E)),a.setParent(h.id,h.id+"-group"),a.setParent(h.id+"-note",h.id+"-group")}else a.setNode(h.id,h)}S.debug("Count=",a.nodeCount(),a);let B=0;o.forEach(function(n){B++,S.debug("Setting edge",n),a.setEdge(n.id1,n.id2,{relation:n,width:at(n.title),height:b.labelHeight*z.getRows(n.title).length,labelpos:"c"},"id"+B)}),X(a),S.debug("Graph after layout",a.nodes());const m=i.node();a.nodes().forEach(function(n){n!==void 0&&a.node(n)!==void 0?(S.warn("Node "+n+": "+JSON.stringify(a.node(n))),r.select("#"+m.id+" #"+n).attr("transform","translate("+(a.node(n).x-a.node(n).width/2)+","+(a.node(n).y+(T[n]?T[n].y:0)-a.node(n).height/2)+" )"),r.select("#"+m.id+" #"+n).attr("data-x-shift",a.node(n).x-a.node(n).width/2),l.querySelectorAll("#"+m.id+" #"+n+" .divider").forEach(h=>{const y=h.parentElement;let E=0,M=0;y&&(y.parentElement&&(E=y.parentElement.getBBox().width),M=parseInt(y.getAttribute("data-x-shift"),10),Number.isNaN(M)&&(M=0)),h.setAttribute("x1",0-M+8),h.setAttribute("x2",E-M-8)})):S.debug("No Node "+n+": "+JSON.stringify(a.node(n)))});let k=m.getBBox();a.edges().forEach(function(n){n!==void 0&&a.edge(n)!==void 0&&(S.debug("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(a.edge(n))),Q(i,a.edge(n),a.edge(n).relation))}),k=m.getBBox();const N={id:d||"root",label:d||"root",width:0,height:0};return N.width=k.width+2*b.padding,N.height=k.height+2*b.padding,S.debug("Doc rendered",N,a),N},"renderDoc"),it={setConf:V,draw:et},yt={parser:W,get db(){return new v(1)},renderer:it,styles:R,init:f(e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute},"init")};export{yt as diagram};
