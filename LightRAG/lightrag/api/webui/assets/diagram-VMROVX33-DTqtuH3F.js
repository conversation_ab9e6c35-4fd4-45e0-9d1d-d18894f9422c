import{s as re}from"./chunk-SKB7J2MH-Dh8Tfa_E.js";import{_ as h,F as q,G as K,K as oe,e as ie,ai as D,l as I,O as B,aj as ce,ak as de,al as L,d as _,b as pe,a as he,q as ue,t as me,g as fe,s as ye,H as ge,am as Se,z as xe}from"./mermaid-vendor-BNDdXxLk.js";import{p as be}from"./chunk-353BL4L5-_6GQP54L.js";import{p as ve}from"./treemap-75Q7IDZK-BTRfYg24.js";import"./feature-graph-BWr9U7tw.js";import"./react-vendor-DEwriMA6.js";import"./graph-vendor-B-X5JegA.js";import"./ui-vendor-CeCm8EER.js";import"./utils-vendor-BysuhMZA.js";import"./_baseUniq-I-HKKcQv.js";import"./_basePickBy-BPAKxgo7.js";import"./clone-DwaenhoE.js";var F,U=(F=class{constructor(){this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.setAccTitle=pe,this.getAccTitle=he,this.setDiagramTitle=ue,this.getDiagramTitle=me,this.getAccDescription=fe,this.setAccDescription=ye}getNodes(){return this.nodes}getConfig(){const a=ge,o=K();return q({...a.treemap,...o.treemap??{}})}addNode(a,o){this.nodes.push(a),this.levels.set(a,o),o===0&&(this.outerNodes.push(a),this.root??(this.root=a))}getRoot(){return{name:"",children:this.outerNodes}}addClass(a,o){const s=this.classes.get(a)??{id:a,styles:[],textStyles:[]},c=o.replace(/\\,/g,"§§§").replace(/,/g,";").replace(/§§§/g,",").split(";");c&&c.forEach(n=>{Se(n)&&(s!=null&&s.textStyles?s.textStyles.push(n):s.textStyles=[n]),s!=null&&s.styles?s.styles.push(n):s.styles=[n]}),this.classes.set(a,s)}getClasses(){return this.classes}getStylesForClass(a){var o;return((o=this.classes.get(a))==null?void 0:o.styles)??[]}clear(){xe(),this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.root=void 0}},h(F,"TreeMapDB"),F);function J(d){if(!d.length)return[];const a=[],o=[];return d.forEach(s=>{const c={name:s.name,children:s.type==="Leaf"?void 0:[]};for(c.classSelector=s==null?void 0:s.classSelector,s!=null&&s.cssCompiledStyles&&(c.cssCompiledStyles=[s.cssCompiledStyles]),s.type==="Leaf"&&s.value!==void 0&&(c.value=s.value);o.length>0&&o[o.length-1].level>=s.level;)o.pop();if(o.length===0)a.push(c);else{const n=o[o.length-1].node;n.children?n.children.push(c):n.children=[c]}s.type!=="Leaf"&&o.push({node:c,level:s.level})}),a}h(J,"buildHierarchy");var Ce=h((d,a)=>{be(d,a);const o=[];for(const n of d.TreemapRows??[])n.$type==="ClassDefStatement"&&a.addClass(n.className??"",n.styleText??"");for(const n of d.TreemapRows??[]){const p=n.item;if(!p)continue;const f=n.indent?parseInt(n.indent):0,V=we(p),l=p.classSelector?a.getStylesForClass(p.classSelector):[],z=l.length>0?l.join(";"):void 0,b={level:f,name:V,type:p.$type,value:p.value,classSelector:p.classSelector,cssCompiledStyles:z};o.push(b)}const s=J(o),c=h((n,p)=>{for(const f of n)a.addNode(f,p),f.children&&f.children.length>0&&c(f.children,p+1)},"addNodesRecursively");c(s,0)},"populate"),we=h(d=>d.name?String(d.name):"","getItemName"),Q={parser:{yy:void 0},parse:h(async d=>{var a;try{const s=await ve("treemap",d);I.debug("Treemap AST:",s);const c=(a=Q.parser)==null?void 0:a.yy;if(!(c instanceof U))throw new Error("parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");Ce(s,c)}catch(o){throw I.error("Error parsing treemap:",o),o}},"parse")},Te=10,$=10,M=25,Le=h((d,a,o,s)=>{const c=s.db,n=c.getConfig(),p=n.padding??Te,f=c.getDiagramTitle(),V=c.getRoot(),{themeVariables:l}=K();if(!V)return;const z=f?30:0,b=oe(a),G=n.nodeWidth?n.nodeWidth*$:960,O=n.nodeHeight?n.nodeHeight*$:500,H=G,X=O+z;b.attr("viewBox",`0 0 ${H} ${X}`),ie(b,X,H,n.useMaxWidth);let v;try{const e=n.valueFormat||",";if(e==="$0,0")v=h(t=>"$"+D(",")(t),"valueFormat");else if(e.startsWith("$")&&e.includes(",")){const t=/\.\d+/.exec(e),r=t?t[0]:"";v=h(u=>"$"+D(","+r)(u),"valueFormat")}else if(e.startsWith("$")){const t=e.substring(1);v=h(r=>"$"+D(t||"")(r),"valueFormat")}else v=D(e)}catch(e){I.error("Error creating format function:",e),v=D(",")}const N=B().range(["transparent",l.cScale0,l.cScale1,l.cScale2,l.cScale3,l.cScale4,l.cScale5,l.cScale6,l.cScale7,l.cScale8,l.cScale9,l.cScale10,l.cScale11]),Z=B().range(["transparent",l.cScalePeer0,l.cScalePeer1,l.cScalePeer2,l.cScalePeer3,l.cScalePeer4,l.cScalePeer5,l.cScalePeer6,l.cScalePeer7,l.cScalePeer8,l.cScalePeer9,l.cScalePeer10,l.cScalePeer11]),W=B().range([l.cScaleLabel0,l.cScaleLabel1,l.cScaleLabel2,l.cScaleLabel3,l.cScaleLabel4,l.cScaleLabel5,l.cScaleLabel6,l.cScaleLabel7,l.cScaleLabel8,l.cScaleLabel9,l.cScaleLabel10,l.cScaleLabel11]);f&&b.append("text").attr("x",H/2).attr("y",z/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(f);const j=b.append("g").attr("transform",`translate(0, ${z})`).attr("class","treemapContainer"),ee=ce(V).sum(e=>e.value??0).sort((e,t)=>(t.value??0)-(e.value??0)),Y=de().size([G,O]).paddingTop(e=>e.children&&e.children.length>0?M+$:0).paddingInner(p).paddingLeft(e=>e.children&&e.children.length>0?$:0).paddingRight(e=>e.children&&e.children.length>0?$:0).paddingBottom(e=>e.children&&e.children.length>0?$:0).round(!0)(ee),te=Y.descendants().filter(e=>e.children&&e.children.length>0),A=j.selectAll(".treemapSection").data(te).enter().append("g").attr("class","treemapSection").attr("transform",e=>`translate(${e.x0},${e.y0})`);A.append("rect").attr("width",e=>e.x1-e.x0).attr("height",M).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",e=>e.depth===0?"display: none;":""),A.append("clipPath").attr("id",(e,t)=>`clip-section-${a}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-12)).attr("height",M),A.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class",(e,t)=>`treemapSection section${t}`).attr("fill",e=>N(e.data.name)).attr("fill-opacity",.6).attr("stroke",e=>Z(e.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",e=>{if(e.depth===0)return"display: none;";const t=L({cssCompiledStyles:e.data.cssCompiledStyles});return t.nodeStyles+";"+t.borderStyles.join(";")}),A.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",M/2).attr("dominant-baseline","middle").text(e=>e.depth===0?"":e.data.name).attr("font-weight","bold").attr("style",e=>{if(e.depth===0)return"display: none;";const t="dominant-baseline: middle; font-size: 12px; fill:"+W(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",r=L({cssCompiledStyles:e.data.cssCompiledStyles});return t+r.labelStyles.replace("color:","fill:")}).each(function(e){if(e.depth===0)return;const t=_(this),r=e.data.name;t.text(r);const u=e.x1-e.x0,g=6;let S;n.showValues!==!1&&e.value?S=u-10-30-10-g:S=u-g-6;const x=Math.max(15,S),i=t.node();if(i.getComputedTextLength()>x){const m="...";let y=r;for(;y.length>0;){if(y=r.substring(0,y.length-1),y.length===0){t.text(m),i.getComputedTextLength()>x&&t.text("");break}if(t.text(y+m),i.getComputedTextLength()<=x)break}}}),n.showValues!==!1&&A.append("text").attr("class","treemapSectionValue").attr("x",e=>e.x1-e.x0-10).attr("y",M/2).attr("text-anchor","end").attr("dominant-baseline","middle").text(e=>e.value?v(e.value):"").attr("font-style","italic").attr("style",e=>{if(e.depth===0)return"display: none;";const t="text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+W(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",r=L({cssCompiledStyles:e.data.cssCompiledStyles});return t+r.labelStyles.replace("color:","fill:")});const ae=Y.leaves(),E=j.selectAll(".treemapLeafGroup").data(ae).enter().append("g").attr("class",(e,t)=>`treemapNode treemapLeafGroup leaf${t}${e.data.classSelector?` ${e.data.classSelector}`:""}x`).attr("transform",e=>`translate(${e.x0},${e.y0})`);E.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class","treemapLeaf").attr("fill",e=>e.parent?N(e.parent.data.name):N(e.data.name)).attr("style",e=>L({cssCompiledStyles:e.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",e=>e.parent?N(e.parent.data.name):N(e.data.name)).attr("stroke-width",3),E.append("clipPath").attr("id",(e,t)=>`clip-${a}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-4)).attr("height",e=>Math.max(0,e.y1-e.y0-4)),E.append("text").attr("class","treemapLabel").attr("x",e=>(e.x1-e.x0)/2).attr("y",e=>(e.y1-e.y0)/2).attr("style",e=>{const t="text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+W(e.data.name)+";",r=L({cssCompiledStyles:e.data.cssCompiledStyles});return t+r.labelStyles.replace("color:","fill:")}).attr("clip-path",(e,t)=>`url(#clip-${a}-${t})`).text(e=>e.data.name).each(function(e){const t=_(this),r=e.x1-e.x0,u=e.y1-e.y0,g=t.node(),S=4,T=r-2*S,x=u-2*S;if(T<10||x<10){t.style("display","none");return}let i=parseInt(t.style("font-size"),10);const C=8,m=28,y=.6,w=6,k=2;for(;g.getComputedTextLength()>T&&i>C;)i--,t.style("font-size",`${i}px`);let P=Math.max(w,Math.min(m,Math.round(i*y))),R=i+k+P;for(;R>x&&i>C&&(i--,P=Math.max(w,Math.min(m,Math.round(i*y))),!(P<w&&i===C));)t.style("font-size",`${i}px`),R=i+k+P;t.style("font-size",`${i}px`),(g.getComputedTextLength()>T||i<C||x<i)&&t.style("display","none")}),n.showValues!==!1&&E.append("text").attr("class","treemapValue").attr("x",t=>(t.x1-t.x0)/2).attr("y",function(t){return(t.y1-t.y0)/2}).attr("style",t=>{const r="text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+W(t.data.name)+";",u=L({cssCompiledStyles:t.data.cssCompiledStyles});return r+u.labelStyles.replace("color:","fill:")}).attr("clip-path",(t,r)=>`url(#clip-${a}-${r})`).text(t=>t.value?v(t.value):"").each(function(t){const r=_(this),u=this.parentNode;if(!u){r.style("display","none");return}const g=_(u).select(".treemapLabel");if(g.empty()||g.style("display")==="none"){r.style("display","none");return}const S=parseFloat(g.style("font-size")),T=28,x=.6,i=6,C=2,m=Math.max(i,Math.min(T,Math.round(S*x)));r.style("font-size",`${m}px`);const w=(t.y1-t.y0)/2+S/2+C;r.attr("y",w);const k=t.x1-t.x0,se=t.y1-t.y0-4,ne=k-2*4;r.node().getComputedTextLength()>ne||w+m>se||m<i?r.style("display","none"):r.style("display",null)});const le=n.diagramPadding??8;re(b,le,"flowchart",(n==null?void 0:n.useMaxWidth)||!1)},"draw"),$e=h(function(d,a){return a.db.getClasses()},"getClasses"),Fe={draw:Le,getClasses:$e},ze={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},Ne=h(({treemap:d}={})=>{const a=q(ze,d);return`
  .treemapNode.section {
    stroke: ${a.sectionStrokeColor};
    stroke-width: ${a.sectionStrokeWidth};
    fill: ${a.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${a.leafStrokeColor};
    stroke-width: ${a.leafStrokeWidth};
    fill: ${a.leafFillColor};
  }
  .treemapLabel {
    fill: ${a.labelColor};
    font-size: ${a.labelFontSize};
  }
  .treemapValue {
    fill: ${a.valueColor};
    font-size: ${a.valueFontSize};
  }
  .treemapTitle {
    fill: ${a.titleColor};
    font-size: ${a.titleFontSize};
  }
  `},"getStyles"),Ae=Ne,Xe={parser:Q,get db(){return new U},renderer:Fe,styles:Ae};export{Xe as diagram};
